#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化测试延停时长概率密度功能
"""

import pandas as pd
import numpy as np
from parking_time_filter import TimeFilter

def test_time_filter_only():
    """只测试时间过滤器的时段生成功能"""
    print("=== 测试时段生成功能 ===\n")
    
    # 创建简单的测试数据
    test_data = pd.DataFrame({
        'entry_time': pd.to_datetime(['2024-01-01 08:00:00']),
        'exit_time': pd.to_datetime(['2024-01-01 10:00:00'])
    })
    
    # 创建时间过滤器
    params = {}
    time_filter = TimeFilter(test_data, params)
    
    # 测试1：默认配置
    print("1. 默认配置:")
    print("   控制点: [2, 8, 24, 72] 小时")
    print("   时段长度: [0.5, 1, 16, 48] 小时")
    
    try:
        default_periods = time_filter.create_duration_periods()
        print(f"   生成 {len(default_periods)} 个时段:")
        for i, period in enumerate(default_periods, 1):
            print(f"     {i:2d}. {period}")
    except Exception as e:
        print(f"   错误: {str(e)}")
    
    print("\n" + "="*60 + "\n")
    
    # 测试2：简化配置
    print("2. 简化配置:")
    control_points = [1, 4, 12]  # 1小时, 4小时, 12小时
    period_lengths = [0.5, 2, 6]  # 30分钟, 2小时, 6小时
    print(f"   控制点: {control_points} 小时")
    print(f"   时段长度: {period_lengths} 小时")
    
    try:
        simple_periods = time_filter.create_duration_periods(control_points, period_lengths)
        print(f"   生成 {len(simple_periods)} 个时段:")
        for i, period in enumerate(simple_periods, 1):
            print(f"     {i:2d}. {period}")
    except Exception as e:
        print(f"   错误: {str(e)}")

def test_duration_parsing():
    """测试时长解析功能"""
    print("\n=== 测试时长解析功能 ===\n")
    
    # 模拟解析函数
    def parse_duration_to_hours(duration_str):
        """解析时长字符串为小时数"""
        try:
            duration_str = duration_str.strip()
            
            if '分钟' in duration_str:
                minutes = float(duration_str.replace('分钟', ''))
                return minutes / 60.0
            elif '小时' in duration_str:
                if '分钟' in duration_str:
                    parts = duration_str.split('小时')
                    hours = float(parts[0])
                    minutes_part = parts[1].replace('分钟', '').strip()
                    if minutes_part:
                        minutes = float(minutes_part)
                        return hours + minutes / 60.0
                    return hours
                else:
                    return float(duration_str.replace('小时', ''))
            elif '天' in duration_str:
                if '小时' in duration_str:
                    parts = duration_str.split('天')
                    days = float(parts[0])
                    hours_part = parts[1].replace('小时', '').strip()
                    if hours_part:
                        hours = float(hours_part)
                        return days * 24 + hours
                    return days * 24
                else:
                    return float(duration_str.replace('天', '')) * 24
            else:
                return float(duration_str) / 60.0
                
        except Exception as e:
            print(f"解析失败: {duration_str}, 错误: {str(e)}")
            return 0.0
    
    # 测试用例
    test_cases = [
        "30分钟",
        "1小时",
        "1小时30分钟",
        "2小时",
        "8小时",
        "1天",
        "1天2小时",
        "3天",
        "480分钟",
        "1440分钟"
    ]
    
    print("时长字符串解析测试:")
    print("-" * 40)
    for case in test_cases:
        hours = parse_duration_to_hours(case)
        print(f"{case:15} -> {hours:8.2f} 小时")

def test_simple_assignment():
    """测试简单的时段分配"""
    print("\n=== 测试时段分配 ===\n")
    
    # 生成测试时段
    test_data = pd.DataFrame({
        'entry_time': pd.to_datetime(['2024-01-01 08:00:00']),
        'exit_time': pd.to_datetime(['2024-01-01 10:00:00'])
    })
    
    time_filter = TimeFilter(test_data, {})
    periods = time_filter.create_duration_periods([2, 8, 24], [0.5, 2, 12])
    
    print("生成的时段:")
    for i, period in enumerate(periods):
        print(f"  {i}: {period}")
    
    # 测试时长分配
    test_durations = [0.25, 0.75, 1.5, 3, 6, 10, 25, 50]
    
    print(f"\n时长分配测试:")
    print("-" * 50)
    
    def simple_assign(duration_hours, periods):
        """简化的分配逻辑"""
        # 硬编码的分配规则，基于已知的时段结构
        if duration_hours < 0.5:
            return 0  # "0分钟-30分钟"
        elif duration_hours < 1.0:
            return 1  # "30分钟-1小时"
        elif duration_hours < 1.5:
            return 2  # "1小时-1小时30分钟"
        elif duration_hours < 2.0:
            return 3  # "1小时30分钟-2小时"
        elif duration_hours < 4.0:
            return 4  # "2小时-4小时"
        elif duration_hours < 6.0:
            return 5  # "4小时-6小时"
        elif duration_hours < 8.0:
            return 6  # "6小时-8小时"
        elif duration_hours < 24.0:
            return 7  # "8小时-1天"
        else:
            return 8  # ">1天"
    
    for duration in test_durations:
        period_index = simple_assign(duration, periods)
        period_label = periods[min(period_index, len(periods)-1)]
        print(f"{duration:6.2f} 小时 -> 时段 {period_index}: {period_label}")

if __name__ == "__main__":
    test_time_filter_only()
    test_duration_parsing()
    test_simple_assignment()
