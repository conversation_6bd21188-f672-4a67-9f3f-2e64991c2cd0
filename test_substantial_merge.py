#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试实质性合并逻辑
验证只有在真正发生合并时才生成输出文件
"""

import pandas as pd
import os
from pathlib import Path
import tempfile
import shutil
from excel_data_merger import IntegratedDataMerger

def create_test_scenarios():
    """创建不同的测试场景"""
    scenarios = []
    
    # 场景1：只有一个文件 - 不应该生成合并文件
    scenario1_dir = Path("test_scenario1_single_file")
    if scenario1_dir.exists():
        shutil.rmtree(scenario1_dir)
    scenario1_dir.mkdir()
    
    single_data = pd.DataFrame({
        '姓名': ['张三', '李四'],
        '年龄': [20, 21],
        '专业': ['计算机', '数学']
    })
    single_data.to_csv(scenario1_dir / "单个文件.csv", index=False, encoding='utf-8-sig')
    
    scenarios.append({
        'name': '场景1：单个文件',
        'dir': scenario1_dir,
        'expected_merge': False,
        'description': '只有一个文件，不应该生成合并结果'
    })
    
    # 场景2：多个兼容文件 - 应该生成合并文件
    scenario2_dir = Path("test_scenario2_multiple_files")
    if scenario2_dir.exists():
        shutil.rmtree(scenario2_dir)
    scenario2_dir.mkdir()
    
    data1 = pd.DataFrame({
        '姓名': ['张三', '李四'],
        '年龄': [20, 21],
        '专业': ['计算机', '数学']
    })
    data2 = pd.DataFrame({
        '姓名': ['王五', '赵六'],
        '年龄': [19, 22],
        '专业': ['物理', '化学']
    })
    
    data1.to_csv(scenario2_dir / "文件1.csv", index=False, encoding='utf-8-sig')
    data2.to_csv(scenario2_dir / "文件2.csv", index=False, encoding='utf-8-sig')
    
    scenarios.append({
        'name': '场景2：多个兼容文件',
        'dir': scenario2_dir,
        'expected_merge': True,
        'description': '两个兼容文件，应该生成合并结果'
    })
    
    # 场景3：多个不兼容文件 - 不应该生成合并文件
    scenario3_dir = Path("test_scenario3_incompatible_files")
    if scenario3_dir.exists():
        shutil.rmtree(scenario3_dir)
    scenario3_dir.mkdir()
    
    students_data = pd.DataFrame({
        '姓名': ['张三'],
        '年龄': [20],
        '专业': ['计算机']
    })
    products_data = pd.DataFrame({
        '商品名称': ['苹果'],
        '价格': [5.0],
        '库存数量': [100],
        '供应商': ['供应商A'],
        '分类': ['水果']
    })
    
    students_data.to_csv(scenario3_dir / "学生.csv", index=False, encoding='utf-8-sig')
    products_data.to_csv(scenario3_dir / "商品.csv", index=False, encoding='utf-8-sig')
    
    scenarios.append({
        'name': '场景3：多个不兼容文件',
        'dir': scenario3_dir,
        'expected_merge': False,
        'description': '两个不兼容文件，不应该生成合并结果'
    })
    
    # 场景4：Excel文件单个工作表 - 不应该生成合并文件
    scenario4_dir = Path("test_scenario4_single_sheet")
    if scenario4_dir.exists():
        shutil.rmtree(scenario4_dir)
    scenario4_dir.mkdir()
    
    excel_data = pd.DataFrame({
        '姓名': ['张三', '李四'],
        '年龄': [20, 21],
        '专业': ['计算机', '数学']
    })
    excel_data.to_excel(scenario4_dir / "单工作表.xlsx", index=False, sheet_name='Sheet1')
    
    scenarios.append({
        'name': '场景4：Excel单工作表',
        'dir': scenario4_dir,
        'expected_merge': False,
        'description': 'Excel文件只有一个工作表，不应该生成合并结果'
    })
    
    return scenarios

def test_scenario(scenario, merger):
    """测试单个场景"""
    print(f"\n🧪 测试 {scenario['name']}")
    print(f"   描述: {scenario['description']}")
    print(f"   预期合并: {'是' if scenario['expected_merge'] else '否'}")
    
    output_path = scenario['dir'] / "合并结果.csv"
    
    # 执行合并
    result = merger.smart_merge_directory(
        input_path=str(scenario['dir']),
        output_path=str(output_path),
        file_pattern="*",
        recursive=False
    )
    
    # 检查结果
    file_exists = output_path.exists()
    result_empty = result.empty
    
    print(f"   实际结果:")
    print(f"     - 数据形状: {result.shape}")
    print(f"     - 文件生成: {'是' if file_exists else '否'}")
    print(f"     - 数据为空: {'是' if result_empty else '否'}")
    
    # 验证预期
    if scenario['expected_merge']:
        success = file_exists and not result_empty
        status = "✅ 通过" if success else "❌ 失败"
    else:
        success = not file_exists and result_empty
        status = "✅ 通过" if success else "❌ 失败"
    
    print(f"   测试结果: {status}")
    
    return success

def main():
    """主函数"""
    print("🎯 实质性合并逻辑测试")
    print("="*60)
    print("测试目标：只有在发生实质性合并时才生成输出文件")
    print("实质性合并定义：")
    print("  ✅ 多个不同文件的合并")
    print("  ✅ Excel文件内多个工作表的合并")
    print("  ❌ 单个文件单个工作表（无合并行为）")
    
    # 创建测试场景
    scenarios = create_test_scenarios()
    
    # 创建合并器
    merger = IntegratedDataMerger({
        "show_detailed_progress": False,  # 减少输出
        "add_timestamp_to_filename": False,
        "encoding": "utf-8-sig",
        "supported_extensions": ['.xlsx', '.xls', '.csv', '.txt'],
        "save_log": False,
        "continue_on_error": True,
        "output_format": "auto"
    })
    
    # 测试所有场景
    results = []
    for scenario in scenarios:
        success = test_scenario(scenario, merger)
        results.append(success)
    
    # 总结
    passed = sum(results)
    total = len(results)
    
    print(f"\n📊 测试总结:")
    print(f"="*60)
    print(f"   总测试数: {total}")
    print(f"   通过数量: {passed}")
    print(f"   失败数量: {total - passed}")
    print(f"   通过率: {passed/total*100:.1f}%")
    
    if passed == total:
        print(f"\n✅ 所有测试通过！实质性合并逻辑工作正常。")
    else:
        print(f"\n❌ 有测试失败，请检查代码。")
    
    # 清理测试文件
    print(f"\n🧹 清理测试文件...")
    for scenario in scenarios:
        if scenario['dir'].exists():
            shutil.rmtree(scenario['dir'])
    
    return passed == total

if __name__ == "__main__":
    main()
