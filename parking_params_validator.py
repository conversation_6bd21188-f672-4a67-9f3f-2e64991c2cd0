#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
停车分析参数验证和处理模块
提供健壮的参数验证、转换和默认值处理
"""

import pandas as pd
import re
from datetime import datetime, date
from typing import Dict, Any, Optional, Tuple

class ParamsValidator:
    """参数验证器类"""
    
    def __init__(self, logger=None):
        """
        初始化参数验证器
        
        Args:
            logger: Logger, 日志记录器（可选）
        """
        self.logger = logger
        
        # 支持的日期格式
        self.date_formats = [
            '%Y-%m-%d',     # 2025-05-05
            '%Y/%m/%d',     # 2025/05/05
            '%Y.%m.%d',     # 2025.05.05
            '%Y%m%d',       # 20250505
            '%m-%d-%Y',     # 05-05-2025
            '%m/%d/%Y',     # 05/05/2025
            '%d-%m-%Y',     # 05-05-2025
            '%d/%m/%Y',     # 05/05/2025
        ]
        
        # 支持的月份格式
        self.month_formats = [
            '%Y-%m',        # 2025-05
            '%Y/%m',        # 2025/05
            '%Y.%m',        # 2025.05
            '%Y%m',         # 202505
            '%m-%Y',        # 05-2025
            '%m/%Y',        # 05/2025
        ]
    
    def _log_info(self, message):
        """记录信息日志"""
        if self.logger:
            self.logger.info(message)
    
    def _log_warning(self, message):
        """记录警告日志"""
        if self.logger:
            self.logger.warning(message)
    
    def _log_error(self, message):
        """记录错误日志"""
        if self.logger:
            self.logger.error(message)
    
    def validate_date(self, date_str: str) -> Tuple[bool, Optional[str], Optional[str]]:
        """
        验证和标准化日期格式
        
        Args:
            date_str: str, 日期字符串
            
        Returns:
            tuple: (is_valid, standardized_date, error_message)
        """
        if not date_str:
            return False, None, "日期不能为空"
        
        date_str = str(date_str).strip()
        
        # 尝试各种日期格式
        for fmt in self.date_formats:
            try:
                dt = datetime.strptime(date_str, fmt)
                standardized = dt.strftime('%Y-%m-%d')
                return True, standardized, None
            except ValueError:
                continue
        
        # 尝试pandas的智能解析
        try:
            dt = pd.to_datetime(date_str)
            standardized = dt.strftime('%Y-%m-%d')
            return True, standardized, None
        except Exception:
            pass
        
        return False, None, f"无法解析日期格式: {date_str}"
    
    def validate_month(self, month_str: str) -> Tuple[bool, Optional[str], Optional[str]]:
        """
        验证和标准化月份格式
        
        Args:
            month_str: str, 月份字符串
            
        Returns:
            tuple: (is_valid, standardized_month, error_message)
        """
        if not month_str:
            return False, None, "月份不能为空"
        
        month_str = str(month_str).strip()
        
        # 尝试各种月份格式
        for fmt in self.month_formats:
            try:
                dt = datetime.strptime(month_str, fmt)
                standardized = dt.strftime('%Y-%m')
                return True, standardized, None
            except ValueError:
                continue
        
        # 尝试从日期中提取月份
        is_valid, date_str, _ = self.validate_date(month_str)
        if is_valid:
            dt = datetime.strptime(date_str, '%Y-%m-%d')
            standardized = dt.strftime('%Y-%m')
            return True, standardized, None
        
        # 尝试pandas的智能解析
        try:
            dt = pd.to_datetime(month_str)
            standardized = dt.strftime('%Y-%m')
            return True, standardized, None
        except Exception:
            pass
        
        return False, None, f"无法解析月份格式: {month_str}"
    
    def validate_time_params(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """
        验证时间相关参数
        
        Args:
            params: dict, 参数字典
            
        Returns:
            dict: 验证后的参数字典
        """
        validated_params = params.copy()
        
        # 验证时间间隔参数
        time_interval = params.get('time_interval', 60)
        try:
            time_interval = int(time_interval)
            if time_interval <= 0:
                self._log_warning(f"时间间隔必须大于0，使用默认值60分钟")
                time_interval = 60
            elif time_interval > 1440:  # 24小时
                self._log_warning(f"时间间隔过大({time_interval}分钟)，使用默认值60分钟")
                time_interval = 60
        except (ValueError, TypeError):
            self._log_warning(f"时间间隔格式错误，使用默认值60分钟")
            time_interval = 60
        
        validated_params['time_interval'] = time_interval
        
        # 验证滑动步长参数
        time_slip = params.get('time_slip', 60)
        try:
            time_slip = int(time_slip)
            if time_slip <= 0:
                self._log_warning(f"滑动步长必须大于0，使用默认值60分钟")
                time_slip = 60
            elif time_slip > time_interval:
                self._log_warning(f"滑动步长({time_slip})大于时间间隔({time_interval})，调整为{time_interval}")
                time_slip = time_interval
        except (ValueError, TypeError):
            self._log_warning(f"滑动步长格式错误，使用默认值60分钟")
            time_slip = 60
        
        validated_params['time_slip'] = time_slip
        
        return validated_params
    
    def convert_and_validate_params(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """
        转换和验证所有参数
        
        Args:
            params: dict, 原始参数字典
            
        Returns:
            dict: 验证和转换后的参数字典
        """
        validated_params = params.copy()
        
        # 1. 处理日期参数（包括智能推导的结果）
        date_param = params.get('date')
        if date_param and str(date_param).strip():  # 检查非空且非空白字符串
            is_valid, standardized_date, error_msg = self.validate_date(date_param)
            if is_valid:
                validated_params['聚焦日期'] = standardized_date
                self._log_info(f"日期参数转换: {date_param} → {standardized_date}")

                # 如果没有月份参数，从日期中提取
                month_param = params.get('month')
                if not month_param or not str(month_param).strip():
                    dt = datetime.strptime(standardized_date, '%Y-%m-%d')
                    month_str = dt.strftime('%Y-%m')
                    validated_params['聚焦月份'] = month_str
                    self._log_info(f"从日期提取月份: {month_str}")
            else:
                self._log_error(f"日期参数验证失败: {error_msg}")
                # 不设置聚焦日期，让程序使用所有数据
        
        # 2. 处理月份参数（包括智能推导的结果）
        month_param = params.get('month')
        if month_param and str(month_param).strip():  # 检查非空且非空白字符串
            is_valid, standardized_month, error_msg = self.validate_month(month_param)
            if is_valid:
                # 如果已设置聚焦日期，确保月份与日期一致
                if '聚焦日期' in validated_params:
                    focus_date = validated_params['聚焦日期']
                    expected_month = focus_date[:7]
                    if standardized_month == expected_month:
                        validated_params['聚焦月份'] = standardized_month
                        self._log_info(f"月份参数与聚焦日期一致: {standardized_month}")
                    else:
                        # 使用日期对应的月份
                        validated_params['聚焦月份'] = expected_month
                        self._log_info(f"月份参数与聚焦日期不一致，使用日期对应月份: {expected_month}")
                else:
                    # 没有聚焦日期时，直接设置聚焦月份
                    validated_params['聚焦月份'] = standardized_month
                    self._log_info(f"月份参数转换: {month_param} → {standardized_month}")
            else:
                self._log_error(f"月份参数验证失败: {error_msg}")
        
        # 3. 验证时间相关参数
        validated_params = self.validate_time_params(validated_params)
        
        # 4. 验证模式参数
        mode = params.get('mode', 'mode1_simple')
        if mode not in ['mode1', 'mode1_simple', 'mode2']:
            self._log_warning(f"不支持的模式: {mode}，使用默认模式 mode1_simple")
            validated_params['mode'] = 'mode1_simple'
        
        # 5. 处理输出路径
        output = params.get('output', '')
        if not output:
            validated_params['output'] = ''  # 使用输入文件同目录
        
        # 6. 处理日志文件
        log_file = params.get('log_file')
        if log_file:
            try:
                # 确保日志目录存在
                import os
                log_dir = os.path.dirname(log_file)
                if log_dir and not os.path.exists(log_dir):
                    os.makedirs(log_dir, exist_ok=True)
            except Exception as e:
                self._log_warning(f"创建日志目录失败: {str(e)}")
        
        return validated_params
    
    def get_time_range_info(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """
        获取时间范围信息
        
        Args:
            params: dict, 参数字典
            
        Returns:
            dict: 时间范围信息
        """
        info = {
            'has_focus_date': False,
            'has_focus_month': False,
            'focus_date': None,
            'focus_month': None,
            'analysis_scope': 'all_data'
        }
        
        focus_date = params.get('聚焦日期')
        focus_month = params.get('聚焦月份')
        
        if focus_date:
            info['has_focus_date'] = True
            info['focus_date'] = focus_date
            info['analysis_scope'] = 'single_day'
        elif focus_month:
            info['has_focus_month'] = True
            info['focus_month'] = focus_month
            info['analysis_scope'] = 'single_month'
        
        return info

    def analyze_data_time_range(self, data: pd.DataFrame) -> Dict[str, Any]:
        """
        分析数据源的时间范围和流量分布

        Args:
            data: DataFrame, 数据源

        Returns:
            dict: 时间范围分析结果
        """
        analysis = {
            'has_valid_data': False,
            'date_range': None,
            'month_range': None,
            'peak_date': None,
            'peak_month': None,
            'daily_flow': {},
            'monthly_flow': {},
            'error_message': None
        }

        try:
            if data is None or data.empty:
                analysis['error_message'] = "数据源为空"
                return analysis

            # 查找时间字段（支持不同模式的时间字段）
            time_fields = [
                'entry_time', 'timestamp', 'time', '通过时间', '时间记录字段',  # mode1/mode1_simple
                '入场时间', '出场时间', '进场时间字段', '出场时间字段',  # mode2
                '通行时间', '记录时间', '时间戳', '日期时间'  # 通用字段
            ]
            time_field = None

            for field in time_fields:
                if field in data.columns:
                    time_field = field
                    break

            if not time_field:
                # 如果没有找到预定义的时间字段，尝试查找包含时间相关关键词的字段
                for col in data.columns:
                    col_lower = str(col).lower()
                    if any(keyword in col_lower for keyword in ['time', '时间', 'date', '日期', 'timestamp', '时戳']):
                        time_field = col
                        break

            if not time_field:
                analysis['error_message'] = "未找到时间字段"
                return analysis

            # 转换时间字段
            try:
                data_time = pd.to_datetime(data[time_field], errors='coerce')
                data_time = data_time.dropna()

                if data_time.empty:
                    analysis['error_message'] = "时间字段无有效数据"
                    return analysis

            except Exception as e:
                analysis['error_message'] = f"时间字段转换失败: {str(e)}"
                return analysis

            # 分析日期范围
            min_date = data_time.min().date()
            max_date = data_time.max().date()
            analysis['date_range'] = (min_date.strftime('%Y-%m-%d'), max_date.strftime('%Y-%m-%d'))

            # 分析月份范围
            min_month = data_time.min().strftime('%Y-%m')
            max_month = data_time.max().strftime('%Y-%m')
            analysis['month_range'] = (min_month, max_month)

            # 计算每日流量
            daily_counts = data_time.dt.date.value_counts().sort_index()
            analysis['daily_flow'] = {date.strftime('%Y-%m-%d'): count for date, count in daily_counts.items()}

            # 计算每月流量
            monthly_counts = data_time.dt.to_period('M').value_counts().sort_index()
            analysis['monthly_flow'] = {str(month): count for month, count in monthly_counts.items()}

            # 找出流量最大的日期
            if not daily_counts.empty:
                peak_date = daily_counts.idxmax()
                analysis['peak_date'] = peak_date.strftime('%Y-%m-%d')

            # 找出流量最大的月份
            if not monthly_counts.empty:
                peak_month = monthly_counts.idxmax()
                analysis['peak_month'] = str(peak_month)

            analysis['has_valid_data'] = True

            if self.logger:
                self.logger.info(f"数据时间范围: {analysis['date_range'][0]} 至 {analysis['date_range'][1]}")
                self.logger.info(f"流量最大日期: {analysis['peak_date']} ({daily_counts.max()}条记录)")
                self.logger.info(f"流量最大月份: {analysis['peak_month']} ({monthly_counts.max()}条记录)")

        except Exception as e:
            analysis['error_message'] = f"数据分析失败: {str(e)}"
            if self.logger:
                self.logger.error(analysis['error_message'])

        return analysis

    def is_date_in_data_range(self, date_str: str, data_analysis: Dict[str, Any]) -> bool:
        """检查日期是否在数据范围内"""
        if not data_analysis.get('has_valid_data') or not date_str:
            return False

        try:
            target_date = datetime.strptime(date_str, '%Y-%m-%d').date()
            min_date = datetime.strptime(data_analysis['date_range'][0], '%Y-%m-%d').date()
            max_date = datetime.strptime(data_analysis['date_range'][1], '%Y-%m-%d').date()

            return min_date <= target_date <= max_date
        except:
            return False

    def is_month_in_data_range(self, month_str: str, data_analysis: Dict[str, Any]) -> bool:
        """检查月份是否在数据范围内"""
        if not data_analysis.get('has_valid_data') or not month_str:
            return False

        try:
            target_month = datetime.strptime(month_str, '%Y-%m')
            min_month = datetime.strptime(data_analysis['month_range'][0], '%Y-%m')
            max_month = datetime.strptime(data_analysis['month_range'][1], '%Y-%m')

            return min_month <= target_month <= max_month
        except:
            return False

    def find_peak_date_in_month(self, month_str: str, data_analysis: Dict[str, Any]) -> Optional[str]:
        """找出指定月份中流量最大的日期"""
        if not data_analysis.get('has_valid_data') or not month_str:
            return None

        try:
            target_month = month_str
            daily_flow = data_analysis.get('daily_flow', {})

            # 筛选出指定月份的日期
            month_dates = {}
            for date_str, flow in daily_flow.items():
                if date_str.startswith(target_month):
                    month_dates[date_str] = flow

            if not month_dates:
                return None

            # 找出流量最大的日期
            peak_date = max(month_dates.items(), key=lambda x: x[1])[0]
            return peak_date

        except Exception as e:
            if self.logger:
                self.logger.error(f"查找月份{month_str}的峰值日期失败: {str(e)}")
            return None

    def smart_infer_params(self, params: Dict[str, Any], data: pd.DataFrame) -> Dict[str, Any]:
        """
        基于数据源智能推导参数

        Args:
            params: dict, 原始参数
            data: DataFrame, 数据源

        Returns:
            dict: 智能推导后的参数
        """
        # 分析数据源
        data_analysis = self.analyze_data_time_range(data)

        if not data_analysis.get('has_valid_data'):
            self._log_warning(f"数据源分析失败: {data_analysis.get('error_message', '未知错误')}")
            return params

        # 获取原始参数
        original_date = params.get('date')
        original_month = params.get('month')

        # 验证原始日期和月份（检查非空且非空白字符串）
        date_valid, validated_date, date_error = self.validate_date(original_date) if (original_date and str(original_date).strip()) else (False, None, "未提供日期")
        month_valid, validated_month, month_error = self.validate_month(original_month) if (original_month and str(original_month).strip()) else (False, None, "未提供月份")

        # 检查日期和月份是否在数据范围内
        date_in_range = date_valid and self.is_date_in_data_range(validated_date, data_analysis)
        month_in_range = month_valid and self.is_month_in_data_range(validated_month, data_analysis)

        # 智能推导逻辑
        final_date = None
        final_month = None
        inference_reason = []

        if date_valid and date_in_range:
            # 情况1: date有效且在数据范围内
            final_date = validated_date
            final_month = validated_date[:7]  # 从日期提取月份

            if original_month and (not month_valid or validated_month != final_month):
                inference_reason.append(f"强制采用date所在月份 {final_month}，忽略month参数")
            else:
                inference_reason.append(f"使用有效的date参数: {final_date}")

        elif month_valid and month_in_range:
            # 情况2: date无效但month有效且在数据范围内
            final_month = validated_month
            peak_date = self.find_peak_date_in_month(validated_month, data_analysis)

            if peak_date:
                final_date = peak_date
                inference_reason.append(f"date无效，采用month {final_month} 中流量最大的日期: {final_date}")
            else:
                # 如果找不到峰值日期，使用月份第一天
                final_date = f"{validated_month}-01"
                inference_reason.append(f"date无效，采用month {final_month} 的第一天: {final_date}")

        else:
            # 情况3: date和month都无效或不在数据范围内
            peak_date = data_analysis.get('peak_date')
            peak_month = data_analysis.get('peak_month')

            if peak_date and peak_month:
                final_date = peak_date
                final_month = peak_month
                inference_reason.append(f"date和month都无效，采用数据源中流量最大的日期: {final_date}")
            else:
                # 兜底方案：使用数据范围的第一天
                first_date = data_analysis['date_range'][0]
                final_date = first_date
                final_month = first_date[:7]
                inference_reason.append(f"无法确定峰值，使用数据范围的第一天: {final_date}")

        # 更新参数
        inferred_params = params.copy()
        inferred_params['date'] = final_date
        inferred_params['month'] = final_month

        # 确保月份与日期一致
        if final_date and not final_month:
            final_month = final_date[:7]
            inferred_params['month'] = final_month

        # 记录推导过程
        if inference_reason:
            reason_text = "; ".join(inference_reason)
            self._log_info(f"智能参数推导: {reason_text}")
            inferred_params['_inference_reason'] = reason_text

        # 记录数据范围信息
        inferred_params['_data_date_range'] = data_analysis['date_range']
        inferred_params['_data_month_range'] = data_analysis['month_range']
        inferred_params['_data_peak_date'] = data_analysis.get('peak_date')
        inferred_params['_data_peak_month'] = data_analysis.get('peak_month')

        return inferred_params


def validate_and_convert_params(params: Dict[str, Any], data: pd.DataFrame = None, logger=None) -> Dict[str, Any]:
    """
    验证和转换参数的便捷函数（增强版，支持基于数据源的智能推导）

    Args:
        params: dict, 原始参数字典
        data: DataFrame, 数据源（可选，用于智能参数推导）
        logger: Logger, 日志记录器（可选）

    Returns:
        dict: 验证和转换后的参数字典
    """
    validator = ParamsValidator(logger)

    # 如果提供了数据源，先进行智能推导
    if data is not None:
        params = validator.smart_infer_params(params, data)

    # 然后进行常规的参数验证和转换
    return validator.convert_and_validate_params(params)


if __name__ == "__main__":
    # 测试参数验证功能
    print("🧪 测试参数验证功能")
    
    test_cases = [
        {
            'name': '标准格式',
            'params': {
                'date': '2025-05-05',
                'month': '2025-05',
                'mode': 'mode1_simple',
                'time_interval': 60,
                'time_slip': 15
            }
        },
        {
            'name': '非标准日期格式',
            'params': {
                'date': '2025/05/05',
                'mode': 'mode1',
                'time_interval': '30',
                'time_slip': '15'
            }
        },
        {
            'name': '错误格式',
            'params': {
                'date': 'invalid-date',
                'month': 'invalid-month',
                'mode': 'invalid-mode',
                'time_interval': -10,
                'time_slip': 'abc'
            }
        },
        {
            'name': '只有月份',
            'params': {
                'month': '2025-05',
                'mode': 'mode2'
            }
        }
    ]
    
    validator = ParamsValidator()
    
    for test_case in test_cases:
        print(f"\n📋 测试用例: {test_case['name']}")
        print(f"   输入: {test_case['params']}")
        
        try:
            result = validator.convert_and_validate_params(test_case['params'])
            print(f"   输出: {result}")
            
            # 显示时间范围信息
            time_info = validator.get_time_range_info(result)
            print(f"   时间范围: {time_info['analysis_scope']}")
            
        except Exception as e:
            print(f"   错误: {str(e)}")
    
    print(f"\n✅ 参数验证功能测试完成")
