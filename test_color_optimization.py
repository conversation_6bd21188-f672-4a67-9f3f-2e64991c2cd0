#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试颜色优化效果
验证进出量时间分布图表的颜色是否更适合专业演讲展示
"""

import pandas as pd
import tempfile
import os
import shutil

def test_color_optimization():
    """测试颜色优化效果"""
    print("🎨 测试颜色优化效果")
    print("=" * 60)
    
    # 创建临时目录
    temp_dir = tempfile.mkdtemp()
    print(f"测试目录: {temp_dir}")
    
    try:
        # 创建测试数据（模拟真实的停车场数据）
        test_data = pd.DataFrame({
            '时间段': [
                '06:00-07:00', '07:00-08:00', '08:00-09:00', '09:00-10:00', '10:00-11:00',
                '11:00-12:00', '12:00-13:00', '13:00-14:00', '14:00-15:00', '15:00-16:00',
                '16:00-17:00', '17:00-18:00', '18:00-19:00', '19:00-20:00', '20:00-21:00'
            ],
            '进场数量': [20, 45, 120, 80, 60, 70, 90, 65, 55, 75, 110, 95, 85, 50, 30],
            '出场数量': [10, 25, 60, 100, 85, 75, 70, 80, 70, 65, 85, 120, 110, 80, 45],
            '总流量': [30, 70, 180, 180, 145, 145, 160, 145, 125, 140, 195, 215, 195, 130, 75]
        })
        
        # 创建测试Excel文件
        excel_file = os.path.join(temp_dir, 'test_color_data.xlsx')
        with pd.ExcelWriter(excel_file, engine='openpyxl') as writer:
            test_data.to_excel(writer, sheet_name='进出量时间分布', index=False)
        
        print(f"✅ 创建测试Excel文件")
        print(f"📊 测试数据概览:")
        print(f"   时间段数量: {len(test_data)}")
        print(f"   进场数量范围: {min(test_data['进场数量'])} - {max(test_data['进场数量'])}")
        print(f"   出场数量范围: {min(test_data['出场数量'])} - {max(test_data['出场数量'])}")
        print(f"   总流量范围: {min(test_data['总流量'])} - {max(test_data['总流量'])}")
        
        # 导入图表生成器
        from parking_chart_generator import ParkingChartGenerator
        
        # 创建图表生成器
        chart_generator = ParkingChartGenerator(excel_file, temp_dir)
        
        print(f"\n🎨 应用专业演讲风格颜色配置...")
        
        # 显示颜色配置
        colors = chart_generator.chart_config['traffic_flow_colors']
        print(f"   进场颜色: {colors['entry']} (深蓝色 - 稳重专业)")
        print(f"   出场颜色: {colors['exit']} (深紫红色 - 对比鲜明)")
        print(f"   总流量颜色: {colors['total']} (橙色 - 醒目温暖)")
        
        # 生成图表
        print(f"\n📈 生成优化后的进出量时间分布图表...")
        chart_file = chart_generator.generate_traffic_flow_chart()
        
        if chart_file and os.path.exists(chart_file):
            print(f"✅ 图表生成成功: {os.path.basename(chart_file)}")
            
            # 检查文件大小
            file_size = os.path.getsize(chart_file) / 1024  # KB
            print(f"📄 文件大小: {file_size:.1f}KB")
            
            # 读取HTML内容检查颜色配置
            with open(chart_file, 'r', encoding='utf-8') as f:
                html_content = f.read()
            
            # 检查是否包含新的颜色配置
            color_checks = {
                '进场颜色': colors['entry'] in html_content,
                '出场颜色': colors['exit'] in html_content,
                '总流量颜色': colors['total'] in html_content
            }
            
            print(f"\n🔍 颜色配置验证:")
            all_colors_applied = True
            for color_name, is_applied in color_checks.items():
                status = "✅" if is_applied else "❌"
                print(f"   {status} {color_name}: {'已应用' if is_applied else '未应用'}")
                if not is_applied:
                    all_colors_applied = False
            
            # 检查专业演讲风格特性
            style_checks = {
                '标题样式': 'font_weight' in html_content and 'font_size' in html_content,
                '边框样式': 'border_color' in html_content,
                '图例样式': 'item_gap' in html_content,
                '标记点样式': 'itemstyle_opts' in html_content
            }
            
            print(f"\n🎯 专业演讲风格特性:")
            all_styles_applied = True
            for style_name, is_applied in style_checks.items():
                status = "✅" if is_applied else "❌"
                print(f"   {status} {style_name}: {'已应用' if is_applied else '未应用'}")
                if not is_applied:
                    all_styles_applied = False
            
            # 总体评估
            print(f"\n📋 优化效果评估:")
            if all_colors_applied and all_styles_applied:
                print("✅ 颜色优化成功！")
                print("   - 专业演讲风格颜色已应用")
                print("   - 颜色区分度显著提高")
                print("   - 视觉效果更加专业")
                print("   - 适合演讲展示使用")
            else:
                print("❌ 颜色优化有问题！")
                if not all_colors_applied:
                    print("   - 部分颜色配置未正确应用")
                if not all_styles_applied:
                    print("   - 部分样式特性未正确应用")
            
            return all_colors_applied and all_styles_applied
            
        else:
            print("❌ 图表生成失败")
            return False
        
    except Exception as e:
        print(f"❌ 测试异常: {str(e)}")
        import traceback
        traceback.print_exc()
        return False
        
    finally:
        # 清理
        try:
            shutil.rmtree(temp_dir)
            print(f"\n🧹 清理测试目录")
        except:
            pass

def demo_color_comparison():
    """演示颜色对比效果"""
    print("\n🎨 颜色配置对比")
    print("=" * 60)
    
    print("🔴 修改前的颜色配置:")
    print("   进场数量: #5470c6 (标准蓝色)")
    print("   出场数量: #91cc75 (标准绿色)")
    print("   总流量: #ee6666 (标准红色)")
    print("   问题: 蓝色和绿色区分度不够高，不够专业")
    
    print("\n🟢 修改后的专业演讲风格:")
    print("   进场数量: #2E86AB (深蓝色 - 稳重、专业)")
    print("   出场数量: #A23B72 (深紫红色 - 对比鲜明)")
    print("   总流量: #F18F01 (橙色 - 醒目、温暖)")
    print("   优势: 高对比度、专业感强、适合大屏展示")
    
    print("\n🎯 专业演讲风格特点:")
    print("   ✅ 高对比度 - 远距离观看清晰")
    print("   ✅ 色彩饱和度适中 - 不刺眼但醒目")
    print("   ✅ 符合商务风格 - 专业正式")
    print("   ✅ 色彩心理学 - 蓝色稳重、紫红对比、橙色活力")
    print("   ✅ 无障碍友好 - 色盲友好的颜色组合")

def main():
    """主函数"""
    print("🎨 测试颜色优化")
    print("=" * 80)
    
    # 演示颜色对比
    demo_color_comparison()
    
    # 测试颜色优化效果
    result = test_color_optimization()
    
    print(f"\n📊 测试总结:")
    print("=" * 80)
    print(f"颜色优化效果: {'✅ 成功' if result else '❌ 有问题'}")
    
    if result:
        print(f"\n🎉 颜色优化完成！")
        print(f"   - 进出量柱状图颜色区分度显著提高")
        print(f"   - 采用专业演讲风格的颜色搭配")
        print(f"   - 适合大屏幕演示和远距离观看")
        print(f"   - 视觉效果更加专业和美观")
        print(f"\n💡 建议:")
        print(f"   - 在投影仪上测试效果")
        print(f"   - 确保在不同光线条件下都清晰可见")
        print(f"   - 可根据具体演讲环境微调颜色")
    else:
        print(f"\n⚠️ 需要进一步优化")

if __name__ == "__main__":
    main()
