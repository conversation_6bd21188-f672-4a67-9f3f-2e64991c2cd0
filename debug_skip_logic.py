#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试跳过逻辑
"""

import pandas as pd
import numpy as np
from parking_report_generator import ReportGenerator

def debug_skip_logic():
    """调试跳过逻辑"""
    print("=== 调试跳过逻辑 ===\n")
    
    # 创建简单测试数据
    data = pd.DataFrame({
        'timestamp': pd.to_datetime(['2024-01-01 08:00:00'] * 5),
        'direction': ['入场'] * 5,
        'gate': ['A口'] * 5,
        'vtype': ['小型车'] * 5
    })
    
    params = {'mode': 'mode1_simple'}
    
    print(f"测试参数: {params}")
    print(f"数据: {len(data)} 条记录")
    
    # 创建报告生成器
    generator = ReportGenerator(data, params)
    generator.processed_data = data
    
    # 检查参数
    print(f"生成器参数: {generator.params}")
    print(f"模式检查: {generator.params.get('mode')} == 'mode1_simple' -> {generator.params.get('mode') == 'mode1_simple'}")
    
    # 模拟工作表创建顺序检查
    sheet_creation_order = [
        ('概览', None),
        ('数据_总量', None),
        ('停车时长_分析日', None),
        ('延停时长概率密度', None),
        ('车辆类型_分析日', None)
    ]
    
    print(f"\n模拟工作表跳过检查:")
    skip_sheets = ['停车时长_分析日', '停车时长_入场_分析日', '车辆类型_分析日', '在场车辆分布', '道闸组合_分析日', '进出量时间分布_车型_道闸', '延停时长概率密度']
    
    for sheet_name, _ in sheet_creation_order:
        print(f"  检查工作表: {sheet_name}")
        
        if generator.params.get('mode') == 'mode1_simple':
            if sheet_name in skip_sheets:
                print(f"    ✅ 应该跳过: {sheet_name}")
            else:
                print(f"    ➡️  应该创建: {sheet_name}")
        else:
            print(f"    ➡️  非mode1_simple，应该创建: {sheet_name}")
    
    print(f"\nskip_sheets列表: {skip_sheets}")
    print(f"'延停时长概率密度' in skip_sheets: {'延停时长概率密度' in skip_sheets}")

if __name__ == "__main__":
    debug_skip_logic()
