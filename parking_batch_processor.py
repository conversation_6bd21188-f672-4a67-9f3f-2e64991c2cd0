#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
停车数据批量处理器
支持对指定目录下的所有xlsx、csv、txt文件进行批量分析处理
"""

import os
import sys
import glob
import logging
from datetime import datetime
from pathlib import Path
import pandas as pd
from parking_main import main

class BatchProcessor:
    """批量处理器类"""
    
    def __init__(self, input_path, base_params, mode_config, logger=None):
        """
        初始化批量处理器
        
        Args:
            input_path: str, 输入目录路径
            base_params: dict, 基础参数配置
            mode_config: dict, 模式配置
            logger: Logger, 日志记录器（可选）
        """
        self.input_path = Path(input_path)
        self.base_params = base_params.copy()
        self.mode_config = mode_config
        self.logger = logger or self._setup_default_logger()
        
        # 支持的文件格式
        self.supported_extensions = ['.xlsx', '.xls', '.csv', '.txt']
        
        # 处理结果统计
        self.results = {
            'total_files': 0,
            'processed_files': 0,
            'failed_files': 0,
            'skipped_files': 0,
            'success_files': [],
            'failed_files_detail': [],
            'skipped_files_detail': []
        }
    
    def _setup_default_logger(self):
        """设置默认日志记录器"""
        logger = logging.getLogger('BatchProcessor')
        logger.setLevel(logging.INFO)
        
        # 创建控制台处理器
        console_handler = logging.StreamHandler()
        console_handler.setLevel(logging.INFO)
        
        # 创建格式器
        formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
        console_handler.setFormatter(formatter)
        
        # 添加处理器
        if not logger.handlers:
            logger.addHandler(console_handler)
        
        return logger
    
    def find_files(self, recursive=True):
        """
        查找目录下的所有支持格式文件
        
        Args:
            recursive: bool, 是否递归查找子目录
            
        Returns:
            list: 找到的文件路径列表
        """
        files = []
        
        if not self.input_path.exists():
            raise FileNotFoundError(f"输入路径不存在: {self.input_path}")
        
        if not self.input_path.is_dir():
            raise ValueError(f"输入路径不是目录: {self.input_path}")
        
        # 构建搜索模式
        search_patterns = []
        for ext in self.supported_extensions:
            if recursive:
                pattern = f"**/*{ext}"
            else:
                pattern = f"*{ext}"
            search_patterns.append(pattern)
        
        # 查找文件
        for pattern in search_patterns:
            found_files = list(self.input_path.glob(pattern))
            files.extend(found_files)
        
        # 去重并排序
        files = sorted(list(set(files)))
        
        self.logger.info(f"在目录 {self.input_path} 中找到 {len(files)} 个支持的文件")
        
        return files
    
    def validate_file(self, file_path):
        """
        验证文件是否可以处理
        
        Args:
            file_path: Path, 文件路径
            
        Returns:
            tuple: (is_valid, reason)
        """
        try:
            # 检查文件是否存在
            if not file_path.exists():
                return False, "文件不存在"
            
            # 检查文件大小
            file_size_mb = file_path.stat().st_size / (1024 * 1024)
            max_size_mb = 500  # 最大文件大小限制
            if file_size_mb > max_size_mb:
                return False, f"文件过大 ({file_size_mb:.1f}MB > {max_size_mb}MB)"
            
            # 检查文件扩展名
            if file_path.suffix.lower() not in self.supported_extensions:
                return False, f"不支持的文件格式: {file_path.suffix}"
            
            # 尝试读取文件头部验证格式
            try:
                from parking_data_reader import read_data_file
                # 只读取前几行验证格式
                data = read_data_file(str(file_path))
                if data.empty:
                    return False, "文件为空或无法读取数据"
                
                return True, "验证通过"
                
            except Exception as e:
                return False, f"文件格式验证失败: {str(e)}"
        
        except Exception as e:
            return False, f"文件验证异常: {str(e)}"
    
    def process_single_file(self, file_path):
        """
        处理单个文件
        
        Args:
            file_path: Path, 文件路径
            
        Returns:
            tuple: (success, result_info)
        """
        try:
            self.logger.info(f"开始处理文件: {file_path.name}")
            
            # 验证文件
            is_valid, reason = self.validate_file(file_path)
            if not is_valid:
                self.logger.warning(f"跳过文件 {file_path.name}: {reason}")
                return False, f"跳过: {reason}"
            
            # 准备参数
            file_params = self.base_params.copy()
            file_params['input_file'] = str(file_path)
            
            # 设置输出路径（与输入文件同目录）
            if 'output' not in file_params or not file_params['output']:
                file_params['output'] = str(file_path.parent)
            
            # 调用主程序处理
            result = main(file_params, self.mode_config)
            
            if result == 0:
                # 检查输出文件是否生成
                output_file = file_path.parent / f"{file_path.stem}_analysis.xlsx"
                if output_file.exists():
                    file_size_kb = output_file.stat().st_size / 1024
                    success_info = f"成功 (输出: {output_file.name}, {file_size_kb:.1f}KB)"
                    self.logger.info(f"文件处理成功: {file_path.name} -> {output_file.name}")
                    return True, success_info
                else:
                    error_info = "处理完成但未找到输出文件"
                    self.logger.error(f"文件处理异常: {file_path.name} - {error_info}")
                    return False, error_info
            else:
                error_info = f"处理失败 (返回码: {result})"
                self.logger.error(f"文件处理失败: {file_path.name} - {error_info}")
                return False, error_info
        
        except Exception as e:
            error_info = f"处理异常: {str(e)}"
            self.logger.error(f"文件处理异常: {file_path.name} - {error_info}")
            return False, error_info
    
    def process_batch(self, recursive=True, continue_on_error=True):
        """
        批量处理文件
        
        Args:
            recursive: bool, 是否递归处理子目录
            continue_on_error: bool, 遇到错误是否继续处理其他文件
            
        Returns:
            dict: 处理结果统计
        """
        start_time = datetime.now()
        
        try:
            # 查找文件
            files = self.find_files(recursive=recursive)
            self.results['total_files'] = len(files)
            
            if not files:
                self.logger.warning("未找到任何支持的文件")
                return self.results
            
            self.logger.info(f"开始批量处理 {len(files)} 个文件...")
            self.logger.info(f"处理模式: {self.base_params.get('mode', 'unknown')}")
            self.logger.info(f"递归处理: {'是' if recursive else '否'}")
            self.logger.info(f"错误处理: {'继续' if continue_on_error else '停止'}")
            
            # 逐个处理文件
            for i, file_path in enumerate(files, 1):
                print(f"\n📄 处理文件 {i}/{len(files)}: {file_path.name}")
                
                try:
                    success, info = self.process_single_file(file_path)
                    
                    if success:
                        self.results['processed_files'] += 1
                        self.results['success_files'].append({
                            'file': str(file_path),
                            'info': info
                        })
                        print(f"   ✅ {info}")
                    else:
                        if "跳过" in info:
                            self.results['skipped_files'] += 1
                            self.results['skipped_files_detail'].append({
                                'file': str(file_path),
                                'reason': info
                            })
                            print(f"   ⏭️  {info}")
                        else:
                            self.results['failed_files'] += 1
                            self.results['failed_files_detail'].append({
                                'file': str(file_path),
                                'error': info
                            })
                            print(f"   ❌ {info}")
                            
                            if not continue_on_error:
                                self.logger.error("遇到错误，停止批量处理")
                                break
                
                except KeyboardInterrupt:
                    self.logger.info("用户中断批量处理")
                    break
                except Exception as e:
                    error_info = f"处理异常: {str(e)}"
                    self.results['failed_files'] += 1
                    self.results['failed_files_detail'].append({
                        'file': str(file_path),
                        'error': error_info
                    })
                    print(f"   ❌ {error_info}")
                    
                    if not continue_on_error:
                        self.logger.error("遇到异常，停止批量处理")
                        break
            
            # 计算处理时间
            end_time = datetime.now()
            duration = end_time - start_time
            
            # 输出处理结果
            self._print_summary(duration)
            
            return self.results
        
        except Exception as e:
            self.logger.error(f"批量处理失败: {str(e)}")
            raise
    
    def _print_summary(self, duration):
        """打印处理结果摘要"""
        print(f"\n" + "=" * 80)
        print(f"📊 批量处理完成！")
        print(f"=" * 80)
        
        print(f"⏱️  处理时间: {duration}")
        print(f"📁 处理目录: {self.input_path}")
        print(f"🔧 处理模式: {self.base_params.get('mode', 'unknown')}")
        
        print(f"\n📈 处理统计:")
        print(f"   📄 总文件数: {self.results['total_files']}")
        print(f"   ✅ 成功处理: {self.results['processed_files']}")
        print(f"   ❌ 处理失败: {self.results['failed_files']}")
        print(f"   ⏭️  跳过文件: {self.results['skipped_files']}")
        
        if self.results['total_files'] > 0:
            success_rate = (self.results['processed_files'] / self.results['total_files']) * 100
            print(f"   📊 成功率: {success_rate:.1f}%")
        
        # 显示失败文件详情
        if self.results['failed_files_detail']:
            print(f"\n❌ 失败文件详情:")
            for item in self.results['failed_files_detail']:
                file_name = Path(item['file']).name
                print(f"   - {file_name}: {item['error']}")
        
        # 显示跳过文件详情
        if self.results['skipped_files_detail']:
            print(f"\n⏭️  跳过文件详情:")
            for item in self.results['skipped_files_detail']:
                file_name = Path(item['file']).name
                print(f"   - {file_name}: {item['reason']}")
        
        print(f"=" * 80)


def create_batch_processor(input_path, base_params, mode_config):
    """
    创建批量处理器的便捷函数
    
    Args:
        input_path: str, 输入目录路径
        base_params: dict, 基础参数配置
        mode_config: dict, 模式配置
        
    Returns:
        BatchProcessor: 批量处理器实例
    """
    return BatchProcessor(input_path, base_params, mode_config)


if __name__ == "__main__":
    # 示例用法
    print("🚀 停车数据批量处理器")
    print("=" * 50)
    
    # 示例配置
    input_directory = r"C:\Users\<USER>\Desktop\停车分析\批量测试"  # 修改为您的目录
    
    # 基础参数配置
    base_params = {
        'output': '',  # 留空，将使用输入文件同目录
        'date': '2025-05-05',
        'month': '2025-05',
        'mode': 'mode1_simple',
        'time_interval': 60,
        'time_slip': 15,
    }
    
    # 模式配置
    mode_config = {
        'mode1_simple': {
            '车辆唯一标识字段': '',
            '车辆类型字段': '车辆类型',
            '时间记录字段': '通过时间',
            '进出类型字段': '过车方向',
            '进出标识值': ('入场', '出场'),
            '道闸编号字段': '出入口'
        }
    }
    
    try:
        # 创建批量处理器
        processor = create_batch_processor(input_directory, base_params, mode_config)
        
        # 执行批量处理
        results = processor.process_batch(
            recursive=True,        # 递归处理子目录
            continue_on_error=True # 遇到错误继续处理
        )
        
        print(f"\n🎉 批量处理完成！")
        
    except FileNotFoundError:
        print(f"❌ 错误: 输入目录不存在: {input_directory}")
        print("请修改 input_directory 变量为正确的目录路径")
    except Exception as e:
        print(f"❌ 批量处理失败: {str(e)}")
        import traceback
        traceback.print_exc()
