#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试字段映射修复效果
验证ReportGenerator是否正确使用字段映射而不是硬编码字段名
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from parking_report_generatior import ReportGenerator

def create_test_data_with_custom_fields():
    """创建使用自定义字段名的测试数据"""
    np.random.seed(42)
    
    # 创建测试数据，使用非标准字段名
    records = 50
    base_date = datetime(2024, 6, 1)
    
    data = []
    for i in range(records):
        entry_time = base_date + timedelta(hours=i % 24, minutes=np.random.randint(0, 60))
        duration = np.random.uniform(0.5, 8.0)
        exit_time = entry_time + timedelta(hours=duration)
        
        data.append({
            # 使用自定义字段名（非标准字段名）
            '进场时间': entry_time,           # 而不是 entry_time
            '出场时间': exit_time,            # 而不是 exit_time  
            '进场道闸': f'入口{(i % 3) + 1}',   # 而不是 entry_gate
            '出场道闸': f'出口{(i % 3) + 1}',   # 而不是 exit_gate
            '车辆类型': '小型车' if i % 2 == 0 else '大型车',  # 而不是 vtype
            '停车时长': duration              # 而不是 duration
        })
    
    return pd.DataFrame(data)

def test_field_mapping_fix():
    """测试字段映射修复效果"""
    print("=" * 80)
    print("测试字段映射修复效果")
    print("验证ReportGenerator是否正确使用字段映射而不是硬编码字段名")
    print("=" * 80)
    
    try:
        # 1. 创建使用自定义字段名的测试数据
        print(f"\n{'='*60}")
        print("1. 创建使用自定义字段名的测试数据")
        print('='*60)
        
        custom_data = create_test_data_with_custom_fields()
        print(f"数据记录数: {len(custom_data)}")
        print(f"自定义字段名: {list(custom_data.columns)}")
        
        # 2. 设置字段映射参数
        print(f"\n{'='*60}")
        print("2. 设置字段映射参数")
        print('='*60)
        
        # 模拟mode2的字段映射配置
        params = {
            'mode': 'mode2',
            'date': '2024-06-01',
            'time_interval': 60,
            'time_slip': 15,
            # 字段映射配置
            '进场时间字段': '进场时间',
            '出场时间字段': '出场时间', 
            '进场道闸编号字段': '进场道闸',
            '出场道闸编号字段': '出场道闸',
            '车辆类型字段': '车辆类型'
        }
        
        print("字段映射配置:")
        for key, value in params.items():
            if '字段' in key:
                print(f"  {key}: {value}")
        
        # 3. 创建标准化数据（模拟数据处理后的结果）
        print(f"\n{'='*60}")
        print("3. 创建标准化数据")
        print('='*60)
        
        # 将自定义字段名映射为标准字段名
        standardized_data = custom_data.rename(columns={
            '进场时间': 'entry_time',
            '出场时间': 'exit_time',
            '进场道闸': 'entry_gate', 
            '出场道闸': 'exit_gate',
            '车辆类型': 'vtype',
            '停车时长': 'duration'
        })
        
        print(f"标准化后字段名: {list(standardized_data.columns)}")
        
        # 4. 测试修复前的问题（模拟）
        print(f"\n{'='*60}")
        print("4. 测试修复前的问题（模拟）")
        print('='*60)
        
        print("修复前的问题:")
        print("  - ReportGenerator直接使用硬编码字段名如 'entry_time', 'vtype' 等")
        print("  - 无法适应不同数据源的字段名称")
        print("  - 需要在数据预处理阶段强制统一字段名")
        
        # 5. 测试修复后的ReportGenerator
        print(f"\n{'='*60}")
        print("5. 测试修复后的ReportGenerator")
        print('='*60)
        
        # 创建分析结果
        analysis_results = {
            'overview': {
                'original_total_records': len(custom_data)
            }
        }
        
        # 创建ReportGenerator实例
        report_generator = ReportGenerator(
            data=standardized_data,
            analysis_results=analysis_results,
            params=params,
            input_total_records=len(custom_data)
        )
        
        print("ReportGenerator初始化成功")
        print(f"字段映射: {report_generator.field_mapping}")
        
        # 6. 测试字段映射方法
        print(f"\n{'='*60}")
        print("6. 测试字段映射方法")
        print('='*60)
        
        # 测试 _get_field_name 方法
        test_fields = ['entry_time', 'exit_time', 'entry_gate', 'exit_gate', 'vtype', 'duration']
        
        print("字段名映射测试:")
        for field in test_fields:
            mapped_field = report_generator._get_field_name(field)
            print(f"  {field} -> {mapped_field}")
        
        # 7. 测试核心方法是否使用字段映射
        print(f"\n{'='*60}")
        print("7. 测试核心方法是否使用字段映射")
        print('='*60)
        
        # 测试 _calculate_peak_flow 方法
        try:
            peak_flow = report_generator._calculate_peak_flow()
            print(f"✅ _calculate_peak_flow 成功执行，生成 {len(peak_flow)} 个时间段数据")
            if not peak_flow.empty:
                print(f"   包含列: {list(peak_flow.columns)}")
        except Exception as e:
            print(f"❌ _calculate_peak_flow 执行失败: {e}")
        
        # 测试 calculate_parking_stats 方法
        try:
            parking_stats = report_generator.calculate_parking_stats()
            print(f"✅ calculate_parking_stats 成功执行，生成 {len(parking_stats)} 个时长分段数据")
            if not parking_stats.empty:
                print(f"   包含列: {list(parking_stats.columns)}")
        except Exception as e:
            print(f"❌ calculate_parking_stats 执行失败: {e}")
        
        # 测试 _calculate_vehicle_duration_stats 方法
        try:
            vehicle_stats = report_generator._calculate_vehicle_duration_stats()
            print(f"✅ _calculate_vehicle_duration_stats 成功执行，生成 {len(vehicle_stats)} 条车辆类型数据")
            if not vehicle_stats.empty:
                print(f"   包含列: {list(vehicle_stats.columns)}")
                print(f"   车辆类型: {vehicle_stats['车辆类型'].tolist()}")
        except Exception as e:
            print(f"❌ _calculate_vehicle_duration_stats 执行失败: {e}")
        
        # 测试 _get_vehicle_types 方法
        try:
            vehicle_types = report_generator._get_vehicle_types()
            print(f"✅ _get_vehicle_types 成功执行，获取车辆类型: {vehicle_types}")
        except Exception as e:
            print(f"❌ _get_vehicle_types 执行失败: {e}")
        
        # 8. 测试概览数据生成
        print(f"\n{'='*60}")
        print("8. 测试概览数据生成")
        print('='*60)
        
        try:
            overview_data = report_generator._get_overview_data(
                filtered_data=standardized_data
            )
            print("✅ 概览数据生成成功")
            print(f"   总记录数: {overview_data['original_total_records']}")
            print(f"   有效记录数: {overview_data['total_records']}")
            print(f"   数据有效率: {overview_data['filtered_percentage']:.2f}%")
            
            if 'time_range' in overview_data:
                print(f"   时间范围: {overview_data['time_range']['start']} 到 {overview_data['time_range']['end']}")
                
        except Exception as e:
            print(f"❌ 概览数据生成失败: {e}")
        
        # 9. 验证修复效果
        print(f"\n{'='*60}")
        print("9. 验证修复效果")
        print('='*60)
        
        success_count = 0
        total_tests = 4
        
        # 检查字段映射是否正确初始化
        if hasattr(report_generator, 'field_mapping') and report_generator.field_mapping:
            print("✅ 字段映射正确初始化")
            success_count += 1
        else:
            print("❌ 字段映射初始化失败")
        
        # 检查 _get_field_name 方法是否工作
        if report_generator._get_field_name('entry_time') == 'entry_time':
            print("✅ _get_field_name 方法工作正常")
            success_count += 1
        else:
            print("❌ _get_field_name 方法异常")
        
        # 检查核心方法是否能正常执行
        try:
            test_peak = report_generator._calculate_peak_flow()
            test_stats = report_generator.calculate_parking_stats()
            if not test_peak.empty and not test_stats.empty:
                print("✅ 核心统计方法正常执行")
                success_count += 1
            else:
                print("⚠️  核心统计方法执行但结果为空")
        except:
            print("❌ 核心统计方法执行失败")
        
        # 检查车辆类型获取是否正常
        try:
            vtypes = report_generator._get_vehicle_types()
            if vtypes and len(vtypes) > 0:
                print("✅ 车辆类型获取正常")
                success_count += 1
            else:
                print("⚠️  车辆类型获取结果为空")
        except:
            print("❌ 车辆类型获取失败")
        
        # 10. 总结
        print(f"\n{'='*60}")
        print("10. 修复效果总结")
        print('='*60)
        
        success_rate = success_count / total_tests * 100
        
        if success_rate >= 75:
            print("🎉 字段映射修复效果良好！")
            print(f"✅ 成功率: {success_rate:.1f}% ({success_count}/{total_tests})")
            print("✅ ReportGenerator现在使用字段映射而不是硬编码字段名")
            print("✅ 可以适应不同数据源的字段名称")
            print("✅ 提高了代码的灵活性和可维护性")
        else:
            print("⚠️  字段映射修复部分成功")
            print(f"成功率: {success_rate:.1f}% ({success_count}/{total_tests})")
            print("需要进一步检查和完善")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
    
    print("\n" + "=" * 80)
    print("字段映射修复测试完成！")
    print("=" * 80)

if __name__ == "__main__":
    test_field_mapping_fix()
