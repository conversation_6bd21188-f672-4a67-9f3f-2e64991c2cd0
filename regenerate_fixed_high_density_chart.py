#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
重新生成修复X轴的高密度概率密度图
"""

import os
import pandas as pd
from parking_chart_generator import ParkingChartGenerator

def regenerate_high_density_charts():
    """重新生成修复X轴的高密度图表"""
    print("🔄 重新生成修复X轴的高密度概率密度图\n")
    
    # 查找数据文件
    excel_files = [f for f in os.listdir('.') if f.endswith('.xlsx') and not f.startswith('~')]
    
    best_file = None
    best_data_count = 0
    
    for excel_file in excel_files:
        try:
            with pd.ExcelFile(excel_file) as xls:
                if '数据_总量' in xls.sheet_names:
                    df = pd.read_excel(excel_file, sheet_name='数据_总量')
                    if 'duration' in df.columns:
                        valid_duration = df['duration'].dropna()
                        valid_duration = valid_duration[valid_duration >= 0]
                        
                        if len(valid_duration) > best_data_count:
                            best_data_count = len(valid_duration)
                            best_file = excel_file
        except:
            continue
    
    if not best_file:
        print("❌ 未找到数据文件")
        return []
    
    print(f"📊 使用数据文件: {best_file}")
    
    # 读取数据进行预览
    df = pd.read_excel(best_file, sheet_name='数据_总量')
    duration_data = df['duration'].dropna()
    duration_data = duration_data[duration_data >= 0]
    
    print(f"📋 数据预览:")
    print(f"   - 总数据点: {len(duration_data)}")
    print(f"   - 唯一值数量: {duration_data.nunique()}")
    print(f"   - 时长范围: {duration_data.min():.3f} - {duration_data.max():.3f} 小时")
    
    # 显示前10个唯一值
    unique_values = sorted(duration_data.unique())
    print(f"\n📊 前10个唯一时长值:")
    for i, value in enumerate(unique_values[:10], 1):
        count = (duration_data == value).sum()
        print(f"   {i:2d}. {value:.3f} 小时 (出现 {count} 次)")
    
    try:
        # 创建图表生成器
        chart_generator = ParkingChartGenerator(best_file)
        
        print(f"\n📁 输出目录: {chart_generator.output_dir}")
        
        generated_files = []
        
        # 重新生成超高密度模式（最重要的）
        print(f"\n📈 重新生成超高密度模式（X轴显示时长）...")
        
        try:
            file_path = chart_generator.generate_high_density_probability_chart(
                sheet_name='数据_总量', 
                density_mode='ultra'
            )
            
            if file_path and os.path.exists(file_path):
                file_size = os.path.getsize(file_path)
                generated_files.append(('ultra', file_path, file_size))
                print(f"   ✅ 超高密度图生成成功: {os.path.basename(file_path)} ({file_size:,} 字节)")
            else:
                print(f"   ❌ 超高密度图生成失败")
                
        except Exception as e:
            print(f"   ❌ 生成超高密度图时出错: {str(e)}")
        
        # 重新生成高密度模式
        print(f"\n📈 重新生成高密度模式（X轴显示时长）...")
        
        try:
            file_path = chart_generator.generate_high_density_probability_chart(
                sheet_name='数据_总量', 
                density_mode='high'
            )
            
            if file_path and os.path.exists(file_path):
                file_size = os.path.getsize(file_path)
                generated_files.append(('high', file_path, file_size))
                print(f"   ✅ 高密度图生成成功: {os.path.basename(file_path)} ({file_size:,} 字节)")
            else:
                print(f"   ❌ 高密度图生成失败")
                
        except Exception as e:
            print(f"   ❌ 生成高密度图时出错: {str(e)}")
        
        return generated_files
        
    except Exception as e:
        print(f"❌ 重新生成图表时出错: {str(e)}")
        import traceback
        traceback.print_exc()
        return []

def verify_x_axis_fix():
    """验证X轴修复效果"""
    print(f"\n✅ 验证X轴修复效果\n")
    
    target_dir = r"C:\Users\<USER>\Desktop\停车分析"
    
    # 检查生成的文件
    density_files = [
        "停车时长概率密度图_超高密度.html",
        "停车时长概率密度图_高密度.html"
    ]
    
    print(f"📁 检查目标目录: {target_dir}")
    
    for file_name in density_files:
        file_path = os.path.join(target_dir, file_name)
        if os.path.exists(file_path):
            file_size = os.path.getsize(file_path)
            print(f"   ✅ {file_name} ({file_size:,} 字节)")
        else:
            print(f"   ❌ {file_name} (不存在)")
    
    print(f"\n🎯 X轴修复说明:")
    print(f"   修复前: X轴显示 '点1', '点2', '点3'...")
    print(f"   修复后: X轴显示实际的停车时长值")
    print(f"   ")
    print(f"   超高密度模式:")
    print(f"   - X轴: 每个唯一的停车时长值（如 0.100h, 0.101h, 1.505h...）")
    print(f"   - Y轴: 该时长值的相对频率")
    print(f"   - 点数: 162个（每个唯一值一个点）")
    print(f"   ")
    print(f"   高密度模式:")
    print(f"   - X轴: 区间中心时长值（如 0.834h, 2.502h, 4.170h...）")
    print(f"   - Y轴: 该区间的概率密度")
    print(f"   - 点数: 约33个（有数据的区间）")

def explain_x_axis_improvement():
    """解释X轴改进"""
    print(f"\n📚 X轴改进详解\n")
    
    print(f"🔧 技术改进:")
    print(f"   1. 修改X轴类型: 从类目轴改为数值轴")
    print(f"   2. 使用实际时长值: 而不是序号")
    print(f"   3. 格式化显示: 添加'h'后缀表示小时")
    print(f"   4. 优化工具提示: 显示精确的时长值")
    
    print(f"\n📊 显示效果对比:")
    print(f"   ")
    print(f"   修复前的X轴:")
    print(f"   点1    点2    点3    点4    点5...")
    print(f"   (无法知道具体时长)")
    print(f"   ")
    print(f"   修复后的X轴:")
    print(f"   0.100h 0.101h 0.102h 1.505h 2.340h...")
    print(f"   (直接显示停车时长)")
    
    print(f"\n🎯 使用体验改进:")
    print(f"   ✅ 可以直接从X轴读取停车时长")
    print(f"   ✅ 容易识别时长分布的集中区域")
    print(f"   ✅ 便于发现异常的时长值")
    print(f"   ✅ 支持按时长范围缩放查看")

def main():
    """主函数"""
    print("🎯 修复高密度概率密度图的X轴显示\n")
    
    # 1. 解释X轴改进
    explain_x_axis_improvement()
    
    # 2. 重新生成修复后的图表
    generated_files = regenerate_high_density_charts()
    
    # 3. 验证修复效果
    verify_x_axis_fix()
    
    # 4. 总结
    print(f"\n{'='*60}")
    print(f"🎉 X轴修复完成!")
    
    if generated_files:
        print(f"✅ 成功重新生成 {len(generated_files)} 个图表:")
        for mode, file_path, file_size in generated_files:
            file_name = os.path.basename(file_path)
            print(f"   📊 {file_name} ({file_size:,} 字节)")
        
        print(f"\n🔍 重点推荐查看:")
        print(f"   📊 停车时长概率密度图_超高密度.html")
        print(f"      - 162个密集的数据点")
        print(f"      - X轴现在显示真实的停车时长")
        print(f"      - 可以直接看到每个时长值的分布")
        
        print(f"\n📁 文件位置: C:\\Users\\<USER>\\Desktop\\停车分析")
        print(f"💡 现在X轴显示的是真正的停车时长，而不是点序号！")
    else:
        print(f"❌ 未生成任何图表")
    
    print(f"{'='*60}")

if __name__ == "__main__":
    main()
