<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Awesome-pyecharts</title>
                <script type="text/javascript" src="https://assets.pyecharts.org/assets/v5/echarts.min.js"></script>
            <script type="text/javascript" src="https://assets.pyecharts.org/assets/v5/themes/macarons.js"></script>

    
</head>
<body >
    <style>.box { justify-content:center; display:flex; flex-wrap:wrap;  } </style>
    <div class="box">
                <div id="********************************" class="chart-container" style="width:1200px; height:150px; "></div>
    <script>
        var chart_******************************** = echarts.init(
            document.getElementById('********************************'), 'macarons', {renderer: 'canvas'});
        var option_******************************** = {
    "animation": true,
    "animationThreshold": 2000,
    "animationDuration": 1000,
    "animationEasing": "cubicOut",
    "animationDelay": 0,
    "animationDurationUpdate": 300,
    "animationEasingUpdate": "cubicOut",
    "animationDelayUpdate": 0,
    "aria": {
        "enabled": false
    },
    "series": [
        {
            "type": "bar",
            "name": "\u4e2d\u578b\u8f66-\u8fdb\u573a",
            "legendHoverLink": true,
            "data": [
                3,
                3,
                4,
                3,
                3,
                4,
                3,
                3,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                4
            ],
            "realtimeSort": false,
            "showBackground": false,
            "stackStrategy": "samesign",
            "cursor": "pointer",
            "barMinHeight": 0,
            "barCategoryGap": "20%",
            "barGap": "30%",
            "large": false,
            "largeThreshold": 400,
            "seriesLayoutBy": "column",
            "datasetIndex": 0,
            "clip": true,
            "zlevel": 0,
            "z": 2,
            "label": {
                "show": false,
                "margin": 8,
                "valueAnimation": false
            },
            "itemStyle": {
                "color": "#2E86AB",
                "borderColor": "#ffffff",
                "borderWidth": 1
            }
        },
        {
            "type": "bar",
            "name": "\u4e2d\u578b\u8f66-\u51fa\u573a",
            "legendHoverLink": true,
            "data": [
                0,
                0,
                1,
                1,
                6,
                0,
                5,
                1,
                6,
                3,
                1,
                3,
                3,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0
            ],
            "realtimeSort": false,
            "showBackground": false,
            "stackStrategy": "samesign",
            "cursor": "pointer",
            "barMinHeight": 0,
            "barCategoryGap": "20%",
            "barGap": "30%",
            "large": false,
            "largeThreshold": 400,
            "seriesLayoutBy": "column",
            "datasetIndex": 0,
            "clip": true,
            "zlevel": 0,
            "z": 2,
            "label": {
                "show": false,
                "margin": 8,
                "valueAnimation": false
            },
            "itemStyle": {
                "color": "#A23B72",
                "borderColor": "#ffffff",
                "borderWidth": 1
            }
        }
    ],
    "legend": [
        {
            "data": [
                "\u4e2d\u578b\u8f66-\u8fdb\u573a",
                "\u4e2d\u578b\u8f66-\u51fa\u573a"
            ],
            "selected": {},
            "show": true,
            "top": "5%",
            "padding": 5,
            "itemGap": 10,
            "itemWidth": 15,
            "itemHeight": 10,
            "textStyle": {
                "fontWeight": "bold",
                "fontSize": 10
            },
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderRadius": 0,
            "pageButtonItemGap": 5,
            "pageButtonPosition": "end",
            "pageFormatter": "{current}/{total}",
            "pageIconColor": "#2f4554",
            "pageIconInactiveColor": "#aaa",
            "pageIconSize": 15,
            "animationDurationUpdate": 800,
            "selector": false,
            "selectorPosition": "auto",
            "selectorItemGap": 7,
            "selectorButtonGap": 10
        }
    ],
    "tooltip": {
        "show": true,
        "trigger": "axis",
        "triggerOn": "mousemove|click",
        "axisPointer": {
            "type": "cross"
        },
        "showContent": true,
        "alwaysShowContent": false,
        "showDelay": 0,
        "hideDelay": 100,
        "enterable": false,
        "confine": false,
        "appendToBody": false,
        "transitionDuration": 0.4,
        "formatter": "{b}<br/>{a0}: {c0}<br/>{a1}: {c1}",
        "textStyle": {
            "color": "#333333",
            "fontSize": 11
        },
        "backgroundColor": "rgba(255, 255, 255, 0.95)",
        "borderColor": "#cccccc",
        "borderWidth": 1,
        "padding": 5,
        "order": "seriesAsc"
    },
    "xAxis": [
        {
            "type": "category",
            "show": true,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "axisLabel": {
                "show": true,
                "rotate": 45,
                "margin": 8,
                "fontSize": 8,
                "valueAnimation": false
            },
            "axisPointer": {
                "show": true,
                "type": "shadow",
                "triggerTooltip": true,
                "triggerOn": "mousemove|click"
            },
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0,
            "data": [
                "00:00-01:00",
                "01:00-02:00",
                "02:00-03:00",
                "03:00-04:00",
                "04:00-05:00",
                "05:00-06:00",
                "06:00-07:00",
                "07:00-08:00",
                "08:00-09:00",
                "09:00-10:00",
                "10:00-11:00",
                "11:00-12:00",
                "12:00-13:00",
                "13:00-14:00",
                "14:00-15:00",
                "15:00-16:00",
                "16:00-17:00",
                "17:00-18:00",
                "18:00-19:00",
                "19:00-20:00",
                "20:00-21:00",
                "21:00-22:00",
                "22:00-23:00",
                "23:00-23:59"
            ]
        }
    ],
    "yAxis": [
        {
            "type": "value",
            "name": "\u6570\u91cf",
            "show": true,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "nameTextStyle": {
                "fontSize": 10
            },
            "gridIndex": 0,
            "axisTick": {
                "show": true,
                "alignWithLabel": false,
                "inside": false
            },
            "axisLabel": {
                "show": true,
                "margin": 8,
                "fontSize": 10,
                "formatter": "{value}",
                "valueAnimation": false
            },
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "min": 0,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0
        }
    ],
    "title": [
        {
            "show": true,
            "text": "\u4e2d\u578b\u8f66\u8fdb\u51fa\u91cf\u5206\u5e03",
            "target": "blank",
            "subtarget": "blank",
            "left": "center",
            "padding": 5,
            "itemGap": 10,
            "textAlign": "auto",
            "textVerticalAlign": "auto",
            "triggerEvent": false,
            "textStyle": {
                "color": "#2c3e50",
                "fontWeight": "bold",
                "fontSize": 14
            }
        }
    ]
};
        chart_********************************.setOption(option_********************************);
    </script>
<br/>                <div id="92f20978c1b4457bab5d01e1ab77df24" class="chart-container" style="width:1200px; height:150px; "></div>
    <script>
        var chart_92f20978c1b4457bab5d01e1ab77df24 = echarts.init(
            document.getElementById('92f20978c1b4457bab5d01e1ab77df24'), 'macarons', {renderer: 'canvas'});
        var option_92f20978c1b4457bab5d01e1ab77df24 = {
    "animation": true,
    "animationThreshold": 2000,
    "animationDuration": 1000,
    "animationEasing": "cubicOut",
    "animationDelay": 0,
    "animationDurationUpdate": 300,
    "animationEasingUpdate": "cubicOut",
    "animationDelayUpdate": 0,
    "aria": {
        "enabled": false
    },
    "series": [
        {
            "type": "bar",
            "name": "\u5927\u578b\u8f66-\u8fdb\u573a",
            "legendHoverLink": true,
            "data": [
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                4,
                3,
                3,
                4,
                3,
                3,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0
            ],
            "realtimeSort": false,
            "showBackground": false,
            "stackStrategy": "samesign",
            "cursor": "pointer",
            "barMinHeight": 0,
            "barCategoryGap": "20%",
            "barGap": "30%",
            "large": false,
            "largeThreshold": 400,
            "seriesLayoutBy": "column",
            "datasetIndex": 0,
            "clip": true,
            "zlevel": 0,
            "z": 2,
            "label": {
                "show": false,
                "margin": 8,
                "valueAnimation": false
            },
            "itemStyle": {
                "color": "#2E86AB",
                "borderColor": "#ffffff",
                "borderWidth": 1
            }
        },
        {
            "type": "bar",
            "name": "\u5927\u578b\u8f66-\u51fa\u573a",
            "legendHoverLink": true,
            "data": [
                1,
                1,
                0,
                3,
                1,
                0,
                0,
                0,
                1,
                1,
                0,
                0,
                0,
                0,
                1,
                0,
                0,
                1,
                1,
                2,
                3,
                2,
                1,
                1
            ],
            "realtimeSort": false,
            "showBackground": false,
            "stackStrategy": "samesign",
            "cursor": "pointer",
            "barMinHeight": 0,
            "barCategoryGap": "20%",
            "barGap": "30%",
            "large": false,
            "largeThreshold": 400,
            "seriesLayoutBy": "column",
            "datasetIndex": 0,
            "clip": true,
            "zlevel": 0,
            "z": 2,
            "label": {
                "show": false,
                "margin": 8,
                "valueAnimation": false
            },
            "itemStyle": {
                "color": "#A23B72",
                "borderColor": "#ffffff",
                "borderWidth": 1
            }
        }
    ],
    "legend": [
        {
            "data": [
                "\u5927\u578b\u8f66-\u8fdb\u573a",
                "\u5927\u578b\u8f66-\u51fa\u573a"
            ],
            "selected": {},
            "show": true,
            "top": "5%",
            "padding": 5,
            "itemGap": 10,
            "itemWidth": 15,
            "itemHeight": 10,
            "textStyle": {
                "fontWeight": "bold",
                "fontSize": 10
            },
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderRadius": 0,
            "pageButtonItemGap": 5,
            "pageButtonPosition": "end",
            "pageFormatter": "{current}/{total}",
            "pageIconColor": "#2f4554",
            "pageIconInactiveColor": "#aaa",
            "pageIconSize": 15,
            "animationDurationUpdate": 800,
            "selector": false,
            "selectorPosition": "auto",
            "selectorItemGap": 7,
            "selectorButtonGap": 10
        }
    ],
    "tooltip": {
        "show": true,
        "trigger": "axis",
        "triggerOn": "mousemove|click",
        "axisPointer": {
            "type": "cross"
        },
        "showContent": true,
        "alwaysShowContent": false,
        "showDelay": 0,
        "hideDelay": 100,
        "enterable": false,
        "confine": false,
        "appendToBody": false,
        "transitionDuration": 0.4,
        "formatter": "{b}<br/>{a0}: {c0}<br/>{a1}: {c1}",
        "textStyle": {
            "color": "#333333",
            "fontSize": 11
        },
        "backgroundColor": "rgba(255, 255, 255, 0.95)",
        "borderColor": "#cccccc",
        "borderWidth": 1,
        "padding": 5,
        "order": "seriesAsc"
    },
    "xAxis": [
        {
            "type": "category",
            "show": true,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "axisLabel": {
                "show": true,
                "rotate": 45,
                "margin": 8,
                "fontSize": 8,
                "valueAnimation": false
            },
            "axisPointer": {
                "show": true,
                "type": "shadow",
                "triggerTooltip": true,
                "triggerOn": "mousemove|click"
            },
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0,
            "data": [
                "00:00-01:00",
                "01:00-02:00",
                "02:00-03:00",
                "03:00-04:00",
                "04:00-05:00",
                "05:00-06:00",
                "06:00-07:00",
                "07:00-08:00",
                "08:00-09:00",
                "09:00-10:00",
                "10:00-11:00",
                "11:00-12:00",
                "12:00-13:00",
                "13:00-14:00",
                "14:00-15:00",
                "15:00-16:00",
                "16:00-17:00",
                "17:00-18:00",
                "18:00-19:00",
                "19:00-20:00",
                "20:00-21:00",
                "21:00-22:00",
                "22:00-23:00",
                "23:00-23:59"
            ]
        }
    ],
    "yAxis": [
        {
            "type": "value",
            "name": "\u6570\u91cf",
            "show": true,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "nameTextStyle": {
                "fontSize": 10
            },
            "gridIndex": 0,
            "axisTick": {
                "show": true,
                "alignWithLabel": false,
                "inside": false
            },
            "axisLabel": {
                "show": true,
                "margin": 8,
                "fontSize": 10,
                "formatter": "{value}",
                "valueAnimation": false
            },
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "min": 0,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0
        }
    ],
    "title": [
        {
            "show": true,
            "text": "\u5927\u578b\u8f66\u8fdb\u51fa\u91cf\u5206\u5e03",
            "target": "blank",
            "subtarget": "blank",
            "left": "center",
            "padding": 5,
            "itemGap": 10,
            "textAlign": "auto",
            "textVerticalAlign": "auto",
            "triggerEvent": false,
            "textStyle": {
                "color": "#2c3e50",
                "fontWeight": "bold",
                "fontSize": 14
            }
        }
    ]
};
        chart_92f20978c1b4457bab5d01e1ab77df24.setOption(option_92f20978c1b4457bab5d01e1ab77df24);
    </script>
<br/>                <div id="4254adc35a7344b1a47bb07b086ce304" class="chart-container" style="width:1200px; height:150px; "></div>
    <script>
        var chart_4254adc35a7344b1a47bb07b086ce304 = echarts.init(
            document.getElementById('4254adc35a7344b1a47bb07b086ce304'), 'macarons', {renderer: 'canvas'});
        var option_4254adc35a7344b1a47bb07b086ce304 = {
    "animation": true,
    "animationThreshold": 2000,
    "animationDuration": 1000,
    "animationEasing": "cubicOut",
    "animationDelay": 0,
    "animationDurationUpdate": 300,
    "animationEasingUpdate": "cubicOut",
    "animationDelayUpdate": 0,
    "aria": {
        "enabled": false
    },
    "series": [
        {
            "type": "bar",
            "name": "\u5c0f\u578b\u8f66-\u8fdb\u573a",
            "legendHoverLink": true,
            "data": [
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                4,
                3,
                3,
                4,
                3,
                3,
                4,
                3,
                3,
                4,
                3,
                3,
                4,
                3,
                3,
                0
            ],
            "realtimeSort": false,
            "showBackground": false,
            "stackStrategy": "samesign",
            "cursor": "pointer",
            "barMinHeight": 0,
            "barCategoryGap": "20%",
            "barGap": "30%",
            "large": false,
            "largeThreshold": 400,
            "seriesLayoutBy": "column",
            "datasetIndex": 0,
            "clip": true,
            "zlevel": 0,
            "z": 2,
            "label": {
                "show": false,
                "margin": 8,
                "valueAnimation": false
            },
            "itemStyle": {
                "color": "#2E86AB",
                "borderColor": "#ffffff",
                "borderWidth": 1
            }
        },
        {
            "type": "bar",
            "name": "\u5c0f\u578b\u8f66-\u51fa\u573a",
            "legendHoverLink": true,
            "data": [
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                1,
                5,
                1,
                4,
                3,
                6,
                3,
                3,
                3,
                4,
                0,
                4,
                6,
                1,
                5,
                1
            ],
            "realtimeSort": false,
            "showBackground": false,
            "stackStrategy": "samesign",
            "cursor": "pointer",
            "barMinHeight": 0,
            "barCategoryGap": "20%",
            "barGap": "30%",
            "large": false,
            "largeThreshold": 400,
            "seriesLayoutBy": "column",
            "datasetIndex": 0,
            "clip": true,
            "zlevel": 0,
            "z": 2,
            "label": {
                "show": false,
                "margin": 8,
                "valueAnimation": false
            },
            "itemStyle": {
                "color": "#A23B72",
                "borderColor": "#ffffff",
                "borderWidth": 1
            }
        }
    ],
    "legend": [
        {
            "data": [
                "\u5c0f\u578b\u8f66-\u8fdb\u573a",
                "\u5c0f\u578b\u8f66-\u51fa\u573a"
            ],
            "selected": {},
            "show": true,
            "top": "5%",
            "padding": 5,
            "itemGap": 10,
            "itemWidth": 15,
            "itemHeight": 10,
            "textStyle": {
                "fontWeight": "bold",
                "fontSize": 10
            },
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderRadius": 0,
            "pageButtonItemGap": 5,
            "pageButtonPosition": "end",
            "pageFormatter": "{current}/{total}",
            "pageIconColor": "#2f4554",
            "pageIconInactiveColor": "#aaa",
            "pageIconSize": 15,
            "animationDurationUpdate": 800,
            "selector": false,
            "selectorPosition": "auto",
            "selectorItemGap": 7,
            "selectorButtonGap": 10
        }
    ],
    "tooltip": {
        "show": true,
        "trigger": "axis",
        "triggerOn": "mousemove|click",
        "axisPointer": {
            "type": "cross"
        },
        "showContent": true,
        "alwaysShowContent": false,
        "showDelay": 0,
        "hideDelay": 100,
        "enterable": false,
        "confine": false,
        "appendToBody": false,
        "transitionDuration": 0.4,
        "formatter": "{b}<br/>{a0}: {c0}<br/>{a1}: {c1}",
        "textStyle": {
            "color": "#333333",
            "fontSize": 11
        },
        "backgroundColor": "rgba(255, 255, 255, 0.95)",
        "borderColor": "#cccccc",
        "borderWidth": 1,
        "padding": 5,
        "order": "seriesAsc"
    },
    "xAxis": [
        {
            "type": "category",
            "show": true,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "axisLabel": {
                "show": true,
                "rotate": 45,
                "margin": 8,
                "fontSize": 8,
                "valueAnimation": false
            },
            "axisPointer": {
                "show": true,
                "type": "shadow",
                "triggerTooltip": true,
                "triggerOn": "mousemove|click"
            },
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0,
            "data": [
                "00:00-01:00",
                "01:00-02:00",
                "02:00-03:00",
                "03:00-04:00",
                "04:00-05:00",
                "05:00-06:00",
                "06:00-07:00",
                "07:00-08:00",
                "08:00-09:00",
                "09:00-10:00",
                "10:00-11:00",
                "11:00-12:00",
                "12:00-13:00",
                "13:00-14:00",
                "14:00-15:00",
                "15:00-16:00",
                "16:00-17:00",
                "17:00-18:00",
                "18:00-19:00",
                "19:00-20:00",
                "20:00-21:00",
                "21:00-22:00",
                "22:00-23:00",
                "23:00-23:59"
            ]
        }
    ],
    "yAxis": [
        {
            "type": "value",
            "name": "\u6570\u91cf",
            "show": true,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "nameTextStyle": {
                "fontSize": 10
            },
            "gridIndex": 0,
            "axisTick": {
                "show": true,
                "alignWithLabel": false,
                "inside": false
            },
            "axisLabel": {
                "show": true,
                "margin": 8,
                "fontSize": 10,
                "formatter": "{value}",
                "valueAnimation": false
            },
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "min": 0,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0
        }
    ],
    "title": [
        {
            "show": true,
            "text": "\u5c0f\u578b\u8f66\u8fdb\u51fa\u91cf\u5206\u5e03",
            "target": "blank",
            "subtarget": "blank",
            "left": "center",
            "padding": 5,
            "itemGap": 10,
            "textAlign": "auto",
            "textVerticalAlign": "auto",
            "triggerEvent": false,
            "textStyle": {
                "color": "#2c3e50",
                "fontWeight": "bold",
                "fontSize": 14
            }
        }
    ]
};
        chart_4254adc35a7344b1a47bb07b086ce304.setOption(option_4254adc35a7344b1a47bb07b086ce304);
    </script>
<br/>                <div id="ad54c8eeed934fb7b6e9279f9a635e13" class="chart-container" style="width:1200px; height:150px; "></div>
    <script>
        var chart_ad54c8eeed934fb7b6e9279f9a635e13 = echarts.init(
            document.getElementById('ad54c8eeed934fb7b6e9279f9a635e13'), 'macarons', {renderer: 'canvas'});
        var option_ad54c8eeed934fb7b6e9279f9a635e13 = {
    "animation": true,
    "animationThreshold": 2000,
    "animationDuration": 1000,
    "animationEasing": "cubicOut",
    "animationDelay": 0,
    "animationDurationUpdate": 300,
    "animationEasingUpdate": "cubicOut",
    "animationDelayUpdate": 0,
    "aria": {
        "enabled": false
    },
    "series": [
        {
            "type": "bar",
            "name": "\u7279\u79cd\u8f66_-\u8fdb\u573a",
            "legendHoverLink": true,
            "data": [
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                4,
                3,
                3,
                0,
                0,
                0,
                0,
                0,
                0,
                0
            ],
            "realtimeSort": false,
            "showBackground": false,
            "stackStrategy": "samesign",
            "cursor": "pointer",
            "barMinHeight": 0,
            "barCategoryGap": "20%",
            "barGap": "30%",
            "large": false,
            "largeThreshold": 400,
            "seriesLayoutBy": "column",
            "datasetIndex": 0,
            "clip": true,
            "zlevel": 0,
            "z": 2,
            "label": {
                "show": false,
                "margin": 8,
                "valueAnimation": false
            },
            "itemStyle": {
                "color": "#2E86AB",
                "borderColor": "#ffffff",
                "borderWidth": 1
            }
        },
        {
            "type": "bar",
            "name": "\u7279\u79cd\u8f66_-\u51fa\u573a",
            "legendHoverLink": true,
            "data": [
                0,
                0,
                1,
                2,
                1,
                0,
                0,
                0,
                0,
                1,
                0,
                0,
                0,
                1,
                0,
                0,
                0,
                0,
                1,
                0,
                1,
                1,
                1,
                0
            ],
            "realtimeSort": false,
            "showBackground": false,
            "stackStrategy": "samesign",
            "cursor": "pointer",
            "barMinHeight": 0,
            "barCategoryGap": "20%",
            "barGap": "30%",
            "large": false,
            "largeThreshold": 400,
            "seriesLayoutBy": "column",
            "datasetIndex": 0,
            "clip": true,
            "zlevel": 0,
            "z": 2,
            "label": {
                "show": false,
                "margin": 8,
                "valueAnimation": false
            },
            "itemStyle": {
                "color": "#A23B72",
                "borderColor": "#ffffff",
                "borderWidth": 1
            }
        }
    ],
    "legend": [
        {
            "data": [
                "\u7279\u79cd\u8f66_-\u8fdb\u573a",
                "\u7279\u79cd\u8f66_-\u51fa\u573a"
            ],
            "selected": {},
            "show": true,
            "top": "5%",
            "padding": 5,
            "itemGap": 10,
            "itemWidth": 15,
            "itemHeight": 10,
            "textStyle": {
                "fontWeight": "bold",
                "fontSize": 10
            },
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderRadius": 0,
            "pageButtonItemGap": 5,
            "pageButtonPosition": "end",
            "pageFormatter": "{current}/{total}",
            "pageIconColor": "#2f4554",
            "pageIconInactiveColor": "#aaa",
            "pageIconSize": 15,
            "animationDurationUpdate": 800,
            "selector": false,
            "selectorPosition": "auto",
            "selectorItemGap": 7,
            "selectorButtonGap": 10
        }
    ],
    "tooltip": {
        "show": true,
        "trigger": "axis",
        "triggerOn": "mousemove|click",
        "axisPointer": {
            "type": "cross"
        },
        "showContent": true,
        "alwaysShowContent": false,
        "showDelay": 0,
        "hideDelay": 100,
        "enterable": false,
        "confine": false,
        "appendToBody": false,
        "transitionDuration": 0.4,
        "formatter": "{b}<br/>{a0}: {c0}<br/>{a1}: {c1}",
        "textStyle": {
            "color": "#333333",
            "fontSize": 11
        },
        "backgroundColor": "rgba(255, 255, 255, 0.95)",
        "borderColor": "#cccccc",
        "borderWidth": 1,
        "padding": 5,
        "order": "seriesAsc"
    },
    "xAxis": [
        {
            "type": "category",
            "name": "\u65f6\u95f4\u6bb5",
            "show": true,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "axisLabel": {
                "show": true,
                "rotate": 45,
                "margin": 8,
                "fontSize": 8,
                "valueAnimation": false
            },
            "axisPointer": {
                "show": true,
                "type": "shadow",
                "triggerTooltip": true,
                "triggerOn": "mousemove|click"
            },
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0,
            "data": [
                "00:00-01:00",
                "01:00-02:00",
                "02:00-03:00",
                "03:00-04:00",
                "04:00-05:00",
                "05:00-06:00",
                "06:00-07:00",
                "07:00-08:00",
                "08:00-09:00",
                "09:00-10:00",
                "10:00-11:00",
                "11:00-12:00",
                "12:00-13:00",
                "13:00-14:00",
                "14:00-15:00",
                "15:00-16:00",
                "16:00-17:00",
                "17:00-18:00",
                "18:00-19:00",
                "19:00-20:00",
                "20:00-21:00",
                "21:00-22:00",
                "22:00-23:00",
                "23:00-23:59"
            ]
        }
    ],
    "yAxis": [
        {
            "type": "value",
            "name": "\u6570\u91cf",
            "show": true,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "nameTextStyle": {
                "fontSize": 10
            },
            "gridIndex": 0,
            "axisTick": {
                "show": true,
                "alignWithLabel": false,
                "inside": false
            },
            "axisLabel": {
                "show": true,
                "margin": 8,
                "fontSize": 10,
                "formatter": "{value}",
                "valueAnimation": false
            },
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "min": 0,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0
        }
    ],
    "title": [
        {
            "show": true,
            "text": "\u7279\u79cd\u8f66_\u8fdb\u51fa\u91cf\u5206\u5e03",
            "target": "blank",
            "subtarget": "blank",
            "left": "center",
            "padding": 5,
            "itemGap": 10,
            "textAlign": "auto",
            "textVerticalAlign": "auto",
            "triggerEvent": false,
            "textStyle": {
                "color": "#2c3e50",
                "fontWeight": "bold",
                "fontSize": 14
            }
        }
    ]
};
        chart_ad54c8eeed934fb7b6e9279f9a635e13.setOption(option_ad54c8eeed934fb7b6e9279f9a635e13);
    </script>
<br/>    </div>
    <script>
    </script>
</body>
</html>
