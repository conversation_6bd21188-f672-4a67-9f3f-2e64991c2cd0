#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修复后的输出目录设置
"""

import os
from parking_chart_generator import ParkingChartGenerator

def test_output_directory():
    """测试输出目录设置"""
    print("🔍 测试输出目录设置\n")
    
    # 查找Excel文件
    excel_files = [f for f in os.listdir('.') if f.endswith('.xlsx') and not f.startswith('~')]
    
    if not excel_files:
        print("❌ 未找到Excel文件")
        return
    
    excel_file = excel_files[0]  # 使用第一个Excel文件
    print(f"📊 使用Excel文件: {excel_file}")
    
    # 显示路径信息
    current_dir = os.getcwd()
    excel_path = os.path.abspath(excel_file)
    excel_dir = os.path.dirname(excel_path)
    
    print(f"📁 当前目录: {current_dir}")
    print(f"📁 Excel文件路径: {excel_path}")
    print(f"📁 Excel文件目录: {excel_dir}")
    
    # 计算预期的输出目录
    if 'pythoncode' in excel_dir and '1.开发中' in excel_dir:
        expected_output_dir = os.path.dirname(os.path.dirname(excel_dir))
        print(f"📁 预期输出目录: {expected_output_dir}")
    else:
        expected_output_dir = excel_dir
        print(f"📁 预期输出目录: {expected_output_dir}")
    
    # 创建图表生成器并检查输出目录
    try:
        chart_generator = ParkingChartGenerator(excel_file)
        actual_output_dir = chart_generator.output_dir
        
        print(f"📁 实际输出目录: {actual_output_dir}")
        
        # 验证目录是否正确
        if actual_output_dir == expected_output_dir:
            print(f"✅ 输出目录设置正确")
        else:
            print(f"❌ 输出目录设置错误")
            print(f"   预期: {expected_output_dir}")
            print(f"   实际: {actual_output_dir}")
        
        # 检查目录是否存在
        if os.path.exists(actual_output_dir):
            print(f"✅ 输出目录存在")
            
            # 列出目录中的HTML文件
            html_files = [f for f in os.listdir(actual_output_dir) if f.endswith('.html')]
            print(f"📋 目录中的HTML文件: {len(html_files)} 个")
            for i, file in enumerate(html_files, 1):
                print(f"   {i}. {file}")
        else:
            print(f"❌ 输出目录不存在")
        
        return chart_generator
        
    except Exception as e:
        print(f"❌ 创建图表生成器失败: {str(e)}")
        return None

def regenerate_scatter_charts_with_correct_dir():
    """使用正确的目录重新生成散点图"""
    print(f"\n🔄 使用正确目录重新生成散点图\n")
    
    chart_generator = test_output_directory()
    
    if not chart_generator:
        return
    
    print(f"\n📈 开始生成散点图...")
    
    generated_files = []
    
    # 生成延停时长概率密度散点图
    if '延停时长概率密度' in chart_generator.excel_data:
        print(f"📊 生成延停时长概率密度散点图...")
        file1 = chart_generator.generate_duration_probability_density_scatter()
        if file1:
            generated_files.append(file1)
            print(f"   ✅ 生成成功: {file1}")
        else:
            print(f"   ❌ 生成失败")
    
    # 生成停车时长频率分布散点图
    if '停车时长频率分布' in chart_generator.excel_data:
        print(f"📊 生成停车时长频率分布散点图...")
        file2 = chart_generator.generate_duration_frequency_scatter()
        if file2:
            generated_files.append(file2)
            print(f"   ✅ 生成成功: {file2}")
        else:
            print(f"   ❌ 生成失败")
    
    # 验证文件是否在正确位置
    print(f"\n✅ 验证文件位置:")
    target_dir = r"C:\Users\<USER>\Desktop\停车分析"
    
    for file_path in generated_files:
        file_name = os.path.basename(file_path)
        file_dir = os.path.dirname(file_path)
        
        print(f"   📄 {file_name}")
        print(f"      位置: {file_dir}")
        
        if file_dir == target_dir:
            print(f"      ✅ 位置正确")
        else:
            print(f"      ❌ 位置错误，应该在: {target_dir}")
        
        if os.path.exists(file_path):
            file_size = os.path.getsize(file_path)
            print(f"      📊 大小: {file_size:,} 字节")
        else:
            print(f"      ❌ 文件不存在")
    
    return generated_files

def clean_old_scatter_files():
    """清理开发目录中的旧散点图文件"""
    print(f"\n🧹 清理开发目录中的旧散点图文件\n")
    
    current_dir = "."
    html_files = [f for f in os.listdir(current_dir) if f.endswith('.html')]
    scatter_files = [f for f in html_files if '散点图' in f]
    
    if not scatter_files:
        print(f"   ✅ 开发目录中没有散点图文件需要清理")
        return
    
    for file in scatter_files:
        try:
            os.remove(file)
            print(f"   🗑️  删除: {file}")
        except Exception as e:
            print(f"   ❌ 删除失败 {file}: {str(e)}")

if __name__ == "__main__":
    print("🎯 测试并修复散点图输出目录\n")
    
    # 1. 重新生成散点图到正确目录
    generated_files = regenerate_scatter_charts_with_correct_dir()
    
    # 2. 清理开发目录中的旧文件
    clean_old_scatter_files()
    
    # 3. 最终验证
    print(f"\n{'='*60}")
    print(f"🎉 操作完成!")
    
    if generated_files:
        print(f"✅ 成功生成 {len(generated_files)} 个散点图文件")
        print(f"📁 文件位置: C:\\Users\\<USER>\\Desktop\\停车分析")
        
        for file_path in generated_files:
            file_name = os.path.basename(file_path)
            print(f"   📊 {file_name}")
    else:
        print(f"❌ 未生成任何文件")
    
    print(f"{'='*60}")
