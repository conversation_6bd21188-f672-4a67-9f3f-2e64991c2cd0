#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试重复检测修复
验证Excel数据合并器的重复检测逻辑是否正确
"""

import pandas as pd
import tempfile
import os
from pathlib import Path
import shutil

def create_test_data():
    """创建测试数据"""
    # 创建临时目录
    temp_dir = tempfile.mkdtemp()
    print(f"创建测试目录: {temp_dir}")
    
    # 测试数据
    data1 = pd.DataFrame({
        'A': [1, 2, 3, 4, 5],
        'B': ['a', 'b', 'c', 'd', 'e'],
        'C': [10, 20, 30, 40, 50]
    })
    
    data2 = pd.DataFrame({
        'A': [6, 7, 8, 9, 10],
        'B': ['f', 'g', 'h', 'i', 'j'],
        'C': [60, 70, 80, 90, 100]
    })
    
    # data3 与 data1 相同（重复数据）
    data3 = data1.copy()
    
    # 创建测试文件
    file1 = os.path.join(temp_dir, 'file1.xlsx')
    file2 = os.path.join(temp_dir, 'file2.csv')
    file3 = os.path.join(temp_dir, 'file3.xlsx')  # 重复文件
    
    # 保存文件
    data1.to_excel(file1, index=False)
    data2.to_csv(file2, index=False)
    data3.to_excel(file3, index=False)  # 与file1重复
    
    print(f"创建测试文件:")
    print(f"  file1.xlsx: {len(data1)} 行")
    print(f"  file2.csv: {len(data2)} 行")
    print(f"  file3.xlsx: {len(data3)} 行 (与file1重复)")
    
    return temp_dir, [file1, file2, file3], [data1, data2, data3]

def test_duplicate_detection():
    """测试重复检测功能"""
    print("🧪 开始测试重复检测修复...")
    print("=" * 60)
    
    # 创建测试数据
    temp_dir, files, data_list = create_test_data()
    
    try:
        # 导入修复后的合并器
        from excel_data_merger import IntegratedDataMerger
        
        # 创建合并器实例
        merger = IntegratedDataMerger()
        
        # 执行合并
        print(f"\n📊 执行合并测试...")
        output_file = os.path.join(temp_dir, 'merged_result.xlsx')
        
        result = merger.smart_merge_directory(
            input_path=temp_dir,
            output_path=output_file,
            file_pattern="*",
            recursive=False
        )
        
        # 分析结果
        print(f"\n📈 合并结果分析:")
        print(f"=" * 60)
        
        stats = merger.merge_stats
        print(f"📄 总数据源: {stats['total_sources']}")
        print(f"✅ 成功合并: {stats['merged_sources']}")
        print(f"🔄 重复数量: {stats['duplicate_sources']}")
        print(f"❌ 跳过数量: {stats['skipped_sources']}")
        print(f"📊 合并行数: {stats['merged_rows']}")
        
        # 验证结果
        expected_rows = len(data_list[0]) + len(data_list[1])  # file1 + file2，file3应该被跳过
        actual_rows = len(result)
        
        print(f"\n🔍 验证结果:")
        print(f"预期行数: {expected_rows} (file1: {len(data_list[0])} + file2: {len(data_list[1])})")
        print(f"实际行数: {actual_rows}")
        print(f"统计行数: {stats['merged_rows']}")
        
        # 检查是否正确
        is_correct = (
            stats['duplicate_sources'] == 1 and  # 应该检测到1个重复
            actual_rows == expected_rows and      # 实际行数应该等于预期行数
            stats['merged_rows'] == expected_rows # 统计行数应该等于预期行数
        )
        
        if is_correct:
            print("✅ 测试通过！重复检测逻辑正确")
        else:
            print("❌ 测试失败！重复检测逻辑有问题")
            
        # 显示重复详情
        if stats['duplicate_details']:
            print(f"\n🔄 重复检测详情:")
            for detail in stats['duplicate_details']:
                print(f"   - {detail['source']} → 重复于 {detail['duplicate_of']}")
                print(f"     原因: {detail['reason']}")
        
        return is_correct
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {str(e)}")
        import traceback
        traceback.print_exc()
        return False
        
    finally:
        # 清理测试文件
        try:
            shutil.rmtree(temp_dir)
            print(f"\n🧹 清理测试目录: {temp_dir}")
        except Exception as e:
            print(f"⚠️ 清理失败: {str(e)}")

def test_excel_internal_duplicate():
    """测试Excel文件内部重复检测"""
    print("\n🧪 测试Excel文件内部重复检测...")
    print("=" * 60)
    
    # 创建临时目录
    temp_dir = tempfile.mkdtemp()
    print(f"创建测试目录: {temp_dir}")
    
    try:
        # 创建包含重复工作表的Excel文件
        excel_file = os.path.join(temp_dir, 'test_internal_duplicate.xlsx')
        
        data1 = pd.DataFrame({
            'A': [1, 2, 3],
            'B': ['x', 'y', 'z'],
            'C': [100, 200, 300]
        })
        
        data2 = pd.DataFrame({
            'A': [4, 5, 6],
            'B': ['a', 'b', 'c'],
            'C': [400, 500, 600]
        })
        
        # data3 与 data1 相同（内部重复）
        data3 = data1.copy()
        
        # 创建Excel文件，包含3个工作表，其中Sheet3与Sheet1重复
        with pd.ExcelWriter(excel_file, engine='openpyxl') as writer:
            data1.to_excel(writer, sheet_name='Sheet1', index=False)
            data2.to_excel(writer, sheet_name='Sheet2', index=False)
            data3.to_excel(writer, sheet_name='Sheet3', index=False)  # 与Sheet1重复
        
        print(f"创建Excel文件: {excel_file}")
        print(f"  Sheet1: {len(data1)} 行")
        print(f"  Sheet2: {len(data2)} 行")
        print(f"  Sheet3: {len(data3)} 行 (与Sheet1重复)")
        
        # 导入合并器
        from excel_data_merger import IntegratedDataMerger
        
        # 创建合并器实例
        merger = IntegratedDataMerger()
        
        # 执行合并
        output_file = os.path.join(temp_dir, 'merged_internal.xlsx')
        result = merger.smart_merge_directory(
            input_path=temp_dir,
            output_path=output_file,
            file_pattern="*.xlsx",
            recursive=False
        )
        
        # 分析结果
        stats = merger.merge_stats
        print(f"\n📈 Excel内部合并结果:")
        print(f"📄 总数据源: {stats['total_sources']}")
        print(f"✅ 成功合并: {stats['merged_sources']}")
        print(f"🔄 重复数量: {stats['duplicate_sources']}")
        print(f"📊 合并行数: {stats['merged_rows']}")
        
        # 验证结果
        expected_rows = len(data1) + len(data2)  # Sheet1 + Sheet2，Sheet3应该被跳过
        actual_rows = len(result)
        
        print(f"\n🔍 验证结果:")
        print(f"预期行数: {expected_rows} (Sheet1: {len(data1)} + Sheet2: {len(data2)})")
        print(f"实际行数: {actual_rows}")
        
        is_correct = (
            stats['duplicate_sources'] == 1 and  # 应该检测到1个重复工作表
            actual_rows == expected_rows          # 实际行数应该等于预期行数
        )
        
        if is_correct:
            print("✅ Excel内部重复检测测试通过！")
        else:
            print("❌ Excel内部重复检测测试失败！")
            
        return is_correct
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {str(e)}")
        import traceback
        traceback.print_exc()
        return False
        
    finally:
        # 清理测试文件
        try:
            shutil.rmtree(temp_dir)
            print(f"🧹 清理测试目录: {temp_dir}")
        except Exception as e:
            print(f"⚠️ 清理失败: {str(e)}")

def main():
    """主测试函数"""
    print("🔧 Excel数据合并器重复检测修复测试")
    print("=" * 80)
    
    # 测试1: 跨文件重复检测
    test1_result = test_duplicate_detection()
    
    # 测试2: Excel内部重复检测
    test2_result = test_excel_internal_duplicate()
    
    # 总结
    print(f"\n📊 测试总结:")
    print("=" * 80)
    print(f"跨文件重复检测: {'✅ 通过' if test1_result else '❌ 失败'}")
    print(f"Excel内部重复检测: {'✅ 通过' if test2_result else '❌ 失败'}")
    
    if test1_result and test2_result:
        print(f"\n🎉 所有测试通过！重复检测修复成功！")
    else:
        print(f"\n⚠️ 部分测试失败，需要进一步检查")

if __name__ == "__main__":
    main()
