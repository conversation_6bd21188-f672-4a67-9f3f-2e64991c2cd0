#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试出入口进出量方向Timeline图表生成功能
验证每个出入口的进场和出场数据是否正确显示
"""

import os

def test_gate_direction_timeline():
    """测试出入口进出量方向Timeline图表生成"""
    print("🚪 测试出入口进出量方向Timeline图表生成")
    print("=" * 50)
    
    # Excel文件路径
    excel_file = r'C:\Users\<USER>\Desktop\停车分析\义乌火车站道闸_私家车_合并_20250617_083509_analysis_20250618_211554.xlsx'
    
    if not os.path.exists(excel_file):
        print(f"❌ Excel文件不存在: {excel_file}")
        return False
    
    try:
        # 导入图表生成器
        from parking_chart_generator import ParkingChartGenerator
        
        # 创建图表生成器
        print("📊 创建图表生成器...")
        chart_generator = ParkingChartGenerator(excel_file, None)
        
        # 检查是否有进出量时间分布(按道闸)工作表
        target_sheet = '进出量时间分布(按道闸)'
        if target_sheet not in chart_generator.excel_data:
            print(f"❌ 未找到'{target_sheet}'工作表")
            available_sheets = list(chart_generator.excel_data.keys())
            print(f"可用工作表: {available_sheets}")
            return False
        
        # 获取数据并分析结构
        data = chart_generator.excel_data[target_sheet]
        columns = list(data.columns)
        total_cols = len(columns)
        
        print(f"📋 数据结构分析:")
        print(f"   总列数: {total_cols}")
        print(f"   第1列: {columns[0]} (时间段)")
        
        if total_cols > 1:
            gate_cols = columns[1:]
            gate_cols_count = len(gate_cols)
            estimated_gates = gate_cols_count // 3
            
            print(f"   出入口相关列数: {gate_cols_count}")
            print(f"   预估出入口数量: {estimated_gates}")
            
            # 显示每个出入口的列结构
            for i in range(min(estimated_gates, 3)):
                start_idx = 1 + i * 3
                if start_idx + 2 < total_cols:
                    print(f"   出入口{i+1}:")
                    print(f"     进场: {columns[start_idx]}")
                    print(f"     出场: {columns[start_idx+1]}")
                    print(f"     总量: {columns[start_idx+2]}")
        
        # 尝试生成方向Timeline图表
        print(f"\n🎬 尝试生成出入口进出量方向Timeline图表...")
        result = chart_generator.generate_gate_traffic_direction_timeline()
        
        if result:
            print(f"✅ 成功生成: {os.path.basename(result)}")
            
            # 检查文件是否存在
            if os.path.exists(result):
                file_size = os.path.getsize(result) / 1024
                print(f"📄 文件大小: {file_size:.1f}KB")
                print(f"📁 文件路径: {result}")
                
                # 读取HTML内容检查方向Timeline特征
                with open(result, 'r', encoding='utf-8') as f:
                    html_content = f.read()
                
                # 检查方向Timeline相关特征
                direction_checks = {
                    'Timeline组件': 'Timeline' in html_content,
                    '进场数据系列': '进场' in html_content,
                    '出场数据系列': '出场' in html_content,
                    '多个时间段': html_content.count('add(') > 1,
                    '柱状图': 'Bar(' in html_content,
                    '固定Y轴': 'max_:' in html_content or 'max":' in html_content,
                    '专业颜色': '#2E86AB' in html_content and '#A23B72' in html_content,
                }
                
                print(f"\n📊 方向Timeline图表验证:")
                all_checks_passed = True
                for check_name, is_passed in direction_checks.items():
                    status = "✅" if is_passed else "❌"
                    print(f"   {status} {check_name}: {'通过' if is_passed else '未通过'}")
                    if not is_passed:
                        all_checks_passed = False
                
                # 检查数据系列数量
                entry_count = html_content.count('进场')
                exit_count = html_content.count('出场')
                
                print(f"\n🔍 数据系列信息:")
                print(f"   进场数据系列: {entry_count} 个")
                print(f"   出场数据系列: {exit_count} 个")
                print(f"   每个时间段包含进场和出场两个数据系列")
                
                if all_checks_passed:
                    print("\n✅ 方向Timeline图表生成成功！")
                    print("   - 每个出入口显示进场和出场数据")
                    print("   - 支持时间段动态切换")
                    print("   - 使用专业演讲风格颜色")
                    print("   - Y轴刻度固定，便于对比")
                    print("   - 文件名: 出入口进出量_方向.html")
                    return True
                else:
                    print("\n⚠️ 方向Timeline图表生成但可能不是预期格式")
                    return False
            else:
                print(f"❌ 文件未生成: {result}")
                return False
        else:
            print("❌ 未能生成方向Timeline图表")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def demo_direction_timeline_features():
    """演示方向Timeline功能特点"""
    print("\n🚪 方向Timeline图表特点")
    print("=" * 50)
    
    print("🎯 功能对比:")
    print("   总量Timeline (出入口进出量_总量.html):")
    print("     - 每个出入口只显示总量")
    print("     - 单一数据系列")
    print("     - 适合查看整体流量")
    
    print("\n   方向Timeline (出入口进出量_方向.html):")
    print("     - 每个出入口显示进场和出场")
    print("     - 双数据系列")
    print("     - 适合分析进出流向")
    
    print("\n🎨 视觉设计:")
    print("   - 进场数据: 深蓝色 (#2E86AB)")
    print("   - 出场数据: 深紫红色 (#A23B72)")
    print("   - 高对比度，便于区分")
    print("   - 专业演讲风格")
    
    print("\n📊 数据展示:")
    print("   - X轴: 各个出入口名称")
    print("   - Y轴: 车辆数量（固定刻度）")
    print("   - 图例: 进场/出场标识")
    print("   - Tooltip: 详细数值显示")
    
    print("\n🎮 交互功能:")
    print("   - 时间轴滑块: 选择时间段")
    print("   - 自动播放: 观察流向变化")
    print("   - 固定Y轴: 准确对比数据")
    print("   - 长时间轴: 精确操作")
    
    print("\n💡 应用场景:")
    print("   - 流向分析: 分析各出入口的进出流向")
    print("   - 平衡评估: 评估进出流量是否平衡")
    print("   - 瓶颈识别: 识别进出方向的瓶颈")
    print("   - 运营优化: 优化出入口管理策略")

def main():
    """主函数"""
    # 演示功能特点
    demo_direction_timeline_features()
    
    # 测试功能
    success = test_gate_direction_timeline()
    
    if success:
        print("\n🎉 测试成功！方向Timeline图表已生成！")
        print("📁 文件名: 出入口进出量_方向.html")
        print("💡 现在可以查看每个出入口的进出流向")
        print("🔍 建议在浏览器中验证效果:")
        print("   1. 观察每个出入口的进场和出场柱状图")
        print("   2. 使用时间轴切换不同时间段")
        print("   3. 对比进出流量的变化趋势")
        print("   4. 分析各出入口的流向特点")
        
        print("\n📋 与总量图表的区别:")
        print("   - 总量图表: 显示每个出入口的总流量")
        print("   - 方向图表: 显示每个出入口的进场和出场")
        print("   - 两者互补，提供不同维度的分析")
    else:
        print("\n⚠️ 测试失败")
        print("💡 可能的原因:")
        print("   1. Excel文件中没有'进出量时间分布(按道闸)'工作表")
        print("   2. 数据列结构不符合预期")
        print("   3. 数据格式不正确或为空")
    
    print("\n" + "="*50)
    input("按回车键退出...")

if __name__ == "__main__":
    main()
