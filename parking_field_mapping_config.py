"""
字段映射配置文件
定义了系统中使用的标准化字段名称和映射关系
"""

# 标准化字段名称（基础字段）
STANDARDIZED_FIELDS = {
    'vid': 'vid',                    # 车辆唯一标识
    'vtype': 'vtype',                # 车辆类型
    'entry_time': 'entry_time',      # 进场时间
    'exit_time': 'exit_time',        # 出场时间
    'entry_gate': 'entry_gate',      # 进场道闸
    'exit_gate': 'exit_gate',        # 出场道闸
    'direction': 'direction',         # 进出方向
    'gate': 'gate',                  # 当前道闸
    'timestamp': 'timestamp',        # 时间戳
    'duration': 'duration'           # 停车时长
}

# 分析结果字段（非必需）
ANALYSIS_FIELDS = {
    'time_period': 'time_period',    # 时间段
    'count': 'count'                 # 数量
}

# 标准列名（用于结果展示）
STANDARD_COLUMNS = {
    'time_period': '时间段',
    'vtype': '车辆类型',
    'gate': '出入口',
    'direction': '进场/出场',
    'count': '数量'
}

# 方向映射
DIRECTION_MAPPING = {
    'entry': '进场',
    'exit': '出场'
}

# 默认值配置
DEFAULT_VALUES = {
    'vtype': '所有车辆',
    'direction': '进场'
}

# 字段验证规则
FIELD_VALIDATION = {
    'vid': {'required': True, 'type': str},
    'vtype': {'required': False, 'type': str},
    'entry_time': {'required': True, 'type': 'datetime64[ns]'},
    'exit_time': {'required': True, 'type': 'datetime64[ns]'},
    'entry_gate': {'required': False, 'type': str},
    'exit_gate': {'required': False, 'type': str},
    'direction': {'required': False, 'type': str},
    'gate': {'required': False, 'type': str},
    'timestamp': {'required': False, 'type': 'datetime64[ns]'},
    'duration': {'required': False, 'type': float},
    'time_period': {'required': False, 'type': str},
    'count': {'required': False, 'type': int}
}

def get_field_mapping(mode, params, mode_config=None, logger=None):
    """
    获取字段映射配置
    
    Args:
        mode: str, 处理模式
        params: dict, 处理参数
        mode_config: dict, 模式配置（可选）
        logger: Logger对象, 用于日志记录（可选）
    
    Returns:
        dict: 字段映射字典
    """
    try:
        # 如果提供了mode_config，使用它
        if mode_config and mode in mode_config:
            config = mode_config[mode]
            return {
                'vid': config.get('车辆唯一标识字段', ''),
                'vtype': config.get('车辆类型字段', ''),
                'timestamp': config.get('时间记录字段', ''),
                'direction': config.get('进出类型字段', ''),
                'gate': config.get('道闸编号字段', ''),
                'entry_time': config.get('进场时间字段', ''),
                'exit_time': config.get('出场时间字段', ''),
                'entry_gate': config.get('进场道闸编号字段', ''),
                'exit_gate': config.get('出场道闸编号字段', '')
            }
        
        # 否则使用params中的配置
        # 首先尝试从params[mode]中获取配置
        mode_params = params.get(mode, {})
        if mode_params:
            return {
                'vid': mode_params.get('车辆唯一标识字段', ''),
                'vtype': mode_params.get('车辆类型字段', ''),
                'timestamp': mode_params.get('时间记录字段', ''),
                'direction': mode_params.get('进出类型字段', ''),
                'gate': mode_params.get('道闸编号字段', ''),
                'entry_time': mode_params.get('进场时间字段', ''),
                'exit_time': mode_params.get('出场时间字段', ''),
                'entry_gate': mode_params.get('进场道闸编号字段', ''),
                'exit_gate': mode_params.get('出场道闸编号字段', '')
            }

        # 如果没有找到模式特定配置，尝试直接从params获取
        return {
            'vid': params.get('车辆唯一标识字段', ''),
            'vtype': params.get('车辆类型字段', ''),
            'timestamp': params.get('时间记录字段', ''),
            'direction': params.get('进出类型字段', ''),
            'gate': params.get('道闸编号字段', ''),
            'entry_time': params.get('进场时间字段', ''),
            'exit_time': params.get('出场时间字段', ''),
            'entry_gate': params.get('进场道闸编号字段', ''),
            'exit_gate': params.get('出场道闸编号字段', '')
        }
    except Exception as e:
        if logger:
            logger.error(f"获取字段映射失败: {str(e)}")
        return {}

def validate_required_fields(data, field_mapping, mode=None, logger=None):
    """
    验证必需字段是否存在

    Args:
        data: DataFrame, 待验证的数据
        field_mapping: dict, 字段映射
        mode: str, 处理模式（可选，用于模式特定验证）
        logger: Logger对象, 用于日志记录（可选）

    Returns:
        bool: 验证是否通过
    """
    try:
        # 检查数据是否为空
        if data is None or data.empty:
            if logger:
                logger.error("数据为空")
            return False

        # 获取数据列名
        columns = data.columns.tolist()

        # 选择验证规则
        if mode and mode in MODE_SPECIFIC_VALIDATION:
            validation_rules = MODE_SPECIFIC_VALIDATION[mode]
        else:
            validation_rules = FIELD_VALIDATION

        # 检查必需字段
        for field, validation in validation_rules.items():
            if validation.get('required', False):
                mapped_field = field_mapping.get(field)
                if not mapped_field or mapped_field not in columns:
                    if logger:
                        logger.error(f"缺少必需字段: {field} (映射为: {mapped_field})")
                    return False

        return True

    except Exception as e:
        if logger:
            logger.error(f"字段验证失败: {str(e)}")
        return False

# 模式特定的字段验证规则
MODE_SPECIFIC_VALIDATION = {
    'mode1_simple': {
        'vid': {'required': False, 'type': str},  # 简化模式不需要车辆标识
        'vtype': {'required': False, 'type': str},
        'timestamp': {'required': True, 'type': 'datetime64[ns]'},
        'direction': {'required': True, 'type': str},
        'gate': {'required': True, 'type': str}
    },
    'mode1': {
        'vid': {'required': True, 'type': str},  # 标准mode1需要车辆标识
        'vtype': {'required': False, 'type': str},
        'timestamp': {'required': True, 'type': 'datetime64[ns]'},
        'direction': {'required': True, 'type': str},
        'gate': {'required': True, 'type': str}
    },
    'mode2': {
        'vid': {'required': False, 'type': str},  # mode2可选车辆标识
        'vtype': {'required': False, 'type': str},
        'entry_time': {'required': True, 'type': 'datetime64[ns]'},
        'exit_time': {'required': True, 'type': 'datetime64[ns]'},
        'entry_gate': {'required': True, 'type': str},
        'exit_gate': {'required': True, 'type': str}
    }
}
