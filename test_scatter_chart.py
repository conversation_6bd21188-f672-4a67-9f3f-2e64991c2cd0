#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试延停时长概率密度散点图生成
"""

import pandas as pd
import numpy as np
import os
from parking_report_generator import ReportGenerator
from parking_chart_generator import ParkingChartGenerator

def create_test_data_and_report():
    """创建测试数据并生成报告"""
    print("=== 创建测试数据并生成报告 ===\n")
    
    # 创建丰富的测试数据
    np.random.seed(42)
    
    # 生成多样化的停车时长数据
    durations = []
    vehicle_types = []
    
    # 短时停车 (0-2小时) - 购物、办事
    short_durations = np.random.exponential(0.8, 50)
    short_durations = np.clip(short_durations, 0.1, 2.0)
    durations.extend(short_durations)
    vehicle_types.extend(['小型车'] * len(short_durations))
    
    # 中时停车 (2-8小时) - 工作、会议
    medium_durations = np.random.normal(4.5, 1.2, 30)
    medium_durations = np.clip(medium_durations, 2.0, 8.0)
    durations.extend(medium_durations)
    vehicle_types.extend(['中型车'] * len(medium_durations))
    
    # 长时停车 (8小时-3天) - 过夜、住宿
    long_durations = np.random.lognormal(2.5, 0.8, 20)
    long_durations = np.clip(long_durations, 8.0, 72.0)
    durations.extend(long_durations)
    vehicle_types.extend(['大型车'] * len(long_durations))
    
    # 超长时停车 (>3天) - 长期停放
    very_long_durations = np.random.uniform(72, 168, 10)
    durations.extend(very_long_durations)
    vehicle_types.extend(['特种车'] * len(very_long_durations))
    
    # 创建完整数据
    n_records = len(durations)
    base_time = pd.Timestamp('2024-01-01 08:00:00')
    
    entry_times = [base_time + pd.Timedelta(hours=i*0.3) for i in range(n_records)]
    exit_times = [entry_times[i] + pd.Timedelta(hours=durations[i]) for i in range(n_records)]
    
    test_data = pd.DataFrame({
        'entry_time': entry_times,
        'exit_time': exit_times,
        'duration': durations,
        'vtype': vehicle_types,
        'vehicle_id': [f'车{i+1:04d}' for i in range(n_records)],
        'entry_gate': np.random.choice(['A口', 'B口', 'C口'], n_records),
        'exit_gate': np.random.choice(['A口', 'B口', 'C口'], n_records)
    })
    
    print(f"创建测试数据: {len(test_data)} 条记录")
    print(f"停车时长范围: {test_data['duration'].min():.2f} - {test_data['duration'].max():.2f} 小时")
    print(f"车辆类型分布: {test_data['vtype'].value_counts().to_dict()}")
    
    # 配置参数
    params = {
        'mode': 'mode2',
        '聚焦日期': '2024-01-01',
        '聚焦月份': '2024-01'
    }
    
    # 创建分析结果（模拟）
    analysis_results = {
        'peak_flow': pd.DataFrame({
            '时段': ['08:00-09:00', '09:00-10:00', '10:00-11:00'],
            '进场': [10, 15, 8],
            '出场': [5, 12, 10],
            '总量': [15, 27, 18]
        })
    }
    
    # 生成报告
    generator = ReportGenerator(test_data, analysis_results, params)
    generator.processed_data = test_data
    
    output_path = os.path.join(os.getcwd(), "散点图测试报告.xlsx")
    if os.path.exists(output_path):
        os.remove(output_path)
    
    result_path = generator.export_to_excel(output_path)
    
    if result_path and os.path.exists(result_path):
        print(f"✅ 报告生成成功: {result_path}")
        return result_path
    else:
        print("❌ 报告生成失败")
        return None

def test_scatter_chart_generation(excel_file):
    """测试散点图生成"""
    print(f"\n=== 测试散点图生成 ===\n")
    
    if not excel_file or not os.path.exists(excel_file):
        print("❌ Excel文件不存在，无法测试散点图")
        return
    
    try:
        # 创建图表生成器
        chart_generator = ParkingChartGenerator(excel_file)
        
        print(f"📊 Excel文件: {excel_file}")
        print(f"📋 可用工作表: {list(chart_generator.excel_data.keys())}")
        
        # 检查是否有延停时长概率密度数据
        if '延停时长概率密度' in chart_generator.excel_data:
            density_data = chart_generator.excel_data['延停时长概率密度']
            print(f"✅ 找到延停时长概率密度数据: {len(density_data)} 行 x {len(density_data.columns)} 列")
            print(f"📋 数据列: {list(density_data.columns)}")
            
            # 显示前几行数据
            print(f"\n📊 数据预览:")
            print(density_data.head(3).to_string())
            
            # 生成散点图
            print(f"\n📈 开始生成散点图...")
            scatter_file = chart_generator.generate_duration_probability_density_scatter()
            
            if scatter_file and os.path.exists(scatter_file):
                print(f"✅ 散点图生成成功: {scatter_file}")
                
                # 验证文件大小
                file_size = os.path.getsize(scatter_file)
                print(f"📄 文件大小: {file_size} 字节")
                
                if file_size > 1000:  # 至少1KB
                    print(f"✅ 文件大小正常")
                else:
                    print(f"⚠️  文件可能过小")
                
                return scatter_file
            else:
                print(f"❌ 散点图生成失败")
                return None
        else:
            print(f"❌ 未找到延停时长概率密度数据")
            return None
            
    except Exception as e:
        print(f"❌ 散点图测试异常: {str(e)}")
        import traceback
        traceback.print_exc()
        return None

def test_all_charts_generation(excel_file):
    """测试所有图表生成（包括新的散点图）"""
    print(f"\n=== 测试所有图表生成 ===\n")
    
    if not excel_file or not os.path.exists(excel_file):
        print("❌ Excel文件不存在，无法测试图表生成")
        return
    
    try:
        # 创建图表生成器
        chart_generator = ParkingChartGenerator(excel_file)
        
        # 生成所有图表
        print(f"📊 开始生成所有图表...")
        generated_files = chart_generator.generate_all_charts()
        
        print(f"\n📈 图表生成结果:")
        if generated_files:
            for i, file_path in enumerate(generated_files, 1):
                file_name = os.path.basename(file_path)
                file_size = os.path.getsize(file_path) if os.path.exists(file_path) else 0
                print(f"   {i:2d}. {file_name} ({file_size} 字节)")
            
            # 检查是否包含散点图
            scatter_files = [f for f in generated_files if '散点图' in f]
            if scatter_files:
                print(f"\n✅ 成功生成散点图: {len(scatter_files)} 个")
                for scatter_file in scatter_files:
                    print(f"   📊 {os.path.basename(scatter_file)}")
            else:
                print(f"\n⚠️  未生成散点图")
        else:
            print(f"❌ 未生成任何图表")
            
    except Exception as e:
        print(f"❌ 图表生成测试异常: {str(e)}")
        import traceback
        traceback.print_exc()

def main():
    """主测试函数"""
    print("🎯 延停时长概率密度散点图测试\n")
    
    # 步骤1：创建测试数据并生成报告
    excel_file = create_test_data_and_report()
    
    if excel_file:
        # 步骤2：测试单独的散点图生成
        scatter_file = test_scatter_chart_generation(excel_file)
        
        # 步骤3：测试所有图表生成
        test_all_charts_generation(excel_file)
        
        print(f"\n{'='*60}")
        print(f"测试总结:")
        print(f"1. ✅ 测试数据和报告生成")
        print(f"2. {'✅' if scatter_file else '❌'} 散点图单独生成测试")
        print(f"3. ✅ 所有图表生成测试")
        
        if scatter_file:
            print(f"\n🎉 延停时长概率密度散点图功能测试成功！")
            print(f"📊 散点图文件: {os.path.basename(scatter_file)}")
        else:
            print(f"\n⚠️  散点图功能需要进一步检查")
    else:
        print(f"\n❌ 测试失败：无法生成基础报告")

if __name__ == "__main__":
    main()
