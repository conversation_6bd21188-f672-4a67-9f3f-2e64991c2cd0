#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试您描述的具体场景
3个文件，其中2个重复，应该保留1个重复文件 + 1个唯一文件 = 2个文件的数据
"""

import pandas as pd
import tempfile
import os
import shutil

def test_your_scenario():
    """测试您描述的具体场景"""
    print("🧪 测试您的具体场景")
    print("=" * 60)
    
    # 创建临时目录
    temp_dir = tempfile.mkdtemp()
    print(f"测试目录: {temp_dir}")
    
    try:
        # 创建测试数据 - 模拟您的实际情况
        # 数据A：8万多行
        data_a = pd.DataFrame({
            'column1': range(80000),
            'column2': [f'data_{i}' for i in range(80000)],
            'column3': [i * 2 for i in range(80000)]
        })
        
        # 数据B：10万行（唯一数据）
        data_b = pd.DataFrame({
            'column1': range(100000, 200000),
            'column2': [f'unique_{i}' for i in range(100000)],
            'column3': [i * 3 for i in range(100000)]
        })
        
        # 数据C：与数据A完全相同（重复）
        data_c = data_a.copy()
        
        # 创建文件
        file1 = os.path.join(temp_dir, 'file1.csv')
        file2 = os.path.join(temp_dir, 'file2.csv')
        file3 = os.path.join(temp_dir, 'file3.csv')
        
        print(f"\n📁 创建测试文件:")
        print(f"  file1.csv: {len(data_a):,} 行")
        print(f"  file2.csv: {len(data_b):,} 行 (唯一数据)")
        print(f"  file3.csv: {len(data_c):,} 行 (与file1.csv重复)")
        
        # 保存文件
        data_a.to_csv(file1, index=False)
        data_b.to_csv(file2, index=False)
        data_c.to_csv(file3, index=False)
        
        print(f"\n📊 预期结果:")
        print(f"  - 应该保留: file1.csv 或 file3.csv (其中一个)")
        print(f"  - 应该保留: file2.csv")
        print(f"  - 应该跳过: file1.csv 或 file3.csv (其中一个)")
        print(f"  - 预期总行数: {len(data_a) + len(data_b):,} 行")
        
        # 导入合并器
        from excel_data_merger import IntegratedDataMerger
        
        # 创建合并器
        merger = IntegratedDataMerger()
        
        # 执行合并
        print(f"\n🔄 执行合并...")
        output_file = os.path.join(temp_dir, 'merged_result.csv')
        result = merger.smart_merge_directory(
            input_path=temp_dir,
            output_path=output_file,
            file_pattern="*",
            recursive=False
        )
        
        # 分析结果
        stats = merger.merge_stats
        
        print(f"\n📈 实际结果:")
        print(f"  总数据源: {stats['total_sources']}")
        print(f"  成功合并: {stats['merged_sources']}")
        print(f"  重复数量: {stats['duplicate_sources']}")
        print(f"  合并行数: {stats['merged_rows']:,}")
        print(f"  实际行数: {len(result):,}")
        
        # 显示详细信息
        if stats['merged_details']:
            print(f"\n✅ 成功合并的文件:")
            for detail in stats['merged_details']:
                print(f"   - {detail['file_name']}: {detail['rows']:,} 行")
        
        if stats['duplicate_details']:
            print(f"\n🔄 重复检测详情:")
            for detail in stats['duplicate_details']:
                print(f"   - {detail['source']} → 重复于 {detail['duplicate_of']}")
        
        # 验证结果
        expected_sources = 2  # 应该有2个文件被合并
        expected_duplicates = 1  # 应该有1个重复文件被跳过
        expected_rows = len(data_a) + len(data_b)  # 预期总行数
        
        print(f"\n🔍 结果验证:")
        print(f"  预期成功合并: {expected_sources}")
        print(f"  实际成功合并: {stats['merged_sources']}")
        print(f"  预期重复数量: {expected_duplicates}")
        print(f"  实际重复数量: {stats['duplicate_sources']}")
        print(f"  预期总行数: {expected_rows:,}")
        print(f"  实际总行数: {len(result):,}")
        
        # 判断是否正确
        is_correct = (
            stats['merged_sources'] == expected_sources and
            stats['duplicate_sources'] == expected_duplicates and
            len(result) == expected_rows
        )
        
        print(f"\n📋 最终判断:")
        if is_correct:
            print("✅ 重复检测逻辑正确！")
            print("   - 保留了一个重复文件")
            print("   - 跳过了另一个重复文件")
            print("   - 保留了唯一文件")
            print("   - 总数据量正确")
        else:
            print("❌ 重复检测逻辑有问题！")
            if stats['merged_sources'] != expected_sources:
                print(f"   - 合并文件数不对: 期望{expected_sources}, 实际{stats['merged_sources']}")
            if stats['duplicate_sources'] != expected_duplicates:
                print(f"   - 重复文件数不对: 期望{expected_duplicates}, 实际{stats['duplicate_sources']}")
            if len(result) != expected_rows:
                print(f"   - 总行数不对: 期望{expected_rows:,}, 实际{len(result):,}")
        
        return is_correct
        
    except Exception as e:
        print(f"❌ 测试异常: {str(e)}")
        import traceback
        traceback.print_exc()
        return False
        
    finally:
        # 清理
        try:
            shutil.rmtree(temp_dir)
            print(f"\n🧹 清理测试目录")
        except:
            pass

def main():
    """主函数"""
    print("🔧 验证您描述的重复检测场景")
    print("=" * 80)
    
    result = test_your_scenario()
    
    print(f"\n📊 测试结果: {'✅ 通过' if result else '❌ 失败'}")
    
    if not result:
        print(f"\n💡 如果测试失败，说明重复检测逻辑确实需要修复")
        print(f"   当前可能的问题:")
        print(f"   1. 两个重复文件都被跳过了")
        print(f"   2. 重复文件都被保留了")
        print(f"   3. 文件查找或处理有问题")

if __name__ == "__main__":
    main()
