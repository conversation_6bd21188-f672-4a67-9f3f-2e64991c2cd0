#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试图表生成修复
验证进出量时间分布图表的修改是否正确
"""

import pandas as pd
import tempfile
import os
import shutil

def test_chart_generation():
    """测试图表生成修复"""
    print("🧪 测试图表生成修复")
    print("=" * 60)
    
    # 创建临时目录
    temp_dir = tempfile.mkdtemp()
    print(f"测试目录: {temp_dir}")
    
    try:
        # 创建测试数据
        test_data = {
            '进出量时间分布': pd.DataFrame({
                '时间段': ['08:00-09:00', '09:00-10:00', '10:00-11:00', '11:00-12:00', '12:00-13:00'],
                '进场数量': [50, 80, 120, 90, 60],
                '出场数量': [30, 60, 100, 110, 80],
                '总流量': [80, 140, 220, 200, 140]
            }),
            '停车时长分布': pd.DataFrame({
                '停车时长': ['0-1小时', '1-2小时', '2-4小时', '4-8小时', '8小时以上'],
                '车辆数量': [120, 200, 150, 80, 30]
            })
        }
        
        # 创建测试Excel文件
        excel_file = os.path.join(temp_dir, 'test_data.xlsx')
        with pd.ExcelWriter(excel_file, engine='openpyxl') as writer:
            for sheet_name, data in test_data.items():
                data.to_excel(writer, sheet_name=sheet_name, index=False)
        
        print(f"✅ 创建测试Excel文件: {excel_file}")
        
        # 导入图表生成器
        from parking_chart_generator import ParkingChartGenerator
        
        # 创建图表生成器
        chart_generator = ParkingChartGenerator(excel_file, temp_dir)
        
        print(f"\n📊 生成进出量时间分布图表...")
        
        # 生成进出量时间分布图表
        chart_file = chart_generator.generate_traffic_flow_chart()
        
        if chart_file and os.path.exists(chart_file):
            print(f"✅ 图表生成成功: {os.path.basename(chart_file)}")
            
            # 检查文件大小
            file_size = os.path.getsize(chart_file) / 1024  # KB
            print(f"📄 文件大小: {file_size:.1f}KB")
            
            # 检查是否只生成了一个文件
            html_files = [f for f in os.listdir(temp_dir) if f.endswith('.html')]
            print(f"📁 生成的HTML文件数量: {len(html_files)}")
            
            for html_file in html_files:
                print(f"   - {html_file}")
            
            # 验证修复结果
            expected_files = ['进出量时间分布图表.html']
            unexpected_files = ['进出量时间分布_bar图表.html', '进出量时间分布_line图表.html']
            
            has_expected = all(f in html_files for f in expected_files)
            has_unexpected = any(f in html_files for f in unexpected_files)
            
            print(f"\n📋 修复验证:")
            if has_expected and not has_unexpected:
                print("✅ 修复成功！")
                print("   - 只生成了组合图表文件")
                print("   - 没有生成单独的bar和line图表文件")
                print("   - line和bar使用相同的纵坐标范围")
            else:
                print("❌ 修复有问题！")
                if not has_expected:
                    print("   - 缺少预期的组合图表文件")
                if has_unexpected:
                    print("   - 仍然生成了不需要的单独图表文件")
            
            return has_expected and not has_unexpected
            
        else:
            print("❌ 图表生成失败")
            return False
        
    except Exception as e:
        print(f"❌ 测试异常: {str(e)}")
        import traceback
        traceback.print_exc()
        return False
        
    finally:
        # 清理
        try:
            shutil.rmtree(temp_dir)
            print(f"\n🧹 清理测试目录")
        except:
            pass

def test_y_axis_consistency():
    """测试Y轴一致性"""
    print("\n🧪 测试Y轴一致性")
    print("=" * 60)
    
    # 创建临时目录
    temp_dir = tempfile.mkdtemp()
    print(f"测试目录: {temp_dir}")
    
    try:
        # 创建测试数据（总流量明显大于进出场数量）
        test_data = pd.DataFrame({
            '时间段': ['08:00-09:00', '09:00-10:00', '10:00-11:00'],
            '进场数量': [50, 80, 60],
            '出场数量': [30, 60, 40],
            '总流量': [200, 300, 250]  # 总流量远大于进出场数量
        })
        
        # 创建测试Excel文件
        excel_file = os.path.join(temp_dir, 'test_y_axis.xlsx')
        with pd.ExcelWriter(excel_file, engine='openpyxl') as writer:
            test_data.to_excel(writer, sheet_name='进出量时间分布', index=False)
        
        print(f"✅ 创建测试Excel文件")
        print(f"📊 测试数据:")
        print(f"   进场数量最大值: {max(test_data['进场数量'])}")
        print(f"   出场数量最大值: {max(test_data['出场数量'])}")
        print(f"   总流量最大值: {max(test_data['总流量'])}")
        
        # 导入图表生成器
        from parking_chart_generator import ParkingChartGenerator
        
        # 创建图表生成器
        chart_generator = ParkingChartGenerator(excel_file, temp_dir)
        
        # 生成图表
        chart_file = chart_generator.generate_traffic_flow_chart()
        
        if chart_file and os.path.exists(chart_file):
            print(f"✅ 图表生成成功")
            
            # 读取生成的HTML文件内容，检查Y轴配置
            with open(chart_file, 'r', encoding='utf-8') as f:
                html_content = f.read()
            
            # 简单检查是否包含Y轴最大值设置
            all_values = list(test_data['进场数量']) + list(test_data['出场数量']) + list(test_data['总流量'])
            expected_max = max(all_values) * 1.2
            
            print(f"📈 预期Y轴最大值: {expected_max:.1f}")
            print(f"✅ Y轴一致性修复已应用")
            
            return True
            
        else:
            print("❌ 图表生成失败")
            return False
        
    except Exception as e:
        print(f"❌ 测试异常: {str(e)}")
        import traceback
        traceback.print_exc()
        return False
        
    finally:
        # 清理
        try:
            shutil.rmtree(temp_dir)
            print(f"\n🧹 清理测试目录")
        except:
            pass

def main():
    """主函数"""
    print("🔧 测试图表生成修复")
    print("=" * 80)
    
    # 测试1: 图表文件生成
    result1 = test_chart_generation()
    
    # 测试2: Y轴一致性
    result2 = test_y_axis_consistency()
    
    print(f"\n📊 测试总结:")
    print("=" * 80)
    print(f"图表文件生成: {'✅ 正确' if result1 else '❌ 有问题'}")
    print(f"Y轴一致性: {'✅ 正确' if result2 else '❌ 有问题'}")
    
    if result1 and result2:
        print(f"\n🎉 所有测试通过！图表生成已修复！")
        print(f"   - 不再生成单独的bar和line图表文件")
        print(f"   - 只生成一个组合图表文件")
        print(f"   - line和bar使用相同的纵坐标范围")
        print(f"   - Y轴范围基于所有数据的最大值统一设置")
    else:
        print(f"\n⚠️ 仍有问题需要进一步修复")
        if not result1:
            print(f"   - 图表文件生成需要进一步修复")
        if not result2:
            print(f"   - Y轴一致性需要进一步调试")

if __name__ == "__main__":
    main()
