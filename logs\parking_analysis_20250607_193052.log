2025-06-07 19:30:52,303 - main - INFO - 开始数据分析任务
2025-06-07 19:30:52,303 - main - INFO - 使用处理模式: mode_p2
2025-06-07 19:30:52,304 - main - INFO - 读取数据文件: C:\Users\<USER>\Desktop\停车分析\数据\火车站\5.31-6.2私家车.csv
2025-06-07 19:31:16,754 - DataReader - INFO - 检测到文件编码: GB2312
2025-06-07 19:31:16,891 - DataReader - INFO - 成功读取数据文件，共 15479 行
2025-06-07 19:31:16,923 - DataReader - INFO - 数据结构验证通过
2025-06-07 19:31:16,924 - main - INFO - 开始数据处理
2025-06-07 19:31:16,929 - DataProcessor - INFO - 开始数据处理
2025-06-07 19:31:17,091 - DataProcessor - INFO - 数据处理完成，统计信息: {'total_records': 15479, 'vehicle_type_counts': {'临时用户A': 15168, '免费用户A': 311}, 'entry_gate_counts': {'2号进口1': 10783, '地下3号入口2': 3161, '地下3号入口1': 1291, '地面入口': 139, '地面入口2': 37, '地下室2号出口-2': 31, '停车场1号西出口2': 25, '停车场1号西出口': 9, '地面出口': 3}, 'exit_gate_counts': {'地下室2号出口-2': 8948, '停车场1号西出口2': 3960, '停车场1号西出口': 2392, '地面出口': 142, '地面出口2': 37}, 'avg_duration': 1.955036931757004, 'max_duration': 256.15777777777777}
2025-06-07 19:31:17,099 - DataProcessor - WARNING - 过滤掉 3200 条异常停车时长记录
2025-06-07 19:31:17,125 - main - INFO - 开始数据分析
2025-06-07 19:31:17,170 - main - ERROR - 处理过程中出现错误: '道闸编号字段'
Traceback (most recent call last):
  File "c:/Users/<USER>/Desktop/停车分析/pythoncode/1.开发中/p_parking_main.py", line 94, in main
    analysis_results = analyzer.generate_summary()
  File "c:\Users\<USER>\Desktop\停车分析\pythoncode\1.开发中\p_parking_analyzer.py", line 247, in generate_summary
    gate_usage = self.analyze_gate_usage()
  File "c:\Users\<USER>\Desktop\停车分析\pythoncode\1.开发中\p_parking_analyzer.py", line 202, in analyze_gate_usage
    gate_field = self.params['道闸编号字段']
KeyError: '道闸编号字段'
