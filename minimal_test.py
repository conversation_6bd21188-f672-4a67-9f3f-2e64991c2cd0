#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pandas as pd
from parking_time_filter import TimeFilter

# 创建简单测试数据
data = pd.DataFrame({
    'entry_time': pd.to_datetime(['2024-01-01 08:00:00']),
    'exit_time': pd.to_datetime(['2024-01-01 10:00:00'])
})

# 创建时间过滤器
time_filter = TimeFilter(data, {})

# 测试时段生成
print("测试时段生成功能...")
try:
    periods = time_filter.create_duration_periods()
    print(f"成功生成 {len(periods)} 个时段:")
    for i, period in enumerate(periods[:5]):  # 只显示前5个
        print(f"  {i+1}. {period}")
    print("...")
    print(f"  {len(periods)}. {periods[-1]}")
except Exception as e:
    print(f"错误: {str(e)}")
    import traceback
    traceback.print_exc()

print("\n测试完成!")
