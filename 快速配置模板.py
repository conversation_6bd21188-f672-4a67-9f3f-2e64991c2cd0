#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
停车数据图表生成器 - 快速配置模板
复制以下配置到 parking_chart_generator.py 的 if __name__ == "__main__": 块中
"""

# ==================== 快速配置模板 ====================
# 将以下代码复制到 parking_chart_generator.py 的 if __name__ == "__main__": 块中

"""
if __name__ == "__main__":
    # ==================== 配置参数区域 ====================
    # 📁 文件路径配置
    EXCEL_FILE_PATH = r"C:\your\path\to\excel_file.xlsx"  # 🔧 修改为您的Excel文件路径
    OUTPUT_DIR = None  # 输出目录，None表示使用Excel文件所在目录，也可指定如: r"C:\output"
    
    # 🎨 图表样式配置
    CHART_THEME = 'MACARONS'  # 图表主题: LIGHT, DARK, CHALK, MACARONS, PURPLE_PASSION, ROMA, SHINE, VINTAGE等
    BAR_COLORS = ['#1f77b4', '#ff7f0e', '#2ca02c', '#d62728', '#9467bd']  # 柱状图颜色列表
    LINE_COLORS = ['#d62728', '#9467bd', '#8c564b', '#e377c2', '#7f7f7f']  # 线条颜色列表
    LINE_WIDTH = 4  # 线条粗细 (1-10)
    CHART_WIDTH = '1400px'  # 图表宽度
    CHART_HEIGHT = '700px'  # 图表高度
    
    # 📊 图表生成配置
    GENERATE_ALL_CHARTS = True  # 是否生成所有预定义图表
    GENERATE_DASHBOARD = True  # 是否生成综合仪表板
    GENERATE_CUSTOM_CHARTS = True  # 是否生成自定义示例图表
    AUTO_OPEN_DASHBOARD = False  # 是否自动打开仪表板 (True/False)
    
    # 🔧 自定义图表配置
    CUSTOM_CHARTS_CONFIG = [
        {
            'sheet_name': '进出量时间分布',
            'chart_type': 'bar',
            'x_col': 0,
            'y_cols': [1, 2],
            'title': '进出量对比柱状图',
            'subtitle': '显示进场和出场数量对比',
            'colors': ['#FF6B6B', '#4ECDC4']
        },
        {
            'sheet_name': '进出量时间分布',
            'chart_type': 'line',
            'x_col': 0,
            'y_cols': [3],
            'title': '总流量趋势折线图',
            'subtitle': '显示总流量变化趋势',
            'colors': ['#FF9F43']
        }
    ]
    
    # ==================== 执行区域 ====================
    # 以下代码无需修改，直接复制即可
    print("🚗 停车数据图表生成器")
    print("=" * 60)
    print(f"📁 输入文件: {EXCEL_FILE_PATH}")
    print(f"📁 输出目录: {OUTPUT_DIR or '与Excel文件同目录'}")
    print(f"🎨 图表主题: {CHART_THEME}")
    print(f"📏 图表尺寸: {CHART_WIDTH} x {CHART_HEIGHT}")
    print(f"🖌️ 线条粗细: {LINE_WIDTH}px")
    print("=" * 60)
    
    # 检查文件是否存在
    import os
    if not os.path.exists(EXCEL_FILE_PATH):
        print(f"❌ Excel文件不存在: {EXCEL_FILE_PATH}")
        print("请修改 EXCEL_FILE_PATH 变量为正确的文件路径")
        input("按回车键退出...")
        exit(1)
    
    try:
        # 创建图表生成器
        from pyecharts.globals import ThemeType
        
        # 主题映射
        theme_map = {
            'LIGHT': ThemeType.LIGHT,
            'DARK': ThemeType.DARK,
            'CHALK': ThemeType.CHALK,
            'ESSOS': ThemeType.ESSOS,
            'INFOGRAPHIC': ThemeType.INFOGRAPHIC,
            'MACARONS': ThemeType.MACARONS,
            'PURPLE_PASSION': ThemeType.PURPLE_PASSION,
            'ROMA': ThemeType.ROMA,
            'ROMANTIC': ThemeType.ROMANTIC,
            'SHINE': ThemeType.SHINE,
            'VINTAGE': ThemeType.VINTAGE,
            'WALDEN': ThemeType.WALDEN,
            'WESTEROS': ThemeType.WESTEROS,
            'WONDERLAND': ThemeType.WONDERLAND
        }
        
        selected_theme = theme_map.get(CHART_THEME, ThemeType.MACARONS)
        
        chart_generator = ParkingChartGenerator(EXCEL_FILE_PATH, OUTPUT_DIR)
        
        # 列出可用的工作表
        print("\\n📋 Excel文件中的工作表:")
        chart_generator.list_available_sheets()
        
        print(f"\\n🎨 应用自定义样式配置...")
        # 应用自定义样式
        chart_generator.update_chart_config(
            theme=selected_theme,
            bar_colors=BAR_COLORS,
            line_colors=LINE_COLORS,
            line_width=LINE_WIDTH,
            chart_width=CHART_WIDTH,
            chart_height=CHART_HEIGHT
        )
        
        generated_files = []
        
        # 生成预定义图表
        if GENERATE_ALL_CHARTS:
            print(f"\\n📊 生成预定义图表...")
            files = chart_generator.generate_all_charts()
            generated_files.extend(files)
        
        # 生成自定义图表
        if GENERATE_CUSTOM_CHARTS and CUSTOM_CHARTS_CONFIG:
            print(f"\\n🔧 生成自定义图表...")
            for config in CUSTOM_CHARTS_CONFIG:
                try:
                    file_path = chart_generator.generate_custom_chart(**config)
                    if file_path:
                        generated_files.append(file_path)
                        print(f"  ✅ {config['title']}")
                except Exception as e:
                    print(f"  ❌ {config['title']}: {str(e)}")
        
        # 生成综合仪表板
        if GENERATE_DASHBOARD:
            print(f"\\n🏠 生成综合仪表板...")
            dashboard_file = chart_generator.generate_dashboard()
            if dashboard_file:
                generated_files.append(dashboard_file)
        
        # 显示结果
        print(f"\\n🎉 图表生成完成!")
        print("=" * 60)
        if generated_files:
            print(f"📊 共生成 {len(generated_files)} 个文件:")
            for i, file_path in enumerate(generated_files, 1):
                file_name = os.path.basename(file_path)
                file_size = os.path.getsize(file_path) / 1024  # KB
                print(f"  {i:2d}. {file_name} ({file_size:.1f}KB)")
            
            print(f"\\n📁 输出目录: {chart_generator.output_dir}")
            
            # 自动打开仪表板
            if AUTO_OPEN_DASHBOARD:
                dashboard_path = os.path.join(chart_generator.output_dir, "停车数据分析仪表板.html")
                if os.path.exists(dashboard_path):
                    import webbrowser
                    webbrowser.open(f'file://{os.path.abspath(dashboard_path)}')
                    print("🌐 仪表板已自动在浏览器中打开")
            else:
                print("\\n💡 提示: 双击 '停车数据分析仪表板.html' 查看所有图表")
        
    except Exception as e:
        print(f"❌ 生成图表时发生错误: {str(e)}")
        import traceback
        traceback.print_exc()
        input("按回车键退出...")
    
    print("\\n👋 程序执行完成")
"""

# ==================== 常用配置预设 ====================

# 🎨 颜色方案预设
COLOR_SCHEMES = {
    "经典蓝橙": {
        "BAR_COLORS": ['#1f77b4', '#ff7f0e', '#2ca02c', '#d62728', '#9467bd'],
        "LINE_COLORS": ['#d62728', '#9467bd', '#8c564b']
    },
    "清新绿蓝": {
        "BAR_COLORS": ['#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7', '#DDA0DD'],
        "LINE_COLORS": ['#4ECDC4', '#45B7D1', '#96CEB4']
    },
    "暖色调": {
        "BAR_COLORS": ['#FF6B6B', '#FF9F43', '#FECA57', '#FF6348', '#FF7675'],
        "LINE_COLORS": ['#FF6B6B', '#FF9F43', '#FECA57']
    },
    "商务风格": {
        "BAR_COLORS": ['#2C3E50', '#34495E', '#7F8C8D', '#95A5A6', '#BDC3C7'],
        "LINE_COLORS": ['#E74C3C', '#3498DB', '#2ECC71']
    },
    "高对比度": {
        "BAR_COLORS": ['#000000', '#FF0000', '#00FF00', '#0000FF', '#FFFF00'],
        "LINE_COLORS": ['#FF0000', '#00FF00', '#0000FF']
    }
}

# 📏 尺寸预设
SIZE_PRESETS = {
    "小型": {"CHART_WIDTH": '1000px', "CHART_HEIGHT": '500px'},
    "标准": {"CHART_WIDTH": '1200px', "CHART_HEIGHT": '600px'},
    "大型": {"CHART_WIDTH": '1400px', "CHART_HEIGHT": '700px'},
    "超大": {"CHART_WIDTH": '1600px', "CHART_HEIGHT": '800px'},
    "演示": {"CHART_WIDTH": '1800px', "CHART_HEIGHT": '900px'}
}

# 🎭 主题预设
THEME_PRESETS = {
    "浅色简约": "LIGHT",
    "深色酷炫": "DARK", 
    "马卡龙": "MACARONS",
    "紫色激情": "PURPLE_PASSION",
    "复古风格": "VINTAGE",
    "仙境主题": "WONDERLAND",
    "罗马风格": "ROMA"
}

def print_presets():
    """打印所有预设配置"""
    print("🎨 颜色方案预设:")
    for name, colors in COLOR_SCHEMES.items():
        print(f"  {name}: {colors}")
    
    print("\n📏 尺寸预设:")
    for name, size in SIZE_PRESETS.items():
        print(f"  {name}: {size}")
    
    print("\n🎭 主题预设:")
    for name, theme in THEME_PRESETS.items():
        print(f"  {name}: {theme}")

if __name__ == "__main__":
    print("📋 停车数据图表生成器 - 快速配置模板")
    print("=" * 60)
    print("🔧 使用方法:")
    print("1. 复制上面的配置代码到 parking_chart_generator.py")
    print("2. 修改 EXCEL_FILE_PATH 为您的Excel文件路径")
    print("3. 根据需要调整其他参数")
    print("4. 运行程序")
    print("\n" + "=" * 60)
    
    print_presets()
