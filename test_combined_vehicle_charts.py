#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试合并的车辆类型图表生成功能
"""

import os

def test_combined_vehicle_charts():
    """测试合并的车辆类型图表生成"""
    print("🚗 测试合并的车辆类型图表生成")
    print("=" * 50)
    
    # Excel文件路径
    excel_file = r'C:\Users\<USER>\Desktop\停车分析\义乌火车站道闸_私家车_合并_20250617_083509_analysis_20250618_211554.xlsx'
    
    if not os.path.exists(excel_file):
        print(f"❌ Excel文件不存在: {excel_file}")
        return False
    
    try:
        # 导入图表生成器
        from parking_chart_generator import ParkingChartGenerator
        
        # 创建图表生成器
        print("📊 创建图表生成器...")
        chart_generator = ParkingChartGenerator(excel_file, None)
        
        # 检查是否有进出量时间分布工作表
        if '进出量时间分布' not in chart_generator.excel_data:
            print("❌ 未找到'进出量时间分布'工作表")
            available_sheets = list(chart_generator.excel_data.keys())
            print(f"可用工作表: {available_sheets}")
            return False
        
        # 获取数据并分析结构
        data = chart_generator.excel_data['进出量时间分布']
        columns = list(data.columns)
        total_cols = len(columns)
        
        print(f"📋 数据结构分析:")
        print(f"   总列数: {total_cols}")
        print(f"   前4列: {columns[:4]}")
        if total_cols > 4:
            vehicle_cols = columns[4:]
            print(f"   车辆类型相关列: {vehicle_cols}")
        
        # 尝试生成合并的车辆类型图表
        print(f"\n🚗 尝试生成合并的车辆类型图表...")
        result = chart_generator.generate_vehicle_type_traffic_charts()
        
        if result:
            print(f"✅ 成功生成: {os.path.basename(result)}")
            
            # 检查文件是否存在
            if os.path.exists(result):
                file_size = os.path.getsize(result) / 1024
                print(f"📄 文件大小: {file_size:.1f}KB")
                print(f"📁 文件路径: {result}")
                
                # 读取HTML内容检查是否为合并图表
                with open(result, 'r', encoding='utf-8') as f:
                    html_content = f.read()
                
                # 检查是否为单一图表（不是Page布局）
                is_single_chart = 'DraggablePageLayout' not in html_content
                has_multiple_series = html_content.count('series_name') > 2
                
                print(f"\n📊 图表类型验证:")
                print(f"   单一图表: {'✅ 是' if is_single_chart else '❌ 否（仍为Page布局）'}")
                print(f"   多个数据系列: {'✅ 是' if has_multiple_series else '❌ 否'}")
                
                if is_single_chart and has_multiple_series:
                    print("✅ 成功生成合并的车辆类型图表！")
                    print("   - 所有车辆类型在一张图中显示")
                    print("   - 使用统一的图表尺寸")
                    print("   - 便于对比不同车辆类型的数据")
                    return True
                else:
                    print("⚠️ 图表生成但可能不是预期的合并格式")
                    return False
            else:
                print(f"❌ 文件未生成: {result}")
                return False
        else:
            print("❌ 未能生成车辆类型图表")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def demo_combined_chart_features():
    """演示合并图表的特点"""
    print("\n📊 合并图表特点")
    print("=" * 50)
    
    print("🎯 合并图表优势:")
    print("   ✅ 统一的图表尺寸（1200x600px）")
    print("   ✅ 所有车辆类型在一张图中对比")
    print("   ✅ 统一的时间轴，便于横向对比")
    print("   ✅ 智能图例布局（超过8个系列时自动滚动）")
    print("   ✅ 专业演讲风格的颜色配置")
    
    print("\n🎨 视觉效果:")
    print("   - 每种车辆类型使用配对的颜色（进场/出场）")
    print("   - 图例显示所有车辆类型和方向")
    print("   - 支持缩放和交互操作")
    print("   - 优化的tooltip显示")
    
    print("\n📋 数据系列命名:")
    print("   - 格式: '[车辆类型]-进场' / '[车辆类型]-出场'")
    print("   - 例如: '私家车-进场', '私家车-出场'")
    print("   - 自动排序，便于查找")

def main():
    """主函数"""
    # 演示功能特点
    demo_combined_chart_features()
    
    # 测试功能
    success = test_combined_vehicle_charts()
    
    if success:
        print("\n🎉 测试成功！合并的车辆类型图表已生成！")
        print("📁 文件名: 进出量时间分布_车型.html")
        print("💡 现在所有车辆类型都显示在一张图中，便于对比分析")
    else:
        print("\n⚠️ 测试失败")
        print("💡 可能的原因:")
        print("   1. Excel文件中没有足够的车辆类型数据")
        print("   2. 数据格式不正确")
        print("   3. 代码执行过程中出现错误")
    
    print("\n" + "="*50)
    input("按回车键退出...")

if __name__ == "__main__":
    main()
