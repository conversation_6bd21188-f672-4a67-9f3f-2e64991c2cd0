#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的散点图测试
"""

import pandas as pd
import numpy as np
import os

def create_simple_test_excel():
    """创建简单的测试Excel文件"""
    print("=== 创建简单测试Excel文件 ===\n")
    
    # 创建延停时长概率密度数据
    time_periods = [
        "0分钟-30分钟", "30分钟-1小时", "1小时-1小时30分钟", "1小时30分钟-2小时",
        "2小时-3小时", "3小时-4小时", "4小时-5小时", "5小时-6小时",
        "6小时-7小时", "7小时-8小时", "8小时-1天", "1天-3天", ">3天"
    ]
    
    # 模拟频数数据
    np.random.seed(42)
    total_freq = [50, 45, 30, 25, 20, 15, 12, 10, 8, 6, 15, 8, 3]
    
    # 按车辆类型分配频数
    small_car_freq = [int(f * 0.6) for f in total_freq]  # 小型车占60%
    medium_car_freq = [int(f * 0.3) for f in total_freq]  # 中型车占30%
    large_car_freq = [int(f * 0.1) for f in total_freq]   # 大型车占10%
    
    # 创建DataFrame
    density_data = pd.DataFrame({
        '时长区间': time_periods,
        '频数': total_freq,
        '频率': [f/sum(total_freq) for f in total_freq],
        '百分比(%)': [f/sum(total_freq)*100 for f in total_freq],
        '累积频率': pd.Series([f/sum(total_freq) for f in total_freq]).cumsum(),
        '累积百分比(%)': pd.Series([f/sum(total_freq)*100 for f in total_freq]).cumsum(),
        '小型车_频数': small_car_freq,
        '小型车_频率': [f/sum(small_car_freq) for f in small_car_freq],
        '小型车_百分比(%)': [f/sum(small_car_freq)*100 for f in small_car_freq],
        '中型车_频数': medium_car_freq,
        '中型车_频率': [f/sum(medium_car_freq) for f in medium_car_freq],
        '中型车_百分比(%)': [f/sum(medium_car_freq)*100 for f in medium_car_freq],
        '大型车_频数': large_car_freq,
        '大型车_频率': [f/sum(large_car_freq) for f in large_car_freq],
        '大型车_百分比(%)': [f/sum(large_car_freq)*100 for f in large_car_freq]
    })
    
    # 保存到Excel
    excel_file = "简单散点图测试.xlsx"
    if os.path.exists(excel_file):
        os.remove(excel_file)
    
    with pd.ExcelWriter(excel_file, engine='xlsxwriter') as writer:
        density_data.to_excel(writer, sheet_name='延停时长概率密度', index=False)
    
    print(f"✅ 测试Excel文件创建成功: {excel_file}")
    print(f"📊 数据概览: {len(density_data)} 行 x {len(density_data.columns)} 列")
    print(f"📋 车辆类型: 小型车, 中型车, 大型车")
    print(f"📋 总频数: {sum(total_freq)}")
    
    return excel_file

def test_scatter_chart_directly():
    """直接测试散点图生成"""
    print(f"\n=== 直接测试散点图生成 ===\n")
    
    # 创建测试Excel文件
    excel_file = create_simple_test_excel()
    
    try:
        # 导入图表生成器
        from parking_chart_generator import ParkingChartGenerator
        
        print(f"📊 开始测试散点图生成...")
        
        # 创建图表生成器
        chart_generator = ParkingChartGenerator(excel_file)
        
        print(f"✅ 图表生成器创建成功")
        print(f"📋 可用工作表: {list(chart_generator.excel_data.keys())}")
        
        # 检查数据
        if '延停时长概率密度' in chart_generator.excel_data:
            data = chart_generator.excel_data['延停时长概率密度']
            print(f"✅ 数据加载成功: {len(data)} 行")
            print(f"📋 数据列: {list(data.columns)}")
            
            # 显示前3行数据
            print(f"\n📊 数据预览:")
            print(data[['时长区间', '频数', '小型车_频数', '中型车_频数', '大型车_频数']].head(3).to_string())
            
            # 生成散点图
            print(f"\n📈 开始生成散点图...")
            scatter_file = chart_generator.generate_duration_probability_density_scatter()
            
            if scatter_file and os.path.exists(scatter_file):
                print(f"✅ 散点图生成成功: {scatter_file}")
                
                # 检查文件大小
                file_size = os.path.getsize(scatter_file)
                print(f"📄 文件大小: {file_size} 字节")
                
                if file_size > 5000:  # 至少5KB
                    print(f"✅ 文件大小正常，散点图应该包含完整内容")
                else:
                    print(f"⚠️  文件较小，可能内容不完整")
                
                return scatter_file
            else:
                print(f"❌ 散点图生成失败")
                return None
        else:
            print(f"❌ 未找到延停时长概率密度数据")
            return None
            
    except Exception as e:
        print(f"❌ 散点图测试异常: {str(e)}")
        import traceback
        traceback.print_exc()
        return None

def verify_scatter_chart_content(scatter_file):
    """验证散点图内容"""
    print(f"\n=== 验证散点图内容 ===\n")
    
    if not scatter_file or not os.path.exists(scatter_file):
        print(f"❌ 散点图文件不存在")
        return
    
    try:
        # 读取HTML文件内容
        with open(scatter_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        print(f"📄 文件大小: {len(content)} 字符")
        
        # 检查关键内容
        checks = [
            ('延停时长概率密度散点图', '图表标题'),
            ('停车时长频度分布', '数据系列名称'),
            ('小型车', '车辆类型1'),
            ('中型车', '车辆类型2'),
            ('大型车', '车辆类型3'),
            ('时长区间', '工具提示'),
            ('频数', '数据标签'),
            ('visualMap', '视觉映射'),
            ('scatter', '散点图类型')
        ]
        
        print(f"🔍 内容检查:")
        all_passed = True
        for keyword, description in checks:
            if keyword in content:
                print(f"   ✅ {description}: 找到 '{keyword}'")
            else:
                print(f"   ❌ {description}: 未找到 '{keyword}'")
                all_passed = False
        
        if all_passed:
            print(f"\n✅ 散点图内容验证通过")
        else:
            print(f"\n⚠️  散点图内容可能不完整")
            
    except Exception as e:
        print(f"❌ 内容验证异常: {str(e)}")

def main():
    """主函数"""
    print("🎯 延停时长概率密度散点图简单测试\n")
    
    # 测试散点图生成
    scatter_file = test_scatter_chart_directly()
    
    if scatter_file:
        # 验证散点图内容
        verify_scatter_chart_content(scatter_file)
        
        print(f"\n{'='*60}")
        print(f"🎉 测试完成！")
        print(f"📊 散点图文件: {os.path.basename(scatter_file)}")
        print(f"💡 可以在浏览器中打开查看效果")
    else:
        print(f"\n{'='*60}")
        print(f"❌ 测试失败，散点图未生成")

if __name__ == "__main__":
    main()
