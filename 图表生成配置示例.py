#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
停车数据图表生成器 - 配置示例
展示如何为不同的Excel文件配置参数
"""

# ==================== 配置示例1: 基础配置 ====================
def config_example_1():
    """基础配置示例 - 适用于标准停车分析报告"""
    
    # 📁 文件路径配置
    EXCEL_FILE_PATH = r"C:\data\parking_report_2024.xlsx"
    OUTPUT_DIR = None  # 使用Excel文件所在目录
    
    # 🎨 图表样式配置
    CHART_THEME = 'MACARONS'  # 马卡龙主题，色彩柔和
    BAR_COLORS = ['#5470c6', '#91cc75', '#fac858']  # 蓝、绿、黄
    LINE_COLORS = ['#ee6666', '#73c0de', '#3ba272']  # 红、蓝、绿
    LINE_WIDTH = 3  # 中等粗细
    CHART_WIDTH = '1200px'
    CHART_HEIGHT = '600px'
    
    # 📊 图表生成配置
    GENERATE_ALL_CHARTS = True
    GENERATE_DASHBOARD = True
    GENERATE_CUSTOM_CHARTS = False  # 不生成自定义图表
    AUTO_OPEN_DASHBOARD = True  # 自动打开仪表板
    
    return locals()

# ==================== 配置示例2: 高对比度配置 ====================
def config_example_2():
    """高对比度配置示例 - 适用于演示或打印"""
    
    # 📁 文件路径配置
    EXCEL_FILE_PATH = r"C:\reports\monthly_parking_analysis.xlsx"
    OUTPUT_DIR = r"C:\charts_output"  # 指定输出目录
    
    # 🎨 图表样式配置
    CHART_THEME = 'DARK'  # 深色主题
    BAR_COLORS = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7']
    LINE_COLORS = ['#FF6B6B', '#4ECDC4', '#45B7D1']
    LINE_WIDTH = 5  # 较粗的线条
    CHART_WIDTH = '1600px'  # 更大的图表
    CHART_HEIGHT = '800px'
    
    # 📊 图表生成配置
    GENERATE_ALL_CHARTS = True
    GENERATE_DASHBOARD = True
    GENERATE_CUSTOM_CHARTS = True
    AUTO_OPEN_DASHBOARD = False
    
    # 🔧 自定义图表配置
    CUSTOM_CHARTS_CONFIG = [
        {
            'sheet_name': '进出量时间分布',
            'chart_type': 'bar',
            'x_col': 0,
            'y_cols': [1, 2],
            'title': '高对比度进出量对比',
            'subtitle': '适用于演示的高对比度图表',
            'colors': ['#FF6B6B', '#4ECDC4']
        }
    ]
    
    return locals()

# ==================== 配置示例3: 简约风格配置 ====================
def config_example_3():
    """简约风格配置示例 - 适用于报告文档"""
    
    # 📁 文件路径配置
    EXCEL_FILE_PATH = r"C:\parking_data\weekly_report.xlsx"
    OUTPUT_DIR = None
    
    # 🎨 图表样式配置
    CHART_THEME = 'LIGHT'  # 浅色主题
    BAR_COLORS = ['#1f77b4', '#ff7f0e', '#2ca02c']  # 经典蓝橙绿
    LINE_COLORS = ['#d62728']  # 单一红色线条
    LINE_WIDTH = 2  # 细线条
    CHART_WIDTH = '1000px'  # 较小的图表
    CHART_HEIGHT = '500px'
    
    # 📊 图表生成配置
    GENERATE_ALL_CHARTS = True
    GENERATE_DASHBOARD = True
    GENERATE_CUSTOM_CHARTS = False
    AUTO_OPEN_DASHBOARD = False
    
    return locals()

# ==================== 配置示例4: 彩色主题配置 ====================
def config_example_4():
    """彩色主题配置示例 - 适用于多数据对比"""
    
    # 📁 文件路径配置
    EXCEL_FILE_PATH = r"C:\analysis\comprehensive_parking_data.xlsx"
    OUTPUT_DIR = r"C:\charts\colorful"
    
    # 🎨 图表样式配置
    CHART_THEME = 'WONDERLAND'  # 仙境主题
    BAR_COLORS = [
        '#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7',
        '#DDA0DD', '#98D8C8', '#F7DC6F', '#BB8FCE', '#85C1E9'
    ]
    LINE_COLORS = ['#E74C3C', '#3498DB', '#2ECC71', '#F39C12', '#9B59B6']
    LINE_WIDTH = 4
    CHART_WIDTH = '1500px'
    CHART_HEIGHT = '750px'
    
    # 📊 图表生成配置
    GENERATE_ALL_CHARTS = True
    GENERATE_DASHBOARD = True
    GENERATE_CUSTOM_CHARTS = True
    AUTO_OPEN_DASHBOARD = True
    
    # 🔧 自定义图表配置
    CUSTOM_CHARTS_CONFIG = [
        {
            'sheet_name': '进出量时间分布',
            'chart_type': 'bar',
            'x_col': 0,
            'y_cols': [1, 2, 3],
            'title': '多维度流量分析',
            'subtitle': '进场、出场、总流量三维对比',
            'colors': ['#FF6B6B', '#4ECDC4', '#45B7D1']
        },
        {
            'sheet_name': '停车时长分布',
            'chart_type': 'pie',
            'x_col': 0,
            'y_cols': [1],
            'title': '停车时长分布饼图',
            'subtitle': '各时长段占比分析',
            'colors': None  # 使用默认颜色
        }
    ]
    
    return locals()

# ==================== 使用说明 ====================
def usage_instructions():
    """使用说明"""
    print("""
🎯 配置使用说明:

1. 选择合适的配置示例
2. 复制对应的配置参数到 parking_chart_generator.py 的 if __name__ == "__main__": 块中
3. 修改 EXCEL_FILE_PATH 为您的实际文件路径
4. 根据需要调整其他参数
5. 运行程序

📋 配置参数说明:

📁 文件路径:
- EXCEL_FILE_PATH: Excel输入文件路径
- OUTPUT_DIR: 输出目录 (None=Excel文件同目录)

🎨 样式配置:
- CHART_THEME: 图表主题 (LIGHT, DARK, MACARONS等)
- BAR_COLORS: 柱状图颜色列表
- LINE_COLORS: 线条颜色列表  
- LINE_WIDTH: 线条粗细 (1-10)
- CHART_WIDTH/HEIGHT: 图表尺寸

📊 生成配置:
- GENERATE_ALL_CHARTS: 是否生成所有预定义图表
- GENERATE_DASHBOARD: 是否生成综合仪表板
- GENERATE_CUSTOM_CHARTS: 是否生成自定义图表
- AUTO_OPEN_DASHBOARD: 是否自动打开仪表板

🔧 自定义图表:
- CUSTOM_CHARTS_CONFIG: 自定义图表配置列表

💡 主题选项:
LIGHT, DARK, CHALK, ESSOS, INFOGRAPHIC, MACARONS, 
PURPLE_PASSION, ROMA, ROMANTIC, SHINE, VINTAGE, 
WALDEN, WESTEROS, WONDERLAND

🎨 颜色建议:
- 蓝色系: #1f77b4, #5470c6, #45B7D1
- 绿色系: #2ca02c, #91cc75, #4ECDC4  
- 红色系: #d62728, #FF6B6B, #E74C3C
- 橙色系: #ff7f0e, #fac858, #F39C12
- 紫色系: #9467bd, #DDA0DD, #9B59B6
""")

if __name__ == "__main__":
    usage_instructions()
    
    print("🔧 配置示例:")
    print("1. 基础配置 (标准)")
    print("2. 高对比度配置 (演示)")  
    print("3. 简约风格配置 (报告)")
    print("4. 彩色主题配置 (多数据)")
    
    choice = input("\n请选择查看配置示例 (1-4): ").strip()
    
    configs = {
        '1': config_example_1,
        '2': config_example_2, 
        '3': config_example_3,
        '4': config_example_4
    }
    
    if choice in configs:
        config = configs[choice]()
        print(f"\n📋 配置示例 {choice}:")
        print("=" * 50)
        for key, value in config.items():
            if not key.startswith('__'):
                print(f"{key} = {repr(value)}")
    else:
        print("❌ 无效选择")
