#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试出入口进场占比Timeline饼图生成功能
验证各出入口进场量占比是否正确显示
"""

import os

def test_gate_entry_pie():
    """测试出入口进场占比Timeline饼图生成"""
    print("🥧 测试出入口进场占比Timeline饼图生成")
    print("=" * 50)
    
    # Excel文件路径
    excel_file = r'C:\Users\<USER>\Desktop\停车分析\义乌火车站道闸_私家车_合并_20250617_083509_analysis_20250618_211554.xlsx'
    
    if not os.path.exists(excel_file):
        print(f"❌ Excel文件不存在: {excel_file}")
        return False
    
    try:
        # 导入图表生成器
        from parking_chart_generator import ParkingChartGenerator
        
        # 创建图表生成器
        print("📊 创建图表生成器...")
        chart_generator = ParkingChartGenerator(excel_file, None)
        
        # 检查是否有进出量时间分布(按道闸)工作表
        target_sheet = '进出量时间分布(按道闸)'
        if target_sheet not in chart_generator.excel_data:
            print(f"❌ 未找到'{target_sheet}'工作表")
            available_sheets = list(chart_generator.excel_data.keys())
            print(f"可用工作表: {available_sheets}")
            return False
        
        # 获取数据并分析结构
        data = chart_generator.excel_data[target_sheet]
        columns = list(data.columns)
        total_cols = len(columns)
        
        print(f"📋 数据结构分析:")
        print(f"   总列数: {total_cols}")
        print(f"   第1列: {columns[0]} (时间段)")
        
        if total_cols > 1:
            gate_cols = columns[1:]
            gate_cols_count = len(gate_cols)
            estimated_gates = gate_cols_count // 3
            
            print(f"   出入口相关列数: {gate_cols_count}")
            print(f"   预估出入口数量: {estimated_gates}")
            
            # 显示每个出入口的进场列
            for i in range(min(estimated_gates, 3)):
                entry_col_idx = 1 + i * 3
                if entry_col_idx < total_cols:
                    print(f"   出入口{i+1}进场列: {columns[entry_col_idx]}")
        
        # 尝试生成进场占比Timeline饼图
        print(f"\n🥧 尝试生成出入口进场占比Timeline饼图...")
        result = chart_generator.generate_gate_entry_proportion_timeline()
        
        if result:
            print(f"✅ 成功生成: {os.path.basename(result)}")
            
            # 检查文件是否存在
            if os.path.exists(result):
                file_size = os.path.getsize(result) / 1024
                print(f"📄 文件大小: {file_size:.1f}KB")
                print(f"📁 文件路径: {result}")
                
                # 读取HTML内容检查饼图Timeline特征
                with open(result, 'r', encoding='utf-8') as f:
                    html_content = f.read()
                
                # 检查饼图Timeline相关特征
                pie_checks = {
                    'Timeline组件': 'Timeline' in html_content,
                    '饼图组件': 'Pie(' in html_content,
                    '玫瑰图样式': 'rosetype:"radius"' in html_content or 'rosetype":"radius"' in html_content,
                    '半径设置': '30%' in html_content and '55%' in html_content,
                    '多个时间段': html_content.count('add(') > 1,
                    '占比显示': '%' in html_content,
                    '专业颜色': '#2E86AB' in html_content and '#A23B72' in html_content,
                }
                
                print(f"\n📊 饼图Timeline验证:")
                all_checks_passed = True
                for check_name, is_passed in pie_checks.items():
                    status = "✅" if is_passed else "❌"
                    print(f"   {status} {check_name}: {'通过' if is_passed else '未通过'}")
                    if not is_passed:
                        all_checks_passed = False
                
                # 检查数据系列信息
                pie_count = html_content.count('Pie(')
                time_segments = html_content.count('add(')
                
                print(f"\n🔍 饼图信息:")
                print(f"   检测到饼图数量: {pie_count}")
                print(f"   时间段数量: {time_segments}")
                print(f"   每个时间段显示各出入口进场量占比")
                
                if all_checks_passed:
                    print("\n✅ 进场占比Timeline饼图生成成功！")
                    print("   - 玫瑰图样式，美观直观")
                    print("   - 显示各出入口进场量占比")
                    print("   - 支持时间段动态切换")
                    print("   - 使用专业演讲风格颜色")
                    print("   - 文件名: 出入口占比_进.html")
                    return True
                else:
                    print("\n⚠️ 饼图Timeline生成但可能不是预期格式")
                    return False
            else:
                print(f"❌ 文件未生成: {result}")
                return False
        else:
            print("❌ 未能生成进场占比Timeline饼图")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def demo_pie_timeline_features():
    """演示饼图Timeline功能特点"""
    print("\n🥧 进场占比Timeline饼图特点")
    print("=" * 50)
    
    print("🎯 功能特色:")
    print("   ✅ 玫瑰图样式: 美观的径向饼图")
    print("   ✅ 占比显示: 显示各出入口进场量占比")
    print("   ✅ Timeline动态: 可切换不同时间段查看")
    print("   ✅ 专业配色: 使用演讲风格颜色方案")
    
    print("\n📊 数据展示:")
    print("   - 数据来源: 各出入口的进场列数据")
    print("   - 计算方式: 每个出入口进场量 / 总进场量")
    print("   - 显示格式: 出入口名称 + 数量 + 占比")
    print("   - 图例位置: 左侧垂直排列")
    
    print("\n🎨 视觉设计:")
    print("   - 图表类型: 玫瑰图 (rosetype='radius')")
    print("   - 半径范围: 内径30%，外径55%")
    print("   - 标签位置: 外部显示，包含数量和占比")
    print("   - 边框效果: 白色边框，增强立体感")
    
    print("\n🎮 交互功能:")
    print("   - 时间轴滑块: 选择不同时间段")
    print("   - 自动播放: 3秒间隔，观察占比变化")
    print("   - Tooltip: 鼠标悬停显示详细信息")
    print("   - 图例控制: 点击图例显示/隐藏扇区")
    
    print("\n💡 应用场景:")
    print("   - 占比分析: 分析各出入口进场量占比")
    print("   - 流量分布: 了解进场流量的分布情况")
    print("   - 趋势观察: 观察占比随时间的变化")
    print("   - 决策支持: 为出入口管理提供数据支持")
    
    print("\n📋 与其他图表的关系:")
    print("   - 总量Timeline: 显示绝对数量")
    print("   - 方向Timeline: 显示进出对比")
    print("   - 占比饼图: 显示相对占比 ← 新增")
    print("   - 三者互补，提供全面分析")

def main():
    """主函数"""
    # 演示功能特点
    demo_pie_timeline_features()
    
    # 测试功能
    success = test_gate_entry_pie()
    
    if success:
        print("\n🎉 测试成功！进场占比Timeline饼图已生成！")
        print("📁 文件名: 出入口占比_进.html")
        print("💡 现在可以查看各出入口进场量占比")
        print("🔍 建议在浏览器中验证效果:")
        print("   1. 观察玫瑰图的美观效果")
        print("   2. 查看各出入口的占比数据")
        print("   3. 使用时间轴切换不同时间段")
        print("   4. 观察占比随时间的变化趋势")
        
        print("\n📊 现在您拥有三个出入口图表:")
        print("   1. 出入口进出量_总量.html - 总量对比")
        print("   2. 出入口进出量_方向.html - 进出对比")
        print("   3. 出入口占比_进.html - 进场占比 ← 新增")
    else:
        print("\n⚠️ 测试失败")
        print("💡 可能的原因:")
        print("   1. Excel文件中没有'进出量时间分布(按道闸)'工作表")
        print("   2. 数据列结构不符合预期")
        print("   3. 进场数据为空或格式不正确")
    
    print("\n" + "="*50)
    input("按回车键退出...")

if __name__ == "__main__":
    main()
