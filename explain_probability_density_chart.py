#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
详细解释概率密度图的数据处理和含义
"""

import os
import pandas as pd
import numpy as np

def analyze_data_processing():
    """分析数据处理过程"""
    print("🔍 分析概率密度图的数据处理过程\n")
    
    # 查找数据文件
    excel_files = [f for f in os.listdir('.') if f.endswith('.xlsx') and not f.startswith('~')]
    
    best_file = None
    best_data_count = 0
    
    for excel_file in excel_files:
        try:
            with pd.ExcelFile(excel_file) as xls:
                if '数据_总量' in xls.sheet_names:
                    df = pd.read_excel(excel_file, sheet_name='数据_总量')
                    if 'duration' in df.columns:
                        valid_duration = df['duration'].dropna()
                        valid_duration = valid_duration[valid_duration >= 0]
                        
                        if len(valid_duration) > best_data_count:
                            best_data_count = len(valid_duration)
                            best_file = excel_file
        except:
            continue
    
    if not best_file:
        print("❌ 未找到数据文件")
        return
    
    print(f"📊 使用数据文件: {best_file}")
    
    # 读取数据
    df = pd.read_excel(best_file, sheet_name='数据_总量')
    duration_data = df['duration'].dropna()
    duration_data = duration_data[duration_data >= 0]
    
    print(f"\n📋 原始数据分析:")
    print(f"   - 总记录数: {len(df)}")
    print(f"   - 有效duration数据: {len(duration_data)} 个")
    print(f"   - 时长范围: {duration_data.min():.3f} - {duration_data.max():.3f} 小时")
    
    # 显示前20个原始数据点
    print(f"\n📊 前20个原始duration值:")
    for i, value in enumerate(duration_data.head(20), 1):
        print(f"   {i:2d}. {value:.3f} 小时")
    
    # 计算直方图（模拟图表生成过程）
    n_bins = min(50, max(10, int(np.sqrt(len(duration_data)))))
    hist_counts, bin_edges = np.histogram(duration_data, bins=n_bins, density=True)
    bin_centers = (bin_edges[:-1] + bin_edges[1:]) / 2
    bin_width = bin_edges[1] - bin_edges[0]
    
    print(f"\n🔄 数据处理过程:")
    print(f"   1. 原始数据点: {len(duration_data)} 个")
    print(f"   2. 分组区间数: {n_bins} 个")
    print(f"   3. 区间宽度: {bin_width:.3f} 小时")
    print(f"   4. 密度积分验证: {sum(hist_counts) * bin_width:.6f} (应接近1.0)")
    
    # 显示每个区间的详细信息
    print(f"\n📊 各区间的详细信息:")
    print(f"{'区间':<20} {'中心点':<10} {'密度值':<12} {'原始数据点数':<12}")
    print(f"{'-'*60}")
    
    valid_bins = 0
    for i, (center, density) in enumerate(zip(bin_centers, hist_counts)):
        # 计算该区间内的原始数据点数
        bin_start = bin_edges[i]
        bin_end = bin_edges[i+1]
        points_in_bin = len(duration_data[(duration_data >= bin_start) & (duration_data < bin_end)])
        
        if density > 0:
            valid_bins += 1
            range_desc = f"{bin_start:.2f}-{bin_end:.2f}"
            print(f"{range_desc:<20} {center:<10.3f} {density:<12.6f} {points_in_bin:<12}")
    
    print(f"\n📈 图表显示的点数:")
    print(f"   - 有密度的区间: {valid_bins} 个")
    print(f"   - 图表上的点数: {valid_bins} 个")
    
    return duration_data, hist_counts, bin_edges, bin_centers

def explain_chart_meaning():
    """解释图表含义"""
    print(f"\n📚 概率密度图详细解释\n")
    
    print(f"🎯 为什么万个数据点变成几十个点？")
    print(f"   这是概率密度图的核心特征！")
    print(f"   ")
    print(f"   1️⃣ 原始数据 → 分组统计:")
    print(f"      - 原始数据: 每个停车记录的具体时长值")
    print(f"      - 例如: 1.5小时, 2.3小时, 4.7小时, 1.8小时...")
    print(f"      - 这些是离散的个别数据点")
    print(f"   ")
    print(f"   2️⃣ 分组处理:")
    print(f"      - 将时长范围分成若干区间（如14个区间）")
    print(f"      - 例如: [0-12小时], [12-24小时], [24-36小时]...")
    print(f"      - 统计每个区间内有多少个原始数据点")
    print(f"   ")
    print(f"   3️⃣ 密度计算:")
    print(f"      - 密度 = 区间内数据点数 / (总数据点数 × 区间宽度)")
    print(f"      - 这样确保整个分布的面积等于1")
    print(f"   ")
    print(f"   4️⃣ 图表显示:")
    print(f"      - 每个区间用一个点表示")
    print(f"      - 点的位置: X=区间中心, Y=概率密度值")
    print(f"      - 所以万个原始数据 → 十几个密度点")
    
    print(f"\n📊 横纵坐标含义:")
    print(f"   ")
    print(f"   🔸 X轴（横坐标）- 停车时长（小时）:")
    print(f"      - 表示停车时长的具体数值")
    print(f"      - 例如: 0, 12, 24, 36, 48... 小时")
    print(f"      - 每个点的X坐标是该区间的中心点")
    print(f"   ")
    print(f"   🔸 Y轴（纵坐标）- 概率密度值:")
    print(f"      - 不是频率，不是概率，而是概率密度")
    print(f"      - 单位: 1/小时 (概率除以时长单位)")
    print(f"      - 密度值越高 = 该时长范围越常见")
    print(f"      - 密度值 × 区间宽度 = 该区间的概率")
    
    print(f"\n🎯 数据点含义:")
    print(f"   ")
    print(f"   每个散点代表:")
    print(f"   - 一个时长区间的概率密度")
    print(f"   - X坐标: 区间中心时长")
    print(f"   - Y坐标: 该区间的概率密度值")
    print(f"   - 颜色: 车辆类型（如果有分类）")
    print(f"   ")
    print(f"   例如某个点 (24, 0.05):")
    print(f"   - X=24: 代表24小时附近的区间")
    print(f"   - Y=0.05: 该区间的概率密度为0.05")
    print(f"   - 含义: 停车24小时左右是比较常见的")

def demonstrate_with_example():
    """用具体例子演示"""
    print(f"\n💡 具体例子演示\n")
    
    # 分析实际数据
    result = analyze_data_processing()
    if not result:
        return
    
    duration_data, hist_counts, bin_edges, bin_centers = result
    
    # 找到密度最高的区间
    max_density_idx = np.argmax(hist_counts)
    max_density = hist_counts[max_density_idx]
    max_center = bin_centers[max_density_idx]
    max_bin_start = bin_edges[max_density_idx]
    max_bin_end = bin_edges[max_density_idx + 1]
    
    # 计算该区间内的原始数据点
    points_in_max_bin = duration_data[(duration_data >= max_bin_start) & (duration_data < max_bin_end)]
    
    print(f"🔍 以密度最高的区间为例:")
    print(f"   ")
    print(f"   📍 区间范围: {max_bin_start:.2f} - {max_bin_end:.2f} 小时")
    print(f"   📍 区间中心: {max_center:.2f} 小时")
    print(f"   📍 概率密度: {max_density:.6f}")
    print(f"   📍 原始数据点数: {len(points_in_max_bin)} 个")
    print(f"   ")
    print(f"   🎯 这意味着:")
    print(f"      - 图表上显示为点 ({max_center:.2f}, {max_density:.6f})")
    print(f"      - 这一个点代表了 {len(points_in_max_bin)} 个原始停车记录")
    print(f"      - 这些记录的停车时长都在 {max_bin_start:.2f}-{max_bin_end:.2f} 小时范围内")
    
    if len(points_in_max_bin) > 0:
        print(f"   ")
        print(f"   📊 该区间内的原始数据示例:")
        sample_points = points_in_max_bin.head(10)
        for i, value in enumerate(sample_points, 1):
            print(f"      {i:2d}. {value:.3f} 小时")
        if len(points_in_max_bin) > 10:
            print(f"      ... 还有 {len(points_in_max_bin) - 10} 个数据点")

def main():
    """主函数"""
    print("🎯 概率密度图详细解释\n")
    
    # 1. 解释图表含义
    explain_chart_meaning()
    
    # 2. 用具体例子演示
    demonstrate_with_example()
    
    print(f"\n{'='*60}")
    print(f"📝 总结:")
    print(f"   ")
    print(f"   ✅ 万个数据点 → 十几个图表点是正常的")
    print(f"   ✅ 这是概率密度图的标准做法")
    print(f"   ✅ 每个图表点代表一个时长区间的密度")
    print(f"   ✅ 原始数据被分组统计，不是直接显示")
    print(f"   ")
    print(f"   🎯 概率密度图的价值:")
    print(f"      - 显示数据的整体分布形状")
    print(f"      - 识别数据集中的区域")
    print(f"      - 便于比较不同组别的分布")
    print(f"      - 符合概率论的数学定义")
    print(f"{'='*60}")

if __name__ == "__main__":
    main()
