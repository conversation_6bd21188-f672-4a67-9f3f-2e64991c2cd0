"""
停车数据分析器模块，负责数据分析和统计
"""

import sys
import os
# 确保当前文件目录在模块搜索路径中
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
import pandas as pd
import numpy as np
import traceback
from datetime import datetime, timedelta
from openpyxl.utils import get_column_letter
from parking_data_base import ParkingDataBase

class ParkingAnalyzer(ParkingDataBase):
    """停车数据分析器，继承自ParkingDataBase"""
    
    def __init__(self, data, params, logger=None):
        """
        初始化分析器
        
        Args:
            data: DataFrame, 原始数据
            params: dict, 分析参数
            logger: Logger对象, 用于日志记录
        """
        super().__init__(data, params, logger)
        self.analysis_results = {}
    
    def analyze(self):
        """
        执行数据分析
        
        Returns:
            dict: 分析结果
        """
        try:
            # 使用基类处理数据
            processed_data = self.process()
            
            # 执行各项分析
            self.analysis_results = {
                'basic_stats': self._analyze_basic_stats(processed_data),
                'time_distribution': self._analyze_time_distribution(processed_data),
                'vehicle_types': self._analyze_vehicle_types(processed_data),
                'gate_usage': self._analyze_gate_usage(processed_data)
            }
            
            return self.analysis_results
            
        except Exception as e:
            self._log_error(f"数据分析失败: {str(e)}")
            raise
    
    def _analyze_basic_stats(self, data):
        """
        分析基础统计信息
        
        Args:
            data: DataFrame, 待分析的数据
            
        Returns:
            dict: 基础统计信息
        """
        if data is None or data.empty:
            return {
                'total_records': 0,
                'unique_vehicles': 0,
                'avg_duration': 0,
                'max_duration': 0,
                'min_duration': 0,
                'median_duration': 0,
                'std_duration': 0
            }
        
        try:
            # 确保必要的字段存在
            required_fields = ['vid', 'duration']
            for field in required_fields:
                if field not in data.columns:
                    raise ValueError(f"缺少必要字段: {field}")
            
            # 处理duration字段中的异常值
            duration_stats = data['duration'].agg(['mean', 'max', 'min', 'median', 'std'])
            duration_stats = duration_stats.round(2)  # 保留两位小数
            
            # 移除异常的停车时长（例如负值或超过30天的记录）
            valid_duration = data[
                (data['duration'] >= 0) & 
                (data['duration'] <= 30 * 24)  # 30天的小时数
            ]['duration']
            
            return {
                'total_records': len(data),
                'unique_vehicles': data['vid'].nunique(),
                'avg_duration': duration_stats['mean'],
                'max_duration': duration_stats['max'],
                'min_duration': duration_stats['min'],
                'median_duration': duration_stats['median'],
                'std_duration': duration_stats['std'],
                'valid_records': len(valid_duration),
                'invalid_duration_records': len(data) - len(valid_duration)
            }
            
        except Exception as e:
            self._log_error(f"基础统计分析失败: {str(e)}")
            return {
                'total_records': len(data) if data is not None else 0,
                'error': str(e)
            }
    
    def _analyze_time_distribution(self, data):
        """分析时间分布"""
        try:
            # 确保时间字段存在且为datetime类型
            if 'entry_time' not in data.columns or 'exit_time' not in data.columns:
                if self.logger:
                    self.logger.error("缺少进出时间字段")
                print("错误: 缺少进出时间字段")
                return {
                    'entry_by_hour': {},
                    'exit_by_hour': {},
                    'daily_flow': {},
                    'entry_by_gate': {},
                    'exit_by_gate': {},
                    'entry_by_hour_gate': {},
                    'exit_by_hour_gate': {}
                }
                
            # 转换时间字段
            data['entry_time'] = pd.to_datetime(data['entry_time'], errors='coerce')
            data['exit_time'] = pd.to_datetime(data['exit_time'], errors='coerce')
            
            # 移除无效时间记录
            valid_data = data[~data['entry_time'].isna() & ~data['exit_time'].isna()]

            
            # 检查有效数据是否为空
            if valid_data.empty:
                print("警告: 没有有效的时间记录数据")
                if self.logger:
                    self.logger.warning("没有有效的时间记录数据")
                return {
                    'entry_by_hour': {},
                    'exit_by_hour': {},
                    'daily_flow': {},
                    'entry_by_gate': {},
                    'exit_by_gate': {},
                    'entry_by_hour_gate': {},
                    'exit_by_hour_gate': {}
                }
            
            # 按小时统计进场车辆数
            entry_by_hour = valid_data['entry_time'].dt.hour.value_counts().sort_index()
            
            # 按小时统计出场车辆数
            exit_by_hour = valid_data['exit_time'].dt.hour.value_counts().sort_index()
            
            # 按日期统计每日车流量
            daily_flow = valid_data.groupby(valid_data['entry_time'].dt.date).size()
            
            # 按道闸统计进场车辆数
            entry_by_gate = {}
            exit_by_gate = {}
            entry_by_hour_gate = {}
            exit_by_hour_gate = {}
            
            # 确保道闸字段存在
            if 'entry_gate' in valid_data.columns and 'exit_gate' in valid_data.columns:
                # 按道闸统计进场车辆数
                entry_by_gate = valid_data['entry_gate'].value_counts().to_dict()
                
                # 按道闸统计出场车辆数
                exit_by_gate = valid_data['exit_gate'].value_counts().to_dict()
                
                # 按时间段和道闸统计进场车辆数
                # 生成时间段列表用于统计
                from parking_time_filter import TimeFilter
                time_filter = TimeFilter(valid_data, self.params)
                time_periods = time_filter.split_time_period(
                    start_time="00:00",
                    end_time="23:59",
                    slip_time=self.params.get('time_slip', 60),
                    interval_minutes=self.params.get('time_interval', 60)
                )

                for gate in valid_data['entry_gate'].unique():
                    gate_data = valid_data[valid_data['entry_gate'] == gate]
                    if not gate_data.empty:
                        # 按时间段统计，而不是按小时
                        period_counts = {}
                        for period in time_periods:
                            try:
                                start_time_str, end_time_str = period.split('-')
                                start_time = pd.to_datetime(start_time_str, format='%H:%M').time()
                                end_time = pd.to_datetime(end_time_str, format='%H:%M').time()

                                # 筛选该时间段的数据
                                if start_time < end_time:
                                    mask = (gate_data['entry_time'].dt.time >= start_time) & \
                                           (gate_data['entry_time'].dt.time < end_time)
                                else:  # 跨天情况
                                    mask = (gate_data['entry_time'].dt.time >= start_time) | \
                                           (gate_data['entry_time'].dt.time < end_time)

                                count = mask.sum()
                                if count > 0:
                                    # 使用完整的时间段字符串作为键，确保每个时间段都有独立的统计
                                    period_counts[period] = count

                            except (ValueError, IndexError):
                                continue

                        entry_by_hour_gate[gate] = period_counts

                # 按时间段和道闸统计出场车辆数
                for gate in valid_data['exit_gate'].unique():
                    gate_data = valid_data[valid_data['exit_gate'] == gate]
                    if not gate_data.empty:
                        # 按时间段统计，而不是按小时
                        period_counts = {}
                        for period in time_periods:
                            try:
                                start_time_str, end_time_str = period.split('-')
                                start_time = pd.to_datetime(start_time_str, format='%H:%M').time()
                                end_time = pd.to_datetime(end_time_str, format='%H:%M').time()

                                # 筛选该时间段的数据
                                if start_time < end_time:
                                    mask = (gate_data['exit_time'].dt.time >= start_time) & \
                                           (gate_data['exit_time'].dt.time < end_time)
                                else:  # 跨天情况
                                    mask = (gate_data['exit_time'].dt.time >= start_time) | \
                                           (gate_data['exit_time'].dt.time < end_time)

                                count = mask.sum()
                                if count > 0:
                                    # 使用完整的时间段字符串作为键，确保每个时间段都有独立的统计
                                    period_counts[period] = count

                            except (ValueError, IndexError):
                                continue

                        exit_by_hour_gate[gate] = period_counts
            else:
                print("警告: 缺少道闸字段，无法按道闸统计")
                if self.logger:
                    self.logger.warning("缺少道闸字段，无法按道闸统计")
            
            return {
                'entry_by_hour': entry_by_hour.to_dict(),
                'exit_by_hour': exit_by_hour.to_dict(),
                'daily_flow': daily_flow.to_dict(),
                'entry_by_gate': entry_by_gate,
                'exit_by_gate': exit_by_gate,
                'entry_by_hour_gate': entry_by_hour_gate,
                'exit_by_hour_gate': exit_by_hour_gate
            }
            
        except Exception as e:
            error_msg = f"时间分布分析失败: {str(e)}"
            print(f"错误: {error_msg}")
            print(f"错误详情: {traceback.format_exc()}")
            if self.logger:
                self.logger.error(error_msg)
                self.logger.error(traceback.format_exc())
            return {
                'entry_by_hour': {},
                'exit_by_hour': {},
                'daily_flow': {}
            }
    
    def _analyze_vehicle_types(self, data):
        """分析车辆类型分布"""
        type_counts = data['vtype'].value_counts()
        type_durations = data.groupby('vtype')['duration'].agg(['mean', 'median', 'std']).round(2)
        
        return {
            'type_distribution': type_counts.to_dict(),
            'type_durations': type_durations.to_dict('index')
        }
    
    def _analyze_gate_usage(self, data):
        """分析道闸使用情况"""
        try:
            # 确保字段存在
            if 'entry_gate' not in data.columns or 'exit_gate' not in data.columns:
                print("警告: 缺少进出道闸字段")
                if self.logger:
                    self.logger.warning("缺少进出道闸字段")
                return {
                    'entry_gates': {},
                    'exit_gates': {},
                    'common_pairs': {}
                }
            
            # 统计道闸使用情况
            entry_gate_usage = data['entry_gate'].value_counts()
            exit_gate_usage = data['exit_gate'].value_counts()
            
            # 分析最常用的进出组合
            gate_pairs = data.groupby(['entry_gate', 'exit_gate']).size().sort_values(ascending=False)
            
            # 确保有足够的记录
            if len(entry_gate_usage) == 0 or len(exit_gate_usage) == 0:
                if self.logger:
                    self.logger.warning("道闸使用数据为空")
            
            return {
                'entry_gates': entry_gate_usage.to_dict(),
                'exit_gates': exit_gate_usage.to_dict(),
                'common_pairs': gate_pairs.head(5).to_dict()
            }
            
        except Exception as e:
            error_msg = f"道闸使用分析失败: {str(e)}"
            print(f"错误: {error_msg}")
            print(f"错误详情: {traceback.format_exc()}")
            if self.logger:
                self.logger.error(error_msg)
                self.logger.error(traceback.format_exc())
            return {
                'entry_gates': {},
                'exit_gates': {},
                'common_pairs': {}
            }
    
    def get_peak_hours(self, threshold=0.8):
        """
        获取高峰时段（仅按全日最大量标记）

        Args:
            threshold: float, 判定为高峰时段的阈值（相对于最大值的比例）

        Returns:
            dict: 进出场高峰时段
        """
        if not self.analysis_results or 'time_distribution' not in self.analysis_results:
            raise ValueError("请先执行analyze()方法")

        time_dist = self.analysis_results['time_distribution']

        def find_peak_hours(hourly_data):
            if not hourly_data:
                return []
            max_value = max(hourly_data.values())
            # 只返回流量等于最大值的时段
            return [hour for hour, count in hourly_data.items() if count == max_value and count > 0]

        return {
            'entry_peak_hours': find_peak_hours(time_dist['entry_by_hour']),
            'exit_peak_hours': find_peak_hours(time_dist['exit_by_hour'])
        }
    
    def get_utilization_rate(self, capacity):
        """
        计算停车场利用率
        
        Args:
            capacity: int, 停车场容量
            
        Returns:
            dict: 包含利用率信息的字典
        """
        if self.processed_data is None or self.processed_data.empty:
            raise ValueError("请先执行analyze()方法")
        
        try:
            # 确保时间字段存在且为datetime类型
            for field in ['entry_time', 'exit_time']:
                if field not in self.processed_data.columns:
                    raise ValueError(f"缺少必要的时间字段: {field}")
                if not pd.api.types.is_datetime64_any_dtype(self.processed_data[field]):
                    self.processed_data[field] = pd.to_datetime(self.processed_data[field])
            
            # 防止除以零
            if capacity <= 0:
                raise ValueError("停车场容量必须大于0")
            
            # 计算每个时间点的在场车辆数
            timeline = pd.date_range(
                start=self.processed_data['entry_time'].min(),
                end=self.processed_data['exit_time'].max(),
                freq='H'
            )
            
            occupancy = []
            for time in timeline:
                count = len(self.processed_data[
                    (self.processed_data['entry_time'] <= time) & 
                    (self.processed_data['exit_time'] > time)
                ])
                occupancy.append(count)
            
            occupancy = np.array(occupancy)
            
            # 计算利用率统计
            return {
                'average_utilization': round(occupancy.mean() / capacity * 100, 2),
                'max_utilization': round(occupancy.max() / capacity * 100, 2),
                'min_utilization': round(occupancy.min() / capacity * 100, 2),
                'over_capacity_hours': sum(occupancy > capacity),
                'total_hours': len(timeline),
                'capacity': capacity
            }
            
        except Exception as e:
            self._log_error(f"计算停车场利用率失败: {str(e)}")
            return {
                'average_utilization': 0,
                'max_utilization': 0,
                'min_utilization': 0,
                'over_capacity_hours': 0,
                'total_hours': 0,
                'capacity': capacity,
                'error': str(e)
            }
    
    def _log_error(self, message):
        """记录错误信息"""
        if self.logger:
            self.logger.error(message)

class TimePeriodAnalyzer(ParkingAnalyzer):
    """时间周期分析器，继承自ParkingAnalyzer"""
    
    def __init__(self, data, total_records, mode='mode1', period_info=None, params=None):
        """
        初始化时间周期分析器
        
        Args:
            data: DataFrame, 处理后的数据
            total_records: int, 原始记录总数
            mode: str, 处理模式
            period_info: dict, 时间周期信息
            params: dict, 分析参数
        """
        # 构建完整的参数字典
        full_params = {
            'mode': mode,
            # mode1的默认字段映射
            '车辆唯一标识字段': '车牌号码',
            '车辆类型字段': '车辆类型',
            '时间记录字段': '通行时间',
            '进出类型字段': '方向',
            '道闸编号字段': '出入口',
            '进出标识值': ['入场', '出场']  # 改为列表以保持一致性
        }
        
        # 如果提供了自定义参数，更新默认参数
        if params:
            full_params.update(params)
            # 确保进出标识值是列表格式
            if '进出标识值' in params and isinstance(params['进出标识值'], tuple):
                full_params['进出标识值'] = list(params['进出标识值'])
        
        # 调用父类初始化
        super().__init__(data, full_params)

        # 设置其他属性
        self.total_records = total_records
        self.mode = mode  # 确保mode属性被设置
        self.period_info = period_info or {}
        self.time_periods = {}

        # 确保processed_data被正确设置
        if self.processed_data is None and data is not None:
            self.processed_data = data.copy()

        # 记录初始化信息到日志
        if self.logger:
            self.logger.info(f"初始化时间周期分析器: 总记录数={total_records}, 处理模式={mode}")
        

    
    def set_time_periods(self, periods):
        """
        设置时间周期
        
        Args:
            periods: list/dict, 时间周期定义
                如果是list，格式如：['00:00-01:00', '01:00-02:00', ...]
                如果是dict，格式可以是：
                    1. {'morning': ('08:00', '12:00'), ...} - 命名时间段
                    2. {'unit': 'year|month|day', 'days': int} - 时间单位信息
                
        Raises:
            ValueError: 当时间周期格式无效时抛出
        """
        try:
            if isinstance(periods, dict):
                # 检查是否是时间单位信息字典
                if 'unit' in periods and 'days' in periods:
                    # 生成默认的时间段列表，使用配置中的时间参数
                    from parking_time_filter import TimeFilter
                    time_filter = TimeFilter(self.processed_data, self.params)
                    formatted_periods = time_filter.split_time_period(
                        start_time="00:00",
                        end_time="23:59",
                        slip_time=self.params.get('time_slip', 60),
                        interval_minutes=self.params.get('time_interval', 60)
                    )
                    self.time_periods = formatted_periods
                else:
                    # 将命名时间段字典格式转换为标准的时间段字符串列表
                    formatted_periods = []
                    for name, time_range in periods.items():
                        try:
                            if isinstance(time_range, (tuple, list)) and len(time_range) == 2:
                                start, end = time_range
                                # 验证时间格式
                                datetime.strptime(start, '%H:%M')
                                datetime.strptime(end, '%H:%M')
                                formatted_periods.append(f"{start}-{end}")
                            else:
                                self._log_error(f"时间段 {name} 的格式无效，应为(start, end)元组")
                        except ValueError as e:
                            self._log_error(f"时间段 {name} 的时间格式无效: {str(e)}")
                            continue
                    self.time_periods = formatted_periods
            elif isinstance(periods, (list, tuple)):
                # 验证列表中的每个时间段格式
                formatted_periods = []
                for period in periods:
                    if not isinstance(period, str):
                        self._log_error(f"无效的时间段格式: {period}，应为字符串")
                        continue
                    if '-' not in period:
                        self._log_error(f"无效的时间段格式: {period}，缺少分隔符'-'")
                        continue
                    try:
                        start, end = period.split('-')
                        # 验证时间格式
                        datetime.strptime(start, '%H:%M')
                        datetime.strptime(end, '%H:%M')
                        formatted_periods.append(period)
                    except ValueError as e:
                        self._log_error(f"时间段 {period} 的格式无效: {str(e)}")
                        continue
                self.time_periods = formatted_periods
            else:
                raise ValueError("不支持的时间周期格式，必须是字典或列表")
            
            # 如果没有有效的时间段，抛出异常
            if not self.time_periods:
                raise ValueError("没有有效的时间段")
                
            
        except Exception as e:
            self._log_error(f"设置时间周期失败: {str(e)}\n{traceback.format_exc()}")
            raise ValueError(f"设置时间周期失败: {str(e)}")
    
    def analyze(self, focus_date=None, focus_month=None):
        """
        执行数据分析
        
        Args:
            focus_date: str, 聚焦分析的日期，格式：YYYY-MM-DD
            focus_month: str, 聚焦分析的月份，格式：YYYY-MM
            
        Returns:
            dict: 分析结果
        """
        try:
            # 使用已有的处理数据，如果没有则重新处理
            if self.processed_data is not None and len(self.processed_data) > 0:
                processed_data = self.processed_data
            else:
                processed_data = self.process()

            # 执行基础分析
            base_results = {
                'basic_stats': self._analyze_basic_stats(processed_data),
                'time_distribution': self._analyze_time_distribution(processed_data),
                'vehicle_types': self._analyze_vehicle_types(processed_data),
                'gate_usage': self._analyze_gate_usage(processed_data)
            }
            
            # 设置默认的时间周期（如果没有提供）
            if not self.time_periods:
                try:
                    if self.period_info:
                        self.set_time_periods(self.period_info)
                    else:
                        # 否则生成默认的时间周期，使用配置中的时间参数
                        from parking_time_filter import TimeFilter
                        time_filter = TimeFilter(self.processed_data, self.params)
                        default_periods = time_filter.split_time_period(
                            start_time="00:00",
                            end_time="23:59",
                            slip_time=self.params.get('time_slip', 60),
                            interval_minutes=self.params.get('time_interval', 60)
                        )
                        self.set_time_periods(default_periods)
                except Exception as e:
                    self._log_error(f"设置默认时间周期失败: {str(e)}")
                    # 使用最简单的默认时间周期
                    simple_periods = [
                        "00:00-06:00", "06:00-12:00",
                        "12:00-18:00", "18:00-23:59"
                    ]
                    self.time_periods = simple_periods

            
            # 根据时间周期进行分析
            period_results = self._analyze_by_period(focus_date, focus_month)
            gate_pairs_results = self._analyze_gate_pairs_by_period(focus_date, focus_month)
            
            # 检查时间周期分析结果是否有效
            if not period_results:
                self._log_error("时间周期分析结果为空")
                base_results.update({
                    'period_analysis': {},
                    'period_comparison': {
                        'busiest_period': None,
                        'quietest_period': None,
                        'volume_distribution': {},
                        'period_details': {}
                    },
                    'gate_pairs_analysis': {}
                })
            else:
                base_results.update({
                    'period_analysis': period_results,
                    'period_comparison': self.get_period_comparison(),
                    'gate_pairs_analysis': gate_pairs_results
                })

            # 添加overview结果（如果基础分析中没有）
            if 'overview' not in base_results:
                base_results['overview'] = self._generate_overview_data(focus_date, focus_month)

            return base_results

        except Exception as e:
            self._log_error(f"时间周期分析失败: {str(e)}\n{traceback.format_exc()}")
            raise

    def _generate_overview_data(self, focus_date=None, focus_month=None):
        """
        生成概览数据

        Args:
            focus_date: str, 聚焦分析的日期
            focus_month: str, 聚焦分析的月份

        Returns:
            dict: 概览数据
        """
        try:
            processed_data = self.processed_data.copy()

            # 确保时间字段为datetime类型
            if 'entry_time' in processed_data.columns:
                processed_data['entry_time'] = pd.to_datetime(processed_data['entry_time'], errors='coerce')
            if 'exit_time' in processed_data.columns:
                processed_data['exit_time'] = pd.to_datetime(processed_data['exit_time'], errors='coerce')

            # 移除无效时间记录
            processed_data = processed_data.dropna(subset=['entry_time', 'exit_time'])

            # 如果指定了聚焦日期或月份，进行数据过滤
            if focus_date:
                try:
                    focus_date_dt = pd.to_datetime(focus_date).date()
                    processed_data = processed_data[processed_data['entry_time'].dt.date == focus_date_dt]
                except Exception as e:
                    if self.logger:
                        self.logger.error(f"日期过滤错误: {str(e)}")
            elif focus_month:
                try:
                    focus_month_period = pd.Period(focus_month, freq='M')
                    processed_data = processed_data[processed_data['entry_time'].dt.to_period('M') == focus_month_period]
                except Exception as e:
                    if self.logger:
                        self.logger.error(f"月份过滤错误: {str(e)}")

            # 生成概览数据
            overview_data = {
                'original_total_records': self.total_records,
                'total_records': len(processed_data),
                'filtered_percentage': (len(processed_data) / self.total_records * 100) if self.total_records > 0 else 0,
                'unique_vehicles': processed_data['vid'].nunique() if not processed_data.empty else 0,
                'analysis_mode': self.mode,
                'focus_period': focus_date if focus_date else focus_month
            }

            # 添加时间范围信息
            if not processed_data.empty:
                overview_data['time_range'] = {
                    'start': processed_data['entry_time'].min().strftime('%Y-%m-%d %H:%M:%S'),
                    'end': processed_data['exit_time'].max().strftime('%Y-%m-%d %H:%M:%S')
                }

            # 添加停车时长统计
            if 'duration' in processed_data.columns and not processed_data.empty:
                duration_stats = processed_data['duration'].agg(['mean', 'max', 'min', 'median'])
                overview_data.update({
                    'average_duration': round(duration_stats['mean'], 2),
                    'max_duration': round(duration_stats['max'], 2),
                    'min_duration': duration_stats['min'],
                    'median_duration': round(duration_stats['median'], 2)
                })

            # 添加车辆类型分布
            if 'vtype' in processed_data.columns and not processed_data.empty:
                vtype_counts = processed_data['vtype'].value_counts(normalize=True)
                overview_data['vehicle_distribution'] = vtype_counts.to_dict()

            return overview_data

        except Exception as e:
            self._log_error(f"生成概览数据失败: {str(e)}")
            # 返回基本的概览数据
            return {
                'original_total_records': self.total_records,
                'total_records': len(self.processed_data) if hasattr(self, 'processed_data') else 0,
                'filtered_percentage': 0,
                'unique_vehicles': 0,
                'analysis_mode': self.mode,
                'focus_period': focus_date if focus_date else focus_month
            }
    
    def _analyze_by_period(self, focus_date=None, focus_month=None):
        """
        按时间周期分析数据
        
        Args:
            focus_date: str, 聚焦分析的日期
            focus_month: str, 聚焦分析的月份
            
        Returns:
            dict: 各时间周期的分析结果
        """
        if not self.time_periods:
            raise ValueError("未设置时间周期")
        
        try:
            processed_data = self.processed_data.copy()
            
            # 确保时间字段为datetime类型
            if 'entry_time' not in processed_data.columns or 'exit_time' not in processed_data.columns:
                if self.logger:
                    self.logger.error("缺少必要的时间字段: entry_time 或 exit_time")
                return {}
            
            # 转换时间字段
            processed_data['entry_time'] = pd.to_datetime(processed_data['entry_time'], errors='coerce')
            processed_data['exit_time'] = pd.to_datetime(processed_data['exit_time'], errors='coerce')
            
            # 移除无效时间记录
            processed_data = processed_data.dropna(subset=['entry_time', 'exit_time'])
            
            # 如果指定了聚焦日期或月份，进行数据过滤
            if focus_date:
                try:
                    focus_date_dt = pd.to_datetime(focus_date).date()
                    processed_data = processed_data[processed_data['entry_time'].dt.date == focus_date_dt]
                except Exception as e:
                    if self.logger:
                        self.logger.error(f"日期过滤错误: {str(e)}")
            elif focus_month:
                try:
                    focus_month_period = pd.Period(focus_month, freq='M')
                    processed_data = processed_data[processed_data['entry_time'].dt.to_period('M') == focus_month_period]
                except Exception as e:
                    if self.logger:
                        self.logger.error(f"月份过滤错误: {str(e)}")
            
            # 检查过滤后的数据是否为空
            if processed_data.empty:
                if self.logger:
                    self.logger.warning("过滤后没有有效数据")
                return {}
            
            period_results = {}
            
            # 遍历时间段
            for period in self.time_periods:
                try:
                    # 从时间段字符串中提取开始和结束时间
                    if isinstance(period, str) and '-' in period:
                        start_time_str, end_time_str = period.split('-')
                    else:
                        # 如果period不是预期的格式，记录错误并跳过
                        if self.logger:
                            self.logger.error(f"无效的时间段格式: {period}")
                        continue
                    
                    # 转换时间字符串为datetime.time对象
                    start = datetime.strptime(start_time_str, '%H:%M').time()
                    end = datetime.strptime(end_time_str, '%H:%M').time()
                    
                    # 获取进出标识值
                    entry_value = self.params.get('进出标识值', ['入场'])[0]
                    
                    # 分别筛选进场和出场数据
                    entry_data = processed_data[processed_data['direction'] == entry_value]
                    exit_data = processed_data[processed_data['direction'] != entry_value]
                    
                    # 处理跨天的情况
                    if start < end:
                        period_entry_data = entry_data[
                            (entry_data['entry_time'].dt.time >= start) &
                            (entry_data['entry_time'].dt.time < end)
                        ]
                        period_exit_data = exit_data[
                            (exit_data['exit_time'].dt.time >= start) &
                            (exit_data['exit_time'].dt.time < end)
                        ]
                    else:
                        # 如果开始时间大于结束时间，说明跨天
                        period_entry_data = entry_data[
                            (entry_data['entry_time'].dt.time >= start) |
                            (entry_data['entry_time'].dt.time < end)
                        ]
                        period_exit_data = exit_data[
                            (exit_data['exit_time'].dt.time >= start) |
                            (exit_data['exit_time'].dt.time < end)
                        ]
                    
                    # 合并进出场数据
                    period_data = pd.concat([period_entry_data, period_exit_data])
                    
                    
                    # 分析该时间段的数据
                    period_stats = {
                        'entry_count': len(period_entry_data),
                        'exit_count': len(period_exit_data),
                        'total_count': len(period_data),
                        'unique_vehicles': period_data['vid'].nunique(),
                        'avg_duration': period_data['duration'].mean() if not period_data.empty else 0,
                        'vehicle_types': {},
                        'most_used_gates': {
                            'entry': {},
                            'exit': {}
                        }
                    }
                    
                    # 分别统计进出场车辆类型
                    if not period_entry_data.empty:
                        period_stats['vehicle_types']['entry'] = period_entry_data['vtype'].value_counts().to_dict()
                    if not period_exit_data.empty:
                        period_stats['vehicle_types']['exit'] = period_exit_data['vtype'].value_counts().to_dict()
                    
                    # 统计最常用的进出口
                    if not period_entry_data.empty:
                        entry_gates = period_entry_data['gate'].value_counts()
                        if not entry_gates.empty:
                            period_stats['most_used_gates']['entry'] = {
                                'gate': entry_gates.index[0],
                                'count': int(entry_gates.iloc[0]),
                                'percentage': round(entry_gates.iloc[0] / len(period_entry_data) * 100, 2)
                            }
                    
                    if not period_exit_data.empty:
                        exit_gates = period_exit_data['gate'].value_counts()
                        if not exit_gates.empty:
                            period_stats['most_used_gates']['exit'] = {
                                'gate': exit_gates.index[0],
                                'count': int(exit_gates.iloc[0]),
                                'percentage': round(exit_gates.iloc[0] / len(period_exit_data) * 100, 2)
                            }
                    
                    period_results[period] = period_stats
                    
                except Exception as e:
                    error_msg = f"处理时间段 {period} 时出错: {str(e)}"
                    if self.logger:
                        self.logger.error(error_msg)
                    # 继续处理下一个时间段
                    continue
            
            return period_results
            
        except Exception as e:
            error_msg = f"时间周期分析失败: {str(e)}"
            if self.logger:
                self.logger.error(error_msg)
            return {}
    
    def _analyze_gate_pairs_by_period(self, focus_date=None, focus_month=None):
        """
        按时间段分析道闸进出组合，同时统计进场时间和出场时间在时间段内的车辆，并按车辆类型分组
        
        Args:
            focus_date: str, 聚焦分析的日期
            focus_month: str, 聚焦分析的月份
            
        Returns:
            dict: 各时间段内道闸进出组合的统计结果，包含进场和出场两种统计，以及按车辆类型的分组
        """
        if not self.time_periods:
            print("错误: 未设置时间周期")
            raise ValueError("未设置时间周期")
        
        try:
            processed_data = self.processed_data.copy()
            
            # 确保时间字段为datetime类型
            if 'entry_time' not in processed_data.columns or 'exit_time' not in processed_data.columns:
                print("错误: 缺少必要的时间字段")
                if self.logger:
                    self.logger.error("缺少必要的时间字段: entry_time 或 exit_time")
                return {}
            
            # 转换时间字段
            processed_data['entry_time'] = pd.to_datetime(processed_data['entry_time'], errors='coerce')
            processed_data['exit_time'] = pd.to_datetime(processed_data['exit_time'], errors='coerce')
            
            # 移除无效时间记录
            processed_data = processed_data.dropna(subset=['entry_time', 'exit_time'])
            
            # 如果指定了聚焦日期或月份，进行数据过滤
            if focus_date:
                try:
                    focus_date_dt = pd.to_datetime(focus_date).date()
                    processed_data = processed_data[processed_data['entry_time'].dt.date == focus_date_dt]
                except Exception as e:
                    print(f"日期过滤错误: {str(e)}")
                    if self.logger:
                        self.logger.error(f"日期过滤错误: {str(e)}")
            elif focus_month:
                try:
                    focus_month_period = pd.Period(focus_month, freq='M')
                    processed_data = processed_data[processed_data['entry_time'].dt.to_period('M') == focus_month_period]
                except Exception as e:
                    print(f"月份过滤错误: {str(e)}")
                    if self.logger:
                        self.logger.error(f"月份过滤错误: {str(e)}")
            
            # 检查过滤后的数据是否为空
            if processed_data.empty:
                print("警告: 过滤后没有有效数据")
                if self.logger:
                    self.logger.warning("过滤后没有有效数据")
                return {}
            
            # 确保vtype字段存在，如果不存在则添加默认值
            if 'vtype' not in processed_data.columns:
                processed_data['vtype'] = '未知'
            
            # 填充vtype中的空值
            processed_data['vtype'].fillna('未知', inplace=True)
            
            gate_pairs_results = {}
            
            # 遍历时间段
            for period in self.time_periods:
                try:
                    # 从时间段字符串中提取开始和结束时间
                    if isinstance(period, str) and '-' in period:
                        start_time_str, end_time_str = period.split('-')
                    else:
                        continue
                    
                    # 转换时间字符串为datetime.time对象
                    start = datetime.strptime(start_time_str, '%H:%M').time()
                    end = datetime.strptime(end_time_str, '%H:%M').time()
                    
                    # 筛选进场时间在该时段内的数据
                    if start < end:
                        entry_period_data = processed_data[
                            (processed_data['entry_time'].dt.time >= start) &
                            (processed_data['entry_time'].dt.time < end)
                        ]
                        exit_period_data = processed_data[
                            (processed_data['exit_time'].dt.time >= start) &
                            (processed_data['exit_time'].dt.time < end)
                        ]
                    else:
                        # 处理跨天的情况
                        entry_period_data = processed_data[
                            (processed_data['entry_time'].dt.time >= start) |
                            (processed_data['entry_time'].dt.time < end)
                        ]
                        exit_period_data = processed_data[
                            (processed_data['exit_time'].dt.time >= start) |
                            (processed_data['exit_time'].dt.time < end)
                        ]
                    
                    period_results = {
                        'entry_based': {
                            'total': {},  # 总体统计
                            'by_vtype': {}  # 按车辆类型统计
                        },
                        'exit_based': {
                            'total': {},  # 总体统计
                            'by_vtype': {}  # 按车辆类型统计
                        }
                    }
                    
                    # 统计以进场时间为准的道闸组合（总体）
                    if not entry_period_data.empty:
                        entry_gate_pairs = entry_period_data.groupby(['entry_gate', 'exit_gate']).size().sort_values(ascending=False)
                        period_results['entry_based']['total'] = entry_gate_pairs.to_dict()
                        
                        # 按车辆类型统计
                        for vtype in entry_period_data['vtype'].unique():
                            vtype_data = entry_period_data[entry_period_data['vtype'] == vtype]
                            if not vtype_data.empty:
                                vtype_gate_pairs = vtype_data.groupby(['entry_gate', 'exit_gate']).size().sort_values(ascending=False)
                                period_results['entry_based']['by_vtype'][vtype] = vtype_gate_pairs.to_dict()
                    
                    # 统计以出场时间为准的道闸组合（总体）
                    if not exit_period_data.empty:
                        exit_gate_pairs = exit_period_data.groupby(['entry_gate', 'exit_gate']).size().sort_values(ascending=False)
                        period_results['exit_based']['total'] = exit_gate_pairs.to_dict()
                        
                        # 按车辆类型统计
                        for vtype in exit_period_data['vtype'].unique():
                            vtype_data = exit_period_data[exit_period_data['vtype'] == vtype]
                            if not vtype_data.empty:
                                vtype_gate_pairs = vtype_data.groupby(['entry_gate', 'exit_gate']).size().sort_values(ascending=False)
                                period_results['exit_based']['by_vtype'][vtype] = vtype_gate_pairs.to_dict()
                    
                    gate_pairs_results[period] = period_results
                    
                except Exception as e:
                    error_msg = f"处理时间段 {period} 时出错: {str(e)}"
                    print(f"错误: {error_msg}")
                    print(f"错误详情: {traceback.format_exc()}")
                    if self.logger:
                        self.logger.error(error_msg)
                        self.logger.error(traceback.format_exc())
                    continue
            
            return gate_pairs_results
            
        except Exception as e:
            error_msg = f"道闸进出组合分析失败: {str(e)}"
            print(f"错误: {error_msg}")
            print(f"错误详情: {traceback.format_exc()}")
            if self.logger:
                self.logger.error(error_msg)
                self.logger.error(traceback.format_exc())
            return {}

    def generate_report(self, output_path="parking_analysis_report.xlsx"):
        """
        生成分析报告，包含道闸进出组合统计
        
        Args:
            output_path: str, 报告输出路径
            
        Returns:
            str: 生成的报告路径
        """
        try:
            # 获取分析结果
            analysis_results = self.analyze()
            
            # 创建Excel写入器
            with pd.ExcelWriter(output_path) as writer:
                # 1. 生成道闸进出组合统计sheet
                if 'gate_pairs_analysis' in analysis_results:
                    report_data = []
                    for period, gate_pairs in analysis_results['gate_pairs_analysis'].items():
                        total = sum(gate_pairs.values()) if gate_pairs else 0
                        for (entry_gate, exit_gate), count in sorted(gate_pairs.items(), 
                                                                  key=lambda x: x[1], 
                                                                  reverse=True):
                            percentage = round((count / total) * 100, 2) if total > 0 else 0
                            report_data.append({
                                '时间段': period,
                                '入口道闸': entry_gate,
                                '出口道闸': exit_gate, 
                                '数量': count,
                                '占比(%)': percentage
                            })
                    
                    df = pd.DataFrame(report_data)
                    df.to_excel(writer, sheet_name='道闸进出组合统计', index=False)
                    
                    # 设置列宽自适应
                    worksheet = writer.sheets['道闸进出组合统计']
                    for idx, col in enumerate(df.columns):
                        max_len = max(df[col].astype(str).map(len).max(), len(col)) + 2
                        worksheet.column_dimensions[get_column_letter(idx+1)].width = max_len
                
                # 2. 生成其他分析sheet（如果有）
                # 这里可以添加其他sheet的生成逻辑
                
            return output_path
            
        except Exception as e:
            error_msg = f"生成报告失败: {str(e)}"
            print(f"错误: {error_msg}")
            print(f"错误详情: {traceback.format_exc()}")
            if self.logger:
                self.logger.error(error_msg)
                self.logger.error(traceback.format_exc())
            raise

    def _log_message(self, message, level='info', include_traceback=False):
        """
        记录消息
        
        Args:
            message: str, 要记录的消息
            level: str, 日志级别 ('debug', 'info', 'warning', 'error')
            include_traceback: bool, 是否包含堆栈跟踪
        """
        # 打印到控制台
        if level == 'error':
            print(f"错误: {message}")
            if include_traceback:
                print(f"错误详情: {traceback.format_exc()}")
        elif level == 'warning':
            print(f"警告: {message}")
        elif level == 'debug':
            print(f"调试: {message}")
        else:  # info
            print(message)
        
        # 记录到日志
        if self.logger:
            if level == 'error':
                self.logger.error(message)
                if include_traceback:
                    self.logger.error(traceback.format_exc())
            elif level == 'warning':
                self.logger.warning(message)
            elif level == 'debug':
                self.logger.debug(message)
            else:  # info
                self.logger.info(message)
    
    def _handle_exception(self, e, context="操作"):
        """
        处理异常
        
        Args:
            e: Exception, 异常对象
            context: str, 发生异常的上下文描述
        
        Returns:
            str: 错误消息
        """
        error_msg = f"{context}失败: {str(e)}"
        self._log_message(error_msg, level='error', include_traceback=True)
        return error_msg
        
    # 保留原有的analyze_by_period方法以保持向后兼容
    def analyze_by_period(self):
        """
        按时间周期分析数据（向后兼容方法）
        
        Returns:
            dict: 各时间周期的分析结果，如果没有有效数据则返回空字典
        """
        try:
            if not self.time_periods:
                self._log_error("未设置时间周期")
                return {}
                
            if self.processed_data is None or self.processed_data.empty:
                self._log_error("没有有效的处理数据")
                return {}
                
            return self._analyze_by_period()
            
        except Exception as e:
            self._log_error(f"时间周期分析失败: {str(e)}\n{traceback.format_exc()}")
            return {}
    
    def generate_gate_pairs_report(self, output_path="gate_pairs_report.xlsx"):
        """
        生成道闸进出组合统计报告
        
        Args:
            output_path: str, 报告输出路径
            
        Returns:
            str: 生成的报告路径
        """
        try:
            # 获取分析结果
            analysis_results = self.analyze()
            if 'gate_pairs_analysis' not in analysis_results:
                raise ValueError("分析结果中缺少道闸进出组合数据")
                
            # 创建DataFrame用于导出
            report_data = []
            for period, gate_pairs in analysis_results['gate_pairs_analysis'].items():
                total = sum(gate_pairs.values()) if gate_pairs else 0
                for (entry_gate, exit_gate), count in sorted(gate_pairs.items(), 
                                                          key=lambda x: x[1], 
                                                          reverse=True):
                    percentage = round((count / total) * 100, 2) if total > 0 else 0
                    report_data.append({
                        '时间段': period,
                        '入口道闸': entry_gate,
                        '出口道闸': exit_gate, 
                        '数量': count,
                        '占比(%)': percentage
                    })
            
            df = pd.DataFrame(report_data)
            
            # 导出Excel
            with pd.ExcelWriter(output_path) as writer:
                df.to_excel(writer, 
                          sheet_name='道闸进出组合统计',
                          index=False)
                
                # 设置列宽自适应
                worksheet = writer.sheets['道闸进出组合统计']
                for idx, col in enumerate(df.columns):
                    max_len = max(df[col].astype(str).map(len).max(), len(col)) + 2
                    worksheet.column_dimensions[get_column_letter(idx+1)].width = max_len
            
            return output_path
            
        except Exception as e:
            error_msg = f"生成道闸进出组合报告失败: {str(e)}"
            self._log_error(error_msg)
            raise ValueError(error_msg)

    def get_period_comparison(self):
        """
        比较不同时间周期的数据，直接取各时段最大值作为高峰
        
        Returns:
            dict: 时间周期比较结果
        """
        try:
            period_results = self.analyze_by_period()
            
            # 检查是否有有效的时间段数据
            if not period_results:
                self._log_error("时间段分析结果为空")
                return {
                    'busiest_entry_period': None,
                    'busiest_exit_period': None,
                    'busiest_total_period': None,
                    'quietest_period': None,
                    'volume_distribution': {
                        'entry': {},
                        'exit': {},
                        'total': {}
                    },
                    'period_details': {}
                }
            
            # 直接找出各时段的最大值
            busiest_entry_period = max(period_results.items(), 
                                     key=lambda x: x[1].get('entry_count', 0))[0] if period_results else None
            busiest_exit_period = max(period_results.items(), 
                                    key=lambda x: x[1].get('exit_count', 0))[0] if period_results else None
            busiest_total_period = max(period_results.items(), 
                                     key=lambda x: x[1].get('total_count', 0))[0] if period_results else None
            quietest_period = min(period_results.items(), 
                                key=lambda x: x[1].get('total_count', 0))[0] if period_results else None
            
            # 计算各时段的车流量占比
            total_entry = sum(p.get('entry_count', 0) for p in period_results.values())
            total_exit = sum(p.get('exit_count', 0) for p in period_results.values())
            total_vehicles = sum(p.get('total_count', 0) for p in period_results.values())
            
            entry_distribution = {
                period: round(data.get('entry_count', 0) / total_entry * 100, 2) if total_entry > 0 else 0
                for period, data in period_results.items()
            }
            
            exit_distribution = {
                period: round(data.get('exit_count', 0) / total_exit * 100, 2) if total_exit > 0 else 0
                for period, data in period_results.items()
            }
            
            total_distribution = {
                period: round(data.get('total_count', 0) / total_vehicles * 100, 2) if total_vehicles > 0 else 0
                for period, data in period_results.items()
            }
            
            return {
                'busiest_entry_period': busiest_entry_period,
                'busiest_exit_period': busiest_exit_period,
                'busiest_total_period': busiest_total_period,
                'quietest_period': quietest_period,
                'volume_distribution': {
                    'entry': entry_distribution,
                    'exit': exit_distribution,
                    'total': total_distribution
                },
                'period_details': period_results
            }
            
        except Exception as e:
            self._log_error(f"计算时间段比较失败: {str(e)}\n{traceback.format_exc()}")
            return {
                'busiest_entry_period': None,
                'busiest_exit_period': None,
                'busiest_total_period': None,
                'quietest_period': None,
                'volume_distribution': {
                    'entry': {},
                    'exit': {},
                    'total': {}
                },
                'period_details': {}
            }