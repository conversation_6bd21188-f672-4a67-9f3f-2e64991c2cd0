#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修复后的车辆类型图表生成功能
"""

import os

def test_fixed_vehicle_charts():
    """测试修复后的车辆类型图表生成"""
    print("🚗 测试修复后的车辆类型图表生成")
    print("=" * 50)
    
    # Excel文件路径
    excel_file = r'C:\Users\<USER>\Desktop\停车分析\义乌火车站道闸_私家车_合并_20250617_083509_analysis_20250618_211554.xlsx'
    
    if not os.path.exists(excel_file):
        print(f"❌ Excel文件不存在: {excel_file}")
        return False
    
    try:
        # 导入图表生成器
        from parking_chart_generator import ParkingChartGenerator
        
        # 创建图表生成器
        print("📊 创建图表生成器...")
        chart_generator = ParkingChartGenerator(excel_file, None)
        
        # 检查是否有进出量时间分布工作表
        if '进出量时间分布' not in chart_generator.excel_data:
            print("❌ 未找到'进出量时间分布'工作表")
            return False
        
        # 获取数据并分析结构
        data = chart_generator.excel_data['进出量时间分布']
        columns = list(data.columns)
        total_cols = len(columns)
        
        print(f"📋 数据结构分析:")
        print(f"   总列数: {total_cols}")
        print(f"   前4列: {columns[:4]}")
        if total_cols > 7:
            print(f"   后3列: {columns[-3:]}")
            vehicle_cols = columns[4:-3]
            print(f"   车辆类型列数: {len(vehicle_cols)}")
            print(f"   车辆类型列: {vehicle_cols}")
        else:
            print(f"   列数不足，无法识别车辆类型数据")
            return False
        
        # 尝试生成车辆类型图表
        print(f"\n🚗 尝试生成车辆类型图表...")
        result = chart_generator.generate_vehicle_type_traffic_charts()
        
        if result:
            print(f"✅ 成功生成: {os.path.basename(result)}")
            
            # 检查文件是否存在
            if os.path.exists(result):
                file_size = os.path.getsize(result) / 1024
                print(f"📄 文件大小: {file_size:.1f}KB")
                return True
            else:
                print(f"❌ 文件未生成: {result}")
                return False
        else:
            print("❌ 未能生成车辆类型图表")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_fixed_vehicle_charts()
    if success:
        print("\n🎉 测试成功！车辆类型图表已生成！")
    else:
        print("\n⚠️ 测试失败，请检查问题")
    
    input("\n按回车键退出...")
