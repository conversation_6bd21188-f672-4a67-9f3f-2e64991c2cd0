2025-06-07 18:23:29,924 - main - INFO - 开始数据分析任务
2025-06-07 18:23:29,924 - main - INFO - 使用处理模式: mode_p2
2025-06-07 18:23:29,925 - main - INFO - 读取数据文件: C:\Users\<USER>\Desktop\停车分析\数据\火车站\5.31-6.2私家车.csv
2025-06-07 18:23:48,541 - DataReader - INFO - 检测到文件编码: GB2312
2025-06-07 18:23:48,657 - DataReader - INFO - 成功读取数据文件，共 15479 行
2025-06-07 18:23:48,684 - DataReader - INFO - 数据结构验证通过
2025-06-07 18:23:48,684 - main - INFO - 开始数据处理
2025-06-07 18:23:48,690 - DataProcessor - INFO - 开始数据处理
2025-06-07 18:23:48,809 - DataProcessor - ERROR - 数据处理失败: 'gate'
Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\停车分析\pythoncode\1.开发中\p_parking_data_base.py", line 188, in process
    stats = self.calculate_statistics()
  File "c:\Users\<USER>\Desktop\停车分析\pythoncode\1.开发中\p_parking_data_base.py", line 133, in calculate_statistics
    'gate_counts': self.data[self.field_mapping['gate']].value_counts().to_dict()
KeyError: 'gate'
2025-06-07 18:23:48,810 - main - ERROR - 处理过程中出现错误: 'gate'
Traceback (most recent call last):
  File "c:/Users/<USER>/Desktop/停车分析/pythoncode/1.开发中/p_parking_main.py", line 89, in main
    processed_data = processor.process()
  File "c:\Users\<USER>\Desktop\停车分析\pythoncode\1.开发中\p_parking_data_processor.py", line 200, in process
    processed_data = super().process()
  File "c:\Users\<USER>\Desktop\停车分析\pythoncode\1.开发中\p_parking_data_base.py", line 188, in process
    stats = self.calculate_statistics()
  File "c:\Users\<USER>\Desktop\停车分析\pythoncode\1.开发中\p_parking_data_base.py", line 133, in calculate_statistics
    'gate_counts': self.data[self.field_mapping['gate']].value_counts().to_dict()
KeyError: 'gate'
