<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Awesome-pyecharts</title>
                <script type="text/javascript" src="https://assets.pyecharts.org/assets/v5/echarts.min.js"></script>
            <script type="text/javascript" src="https://assets.pyecharts.org/assets/v5/themes/macarons.js"></script>

    
</head>
<body >
    <div id="b0e5a868596547d0a4750662078a57e6" class="chart-container" style="width:1200px; height:600px; "></div>
    <script>
        var chart_b0e5a868596547d0a4750662078a57e6 = echarts.init(
            document.getElementById('b0e5a868596547d0a4750662078a57e6'), 'macarons', {renderer: 'canvas'});
        var option_b0e5a868596547d0a4750662078a57e6 = {
    "animation": true,
    "animationThreshold": 2000,
    "animationDuration": 1000,
    "animationEasing": "cubicOut",
    "animationDelay": 0,
    "animationDurationUpdate": 300,
    "animationEasingUpdate": "cubicOut",
    "animationDelayUpdate": 0,
    "aria": {
        "enabled": false
    },
    "series": [
        {
            "type": "bar",
            "name": "\u5e73\u5747\u505c\u8f66\u65f6\u957f(\u5c0f\u65f6)",
            "legendHoverLink": true,
            "data": [
                110,
                30,
                20,
                50,
                10
            ],
            "realtimeSort": false,
            "showBackground": false,
            "stackStrategy": "samesign",
            "cursor": "pointer",
            "barMinHeight": 0,
            "barCategoryGap": "20%",
            "barGap": "30%",
            "large": false,
            "largeThreshold": 400,
            "seriesLayoutBy": "column",
            "datasetIndex": 0,
            "clip": true,
            "zlevel": 0,
            "z": 2,
            "label": {
                "show": true,
                "position": "top",
                "margin": 8,
                "valueAnimation": false
            }
        }
    ],
    "legend": [
        {
            "data": [
                "\u5e73\u5747\u505c\u8f66\u65f6\u957f(\u5c0f\u65f6)"
            ],
            "selected": {},
            "show": true,
            "padding": 5,
            "itemGap": 10,
            "itemWidth": 25,
            "itemHeight": 14,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderRadius": 0,
            "pageButtonItemGap": 5,
            "pageButtonPosition": "end",
            "pageFormatter": "{current}/{total}",
            "pageIconColor": "#2f4554",
            "pageIconInactiveColor": "#aaa",
            "pageIconSize": 15,
            "animationDurationUpdate": 800,
            "selector": false,
            "selectorPosition": "auto",
            "selectorItemGap": 7,
            "selectorButtonGap": 10
        }
    ],
    "tooltip": {
        "show": true,
        "trigger": "axis",
        "triggerOn": "mousemove|click",
        "axisPointer": {
            "type": "line"
        },
        "showContent": true,
        "alwaysShowContent": false,
        "showDelay": 0,
        "hideDelay": 100,
        "enterable": false,
        "confine": false,
        "appendToBody": false,
        "transitionDuration": 0.4,
        "formatter": "{b}: {c}\u5c0f\u65f6",
        "textStyle": {
            "color": "#333333",
            "fontSize": 12
        },
        "backgroundColor": "rgba(255, 255, 255, 0.95)",
        "borderColor": "#cccccc",
        "borderWidth": 1,
        "padding": 5,
        "order": "seriesAsc"
    },
    "xAxis": [
        {
            "name": "\u8f66\u8f86\u7c7b\u578b",
            "show": true,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0,
            "data": [
                "\u603b\u8ba1",
                "\u4e2d\u578b\u8f66",
                "\u5927\u578b\u8f66",
                "\u5c0f\u578b\u8f66",
                "\u7279\u79cd\u8f66"
            ]
        }
    ],
    "yAxis": [
        {
            "name": "\u5e73\u5747\u505c\u8f66\u65f6\u957f(\u5c0f\u65f6)",
            "show": true,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "axisLabel": {
                "show": true,
                "margin": 8,
                "formatter": "{value}h",
                "valueAnimation": false
            },
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0
        }
    ],
    "title": [
        {
            "show": true,
            "text": "\u4e0d\u540c\u8f66\u8f86\u7c7b\u578b\u5e73\u5747\u505c\u8f66\u65f6\u957f",
            "target": "blank",
            "subtext": "\u663e\u793a\u5404\u7c7b\u8f66\u8f86\u7684\u5e73\u5747\u505c\u8f66\u65f6\u957f\u5bf9\u6bd4",
            "subtarget": "blank",
            "left": "center",
            "padding": 5,
            "itemGap": 10,
            "textAlign": "auto",
            "textVerticalAlign": "auto",
            "triggerEvent": false
        }
    ]
};
        chart_b0e5a868596547d0a4750662078a57e6.setOption(option_b0e5a868596547d0a4750662078a57e6);
    </script>
</body>
</html>
