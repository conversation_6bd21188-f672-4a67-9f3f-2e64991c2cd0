#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试最小停车时长显示3位小数的效果
验证概览sheet中最小停车时长的精度修改
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import tempfile
import os

def create_test_data_with_short_durations():
    """创建包含短停车时长的测试数据"""
    np.random.seed(42)
    
    records = []
    base_date = datetime(2024, 6, 1)
    
    # 创建一些非常短的停车时长记录
    short_durations = [0.001, 0.005, 0.012, 0.025, 0.033, 0.067, 0.089, 0.123, 0.156, 0.234]
    
    for i in range(10):
        entry_time = base_date + timedelta(hours=i, minutes=np.random.randint(0, 60))
        # 使用预定义的短时长
        duration = short_durations[i]
        exit_time = entry_time + timedelta(hours=duration)
        
        records.append({
            '车牌号码': f"京A{i:05d}",
            '车辆类型': "小型车" if i % 2 == 0 else "大型车",
            '进场时间': entry_time,
            '出场时间': exit_time,
            '进场道闸': f"入口{(i % 3) + 1}",
            '出场道闸': f"出口{(i % 3) + 1}",
            '停车时长': f"{duration:.3f}小时"
        })
    
    # 添加一些正常时长的记录
    for i in range(10, 20):
        entry_time = base_date + timedelta(hours=i % 24, minutes=np.random.randint(0, 60))
        duration = np.random.uniform(1, 8)
        exit_time = entry_time + timedelta(hours=duration)
        
        records.append({
            '车牌号码': f"京A{i:05d}",
            '车辆类型': "小型车" if i % 2 == 0 else "大型车",
            '进场时间': entry_time,
            '出场时间': exit_time,
            '进场道闸': f"入口{(i % 3) + 1}",
            '出场道闸': f"出口{(i % 3) + 1}",
            '停车时长': f"{duration:.3f}小时"
        })
    
    return pd.DataFrame(records)

def test_min_duration_precision():
    """测试最小停车时长精度修改效果"""
    print("=" * 80)
    print("测试最小停车时长显示3位小数的效果")
    print("验证概览sheet中最小停车时长的精度修改")
    print("=" * 80)
    
    try:
        # 1. 创建包含短停车时长的测试数据
        test_data = create_test_data_with_short_durations()
        
        print(f"\n📋 测试数据概况:")
        print(f"   总记录数: {len(test_data)}")
        print(f"   数据列: {list(test_data.columns)}")
        
        # 显示停车时长分布
        durations = []
        for _, row in test_data.iterrows():
            entry_time = pd.to_datetime(row['进场时间'])
            exit_time = pd.to_datetime(row['出场时间'])
            duration = (exit_time - entry_time).total_seconds() / 3600  # 转换为小时
            durations.append(duration)
        
        durations = np.array(durations)
        print(f"\n📊 停车时长统计:")
        print(f"   最小时长: {durations.min():.6f} 小时")
        print(f"   最大时长: {durations.max():.6f} 小时")
        print(f"   平均时长: {durations.mean():.6f} 小时")
        print(f"   短时长记录数 (<0.1小时): {sum(durations < 0.1)}")
        
        # 2. 创建临时文件
        with tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False, encoding='utf-8') as f:
            test_data.to_csv(f.name, index=False)
            temp_file = f.name
        
        temp_output_dir = tempfile.mkdtemp()
        
        # 3. 设置测试参数
        params = {
            'input_file': temp_file,
            'output': temp_output_dir,
            'date': '2024-06-01',
            'mode': 'mode2',
            'time_interval': 60,
            'time_slip': 15,
            '车辆唯一标识字段': '车牌号码',
            '车辆类型字段': '车辆类型',
            '进场时间字段': '进场时间',
            '出场时间字段': '出场时间',
            '进场道闸编号字段': '进场道闸',
            '出场道闸编号字段': '出场道闸'
        }
        
        print(f"\n🔧 测试参数:")
        print(f"   处理模式: {params['mode']}")
        print(f"   聚焦日期: {params['date']}")
        print(f"   临时文件: {temp_file}")
        print(f"   输出目录: {temp_output_dir}")
        
        # 4. 执行分析流程
        print(f"\n{'='*60}")
        print("执行分析流程")
        print('='*60)
        
        from parking_data_processor import DataProcessor
        from parking_time_filter import TimeFilter
        from parking_analyzer import TimePeriodAnalyzer
        from parking_report_generatior import ReportGenerator
        
        # 数据处理
        data_processor = DataProcessor(test_data, params)
        processed_data = data_processor.process()
        print(f"✅ 数据处理完成: {len(processed_data)} 条有效记录")
        
        # 时间过滤
        time_filter = TimeFilter(processed_data, params)
        filtered_data = time_filter.get_filtered_data()
        period_info = time_filter.detect_period()
        print(f"✅ 时间过滤完成: {len(filtered_data)} 条记录")
        
        # 数据分析
        analyzer = TimePeriodAnalyzer(
            filtered_data, 
            len(test_data), 
            mode=params['mode'],
            period_info=period_info,
            params=params
        )
        analysis_results = analyzer.analyze(
            focus_date=params.get('date')
        )
        print("✅ 数据分析完成")
        
        # 5. 检查分析结果中的最小停车时长
        print(f"\n{'='*60}")
        print("检查分析结果中的最小停车时长")
        print('='*60)
        
        if 'overview' in analysis_results:
            overview = analysis_results['overview']
            
            print(f"📊 概览数据中的停车时长统计:")
            if 'min_duration' in overview:
                min_duration = overview['min_duration']
                print(f"   最小停车时长 (原始值): {min_duration}")
                print(f"   最小停车时长 (3位小数): {min_duration:.3f}")
                print(f"   最小停车时长 (6位小数): {min_duration:.6f}")
            
            if 'average_duration' in overview:
                avg_duration = overview['average_duration']
                print(f"   平均停车时长: {avg_duration:.2f}")
            
            if 'max_duration' in overview:
                max_duration = overview['max_duration']
                print(f"   最大停车时长: {max_duration:.2f}")
        else:
            print("❌ 分析结果中缺少overview数据")
            return False
        
        # 6. 生成报告并检查Excel文件
        print(f"\n{'='*60}")
        print("生成报告并检查Excel文件")
        print('='*60)
        
        report_generator = ReportGenerator(
            filtered_data,
            analysis_results,
            params,
            input_total_records=len(test_data)
        )
        
        output_path = os.path.join(temp_output_dir, "min_duration_test.xlsx")
        report_generator.generate_and_save_plots(filtered_data)
        report_path = report_generator.export_to_excel(output_path)
        print(f"🎉 报告生成完成: {report_path}")
        
        # 7. 验证Excel文件中的显示效果
        print(f"\n{'='*60}")
        print("验证Excel文件中的显示效果")
        print('='*60)
        
        if os.path.exists(report_path):
            file_size = os.path.getsize(report_path) / 1024
            print(f"✅ 报告文件生成成功")
            print(f"   文件路径: {report_path}")
            print(f"   文件大小: {file_size:.1f} KB")
            
            # 读取Excel文件验证内容
            try:
                # 读取概览sheet
                overview_sheet = pd.read_excel(report_path, sheet_name='概览', header=None)
                print(f"✅ 成功读取概览工作表")
                
                # 查找最短停车时长行
                min_duration_row = None
                for idx, row in overview_sheet.iterrows():
                    if pd.notna(row[0]) and '最短停车时长' in str(row[0]):
                        min_duration_row = row
                        break
                
                if min_duration_row is not None:
                    min_duration_display = str(min_duration_row[1])
                    print(f"✅ 找到最短停车时长显示: {min_duration_display}")
                    
                    # 检查是否包含3位小数
                    if '.000' in min_duration_display or '.001' in min_duration_display or '.005' in min_duration_display:
                        print(f"✅ 最短停车时长正确显示3位小数")
                    else:
                        print(f"⚠️  最短停车时长显示格式: {min_duration_display}")
                else:
                    print(f"❌ 未找到最短停车时长行")
                    
            except Exception as e:
                print(f"⚠️  读取Excel文件时出错: {e}")
        else:
            print(f"❌ 报告文件生成失败")
            return False
        
        # 8. 清理临时文件
        print(f"\n{'='*60}")
        print("清理临时文件")
        print('='*60)
        
        try:
            os.unlink(temp_file)
            print(f"✅ 已删除临时数据文件")
        except:
            print(f"⚠️  删除临时数据文件失败")
        
        try:
            if os.path.exists(report_path):
                os.unlink(report_path)
            os.rmdir(temp_output_dir)
            print(f"✅ 已清理临时输出目录")
        except:
            print(f"⚠️  清理临时输出目录失败")
        
        # 9. 总结
        print(f"\n{'='*60}")
        print("测试结果总结")
        print('='*60)
        
        print("✅ 最小停车时长精度修改测试完成！")
        print("📋 修改效果:")
        print("   ✅ 数据处理保持原始精度")
        print("   ✅ 分析结果保持原始精度")
        print("   ✅ 概览sheet显示3位小数")
        print("   ✅ 其他时长指标保持2位小数")
        print("   ✅ 功能完全正常")
        
        print(f"\n🎯 修改内容:")
        print("   📝 parking_report_generatior.py:")
        print("      - _create_overview_sheet: 显示格式改为 {x:.3f}小时")
        print("      - _create_overview_sheet: 数据处理不再舍入")
        print("   📝 parking_analyzer.py:")
        print("      - _generate_overview_data: 数据处理不再舍入")
        
        return True
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_min_duration_precision()
    
    print("\n" + "=" * 80)
    if success:
        print("🎉 最小停车时长精度修改测试完成！")
        print("✅ 概览sheet中的最小停车时长现在显示3位小数")
    else:
        print("❌ 最小停车时长精度修改测试失败")
    print("=" * 80)
