2025-06-07 20:08:18,049 - main - INFO - 开始数据分析任务
2025-06-07 20:08:18,049 - main - INFO - 使用处理模式: mode_p2
2025-06-07 20:08:18,049 - main - INFO - 读取数据文件: C:\Users\<USER>\Desktop\停车分析\数据\火车站\5.31-6.2私家车.csv
2025-06-07 20:08:39,099 - DataReader - INFO - 检测到文件编码: GB2312
2025-06-07 20:08:39,221 - DataReader - INFO - 成功读取数据文件，共 15479 行
2025-06-07 20:08:39,243 - DataReader - INFO - 数据结构验证通过
2025-06-07 20:08:39,243 - main - INFO - 开始数据处理
2025-06-07 20:08:39,248 - DataProcessor - INFO - 开始数据处理
2025-06-07 20:08:39,354 - DataProcessor - INFO - 数据处理完成，统计信息: {'total_records': 15479, 'vehicle_type_counts': {'临时用户A': 15168, '免费用户A': 311}, 'entry_gate_counts': {'2号进口1': 10783, '地下3号入口2': 3161, '地下3号入口1': 1291, '地面入口': 139, '地面入口2': 37, '地下室2号出口-2': 31, '停车场1号西出口2': 25, '停车场1号西出口': 9, '地面出口': 3}, 'exit_gate_counts': {'地下室2号出口-2': 8948, '停车场1号西出口2': 3960, '停车场1号西出口': 2392, '地面出口': 142, '地面出口2': 37}, 'avg_duration': 1.955036931757004, 'max_duration': 256.15777777777777}
2025-06-07 20:08:39,359 - DataProcessor - WARNING - 过滤掉 3200 条异常停车时长记录
2025-06-07 20:08:39,379 - main - INFO - 开始数据分析
2025-06-07 20:08:39,426 - main - INFO - 生成分析报告
2025-06-07 20:08:39,427 - DataReader - INFO - 输出文件路径: reports\5.31-6.2私家车_分析结果_20250607_200839.xlsx
2025-06-07 20:08:41,137 - ReportGenerator - WARNING - 生成停车时长分布图失败: 'NoneType' object is not iterable
2025-06-07 20:08:41,810 - ReportGenerator - INFO - Excel报告已生成: reports\5.31-6.2私家车_分析结果_20250607_200839.xlsx
2025-06-07 20:08:41,866 - main - INFO - 分析报告已生成: reports\5.31-6.2私家车_分析结果_20250607_200839.xlsx
2025-06-07 20:08:41,867 - main - INFO - 数据分析任务完成
