#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试出入口进出量综合分析页面生成功能
验证2x2布局的合并图表是否正确显示
"""

import os

def test_combined_gate_analysis():
    """测试出入口进出量综合分析页面生成"""
    print("📊 测试出入口进出量综合分析页面生成")
    print("=" * 50)
    
    # Excel文件路径
    excel_file = r'C:\Users\<USER>\Desktop\停车分析\义乌火车站道闸_私家车_合并_20250617_083509_analysis_20250618_211554.xlsx'
    
    if not os.path.exists(excel_file):
        print(f"❌ Excel文件不存在: {excel_file}")
        return False
    
    try:
        # 导入图表生成器
        from parking_chart_generator import ParkingChartGenerator
        
        # 创建图表生成器
        print("📊 创建图表生成器...")
        chart_generator = ParkingChartGenerator(excel_file, None)
        
        # 检查是否有进出量时间分布(按道闸)工作表
        target_sheet = '进出量时间分布(按道闸)'
        if target_sheet not in chart_generator.excel_data:
            print(f"❌ 未找到'{target_sheet}'工作表")
            available_sheets = list(chart_generator.excel_data.keys())
            print(f"可用工作表: {available_sheets}")
            return False
        
        # 获取数据并分析结构
        data = chart_generator.excel_data[target_sheet]
        columns = list(data.columns)
        total_cols = len(columns)
        
        print(f"📋 数据结构分析:")
        print(f"   总列数: {total_cols}")
        print(f"   第1列: {columns[0]} (时间段)")
        
        if total_cols > 1:
            gate_cols_count = total_cols - 1
            estimated_gates = gate_cols_count // 3
            print(f"   预估出入口数量: {estimated_gates}")
        
        # 尝试生成综合分析页面
        print(f"\n📊 尝试生成出入口进出量综合分析页面...")
        print("📋 注意观察控制台输出的详细信息...")
        
        result = chart_generator.generate_combined_gate_analysis()
        
        if result:
            print(f"\n✅ 成功生成: {os.path.basename(result)}")
            
            # 检查文件是否存在
            if os.path.exists(result):
                file_size = os.path.getsize(result) / 1024
                print(f"📄 文件大小: {file_size:.1f}KB")
                print(f"📁 文件路径: {result}")
                
                # 读取HTML内容检查合并图表特征
                with open(result, 'r', encoding='utf-8') as f:
                    html_content = f.read()
                
                # 检查2x2布局相关特征
                layout_checks = {
                    'Timeline组件': 'Timeline' in html_content,
                    'Grid布局': 'Grid(' in html_content,
                    '饼图组件': 'Pie(' in html_content,
                    '柱状图组件': 'Bar(' in html_content,
                    '进场占比': '进场占比' in html_content,
                    '出场占比': '出场占比' in html_content,
                    '进出量对比': '进出量对比' in html_content,
                    '2x2布局尺寸': '1400px' in html_content and '800px' in html_content,
                    'Grid定位': 'grid_opts' in html_content or 'GridOpts' in html_content,
                }
                
                print(f"\n📊 2x2布局验证:")
                all_checks_passed = True
                for check_name, is_passed in layout_checks.items():
                    status = "✅" if is_passed else "❌"
                    print(f"   {status} {check_name}: {'通过' if is_passed else '未通过'}")
                    if not is_passed:
                        all_checks_passed = False
                
                # 检查组件数量
                pie_count = html_content.count('Pie(')
                bar_count = html_content.count('Bar(')
                grid_count = html_content.count('Grid(')
                timeline_count = html_content.count('Timeline(')
                
                print(f"\n🔍 组件统计:")
                print(f"   Timeline数量: {timeline_count}")
                print(f"   Grid数量: {grid_count}")
                print(f"   饼图数量: {pie_count}")
                print(f"   柱状图数量: {bar_count}")
                
                # 验证布局结构
                expected_structure = {
                    'Timeline': timeline_count == 1,
                    '每个时间段一个Grid': grid_count >= 1,
                    '每个Grid两个饼图': pie_count >= 2,
                    '每个Grid一个柱状图': bar_count >= 1,
                }
                
                print(f"\n📐 布局结构验证:")
                for check_name, is_correct in expected_structure.items():
                    status = "✅" if is_correct else "❌"
                    print(f"   {status} {check_name}: {'正确' if is_correct else '需检查'}")
                    if not is_correct:
                        all_checks_passed = False
                
                if all_checks_passed:
                    print("\n✅ 综合分析页面生成成功！")
                    print("   - 2x2布局正确实现")
                    print("   - 上方两个区域显示饼图")
                    print("   - 下方合并区域显示柱状图")
                    print("   - Timeline动态切换功能")
                    print("   - 文件名: 出入口进出量_进出方向.html")
                    return True
                else:
                    print("\n⚠️ 综合分析页面生成但布局可能有问题")
                    return False
            else:
                print(f"❌ 文件未生成: {result}")
                return False
        else:
            print("❌ 未能生成综合分析页面")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def demo_layout_design():
    """演示2x2布局设计"""
    print("\n📐 2x2布局设计说明")
    print("=" * 50)
    
    print("🎯 布局结构:")
    print("   ┌─────────────┬─────────────┐")
    print("   │  进场占比   │  出场占比   │")
    print("   │   饼图      │   饼图      │")
    print("   ├─────────────┴─────────────┤")
    print("   │      进出量方向对比       │")
    print("   │        柱状图            │")
    print("   └───────────────────────────┘")
    
    print("\n📊 区域分配:")
    print("   左上 (45% × 40%): 进场占比饼图")
    print("   右上 (45% × 40%): 出场占比饼图")
    print("   下方 (90% × 45%): 进出量方向柱状图")
    print("   间距: 5% 边距，5% 中间间距")
    
    print("\n🎨 视觉设计:")
    print("   - 整体尺寸: 1400px × 800px")
    print("   - 饼图尺寸: 650px × 350px")
    print("   - 柱状图尺寸: 1300px × 350px")
    print("   - 统一的专业演讲风格颜色")
    
    print("\n🎮 交互功能:")
    print("   - Timeline时间轴控制")
    print("   - 4秒播放间隔（适合观察三个图表）")
    print("   - 饼图和柱状图的独立交互")
    print("   - 统一的时间段同步显示")
    
    print("\n💡 分析价值:")
    print("   - 一屏查看三个维度的数据")
    print("   - 进出占比对比分析")
    print("   - 占比与绝对数量的关联分析")
    print("   - 时间维度的动态变化观察")

def main():
    """主函数"""
    # 演示布局设计
    demo_layout_design()
    
    # 测试功能
    success = test_combined_gate_analysis()
    
    if success:
        print("\n🎉 测试成功！综合分析页面已生成！")
        print("📁 文件名: 出入口进出量_进出方向.html")
        print("💡 现在可以在一个页面查看三个图表")
        print("🔍 建议在浏览器中验证效果:")
        print("   1. 观察2x2布局是否正确")
        print("   2. 检查上方两个饼图显示")
        print("   3. 验证下方柱状图合并显示")
        print("   4. 测试Timeline时间轴功能")
        print("   5. 验证各图表的交互功能")
        
        print("\n📊 现在您拥有完整的图表体系:")
        print("   单独图表:")
        print("     - 出入口进出量_总量.html")
        print("     - 出入口进出量_方向.html")
        print("     - 出入口占比_进.html")
        print("     - 出入口占比_出.html")
        print("   合并图表:")
        print("     - 出入口进出量_进出方向.html ← 新增")
    else:
        print("\n⚠️ 测试失败")
        print("💡 可能的原因:")
        print("   1. Excel文件中没有'进出量时间分布(按道闸)'工作表")
        print("   2. 数据格式不正确")
        print("   3. Grid布局组件导入或配置问题")
    
    print("\n" + "="*50)
    input("按回车键退出...")

if __name__ == "__main__":
    main()
