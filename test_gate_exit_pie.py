#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试出入口出场占比Timeline饼图生成功能
验证各出入口出场量占比是否正确显示
"""

import os

def test_gate_exit_pie():
    """测试出入口出场占比Timeline饼图生成"""
    print("🥧 测试出入口出场占比Timeline饼图生成")
    print("=" * 50)
    
    # Excel文件路径
    excel_file = r'C:\Users\<USER>\Desktop\停车分析\义乌火车站道闸_私家车_合并_20250617_083509_analysis_20250618_211554.xlsx'
    
    if not os.path.exists(excel_file):
        print(f"❌ Excel文件不存在: {excel_file}")
        return False
    
    try:
        # 导入图表生成器
        from parking_chart_generator import ParkingChartGenerator
        
        # 创建图表生成器
        print("📊 创建图表生成器...")
        chart_generator = ParkingChartGenerator(excel_file, None)
        
        # 检查是否有进出量时间分布(按道闸)工作表
        target_sheet = '进出量时间分布(按道闸)'
        if target_sheet not in chart_generator.excel_data:
            print(f"❌ 未找到'{target_sheet}'工作表")
            available_sheets = list(chart_generator.excel_data.keys())
            print(f"可用工作表: {available_sheets}")
            return False
        
        # 获取数据并分析结构
        data = chart_generator.excel_data[target_sheet]
        columns = list(data.columns)
        total_cols = len(columns)
        
        print(f"📋 数据结构分析:")
        print(f"   总列数: {total_cols}")
        print(f"   第1列: {columns[0]} (时间段)")
        
        if total_cols > 1:
            gate_cols = columns[1:]
            gate_cols_count = len(gate_cols)
            estimated_gates = gate_cols_count // 3
            
            print(f"   出入口相关列数: {gate_cols_count}")
            print(f"   预估出入口数量: {estimated_gates}")
            
            # 显示每个出入口的出场列
            for i in range(min(estimated_gates, 3)):
                exit_col_idx = 1 + i * 3 + 1  # 出场列
                if exit_col_idx < total_cols:
                    print(f"   出入口{i+1}出场列: {columns[exit_col_idx]}")
        
        # 尝试生成出场占比Timeline饼图
        print(f"\n🥧 尝试生成出入口出场占比Timeline饼图...")
        print("📋 注意观察控制台输出的详细分析信息...")
        
        result = chart_generator.generate_gate_exit_proportion_timeline()
        
        if result:
            print(f"\n✅ 成功生成: {os.path.basename(result)}")
            
            # 检查文件是否存在
            if os.path.exists(result):
                file_size = os.path.getsize(result) / 1024
                print(f"📄 文件大小: {file_size:.1f}KB")
                print(f"📁 文件路径: {result}")
                
                # 读取HTML内容检查饼图Timeline特征
                with open(result, 'r', encoding='utf-8') as f:
                    html_content = f.read()
                
                # 检查出场饼图Timeline相关特征
                exit_pie_checks = {
                    'Timeline组件': 'Timeline' in html_content,
                    '饼图组件': 'Pie(' in html_content,
                    '玫瑰图样式': 'rosetype:"radius"' in html_content or 'rosetype":"radius"' in html_content,
                    '半径设置': '30%' in html_content and '55%' in html_content,
                    '多个时间段': html_content.count('add(') > 1,
                    '出场占比': '出场占比' in html_content,
                    '出场量': '出场量' in html_content,
                    '其他出入口': '其他出入口' in html_content,
                    '专业颜色': '#2E86AB' in html_content and '#A23B72' in html_content,
                    '灰色配置': '#95A5A6' in html_content,
                }
                
                print(f"\n📊 出场饼图Timeline验证:")
                all_checks_passed = True
                for check_name, is_passed in exit_pie_checks.items():
                    status = "✅" if is_passed else "❌"
                    print(f"   {status} {check_name}: {'通过' if is_passed else '未通过'}")
                    if not is_passed:
                        all_checks_passed = False
                
                # 检查数据系列信息
                pie_count = html_content.count('Pie(')
                time_segments = html_content.count('add(')
                other_count = html_content.count('其他出入口')
                
                print(f"\n🔍 饼图信息:")
                print(f"   检测到饼图数量: {pie_count}")
                print(f"   时间段数量: {time_segments}")
                print(f"   '其他出入口'出现次数: {other_count}")
                print(f"   每个时间段显示各出入口出场量占比")
                
                if other_count > 0:
                    ratio = other_count / pie_count if pie_count > 0 else 0
                    print(f"   📈 '其他出入口'出现比例: {ratio:.1%}")
                
                if all_checks_passed:
                    print("\n✅ 出场占比Timeline饼图生成成功！")
                    print("   - 玫瑰图样式，美观直观")
                    print("   - 显示各出入口出场量占比")
                    print("   - 支持时间段动态切换")
                    print("   - 使用专业演讲风格颜色")
                    print("   - 智能阈值处理，突出重点")
                    print("   - 文件名: 出入口占比_出.html")
                    return True
                else:
                    print("\n⚠️ 饼图Timeline生成但可能不是预期格式")
                    return False
            else:
                print(f"❌ 文件未生成: {result}")
                return False
        else:
            print("❌ 未能生成出场占比Timeline饼图")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def demo_complete_pie_system():
    """演示完整的饼图体系"""
    print("\n🥧 完整饼图体系说明")
    print("=" * 50)
    
    print("📊 现在您拥有完整的出入口分析图表体系:")
    
    print("\n1️⃣ 柱状图Timeline系列:")
    print("   📁 出入口进出量_总量.html - 总量对比")
    print("   📁 出入口进出量_方向.html - 进出对比")
    
    print("\n2️⃣ 饼图Timeline系列:")
    print("   📁 出入口占比_进.html - 进场占比 ✅")
    print("   📁 出入口占比_出.html - 出场占比 ← 新增")
    
    print("\n🎯 分析维度对比:")
    print("   柱状图系列:")
    print("     - 显示绝对数量")
    print("     - 便于数值对比")
    print("     - 适合流量分析")
    
    print("\n   饼图系列:")
    print("     - 显示相对占比")
    print("     - 突出主要出入口")
    print("     - 适合分布分析")
    
    print("\n🔄 进出对比分析:")
    print("   进场占比 vs 出场占比:")
    print("     - 对比同一出入口的进出占比")
    print("     - 识别主要进入口和主要出去口")
    print("     - 分析进出流向的不平衡")
    print("     - 发现潜在的流量瓶颈")
    
    print("\n💡 使用建议:")
    print("   1. 先看总量图了解整体情况")
    print("   2. 再看方向图分析进出流向")
    print("   3. 用进场占比图识别主要进入口")
    print("   4. 用出场占比图识别主要出去口")
    print("   5. 对比进出占比发现不平衡")

def main():
    """主函数"""
    # 演示完整饼图体系
    demo_complete_pie_system()
    
    # 测试功能
    success = test_gate_exit_pie()
    
    if success:
        print("\n🎉 测试成功！出场占比Timeline饼图已生成！")
        print("📁 文件名: 出入口占比_出.html")
        print("💡 现在可以查看各出入口出场量占比")
        print("🔍 建议在浏览器中验证效果:")
        print("   1. 观察玫瑰图的美观效果")
        print("   2. 查看各出入口的出场占比数据")
        print("   3. 使用时间轴切换不同时间段")
        print("   4. 观察占比随时间的变化趋势")
        print("   5. 对比进场和出场占比的差异")
        
        print("\n📊 现在您拥有完整的出入口图表体系:")
        print("   柱状图Timeline:")
        print("     1. 出入口进出量_总量.html - 总量对比")
        print("     2. 出入口进出量_方向.html - 进出对比")
        print("   饼图Timeline:")
        print("     3. 出入口占比_进.html - 进场占比")
        print("     4. 出入口占比_出.html - 出场占比 ← 新增")
        
        print("\n🎯 分析建议:")
        print("   - 对比进场和出场占比，识别流向特点")
        print("   - 找出主要进入口和主要出去口")
        print("   - 分析进出流向的平衡性")
        print("   - 为出入口管理提供数据支持")
    else:
        print("\n⚠️ 测试失败")
        print("💡 可能的原因:")
        print("   1. Excel文件中没有'进出量时间分布(按道闸)'工作表")
        print("   2. 数据列结构不符合预期")
        print("   3. 出场数据为空或格式不正确")
    
    print("\n" + "="*50)
    input("按回车键退出...")

if __name__ == "__main__":
    main()
