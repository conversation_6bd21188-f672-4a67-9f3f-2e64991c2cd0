#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的重复检测测试
验证修复后的重复检测逻辑
"""

import pandas as pd
import tempfile
import os
import shutil

def test_simple_duplicate():
    """简单的重复检测测试"""
    print("🧪 简单重复检测测试")
    print("=" * 50)
    
    # 创建临时目录
    temp_dir = tempfile.mkdtemp()
    print(f"测试目录: {temp_dir}")
    
    try:
        # 创建测试数据
        data1 = pd.DataFrame({
            'A': [1, 2, 3],
            'B': ['x', 'y', 'z'],
            'C': [100, 200, 300]
        })
        
        data2 = pd.DataFrame({
            'A': [4, 5, 6],
            'B': ['a', 'b', 'c'],
            'C': [400, 500, 600]
        })
        
        # 创建文件
        file1 = os.path.join(temp_dir, 'unique1.csv')
        file2 = os.path.join(temp_dir, 'unique2.csv')
        file3 = os.path.join(temp_dir, 'duplicate.csv')  # 与file1重复
        
        data1.to_csv(file1, index=False)
        data2.to_csv(file2, index=False)
        data1.to_csv(file3, index=False)  # 重复数据
        
        print(f"创建文件:")
        print(f"  unique1.csv: {len(data1)} 行")
        print(f"  unique2.csv: {len(data2)} 行")
        print(f"  duplicate.csv: {len(data1)} 行 (与unique1.csv重复)")
        
        # 导入合并器
        from excel_data_merger import IntegratedDataMerger
        
        # 创建合并器
        merger = IntegratedDataMerger()
        
        # 执行合并
        output_file = os.path.join(temp_dir, 'result.csv')
        result = merger.smart_merge_directory(
            input_path=temp_dir,
            output_path=output_file,
            file_pattern="*",
            recursive=False
        )
        
        # 分析结果
        stats = merger.merge_stats
        
        print(f"\n📊 合并结果:")
        print(f"总数据源: {stats['total_sources']}")
        print(f"成功合并: {stats['merged_sources']}")
        print(f"重复数量: {stats['duplicate_sources']}")
        print(f"合并行数: {stats['merged_rows']}")
        print(f"实际行数: {len(result)}")
        
        # 验证结果
        expected_sources = 2  # unique1.csv + unique2.csv
        expected_duplicates = 1  # duplicate.csv
        expected_rows = len(data1) + len(data2)  # 3 + 3 = 6
        
        print(f"\n🔍 验证:")
        print(f"预期成功合并: {expected_sources}")
        print(f"预期重复数量: {expected_duplicates}")
        print(f"预期合并行数: {expected_rows}")
        
        # 检查结果
        success = (
            stats['merged_sources'] == expected_sources and
            stats['duplicate_sources'] == expected_duplicates and
            stats['merged_rows'] == expected_rows and
            len(result) == expected_rows
        )
        
        if success:
            print("✅ 测试通过！")
        else:
            print("❌ 测试失败！")
            print(f"实际成功合并: {stats['merged_sources']} (预期: {expected_sources})")
            print(f"实际重复数量: {stats['duplicate_sources']} (预期: {expected_duplicates})")
            print(f"实际合并行数: {stats['merged_rows']} (预期: {expected_rows})")
            print(f"实际结果行数: {len(result)} (预期: {expected_rows})")
        
        return success
        
    except Exception as e:
        print(f"❌ 测试异常: {str(e)}")
        import traceback
        traceback.print_exc()
        return False
        
    finally:
        # 清理
        try:
            shutil.rmtree(temp_dir)
            print(f"🧹 清理测试目录")
        except:
            pass

def main():
    """主函数"""
    print("🔧 重复检测修复验证")
    print("=" * 60)
    
    success = test_simple_duplicate()
    
    print(f"\n📊 测试结果: {'✅ 通过' if success else '❌ 失败'}")

if __name__ == "__main__":
    main()
