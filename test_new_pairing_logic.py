import pandas as pd
from parking_data_base import ParkingDataBase
import logging

# 设置日志
logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)

# 测试数据1: 1:00进，1:50进，1:53进，2:50出，2:55出
print("=" * 60)
print("测试数据1: 1:00进，1:50进，1:53进，2:50出，2:55出")
print("预期结果: 1:53进 → 2:50出")
print("=" * 60)

test_data1 = pd.DataFrame({
    'vid': ['车A', '车A', '车A', '车A', '车A'],
    'vtype': ['小车', '小车', '小车', '小车', '小车'],
    'timestamp': pd.to_datetime([
        '2024-01-01 01:00:00',
        '2024-01-01 01:50:00', 
        '2024-01-01 01:53:00',
        '2024-01-01 02:50:00',
        '2024-01-01 02:55:00'
    ]),
    'direction': ['进', '进', '进', '出', '出'],
    'gate': ['A1', 'A1', 'A1', 'A2', 'A2']
})

# 创建处理器
params = {
    'mode': 'mode1',
    'mode1': {
        '车辆唯一标识字段': '车牌',
        '车辆类型字段': '车辆类型',
        '时间记录字段': '时间',
        '进出类型字段': '方向',
        '进出标识值': ('进', '出'),
        '道闸编号字段': '出入口'
    }
}

processor = ParkingDataBase(params, logger=logger)
processor.processed_data = test_data1.copy()

# 测试新的配对逻辑
vid_data = test_data1.sort_values('timestamp').reset_index(drop=True)
paired_records = processor._pair_records_by_logic_closure(vid_data)

print(f"配对结果数量: {len(paired_records)}")
for i, record in enumerate(paired_records):
    print(f"记录{i+1}: {record['entry_time']} → {record['exit_time']} "
          f"(时长: {record['duration']:.2f}小时)")

print("\n" + "=" * 60)
print("测试数据2: 0:40出，1:00进，1:50进，1:53进，2:50出，2:55出，3:10进，3:30出")
print("预期结果: 1:53进 → 2:50出, 3:10进 → 3:30出")
print("=" * 60)

test_data2 = pd.DataFrame({
    'vid': ['车B', '车B', '车B', '车B', '车B', '车B', '车B', '车B'],
    'vtype': ['小车', '小车', '小车', '小车', '小车', '小车', '小车', '小车'],
    'timestamp': pd.to_datetime([
        '2024-01-01 00:40:00',
        '2024-01-01 01:00:00',
        '2024-01-01 01:50:00', 
        '2024-01-01 01:53:00',
        '2024-01-01 02:50:00',
        '2024-01-01 02:55:00',
        '2024-01-01 03:10:00',
        '2024-01-01 03:30:00'
    ]),
    'direction': ['出', '进', '进', '进', '出', '出', '进', '出'],
    'gate': ['A2', 'A1', 'A1', 'A1', 'A2', 'A2', 'A1', 'A2']
})

vid_data2 = test_data2.sort_values('timestamp').reset_index(drop=True)
paired_records2 = processor._pair_records_by_logic_closure(vid_data2)

print(f"配对结果数量: {len(paired_records2)}")
for i, record in enumerate(paired_records2):
    print(f"记录{i+1}: {record['entry_time']} → {record['exit_time']} "
          f"(时长: {record['duration']:.2f}小时)")

print("\n" + "=" * 60)
print("测试数据3: 正常进出序列")
print("预期结果: 每个进场都有对应的出场")
print("=" * 60)

test_data3 = pd.DataFrame({
    'vid': ['车C', '车C', '车C', '车C'],
    'vtype': ['小车', '小车', '小车', '小车'],
    'timestamp': pd.to_datetime([
        '2024-01-01 01:00:00',
        '2024-01-01 02:00:00',
        '2024-01-01 03:00:00',
        '2024-01-01 04:00:00'
    ]),
    'direction': ['进', '出', '进', '出'],
    'gate': ['A1', 'A2', 'A1', 'A2']
})

vid_data3 = test_data3.sort_values('timestamp').reset_index(drop=True)
paired_records3 = processor._pair_records_by_logic_closure(vid_data3)

print(f"配对结果数量: {len(paired_records3)}")
for i, record in enumerate(paired_records3):
    print(f"记录{i+1}: {record['entry_time']} → {record['exit_time']} "
          f"(时长: {record['duration']:.2f}小时)")
