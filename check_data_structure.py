#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查Excel数据结构
"""

import pandas as pd
import os

def check_excel_structure():
    excel_file = r'C:\Users\<USER>\Desktop\停车分析\义乌火车站道闸_私家车_合并_20250617_083509_analysis_20250618_211554.xlsx'
    
    if not os.path.exists(excel_file):
        print(f'❌ Excel文件不存在: {excel_file}')
        return
    
    try:
        # 读取Excel文件
        excel_data = pd.read_excel(excel_file, sheet_name=None)
        
        print('📋 Excel文件中的工作表:')
        for sheet_name in excel_data.keys():
            print(f'  - {sheet_name}')
        
        # 检查进出量时间分布工作表
        if '进出量时间分布' in excel_data:
            data = excel_data['进出量时间分布']
            print(f'\n📊 进出量时间分布工作表:')
            print(f'  行数: {len(data)}')
            print(f'  列数: {len(data.columns)}')
            print(f'  列名:')
            for i, col in enumerate(data.columns):
                print(f'    {i}: {col}')
            
            # 检查是否有车辆类型相关的列
            vehicle_related_cols = []
            for col in data.columns:
                col_str = str(col).lower()
                if any(keyword in col_str for keyword in ['进场', '出场', '进入', '离开', '入场', '出入']):
                    for vehicle_type in ['私家车', '网约车', '出租车', '货车', '客车', '摩托车', '电动车']:
                        if vehicle_type in str(col):
                            vehicle_related_cols.append(col)
                            break
            
            print(f'\n🚗 车辆类型相关列: {len(vehicle_related_cols)} 个')
            for col in vehicle_related_cols:
                print(f'  - {col}')
                
            if not vehicle_related_cols:
                print('❌ 未找到车辆类型相关的列')
                print('💡 请确保列名包含车辆类型名称和方向关键词')
                print('💡 例如: "私家车进场数量", "网约车出场数量"')
        else:
            print('❌ 未找到进出量时间分布工作表')
            
    except Exception as e:
        print(f'❌ 读取Excel文件失败: {e}')

if __name__ == "__main__":
    check_excel_structure()
