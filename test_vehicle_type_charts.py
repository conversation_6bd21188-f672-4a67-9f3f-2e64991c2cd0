#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试车辆类型进出量时间分布图表生成
验证各类车辆类型的进出量图表是否能正确生成并整合到一个页面
"""

import pandas as pd
import tempfile
import os
import shutil

def test_vehicle_type_charts():
    """测试车辆类型进出量时间分布图表生成"""
    print("🚗 测试车辆类型进出量时间分布图表生成")
    print("=" * 60)
    
    # 创建临时目录
    temp_dir = tempfile.mkdtemp()
    print(f"测试目录: {temp_dir}")
    
    try:
        # 创建包含多种车辆类型的测试数据
        test_data = pd.DataFrame({
            '时间段': [
                '08:00-09:00', '09:00-10:00', '10:00-11:00', '11:00-12:00', 
                '12:00-13:00', '13:00-14:00', '14:00-15:00', '15:00-16:00'
            ],
            '进场数量': [120, 180, 150, 90, 110, 85, 95, 140],
            '出场数量': [80, 120, 160, 130, 100, 110, 120, 160],
            '总流量': [200, 300, 310, 220, 210, 195, 215, 300],
            # 私家车数据
            '私家车进场数量': [80, 120, 100, 60, 70, 55, 60, 90],
            '私家车出场数量': [50, 80, 110, 90, 65, 75, 80, 110],
            # 网约车数据
            '网约车进场数量': [30, 45, 35, 20, 25, 20, 25, 35],
            '网约车出场数量': [20, 30, 35, 30, 25, 25, 30, 35],
            # 出租车数据
            '出租车进场数量': [10, 15, 15, 10, 15, 10, 10, 15],
            '出租车出场数量': [10, 10, 15, 10, 10, 10, 10, 15]
        })
        
        # 创建测试Excel文件
        excel_file = os.path.join(temp_dir, 'test_vehicle_types.xlsx')
        with pd.ExcelWriter(excel_file, engine='openpyxl') as writer:
            test_data.to_excel(writer, sheet_name='进出量时间分布', index=False)
        
        print(f"✅ 创建测试Excel文件")
        print(f"📊 测试数据概览:")
        print(f"   时间段数量: {len(test_data)}")
        print(f"   包含车辆类型: 私家车, 网约车, 出租车")
        print(f"   数据列: {list(test_data.columns)}")
        
        # 导入图表生成器
        from parking_chart_generator import ParkingChartGenerator
        
        # 创建图表生成器
        chart_generator = ParkingChartGenerator(excel_file, temp_dir)
        
        print(f"\n🎨 生成车辆类型进出量时间分布图表...")
        
        # 生成车辆类型图表
        chart_file = chart_generator.generate_vehicle_type_traffic_charts()
        
        if chart_file and os.path.exists(chart_file):
            print(f"✅ 图表生成成功: {os.path.basename(chart_file)}")
            
            # 检查文件大小
            file_size = os.path.getsize(chart_file) / 1024  # KB
            print(f"📄 文件大小: {file_size:.1f}KB")
            
            # 检查文件名是否正确
            expected_filename = "进出量时间分布_车型.html"
            actual_filename = os.path.basename(chart_file)
            filename_correct = actual_filename == expected_filename
            
            print(f"📁 文件名检查:")
            print(f"   预期: {expected_filename}")
            print(f"   实际: {actual_filename}")
            print(f"   结果: {'✅ 正确' if filename_correct else '❌ 错误'}")
            
            # 读取HTML内容检查是否包含多个图表
            with open(chart_file, 'r', encoding='utf-8') as f:
                html_content = f.read()
            
            # 检查是否包含车辆类型相关内容
            vehicle_type_checks = {
                '私家车图表': '私家车进出量时间分布' in html_content,
                '网约车图表': '网约车进出量时间分布' in html_content,
                '出租车图表': '出租车进出量时间分布' in html_content,
                'Page布局': 'DraggablePageLayout' in html_content or 'page' in html_content.lower(),
                '专业颜色': '#2E86AB' in html_content and '#A23B72' in html_content
            }
            
            print(f"\n🔍 图表内容验证:")
            all_checks_passed = True
            for check_name, is_passed in vehicle_type_checks.items():
                status = "✅" if is_passed else "❌"
                print(f"   {status} {check_name}: {'通过' if is_passed else '未通过'}")
                if not is_passed:
                    all_checks_passed = False
            
            # 检查图表数量（通过标题数量估算）
            title_count = html_content.count('进出量时间分布')
            print(f"\n📊 图表数量估算: {title_count} 个图表")
            
            # 总体评估
            print(f"\n📋 功能实现评估:")
            if all_checks_passed and filename_correct:
                print("✅ 车辆类型图表生成成功！")
                print("   - 文件名符合要求")
                print("   - 包含多种车辆类型的图表")
                print("   - 使用Page布局整合到一个页面")
                print("   - 应用了专业演讲风格颜色")
                print("   - 每种车辆类型都有独立的进出场柱状图")
            else:
                print("❌ 车辆类型图表生成有问题！")
                if not filename_correct:
                    print("   - 文件名不符合要求")
                if not all_checks_passed:
                    print("   - 部分功能特性未正确实现")
            
            return all_checks_passed and filename_correct
            
        else:
            print("❌ 图表生成失败")
            return False
        
    except Exception as e:
        print(f"❌ 测试异常: {str(e)}")
        import traceback
        traceback.print_exc()
        return False
        
    finally:
        # 清理
        try:
            shutil.rmtree(temp_dir)
            print(f"\n🧹 清理测试目录")
        except:
            pass

def demo_feature_overview():
    """演示新功能概览"""
    print("\n🚗 车辆类型图表功能概览")
    print("=" * 60)
    
    print("🎯 功能目标:")
    print("   - 为进出量时间分布数据中的各类车辆类型生成独立图表")
    print("   - 每种车辆类型显示进场和出场的柱状图")
    print("   - 将所有车辆类型图表整合到一个页面中")
    print("   - 文件名为: 进出量时间分布_车型.html")
    
    print("\n🔧 技术实现:")
    print("   - 使用pyecharts的Page布局")
    print("   - 自动识别数据中的车辆类型列")
    print("   - 为每种车辆类型分配专业演讲风格颜色")
    print("   - 支持的车辆类型: 私家车、网约车、出租车、货车、客车等")
    
    print("\n🎨 设计特点:")
    print("   - 每个图表独立显示，便于对比")
    print("   - 统一的专业演讲风格")
    print("   - 高对比度颜色配置")
    print("   - 优化的tooltip样式")
    
    print("\n📊 数据要求:")
    print("   - 数据列名包含车辆类型名称")
    print("   - 列名包含'进场'或'出场'关键词")
    print("   - 例如: '私家车进场数量', '网约车出场数量'")

def main():
    """主函数"""
    print("🚗 测试车辆类型进出量时间分布图表")
    print("=" * 80)
    
    # 演示功能概览
    demo_feature_overview()
    
    # 测试功能实现
    result = test_vehicle_type_charts()
    
    print(f"\n📊 测试总结:")
    print("=" * 80)
    print(f"车辆类型图表生成: {'✅ 成功' if result else '❌ 有问题'}")
    
    if result:
        print(f"\n🎉 车辆类型图表功能实现成功！")
        print(f"   - 文件名: 进出量时间分布_车型.html")
        print(f"   - 包含各类车辆类型的独立图表")
        print(f"   - 使用Page布局整合到一个页面")
        print(f"   - 专业演讲风格的视觉效果")
        print(f"\n💡 使用方法:")
        print(f"   1. 确保Excel数据包含车辆类型相关列")
        print(f"   2. 列名格式: '[车辆类型][进场/出场][数量]'")
        print(f"   3. 运行chart_generator.generate_vehicle_type_traffic_charts()")
        print(f"   4. 或使用chart_generator.generate_all_charts()自动生成")
    else:
        print(f"\n⚠️ 需要进一步优化")

if __name__ == "__main__":
    main()
