#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试图表生成问题
"""

import pandas as pd
import numpy as np
import os
from parking_report_generator import ReportGenerator

def debug_chart_step_by_step():
    """逐步调试图表生成过程"""
    print("=== 逐步调试图表生成过程 ===\n")
    
    # 创建简单测试数据
    np.random.seed(42)
    durations = [0.5, 1.5, 3.0, 6.0, 12.0, 25.0, 50.0, 100.0] * 5  # 重复5次，共40条记录
    
    test_data = pd.DataFrame({
        'entry_time': pd.to_datetime(['2024-01-01 08:00:00'] * len(durations)),
        'exit_time': pd.to_datetime(['2024-01-01 10:00:00'] * len(durations)),
        'duration': durations,
        'vtype': ['小型车'] * len(durations),
        'vehicle_id': [f'车{i+1:04d}' for i in range(len(durations))],
        'entry_gate': ['A口'] * len(durations),
        'exit_gate': ['A口'] * len(durations)
    })
    
    params = {'mode': 'mode2', '聚焦日期': '2024-01-01'}
    generator = ReportGenerator(test_data, params)
    generator.processed_data = test_data
    
    print(f"测试数据: {len(test_data)} 条记录")
    print(f"Duration范围: {test_data['duration'].min()} - {test_data['duration'].max()}")
    print()
    
    # 步骤1：测试延停时长概率密度数据计算
    print("步骤1: 测试延停时长概率密度数据计算")
    try:
        density_stats = generator._calculate_duration_probability_density()
        if not density_stats.empty:
            print(f"  ✅ 数据计算成功: {len(density_stats)} 行")
            print(f"  ✅ 列名: {list(density_stats.columns)}")
            print(f"  ✅ 总频数: {density_stats['频数'].sum()}")
        else:
            print("  ❌ 数据计算失败，返回空DataFrame")
            return
    except Exception as e:
        print(f"  ❌ 数据计算异常: {str(e)}")
        return
    
    print()
    
    # 步骤2：创建Excel文件并导出数据
    print("步骤2: 创建Excel文件并导出数据")
    try:
        import xlsxwriter
        test_file = "debug_chart_test.xlsx"
        
        if os.path.exists(test_file):
            os.remove(test_file)
        
        with pd.ExcelWriter(test_file, engine='xlsxwriter') as writer:
            workbook = writer.book
            
            # 导出延停时长概率密度数据
            generator._export_duration_probability_density(writer, workbook)
            
            print(f"  ✅ 数据导出成功")
            
            # 检查工作表是否创建
            sheet_names = [sheet.name for sheet in workbook.worksheets()]
            print(f"  ✅ 工作表列表: {sheet_names}")
            
            if '延停时长概率密度' not in sheet_names:
                print("  ❌ 延停时长概率密度工作表未创建")
                return
            
            # 步骤3：测试图表创建
            print("\n步骤3: 测试图表创建")
            
            chart_config = {
                'title': '延停时长概率密度分布',
                'type': 'duration_probability_density',
                'position': (2, 0),
                'size': {'width': 600, 'height': 400}
            }
            
            # 调用图表创建方法
            chart = generator._create_chart_for_sheet(workbook, chart_config)
            
            if chart:
                print(f"  ✅ 图表对象创建成功")
                print(f"  ✅ 图表类型: {type(chart)}")
                
                # 步骤4：创建测试工作表并插入图表
                print("\n步骤4: 插入图表到工作表")
                
                test_worksheet = workbook.add_worksheet('图表调试')
                
                try:
                    # 插入图表
                    test_worksheet.insert_chart(2, 0, chart, {
                        'x_scale': chart_config['size']['width'] / 480,
                        'y_scale': chart_config['size']['height'] / 288
                    })
                    print(f"  ✅ 图表插入成功")
                    
                    # 添加标题
                    title_format = workbook.add_format({
                        'bold': True,
                        'bg_color': '#D9E1F2',
                        'border': 1,
                        'align': 'center'
                    })
                    test_worksheet.write(1, 0, chart_config['title'], title_format)
                    print(f"  ✅ 图表标题添加成功")
                    
                except Exception as e:
                    print(f"  ❌ 图表插入失败: {str(e)}")
                    import traceback
                    traceback.print_exc()
            else:
                print(f"  ❌ 图表对象创建失败")
                
                # 调试图表创建失败的原因
                print("\n调试图表创建失败原因:")
                
                # 检查工作表是否存在
                existing_sheets = [sheet.name for sheet in workbook.worksheets()]
                print(f"  现有工作表: {existing_sheets}")
                
                if '延停时长概率密度' in existing_sheets:
                    print(f"  ✅ 延停时长概率密度工作表存在")
                    
                    # 直接调用图表创建方法
                    try:
                        direct_chart = generator._create_duration_probability_density_chart(workbook)
                        if direct_chart:
                            print(f"  ✅ 直接调用图表创建方法成功")
                        else:
                            print(f"  ❌ 直接调用图表创建方法失败")
                    except Exception as e:
                        print(f"  ❌ 直接调用图表创建方法异常: {str(e)}")
                        import traceback
                        traceback.print_exc()
                else:
                    print(f"  ❌ 延停时长概率密度工作表不存在")
        
        print(f"\n✅ 调试文件已保存: {test_file}")
        
        # 验证文件内容
        if os.path.exists(test_file):
            with pd.ExcelFile(test_file) as xls:
                final_sheets = xls.sheet_names
                print(f"✅ 最终工作表: {final_sheets}")
                
                if '延停时长概率密度' in final_sheets:
                    df = pd.read_excel(test_file, sheet_name='延停时长概率密度')
                    print(f"✅ 数据验证: {len(df)} 行 x {len(df.columns)} 列")
                    
                    # 显示前几行数据
                    print("前3行数据:")
                    print(df.head(3).to_string())
        
    except Exception as e:
        print(f"  ❌ Excel文件创建异常: {str(e)}")
        import traceback
        traceback.print_exc()

def test_chart_creation_directly():
    """直接测试图表创建方法"""
    print("\n=== 直接测试图表创建方法 ===\n")
    
    # 创建测试数据
    test_data = pd.DataFrame({
        'duration': [0.5, 1.5, 3.0, 6.0, 12.0] * 4,
        'vtype': ['小型车'] * 20
    })
    
    params = {'mode': 'mode2'}
    generator = ReportGenerator(test_data, params)
    generator.processed_data = test_data
    
    try:
        import xlsxwriter
        
        # 创建工作簿
        workbook = xlsxwriter.Workbook('direct_chart_test.xlsx')
        
        # 先创建数据工作表
        density_stats = generator._calculate_duration_probability_density()
        if density_stats.empty:
            print("❌ 无法获取密度统计数据")
            return
        
        # 手动创建延停时长概率密度工作表
        worksheet = workbook.add_worksheet('延停时长概率密度')
        
        # 写入数据
        for col_num, column in enumerate(density_stats.columns):
            worksheet.write(0, col_num, column)
        
        for row_num, row in enumerate(density_stats.itertuples(index=False), start=1):
            for col_num, value in enumerate(row):
                worksheet.write(row_num, col_num, value)
        
        print(f"✅ 数据工作表创建成功: {len(density_stats)} 行")
        
        # 直接调用图表创建方法
        chart = generator._create_duration_probability_density_chart(workbook)
        
        if chart:
            print(f"✅ 图表创建成功: {type(chart)}")
            
            # 创建图表工作表
            chart_worksheet = workbook.add_worksheet('图表测试')
            chart_worksheet.insert_chart(2, 0, chart)
            
            print(f"✅ 图表插入成功")
        else:
            print(f"❌ 图表创建失败")
        
        workbook.close()
        print(f"✅ 文件保存成功: direct_chart_test.xlsx")
        
    except Exception as e:
        print(f"❌ 直接测试异常: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_chart_step_by_step()
    test_chart_creation_directly()
