#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证文件名修改是否正确
检查"进出量时间分布图表.html"是否已改为"进出量时间分布_总量.html"
"""

import os

def verify_filename_change():
    """验证文件名修改"""
    print("🔍 验证文件名修改情况")
    print("=" * 50)
    
    # 检查代码文件
    code_file = "parking_chart_generator.py"
    
    if not os.path.exists(code_file):
        print(f"❌ 代码文件不存在: {code_file}")
        return False
    
    try:
        # 读取代码文件
        with open(code_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查旧文件名是否还存在
        old_filename = "进出量时间分布图表.html"
        new_filename = "进出量时间分布_总量.html"
        
        print("📋 检查文件名修改:")
        
        # 检查旧文件名
        if old_filename in content:
            print(f"   ❌ 发现旧文件名残留: '{old_filename}'")
            
            # 显示找到的行
            lines = content.split('\n')
            for i, line in enumerate(lines, 1):
                if old_filename in line:
                    print(f"      第{i}行: {line.strip()}")
            return False
        else:
            print(f"   ✅ 旧文件名已清理: '{old_filename}'")
        
        # 检查新文件名
        if new_filename in content:
            print(f"   ✅ 新文件名已应用: '{new_filename}'")
            
            # 显示找到的行
            lines = content.split('\n')
            count = 0
            for i, line in enumerate(lines, 1):
                if new_filename in line:
                    count += 1
                    print(f"      第{i}行: {line.strip()}")
            
            print(f"   📊 新文件名出现次数: {count}")
            
            if count >= 2:  # 应该在生成文件和图表标题字典中各出现一次
                print("   ✅ 文件名修改完整")
                return True
            else:
                print("   ⚠️ 文件名修改可能不完整")
                return False
        else:
            print(f"   ❌ 新文件名未找到: '{new_filename}'")
            return False
            
    except Exception as e:
        print(f"❌ 验证失败: {str(e)}")
        return False

def check_current_file_structure():
    """检查当前的文件结构"""
    print("\n📊 当前图表文件结构")
    print("=" * 50)
    
    print("🎯 主要图表文件:")
    print("   ✅ 进出量时间分布_总量.html - 总量柱状图+趋势线")
    print("   ✅ 进出量时间分布_车型.html - 车辆类型紧凑子图")
    
    print("\n🎬 Timeline图表系列:")
    print("   ✅ 出入口进出量_总量.html - 总量Timeline柱状图")
    print("   ✅ 出入口进出量_方向.html - 方向Timeline柱状图")
    print("   ✅ 出入口占比_进.html - 进场占比Timeline饼图")
    print("   ✅ 出入口占比_出.html - 出场占比Timeline饼图")
    
    print("\n📈 其他图表:")
    print("   ✅ 停车时长分布图表.html")
    print("   ✅ 车辆类型停车时长图表.html")
    print("   ✅ 在场车辆分布图表.html")
    
    print("\n🔄 文件名变更:")
    print("   ❌ 进出量时间分布图表.html (旧名称)")
    print("   ✅ 进出量时间分布_总量.html (新名称)")
    
    print("\n💡 命名规则:")
    print("   - 基础图表: [功能]_[类型].html")
    print("   - Timeline图表: [功能]_[维度].html")
    print("   - 传统图表: [功能]图表.html")

def test_generation():
    """测试生成功能"""
    print("\n🔧 建议测试")
    print("=" * 50)
    
    print("💡 验证修改效果:")
    print("   1. 运行图表生成器")
    print("   2. 检查是否生成 '进出量时间分布_总量.html'")
    print("   3. 确认不再生成 '进出量时间分布图表.html'")
    print("   4. 验证图表内容是否正常")
    
    print("\n📋 测试命令:")
    print("   python parking_chart_generator.py")
    print("   或")
    print("   from parking_chart_generator import ParkingChartGenerator")
    print("   chart_gen = ParkingChartGenerator('your_file.xlsx', 'output_dir')")
    print("   chart_gen.generate_traffic_flow_chart()")

def main():
    """主函数"""
    success = verify_filename_change()
    
    if success:
        check_current_file_structure()
        test_generation()
        print("\n🎉 文件名修改验证成功！")
        print("✅ '进出量时间分布图表.html' 已改为 '进出量时间分布_总量.html'")
        print("💡 建议运行图表生成器测试新文件名")
    else:
        print("\n⚠️ 文件名修改验证失败")
        print("💡 可能需要手动检查和修正代码")
    
    print("\n" + "="*50)
    input("按回车键退出...")

if __name__ == "__main__":
    main()
