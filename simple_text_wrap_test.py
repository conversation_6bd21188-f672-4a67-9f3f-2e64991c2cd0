#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的自动换行功能测试
直接测试Excel格式配置和方法
"""

import xlsxwriter
import tempfile
import os

def test_text_wrap_functionality():
    """测试自动换行功能"""
    print("=" * 60)
    print("简单的自动换行功能测试")
    print("=" * 60)
    
    try:
        # 1. 测试Excel格式配置
        print("\n🔍 测试Excel格式配置...")
        
        from parking_report_generatior import ReportGenerator
        
        # 检查EXCEL_FORMATS配置
        excel_formats = ReportGenerator.EXCEL_FORMATS
        print(f"   Excel格式配置:")
        
        for format_name, format_config in excel_formats.items():
            print(f"     {format_name}: {format_config}")
            
            # 特别检查header格式
            if format_name == 'header':
                if 'text_wrap' in format_config:
                    print(f"       ✅ 包含自动换行: text_wrap = {format_config['text_wrap']}")
                else:
                    print(f"       ❌ 缺少自动换行设置")
        
        # 2. 测试创建Excel文件
        print(f"\n📊 测试创建Excel文件...")
        
        temp_dir = tempfile.mkdtemp()
        test_file = os.path.join(temp_dir, 'text_wrap_test.xlsx')
        
        # 创建工作簿
        workbook = xlsxwriter.Workbook(test_file)
        
        # 创建格式
        formats = {k: workbook.add_format(v) for k, v in excel_formats.items()}
        
        # 创建工作表
        worksheet = workbook.add_worksheet('测试自动换行')
        
        # 测试长字段名
        long_headers = [
            "车辆唯一标识号码",
            "车辆类型分类信息",
            "进场时间详细记录",
            "出场时间详细记录",
            "进场道闸编号信息",
            "出场道闸编号信息",
            "停车时长计算结果"
        ]
        
        # 设置表头行高
        worksheet.set_row(0, 35)  # 设置第0行高度为35
        
        # 写入表头
        for col, header in enumerate(long_headers):
            worksheet.write(0, col, header, formats['header'])
            print(f"     写入表头: '{header}' ({len(header)}字符)")
        
        # 写入一些测试数据
        test_data = [
            ["京A12345", "小型车", "2024-06-01 08:30", "2024-06-01 10:15", "入口A", "出口B", "1.75小时"],
            ["京B67890", "大型车", "2024-06-01 09:00", "2024-06-01 11:30", "入口B", "出口A", "2.50小时"],
        ]
        
        for row_num, row_data in enumerate(test_data, start=1):
            for col_num, value in enumerate(row_data):
                worksheet.write(row_num, col_num, value, formats['cell'])
        
        # 设置列宽
        for col in range(len(long_headers)):
            worksheet.set_column(col, col, 15)  # 设置较窄的列宽以测试换行
        
        # 关闭工作簿
        workbook.close()
        
        print(f"   ✅ Excel文件创建成功: {test_file}")
        
        # 3. 测试_set_header_row_height方法
        print(f"\n🔧 测试表头行高设置方法...")
        
        # 创建一个临时的ReportGenerator实例来测试方法
        import pandas as pd
        dummy_data = pd.DataFrame({'test': [1, 2, 3]})
        dummy_results = {'overview': {}}
        dummy_params = {}
        
        report_gen = ReportGenerator(dummy_data, dummy_results, dummy_params)
        
        # 检查方法是否存在
        if hasattr(report_gen, '_set_header_row_height'):
            print(f"   ✅ _set_header_row_height方法存在")
            
            # 测试方法调用
            test_workbook = xlsxwriter.Workbook(os.path.join(temp_dir, 'row_height_test.xlsx'))
            test_worksheet = test_workbook.add_worksheet('test')
            
            try:
                report_gen._set_header_row_height(test_worksheet, 0, 35)
                print(f"   ✅ 方法调用成功")
            except Exception as e:
                print(f"   ❌ 方法调用失败: {e}")
            
            test_workbook.close()
        else:
            print(f"   ❌ _set_header_row_height方法不存在")
        
        # 4. 验证结果
        print(f"\n{'='*40}")
        print("验证结果")
        print('='*40)
        
        checks = []
        
        # 检查1: header格式包含text_wrap
        header_format = excel_formats.get('header', {})
        if header_format.get('text_wrap'):
            checks.append("✅ header格式包含text_wrap设置")
        else:
            checks.append("❌ header格式缺少text_wrap设置")
        
        # 检查2: Excel文件创建成功
        if os.path.exists(test_file):
            checks.append("✅ Excel测试文件创建成功")
        else:
            checks.append("❌ Excel测试文件创建失败")
        
        # 检查3: 方法存在
        if hasattr(report_gen, '_set_header_row_height'):
            checks.append("✅ 表头行高设置方法存在")
        else:
            checks.append("❌ 表头行高设置方法不存在")
        
        for check in checks:
            print(check)
        
        success_count = sum(1 for check in checks if check.startswith("✅"))
        total_count = len(checks)
        
        print(f"\n🎯 测试结果: {success_count}/{total_count} 项通过")
        
        if success_count == total_count:
            print("🎉 自动换行功能配置完全成功！")
        elif success_count >= 2:
            print("✅ 自动换行功能基本配置成功！")
        else:
            print("⚠️  需要进一步检查配置")
        
        # 5. 使用说明
        print(f"\n📖 功能说明:")
        print("1. header格式已添加text_wrap: True")
        print("2. 各导出方法中设置了表头行高(35像素)")
        print("3. 长字段名会在Excel单元格内自动换行")
        print("4. 表头行高度会自动适应换行内容")
        
        print(f"\n📁 测试文件: {test_file}")
        print("   可以手动打开查看自动换行效果")
        
        return True
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_text_wrap_functionality()
    
    print("\n" + "=" * 60)
    print("自动换行功能测试完成！")
    print("=" * 60)
