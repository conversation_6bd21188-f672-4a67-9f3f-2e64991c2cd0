#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证Excel文件中的图表
"""

import os
import zipfile
import xml.etree.ElementTree as ET

def verify_excel_charts(excel_file):
    """验证Excel文件中是否包含图表"""
    print(f"=== 验证Excel文件中的图表: {excel_file} ===\n")
    
    if not os.path.exists(excel_file):
        print(f"❌ 文件不存在: {excel_file}")
        return
    
    try:
        # Excel文件实际上是一个ZIP文件
        with zipfile.ZipFile(excel_file, 'r') as zip_file:
            file_list = zip_file.namelist()
            
            print(f"📁 Excel文件内部结构:")
            
            # 查找图表相关文件
            chart_files = [f for f in file_list if 'chart' in f.lower()]
            drawing_files = [f for f in file_list if 'drawing' in f.lower()]
            worksheet_files = [f for f in file_list if 'worksheet' in f.lower()]
            
            print(f"   📊 图表文件 ({len(chart_files)} 个):")
            for chart_file in chart_files:
                print(f"      - {chart_file}")
            
            print(f"   🎨 绘图文件 ({len(drawing_files)} 个):")
            for drawing_file in drawing_files:
                print(f"      - {drawing_file}")
            
            print(f"   📋 工作表文件 ({len(worksheet_files)} 个):")
            for worksheet_file in worksheet_files:
                print(f"      - {worksheet_file}")
            
            # 检查是否有图表
            if chart_files:
                print(f"\n✅ 发现 {len(chart_files)} 个图表文件")
                
                # 分析第一个图表文件
                if chart_files:
                    first_chart = chart_files[0]
                    print(f"\n🔍 分析图表文件: {first_chart}")
                    
                    try:
                        chart_content = zip_file.read(first_chart)
                        root = ET.fromstring(chart_content)
                        
                        # 查找图表标题
                        title_elements = root.findall('.//{http://schemas.openxmlformats.org/drawingml/2006/chart}title')
                        if title_elements:
                            print(f"   📝 找到图表标题元素")
                        
                        # 查找数据系列
                        series_elements = root.findall('.//{http://schemas.openxmlformats.org/drawingml/2006/chart}ser')
                        if series_elements:
                            print(f"   📈 找到 {len(series_elements)} 个数据系列")
                        
                        # 查找图表类型
                        chart_types = []
                        for chart_type in ['barChart', 'lineChart', 'pieChart', 'areaChart']:
                            elements = root.findall(f'.//{{{root.tag.split("}")[0][1:]}}}{chart_type}')
                            if elements:
                                chart_types.append(chart_type)
                        
                        if chart_types:
                            print(f"   📊 图表类型: {', '.join(chart_types)}")
                        
                    except Exception as e:
                        print(f"   ⚠️  图表文件解析失败: {str(e)}")
            else:
                print(f"\n❌ 未发现图表文件")
            
            # 检查绘图关系
            if drawing_files:
                print(f"\n✅ 发现 {len(drawing_files)} 个绘图文件")
                
                # 检查工作表与绘图的关系
                print(f"\n🔗 检查工作表与绘图的关系:")
                
                for worksheet_file in worksheet_files:
                    try:
                        worksheet_content = zip_file.read(worksheet_file)
                        worksheet_root = ET.fromstring(worksheet_content)
                        
                        # 查找绘图引用
                        drawing_refs = worksheet_root.findall('.//{http://schemas.openxmlformats.org/spreadsheetml/2006/main}drawing')
                        
                        if drawing_refs:
                            worksheet_name = worksheet_file.split('/')[-1]
                            print(f"   📋 {worksheet_name}: 包含 {len(drawing_refs)} 个绘图引用")
                        
                    except Exception as e:
                        print(f"   ⚠️  工作表 {worksheet_file} 解析失败: {str(e)}")
            else:
                print(f"\n❌ 未发现绘图文件")
            
            # 总结
            print(f"\n📊 图表验证总结:")
            print(f"   - 图表文件: {len(chart_files)} 个")
            print(f"   - 绘图文件: {len(drawing_files)} 个")
            
            if chart_files and drawing_files:
                print(f"   ✅ Excel文件包含图表")
            else:
                print(f"   ❌ Excel文件不包含图表")
                
    except Exception as e:
        print(f"❌ 验证失败: {str(e)}")

def verify_multiple_files():
    """验证多个Excel文件"""
    test_files = [
        "simple_chart_test.xlsx",
        "完整报告_图表集成测试.xlsx",
        "图表工作表测试.xlsx"
    ]
    
    for file_name in test_files:
        if os.path.exists(file_name):
            verify_excel_charts(file_name)
            print("\n" + "="*80 + "\n")
        else:
            print(f"⏭️  跳过不存在的文件: {file_name}\n")

if __name__ == "__main__":
    verify_multiple_files()
