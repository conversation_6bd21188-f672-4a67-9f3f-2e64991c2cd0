#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试合并结果文件识别修复
验证合并结果文件是否能被正确处理
"""

import pandas as pd
import tempfile
import os
import shutil

def test_merge_result_file_detection():
    """测试合并结果文件的识别和处理"""
    print("🧪 测试合并结果文件的识别和处理")
    print("=" * 60)
    
    # 创建临时目录
    temp_dir = tempfile.mkdtemp()
    print(f"测试目录: {temp_dir}")
    
    try:
        # 创建原始Excel数据（作为参考结构）
        excel_data = pd.DataFrame({
            'VehicleID': ['车001', '车002', '车003'],
            'Direction': ['进', '出', '进'],
            'Time': ['2024-01-01 08:00:00', '2024-01-01 09:00:00', '2024-01-01 10:00:00'],
            'Gate': ['A门', 'B门', 'A门'],
            'Type': ['网约车', '网约车', '网约车'],
            'PlateNumber': ['浙A12345', '浙B67890', '浙C11111'],
            'Status': ['正常', '正常', '正常']
        })
        
        # 创建合并结果文件（列名不同但列数相同）
        merged_result_data = pd.DataFrame({
            '车牌号': ['浙D22222', '浙E33333'],
            '进出方向': ['出', '进'],
            '时间': ['2024-01-01 11:00:00', '2024-01-01 12:00:00'],
            '道闸': ['C门', 'D门'],
            '车辆类型': ['网约车', '网约车'],
            '车辆编号': ['车004', '车005'],
            '状态': ['正常', '正常']
        })
        
        # 创建文件
        excel_file = os.path.join(temp_dir, '原始数据.xlsx')
        merged_file = os.path.join(temp_dir, '义乌火车站道闸_网约车_合并_20250618_224630.csv')
        
        # 保存Excel文件
        excel_data.to_excel(excel_file, index=False)
        
        # 保存合并结果文件
        merged_result_data.to_csv(merged_file, index=False)
        
        print(f"\n📁 创建的测试文件:")
        print(f"  原始数据.xlsx: {len(excel_data)} 行, {len(excel_data.columns)} 列")
        print(f"    列名: {list(excel_data.columns)}")
        print(f"  {os.path.basename(merged_file)}: {len(merged_result_data)} 行, {len(merged_result_data.columns)} 列")
        print(f"    列名: {list(merged_result_data.columns)}")
        
        # 导入合并器
        from excel_data_merger import IntegratedDataMerger
        
        # 创建合并器
        merger = IntegratedDataMerger()
        
        # 执行合并
        output_file = os.path.join(temp_dir, 'result.csv')
        result = merger.smart_merge_directory(
            input_path=temp_dir,
            output_path=output_file,
            file_pattern="*",
            recursive=False
        )
        
        # 分析结果
        stats = merger.merge_stats
        
        print(f"\n📊 合并结果:")
        print(f"总数据源: {stats['total_sources']}")
        print(f"成功合并: {stats['merged_sources']}")
        print(f"重复数量: {stats['duplicate_sources']}")
        print(f"跳过数量: {stats['skipped_sources']}")
        print(f"合并行数: {stats['merged_rows']}")
        print(f"实际行数: {len(result)}")
        
        # 显示详细信息
        if stats['merged_details']:
            print(f"\n✅ 成功合并的文件:")
            for detail in stats['merged_details']:
                print(f"   - {detail['file_name']}: {detail['rows']} 行 ({detail['reason']})")
        
        if stats['duplicate_details']:
            print(f"\n🔄 重复检测详情:")
            for detail in stats['duplicate_details']:
                print(f"   - {detail['source']} → 重复于 {detail['duplicate_of']}")
        
        if stats['skipped_details']:
            print(f"\n⚠️ 跳过详情:")
            for detail in stats['skipped_details']:
                print(f"   - {detail['source']}: {detail['reason']}")
        
        # 验证结果
        print(f"\n🔍 预期结果:")
        print(f"  - Excel文件和CSV文件都应该被成功合并")
        print(f"  - 合并结果文件应该被识别为兼容（即使列名不同）")
        print(f"  - 不应该有'结构不兼容'的提示")
        
        expected_merged = 2  # Excel文件 + CSV文件
        expected_duplicates = 0  # 不应该有重复
        expected_skipped = 0  # 不应该有跳过
        
        # 检查是否有错误的"结构不兼容"提示
        has_incompatible_message = any(
            '结构不兼容' in detail.get('reason', '') 
            for detail in stats['skipped_details']
        )
        
        # 检查是否有"合并结果文件兼容"的提示
        has_merge_result_compatible = any(
            '合并结果文件兼容' in detail.get('reason', '') 
            for detail in stats['merged_details']
        )
        
        is_correct = (
            stats['merged_sources'] == expected_merged and
            stats['duplicate_sources'] == expected_duplicates and
            stats['skipped_sources'] == expected_skipped and
            not has_incompatible_message and
            has_merge_result_compatible
        )
        
        print(f"\n📋 结果评估:")
        if is_correct:
            print("✅ 合并结果文件识别修复成功！")
            print("   - 合并结果文件被正确识别并强制兼容")
            print("   - 没有错误的'结构不兼容'提示")
            print("   - 列名不同但列数相同的文件被正确处理")
        else:
            print("❌ 合并结果文件识别仍有问题！")
            print(f"   实际成功合并: {stats['merged_sources']} (预期: {expected_merged})")
            print(f"   实际重复数量: {stats['duplicate_sources']} (预期: {expected_duplicates})")
            print(f"   实际跳过数量: {stats['skipped_sources']} (预期: {expected_skipped})")
            
            if has_incompatible_message:
                print("   ⚠️ 仍然有错误的'结构不兼容'提示！")
                for detail in stats['skipped_details']:
                    if '结构不兼容' in detail.get('reason', ''):
                        print(f"      - {detail['source']}: {detail['reason']}")
            
            if not has_merge_result_compatible:
                print("   ⚠️ 没有检测到'合并结果文件兼容'的提示！")
        
        return is_correct
        
    except Exception as e:
        print(f"❌ 测试异常: {str(e)}")
        import traceback
        traceback.print_exc()
        return False
        
    finally:
        # 清理
        try:
            shutil.rmtree(temp_dir)
            print(f"\n🧹 清理测试目录")
        except:
            pass

def test_filename_pattern_detection():
    """测试文件名模式检测"""
    print("\n🧪 测试文件名模式检测")
    print("=" * 60)
    
    from excel_data_merger import IntegratedDataMerger
    merger = IntegratedDataMerger()
    
    # 测试各种文件名模式
    test_cases = [
        ("义乌火车站道闸_网约车_合并_20250618_224630.csv", True),
        ("data_merge_20240101.xlsx", True),
        ("combined_results.csv", True),
        ("merged_data_final.xlsx", True),
        ("原始数据.xlsx", False),
        ("traffic_data.csv", False),
        ("report_20240101.xlsx", False),
        ("合并结果.csv", True),
        ("数据合并_最终版.xlsx", True),
    ]
    
    print("文件名模式检测结果:")
    all_correct = True
    
    for filename, expected in test_cases:
        result = merger._is_merge_result_file(filename)
        status = "✅" if result == expected else "❌"
        print(f"  {status} {filename}: {result} (预期: {expected})")
        if result != expected:
            all_correct = False
    
    return all_correct

def main():
    """主函数"""
    print("🔧 测试合并结果文件识别修复")
    print("=" * 80)
    
    # 测试1: 合并结果文件的识别和处理
    result1 = test_merge_result_file_detection()
    
    # 测试2: 文件名模式检测
    result2 = test_filename_pattern_detection()
    
    print(f"\n📊 测试总结:")
    print("=" * 80)
    print(f"合并结果文件处理: {'✅ 正确' if result1 else '❌ 有问题'}")
    print(f"文件名模式检测: {'✅ 正确' if result2 else '❌ 有问题'}")
    
    if result1 and result2:
        print(f"\n🎉 所有测试通过！合并结果文件识别已修复！")
        print(f"   - 合并结果文件会被正确识别")
        print(f"   - 列名不同但列数相同的文件会被强制兼容")
        print(f"   - 不会再出现错误的'结构不兼容'提示")
    else:
        print(f"\n⚠️ 仍有问题需要进一步修复")

if __name__ == "__main__":
    main()
