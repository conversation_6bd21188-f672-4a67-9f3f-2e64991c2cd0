# 📊 紧凑车辆类型图表布局说明

## 🎯 功能概览

将多个车辆类型子图以紧凑方式并列显示，通过缩小每个子图的高度，使总体显示高度控制在600px左右，相当于一张标准图表的高度。

## 🔄 布局对比

### 修改前（标准布局）
- **子图高度**：每个子图500px高度
- **总体高度**：子图数量 × 500px + 间距
- **显示效果**：需要大量垂直滚动
- **适用场景**：详细分析单个车辆类型

### 修改后（紧凑布局）
- **子图高度**：动态计算，通常150-200px
- **总体高度**：控制在600px左右
- **显示效果**：一屏显示所有车辆类型
- **适用场景**：快速对比多个车辆类型

## 📏 尺寸计算逻辑

### 动态高度计算
```python
total_height = 600          # 目标总高度
title_space = 60           # 整体标题空间
spacing = 20 * (车辆类型数 - 1)  # 子图间距
available_height = total_height - title_space - spacing
sub_chart_height = max(150, available_height // 车辆类型数)
```

### 示例计算
- **3种车辆类型**：(600-60-40) ÷ 3 = 167px/图
- **4种车辆类型**：(600-60-60) ÷ 4 = 120px → 150px（最小值）
- **2种车辆类型**：(600-60-20) ÷ 2 = 260px/图

## 🎨 视觉优化

### 1. 字体尺寸调整
```python
title_textstyle_opts=opts.TextStyleOpts(
    font_size=14,          # 原18px → 14px
    font_weight="bold"
)

axislabel_opts=opts.LabelOpts(
    font_size=10           # 原12px → 10px
)

legend_textstyle_opts=opts.TextStyleOpts(
    font_size=10           # 原12px → 10px
)
```

### 2. 图例优化
```python
legend_opts=opts.LegendOpts(
    item_width=15,         # 缩小图例图标宽度
    item_height=10,        # 缩小图例图标高度
    item_gap=10           # 减少图例项间距
)
```

### 3. 轴标签优化
- **X轴标签**：只在最后一个子图显示，避免重复
- **X轴名称**：只在最后一个子图显示"时间段"
- **Y轴名称**：简化为"数量"

### 4. 空间节省
- 移除数据缩放控件（datazoom）
- 使用SimplePageLayout减少页面间距
- 优化标题和副标题的位置

## 🔧 技术实现

### 核心代码结构
```python
# 使用紧凑的页面布局
page = Page(layout=Page.SimplePageLayout)

# 动态计算子图高度
sub_chart_height = max(150, available_height // len(valid_vehicle_types))

# 创建紧凑子图
for i, vehicle_type in enumerate(valid_vehicle_types):
    chart = Bar(init_opts=opts.InitOpts(
        width="1200px",
        height=f"{sub_chart_height}px"  # 动态高度
    ))
    
    # 只在最后一个图显示X轴标签
    xaxis_opts=opts.AxisOpts(
        axislabel_opts=opts.LabelOpts(
            is_show=i == len(valid_vehicle_types) - 1
        )
    )
```

### 布局特点
1. **保持独立性**：每个车辆类型仍有独立的子图
2. **统一宽度**：所有子图保持1200px宽度
3. **动态高度**：根据车辆类型数量自动调整
4. **最小保障**：确保每个子图至少150px高度

## 📊 显示效果

### 1. 整体布局
- **垂直排列**：子图从上到下依次排列
- **紧凑间距**：子图间距减少到20px
- **统一样式**：所有子图使用一致的视觉风格

### 2. 子图内容
- **完整数据**：每个子图包含该车辆类型的进场和出场数据
- **独立交互**：每个子图可独立进行tooltip交互
- **颜色一致**：使用专业演讲风格的配对颜色

### 3. 可读性保障
- **最小高度**：确保每个子图至少150px高度
- **字体清晰**：虽然缩小但仍保持可读性
- **关键信息**：保留所有重要的数据标识

## 🎯 适用场景

### 1. 概览分析
- **快速对比**：一屏查看所有车辆类型的流量情况
- **趋势识别**：快速识别各车辆类型的流量模式
- **异常发现**：容易发现某个车辆类型的异常数据

### 2. 演示报告
- **管理汇报**：在有限的屏幕空间内展示完整信息
- **客户演示**：避免频繁滚动，保持演示流畅性
- **会议展示**：适合投影仪等显示设备

### 3. 打印输出
- **报告生成**：适合打印到A4纸张
- **文档嵌入**：可嵌入到Word或PDF文档中
- **存档备份**：紧凑格式便于长期保存

## 💡 使用建议

### 1. 最佳车辆类型数量
- **2-3种**：每个子图高度充足，显示效果最佳
- **4-5种**：紧凑但仍清晰可读
- **6种以上**：可能需要考虑分组显示

### 2. 数据质量要求
- **避免空数据**：空的车辆类型会被自动跳过
- **数据平衡**：各车辆类型数据量相近时效果最佳
- **时间段适中**：建议时间段数量不超过24个

### 3. 显示环境
- **屏幕分辨率**：建议1920x1080或更高
- **浏览器缩放**：建议100%缩放比例查看
- **打印设置**：横向打印效果更佳

## 🔄 版本更新

### v2.4 新功能
- ✅ 紧凑布局显示
- ✅ 动态高度计算
- ✅ 字体和元素尺寸优化
- ✅ 智能轴标签显示
- ✅ 空间利用率优化

### 兼容性
- 保持原有的数据处理逻辑
- 保持原有的颜色配置
- 保持原有的文件命名规则
- 不影响其他图表生成功能

---

*功能更新完成时间: 2025-06-20*  
*适用版本: parking_chart_generator.py v2.4+*
