# 🚗 停车数据图表生成器使用说明

## 📋 概述

`parking_chart_generator.py` 是一个专门用于读取停车分析Excel文件并生成交互式HTML图表的工具。它基于pyecharts库，可以生成美观的柱状图、折线图、饼图等多种图表类型。

## 🎯 主要功能

### ✅ 支持的图表类型
- **📈 进出量时间分布图** - 柱状图+趋势线组合
- **⏱️ 停车时长分布图** - 柱状图显示时长分布
- **🚙 车辆类型停车时长图** - 不同车型的平均停车时长
- **🚪 出入口流量占比图** - 饼图显示各出入口流量分布
- **📊 在场车辆分布图** - 面积图显示在场车辆变化
- **🔧 自定义图表** - 支持自定义数据列和图表类型
- **🏠 综合仪表板** - 包含所有图表的综合页面

### ✅ 支持的数据源
- Excel文件(.xlsx, .xls)
- 自动读取所有工作表
- 智能识别数据列

### ✅ 图表特性
- **交互式操作** - 支持缩放、平移、数据点悬停
- **可自定义样式** - 颜色、线条粗细、图表尺寸等
- **响应式设计** - 适配不同屏幕尺寸
- **HTML输出** - 可在任何浏览器中查看

## 🚀 快速开始

### 1. 基本用法

```python
from parking_chart_generator import ParkingChartGenerator

# 创建图表生成器
chart_generator = ParkingChartGenerator('your_excel_file.xlsx')

# 生成所有图表
chart_generator.generate_all_charts()

# 生成综合仪表板
chart_generator.generate_dashboard()
```

### 2. 自定义样式

```python
# 自定义图表样式
chart_generator.update_chart_config(
    bar_colors=['#1f77b4', '#ff7f0e', '#2ca02c'],  # 柱状图颜色
    line_colors=['#d62728', '#9467bd', '#8c564b'],  # 线条颜色
    line_width=4,        # 线条粗细
    chart_width='1400px', # 图表宽度
    chart_height='700px'  # 图表高度
)
```

### 3. 生成特定图表

```python
# 生成进出量时间分布图
chart_generator.generate_traffic_flow_chart()

# 生成停车时长分布图
chart_generator.generate_parking_duration_chart()

# 生成车辆类型图表
chart_generator.generate_vehicle_type_chart()
```

### 4. 自定义图表

```python
# 生成自定义柱状图
chart_generator.generate_custom_chart(
    sheet_name='进出量时间分布',
    chart_type='bar',
    x_col=0,              # X轴数据列索引
    y_cols=[1, 2],        # Y轴数据列索引
    title='自定义标题',
    colors=['#FF6B6B', '#4ECDC4']
)

# 生成自定义折线图
chart_generator.generate_custom_chart(
    sheet_name='进出量时间分布',
    chart_type='line',
    x_col=0,
    y_cols=[3],
    title='总流量趋势图'
)

# 生成自定义饼图
chart_generator.generate_custom_chart(
    sheet_name='出入口流量占比',
    chart_type='pie',
    x_col=0,
    y_cols=[1],
    title='流量分布饼图'
)
```

## 🎨 样式配置选项

### 可配置参数

| 参数 | 类型 | 说明 | 默认值 |
|------|------|------|--------|
| `theme` | ThemeType | 图表主题 | MACARONS |
| `bar_colors` | list | 柱状图颜色列表 | ['#5470c6', '#91cc75', '#fac858'] |
| `line_colors` | list | 线条颜色列表 | ['#ee6666', '#73c0de', '#3ba272'] |
| `line_width` | int | 线条粗细 | 3 |
| `chart_width` | str | 图表宽度 | '1200px' |
| `chart_height` | str | 图表高度 | '600px' |

### 主题选项

```python
from pyecharts.globals import ThemeType

# 可选主题
ThemeType.LIGHT      # 浅色主题
ThemeType.DARK       # 深色主题
ThemeType.CHALK      # 粉笔主题
ThemeType.ESSOS      # 艾索斯主题
ThemeType.INFOGRAPHIC # 信息图主题
ThemeType.MACARONS   # 马卡龙主题（默认）
ThemeType.PURPLE_PASSION # 紫色激情主题
ThemeType.ROMA       # 罗马主题
ThemeType.ROMANTIC   # 浪漫主题
ThemeType.SHINE      # 闪亮主题
ThemeType.VINTAGE    # 复古主题
ThemeType.WALDEN     # 瓦尔登主题
ThemeType.WESTEROS   # 维斯特洛主题
ThemeType.WONDERLAND # 仙境主题
```

## 📊 输出文件说明

### 生成的文件类型

1. **单个图表文件**
   - `进出量时间分布图表.html`
   - `停车时长分布图表.html`
   - `车辆类型停车时长图表.html`
   - `出入口流量占比图表.html`
   - `在场车辆分布图表.html`

2. **自定义图表文件**
   - `{工作表名}_{图表类型}图表.html`

3. **综合仪表板**
   - `停车数据分析仪表板.html` - 包含所有图表的综合页面

### 文件特点

- **独立运行** - 每个HTML文件都可以独立在浏览器中打开
- **交互功能** - 支持缩放、平移、数据点悬停等操作
- **响应式** - 自动适配不同屏幕尺寸
- **美观设计** - 专业的图表样式和布局

## 🔧 高级用法

### 批量处理

```python
import os
from parking_chart_generator import ParkingChartGenerator

# 批量处理多个Excel文件
excel_files = ['file1.xlsx', 'file2.xlsx', 'file3.xlsx']

for excel_file in excel_files:
    if os.path.exists(excel_file):
        chart_generator = ParkingChartGenerator(excel_file)
        chart_generator.generate_all_charts()
        print(f"✅ 已处理: {excel_file}")
```

### 自动打开图表

```python
import webbrowser
import os

# 生成图表后自动打开
chart_generator = ParkingChartGenerator('data.xlsx')
dashboard_file = chart_generator.generate_dashboard()

if dashboard_file:
    webbrowser.open(f'file://{os.path.abspath(dashboard_file)}')
```

## ❓ 常见问题

### Q: 如何修改Excel文件路径？
A: 在 `parking_chart_generator.py` 的 `main()` 函数中修改 `excel_file` 变量的值。

### Q: 图表显示不正常怎么办？
A: 检查Excel文件中的数据格式，确保数据列包含数值而不是文本。

### Q: 如何自定义图表颜色？
A: 使用 `update_chart_config()` 方法修改 `bar_colors` 和 `line_colors` 参数。

### Q: 生成的HTML文件很大怎么办？
A: 这是正常的，因为图表包含了完整的JavaScript库。可以通过减少数据点数量来优化。

### Q: 如何在服务器上部署？
A: 将生成的HTML文件上传到Web服务器即可，无需额外配置。

## 📞 技术支持

如果遇到问题，请检查：
1. Excel文件路径是否正确
2. 数据格式是否符合要求
3. 是否安装了必要的依赖包（pyecharts, pandas）

## 🎉 更新日志

- **v1.0** - 基础图表生成功能
- **v1.1** - 增加自定义图表功能
- **v1.2** - 增加综合仪表板功能
- **v1.3** - 增强样式配置选项
