# 🥧 出入口进场占比Timeline饼图功能说明

## 🎯 功能概览

新增出入口进场占比Timeline饼图，显示各出入口在每个时间段的进场量占比，采用美观的玫瑰图样式，与现有的Timeline图表形成完整的分析体系。

## 📊 图表体系完善

### 现有图表体系
1. **`出入口进出量_总量.html`** - 总量Timeline柱状图
2. **`出入口进出量_方向.html`** - 方向Timeline柱状图
3. **`出入口占比_进.html`** - 进场占比Timeline饼图 ← **新增**

### 分析维度互补
- **绝对数量**：总量和方向Timeline显示具体数值
- **相对占比**：饼图显示各出入口的相对比例
- **时间变化**：三个图表都支持时间段动态切换

## 🔧 技术实现

### 核心代码结构
```python
def generate_gate_entry_proportion_timeline(self, sheet_name='进出量时间分布(按道闸)'):
    """生成出入口进场占比Timeline饼图"""
    
    # 1. 数据提取 - 只使用进场列
    for i in range(gate_count):
        entry_col_idx = gate_start_col + i * 3  # 每组第1列：进场
        
    # 2. Timeline饼图创建
    for time_period in time_periods:
        period_entry_data = [[gate_name, entry_value], ...]
        pie = Pie().add("进场占比", period_entry_data, rosetype="radius")
        timeline.add(pie, time_period)
```

### 按您提供的代码模式实现
```python
# 完全按照您的代码结构
timeline = Timeline()
for time_period in time_periods:
    pie = (
        Pie()
        .add(
            "进场占比",
            period_entry_data,  # [["出入口A", 值], ["出入口B", 值], ...]
            rosetype="radius",
            radius=["30%", "55%"],
        )
        .set_global_opts(title_opts=opts.TitleOpts(f"出入口进场占比 - {time_period}"))
    )
    timeline.add(pie, time_period)
timeline.render("出入口占比_进.html")
```

## 🎨 视觉设计

### 1. 玫瑰图样式
```python
.add(
    series_name="进场占比",
    data_pair=period_entry_data,
    rosetype="radius",      # 玫瑰图样式
    radius=["30%", "55%"],  # 内外半径
)
```

### 2. 专业演讲风格颜色
```python
pie_colors = [
    "#2E86AB", "#A23B72", "#F18F01", "#148F77", "#7D3C98", 
    "#2874A6", "#117A65", "#5B2C6F", "#B7950B", "#D35400",
    "#C0392B", "#E67E22", "#922B21", "#1B4F72"
]
```

### 3. 标签和图例配置
- **标签位置**：外部显示
- **标签格式**：`{出入口名}: {数量}辆\n({占比}%)`
- **图例位置**：左侧垂直排列
- **边框效果**：白色边框，增强立体感

## 📈 功能特点

### 1. 占比分析
- **相对比例**：显示各出入口进场量的相对占比
- **直观对比**：通过扇区大小直观比较占比差异
- **百分比显示**：精确显示每个出入口的占比数值

### 2. 时间维度
- **动态切换**：通过时间轴查看不同时间段的占比变化
- **趋势观察**：观察各出入口占比随时间的变化趋势
- **播放间隔**：3秒间隔，适合饼图观察

### 3. 玫瑰图美学
- **径向展示**：扇区半径反映数值大小
- **美观效果**：比传统饼图更具视觉冲击力
- **专业外观**：适合正式演示和汇报

## 🎯 应用场景

### 1. 流量分布分析
- **主要入口识别**：识别承担主要进场流量的出入口
- **负载均衡评估**：评估各出入口的负载分布是否均衡
- **容量规划**：为各出入口的容量规划提供依据

### 2. 运营管理
- **资源配置**：根据占比分配管理资源和人员
- **效率优化**：优化高占比出入口的运营效率
- **瓶颈预防**：预防高占比出入口成为瓶颈

### 3. 决策支持
- **政策制定**：为进场管理政策提供数据支持
- **设施投资**：为出入口设施投资决策提供依据
- **应急预案**：为高流量出入口制定应急预案

## 📊 数据处理

### 1. 数据提取
- **进场列识别**：自动识别每个出入口的进场数据列
- **数据清洗**：处理空值和异常值
- **占比计算**：计算每个出入口在总进场量中的占比

### 2. 特殊情况处理
```python
# 处理总进场量为0的情况
if total_entry == 0:
    # 为每个出入口分配相等的默认值
    period_entry_data = [[gate_name, 1] for gate_name in gate_entry_data.keys()]
```

### 3. 数据格式
```python
# 饼图数据格式：[["出入口名", 数值], ...]
period_entry_data = [
    ["出入口A", 120],
    ["出入口B", 80],
    ["出入口C", 150]
]
```

## 🎮 交互功能

### 1. Timeline控制
- **时间轴滑块**：手动选择时间段
- **自动播放**：3秒间隔自动播放
- **循环播放**：支持循环观看
- **长时间轴**：与X轴对齐，操作精确

### 2. 饼图交互
- **Tooltip**：鼠标悬停显示详细信息
- **图例控制**：点击图例显示/隐藏扇区
- **扇区高亮**：鼠标悬停扇区高亮显示

### 3. 信息展示
- **标题动态**：显示当前时间段
- **数值显示**：显示具体数量和占比
- **格式化**：`出入口A: 120辆\n(35.3%)`

## 💡 分析技巧

### 1. 占比变化分析
- 使用自动播放观察占比随时间的变化
- 识别占比稳定和波动较大的出入口
- 分析占比变化的规律和原因

### 2. 主要入口识别
- 查找占比最大的出入口
- 分析主要入口在不同时间段的表现
- 评估主要入口的稳定性

### 3. 均衡性评估
- 观察各出入口占比是否均衡
- 识别占比过高或过低的出入口
- 评估流量分布的合理性

## 🔄 版本更新

### v3.0 新功能
- ✅ 出入口进场占比Timeline饼图
- ✅ 玫瑰图样式美观展示
- ✅ 专业演讲风格颜色配置
- ✅ 3秒播放间隔优化
- ✅ 完善的出入口图表体系

### 图表体系完整性
- **三个维度**：总量、方向、占比
- **统一交互**：相同的Timeline操作体验
- **互补分析**：提供全面的出入口流量分析

---

*功能开发完成时间: 2025-06-20*  
*适用版本: parking_chart_generator.py v3.0+*
