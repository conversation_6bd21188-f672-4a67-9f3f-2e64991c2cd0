"""
数据处理器模块，负责停车数据的预处理和标准化
"""

import pandas as pd
from parking_data_base import ParkingDataBase

class DataProcessor(ParkingDataBase):
    """数据处理器，继承自ParkingDataBase"""
    
    def __init__(self, data, params, logger=None):
        """
        初始化数据处理器
        
        Args:
            data: DataFrame, 原始数据
            params: dict, 处理参数
            logger: Logger对象, 用于日志记录
        """
        # 调用父类初始化
        super().__init__(data, params, logger)
        self.processed_records = 0
    
    def process(self):
        """
        处理数据，扩展基类的处理方法
        
        Returns:
            DataFrame: 处理后的数据
        """
        try:
            # 使用基类的数据处理方法
            processed_data = super().process()

            # 额外的数据清理和验证
            processed_data = self._additional_processing(processed_data)

            # 记录处理后的记录数
            self.processed_records = len(processed_data)

            return processed_data
            
        except Exception as e:
            self._log_error(f"数据处理失败: {str(e)}")
            raise
    
    def _additional_processing(self, data):
        """
        执行额外的数据处理步骤
        
        Args:
            data: DataFrame, 基础处理后的数据
            
        Returns:
            DataFrame: 额外处理后的数据
        """
        if self.logger:
            self.logger.info(f"\n原始数据信息:")
            self.logger.info(f"数据列: {self.raw_data.columns.tolist()}")
            self.logger.info(f"数据行数: {len(self.raw_data)}")
        
        # 确保时间字段存在
        if 'timestamp' not in data.columns:
            if 'entry_time' in data.columns:
                data['timestamp'] = data['entry_time']
            elif 'exit_time' in data.columns:
                data['timestamp'] = data['exit_time']
        
        # 处理进出方向字段
        if 'direction' not in data.columns:
            # 如果有entry_time和exit_time，根据时间判断方向
            if 'entry_time' in data.columns and 'exit_time' in data.columns:
                # 创建方向字段
                data['direction'] = None
                # 获取进出标识值
                entry_value = self.params.get('进出标识值', ['入场'])[0]
                exit_value = self.params.get('进出标识值', ['出场'])[1] if len(self.params.get('进出标识值', ['入场', '出场'])) > 1 else '出场'
                
                # 根据时间设置方向
                data.loc[~data['entry_time'].isna(), 'direction'] = entry_value
                data.loc[~data['exit_time'].isna(), 'direction'] = exit_value
                
                if self.logger:
                    self.logger.debug(f"根据时间设置进出方向: 入场={entry_value}, 出场={exit_value}")
                    self.logger.debug(f"入场记录数: {len(data[data['direction'] == entry_value])}")
                    self.logger.debug(f"出场记录数: {len(data[data['direction'] == exit_value])}")
            else:
                # 如果没有时间字段，使用默认值
                data['direction'] = self.params.get('进出标识值', ['入场'])[0]
                if self.logger:
                    self.logger.debug(f"设置默认进出方向: {data['direction']}")
        
                    # 处理道闸字段
                    if 'gate' not in data.columns:
                        # 创建gate字段
                        data['gate'] = None
                
                        # 获取字段映射配置
                        entry_gate_field = self.params.get('进场道闸编号字段', 'entry_gate')
                        exit_gate_field = self.params.get('出场道闸编号字段', 'exit_gate')
                
                        # 检查中文道闸字段
                        chinese_fields = {
                            'entry': ['入场设备', '进场设备', '入口设备'],
                            'exit': ['出场设备', '离场设备', '出口设备']
                        }
                
                        # 自动检测中文道闸字段
                        for field in chinese_fields['entry']:
                            if field in data.columns:
                                entry_gate_field = field
                                break
                        
                        for field in chinese_fields['exit']:
                            if field in data.columns:
                                exit_gate_field = field
                                break
            
            # 根据方向设置道闸
            if entry_gate_field in data.columns and exit_gate_field in data.columns:
                entry_value = self.params.get('进出标识值', ['入场'])[0]
                # 根据方向选择对应的道闸
                data.loc[data['direction'] == entry_value, 'gate'] = data.loc[data['direction'] == entry_value, entry_gate_field]
                data.loc[data['direction'] != entry_value, 'gate'] = data.loc[data['direction'] != entry_value, exit_gate_field]
                
                if self.logger:
                    self.logger.debug("根据方向设置道闸:")
                    self.logger.debug(f"进场道闸字段: {entry_gate_field}")
                    self.logger.debug(f"出场道闸字段: {exit_gate_field}")
                    self.logger.debug(f"入场道闸样本: {data[data['direction'] == entry_value]['gate'].head(5).tolist()}")
                    self.logger.debug(f"出场道闸样本: {data[data['direction'] != entry_value]['gate'].head(5).tolist()}")
                    self.logger.debug(f"入场记录数: {len(data[data['direction'] == entry_value])}")
                    self.logger.debug(f"出场记录数: {len(data[data['direction'] != entry_value])}")
            elif entry_gate_field in data.columns:
                data['gate'] = data[entry_gate_field]
            elif exit_gate_field in data.columns:
                data['gate'] = data[exit_gate_field]
            
        # 移除重复记录，保留所有字段
        data = data.drop_duplicates()

        # 对所有模式，按进出场时间排序
        if 'entry_time' in data.columns and 'exit_time' in data.columns:
            data = data.sort_values(['entry_time', 'exit_time'])
            # 处理无效的停车记录（入场时间晚于出场时间）
            if 'entry_time' in data.columns and 'exit_time' in data.columns:
                invalid_records = data[data['entry_time'] > data['exit_time']]
                if len(invalid_records) > 0 and self.logger:
                    self.logger.warning(f"发现 {len(invalid_records)} 条无效记录（入场时间晚于出场时间）")
                    # 不直接移除，而是修正时间顺序
                    data.loc[data['entry_time'] > data['exit_time'], 'exit_time'] = data['entry_time'] + pd.Timedelta(hours=1)

        return data
    
    def _log_error(self, message):
        """记录错误信息"""
        if self.logger:
            self.logger.error(message)
    
    def get_processing_stats(self):
        """
        获取数据处理统计信息
        
        Returns:
            dict: 包含处理统计信息的字典
        """
        return {
            'total_records': len(self.raw_data) if self.raw_data is not None else 0,
            'processed_records': self.processed_records,
            'success_rate': round(self.processed_records / len(self.raw_data) * 100, 2) if self.raw_data is not None else 0
        }