#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试TimePeriodAnalyzer中overview结果的修复
验证修复后是否正确生成overview数据
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from parking_data_processor import DataProcessor
from parking_time_filter import TimeFilter
from parking_analyzer import TimePeriodAnalyzer

def create_test_data():
    """创建测试数据"""
    np.random.seed(42)
    
    records = []
    base_date = datetime(2024, 6, 1)
    
    for i in range(50):
        entry_time = base_date + timedelta(hours=i % 24, minutes=np.random.randint(0, 60))
        duration = np.random.uniform(1, 8)
        exit_time = entry_time + timedelta(hours=duration)
        
        records.append({
            '车牌号码': f"京A{i:05d}",
            '车辆类型': "小型车" if i % 2 == 0 else "大型车",
            '进场时间': entry_time,
            '出场时间': exit_time,
            '进场道闸': f"入口{(i % 3) + 1}",
            '出场道闸': f"出口{(i % 3) + 1}",
            '停车时长': f"{duration:.2f}小时"
        })
    
    return pd.DataFrame(records)

def test_overview_fix():
    """测试overview修复效果"""
    print("=" * 80)
    print("测试TimePeriodAnalyzer中overview结果的修复")
    print("验证修复后是否正确生成overview数据")
    print("=" * 80)
    
    try:
        # 1. 创建测试数据
        print(f"\n{'='*60}")
        print("1. 创建测试数据")
        print('='*60)
        
        raw_data = create_test_data()
        print(f"原始数据记录数: {len(raw_data)}")
        print(f"原始数据列: {list(raw_data.columns)}")
        
        # 2. 设置参数
        params = {
            'mode': 'mode2',
            '车辆唯一标识字段': '车牌号码',
            '车辆类型字段': '车辆类型',
            '进场时间字段': '进场时间',
            '出场时间字段': '出场时间',
            '进场道闸编号字段': '进场道闸',
            '出场道闸编号字段': '出场道闸',
            'time_interval': 60,
            'time_slip': 15,
            'date': '2024-06-01'
        }
        
        print(f"参数配置: {params}")
        
        # 3. 数据处理
        print(f"\n{'='*60}")
        print("3. 数据处理")
        print('='*60)
        
        processor = DataProcessor(raw_data, params)
        processed_data = processor.process()
        
        print(f"处理后数据记录数: {len(processed_data)}")
        print(f"处理后数据列: {list(processed_data.columns)}")
        
        # 4. 时间过滤
        print(f"\n{'='*60}")
        print("4. 时间过滤")
        print('='*60)
        
        time_filter = TimeFilter(processed_data, params)
        filtered_data = time_filter.get_filtered_data()
        period_info = time_filter.detect_period()
        
        print(f"过滤后数据记录数: {len(filtered_data)}")
        print(f"时间周期信息: {period_info}")
        
        # 5. 测试修复前后的分析结果
        print(f"\n{'='*60}")
        print("5. 测试修复前后的分析结果")
        print('='*60)
        
        # 创建分析器
        analyzer = TimePeriodAnalyzer(
            filtered_data,
            len(raw_data),
            mode=params['mode'],
            period_info=period_info,
            params=params
        )
        
        print(f"分析器初始化成功")
        print(f"  总记录数: {analyzer.total_records}")
        print(f"  当前数据记录数: {len(analyzer.processed_data)}")
        print(f"  处理模式: {analyzer.mode}")
        
        # 执行分析
        analysis_results = analyzer.analyze(
            focus_date=params.get('date')
        )
        
        print(f"\n分析完成，结果键: {list(analysis_results.keys())}")
        
        # 6. 检查overview结果
        print(f"\n{'='*60}")
        print("6. 检查overview结果")
        print('='*60)
        
        if 'overview' in analysis_results:
            print("✅ overview结果存在")
            overview = analysis_results['overview']
            
            print(f"\noverview内容:")
            for key, value in overview.items():
                if isinstance(value, dict):
                    print(f"  {key}: {value}")
                elif isinstance(value, float):
                    print(f"  {key}: {value:.2f}")
                else:
                    print(f"  {key}: {value}")
            
            # 验证关键字段
            required_fields = [
                'original_total_records',
                'total_records', 
                'filtered_percentage',
                'unique_vehicles',
                'analysis_mode'
            ]
            
            missing_fields = [f for f in required_fields if f not in overview]
            
            if missing_fields:
                print(f"⚠️  缺少关键字段: {missing_fields}")
            else:
                print(f"✅ 所有关键字段都存在")
            
            # 验证数据正确性
            print(f"\n数据正确性验证:")
            
            # 检查总记录数
            expected_original = len(raw_data)
            actual_original = overview['original_total_records']
            if actual_original == expected_original:
                print(f"✅ 原始总记录数正确: {actual_original}")
            else:
                print(f"❌ 原始总记录数错误: 期望{expected_original}, 实际{actual_original}")
            
            # 检查有效记录数
            expected_total = len(filtered_data)
            actual_total = overview['total_records']
            if actual_total == expected_total:
                print(f"✅ 有效记录数正确: {actual_total}")
            else:
                print(f"❌ 有效记录数错误: 期望{expected_total}, 实际{actual_total}")
            
            # 检查过滤百分比
            expected_percentage = len(filtered_data) / len(raw_data) * 100
            actual_percentage = overview['filtered_percentage']
            if abs(actual_percentage - expected_percentage) < 0.01:
                print(f"✅ 过滤百分比正确: {actual_percentage:.2f}%")
            else:
                print(f"❌ 过滤百分比错误: 期望{expected_percentage:.2f}%, 实际{actual_percentage:.2f}%")
            
            # 检查唯一车辆数
            expected_vehicles = filtered_data['vid'].nunique()
            actual_vehicles = overview['unique_vehicles']
            if actual_vehicles == expected_vehicles:
                print(f"✅ 唯一车辆数正确: {actual_vehicles}")
            else:
                print(f"❌ 唯一车辆数错误: 期望{expected_vehicles}, 实际{actual_vehicles}")
            
            # 检查分析模式
            expected_mode = params['mode']
            actual_mode = overview['analysis_mode']
            if actual_mode == expected_mode:
                print(f"✅ 分析模式正确: {actual_mode}")
            else:
                print(f"❌ 分析模式错误: 期望{expected_mode}, 实际{actual_mode}")
            
        else:
            print("❌ overview结果不存在")
            return False
        
        # 7. 测试其他分析结果是否受影响
        print(f"\n{'='*60}")
        print("7. 测试其他分析结果是否受影响")
        print('='*60)
        
        expected_keys = [
            'basic_stats', 'time_distribution', 'vehicle_types', 'gate_usage',
            'period_analysis', 'period_comparison', 'gate_pairs_analysis', 'overview'
        ]
        
        missing_keys = [k for k in expected_keys if k not in analysis_results]
        extra_keys = [k for k in analysis_results.keys() if k not in expected_keys]
        
        if missing_keys:
            print(f"⚠️  缺少分析结果: {missing_keys}")
        else:
            print(f"✅ 所有预期的分析结果都存在")
        
        if extra_keys:
            print(f"ℹ️  额外的分析结果: {extra_keys}")
        
        # 检查各分析结果是否为空
        for key, value in analysis_results.items():
            if isinstance(value, dict):
                if value:
                    print(f"✅ {key}: 有数据 ({len(value)} 项)")
                else:
                    print(f"⚠️  {key}: 无数据")
            else:
                print(f"ℹ️  {key}: {type(value).__name__}")
        
        # 8. 总结
        print(f"\n{'='*60}")
        print("8. 修复效果总结")
        print('='*60)
        
        success_checks = [
            'overview' in analysis_results,
            len(missing_fields) == 0 if 'overview' in analysis_results else False,
            overview['original_total_records'] == len(raw_data) if 'overview' in analysis_results else False,
            overview['total_records'] == len(filtered_data) if 'overview' in analysis_results else False,
            len(missing_keys) == 0
        ]
        
        success_count = sum(success_checks)
        total_checks = len(success_checks)
        success_rate = success_count / total_checks * 100
        
        print(f"修复效果评估: {success_rate:.1f}% ({success_count}/{total_checks})")
        
        if success_rate == 100:
            print("🎉 修复完全成功！")
            print("✅ overview结果正确生成")
            print("✅ 所有关键字段都存在且正确")
            print("✅ 其他分析结果未受影响")
            print("✅ 功能和输出结果保持不变")
        elif success_rate >= 80:
            print("✅ 修复基本成功")
            print("大部分功能正常，可能有小问题需要调整")
        else:
            print("⚠️  修复效果不理想")
            print("需要进一步检查和改进")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    print("\n" + "=" * 80)
    print("overview修复测试完成！")
    print("=" * 80)
    
    return True

if __name__ == "__main__":
    test_overview_fix()
