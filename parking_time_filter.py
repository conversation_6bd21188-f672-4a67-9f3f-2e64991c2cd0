import pandas as pd
import re
from datetime import datetime

class TimeFilter:
    # 在类初始化中添加配置管理器参数
    def __init__(self, data, params, config_manager=None):
        """初始化时间筛选器"""
        self.data = data
        self.params = params
        
        # 获取配置
        self.config_manager = config_manager
        if config_manager:
            self.logger = config_manager.get_logger()
            # 合并配置中的时间过滤参数
            time_config = config_manager.get_time_filter_config()
            for key, value in time_config.items():
                if key not in self.params:
                    self.params[key] = value
        else:
            self.logger = None
        
    def detect_period(self):
        """检测数据时间跨度，确定分析粒度（年/月/日）"""
        try:
            # 计算时间跨度
            start = self.data['entry_time'].min()
            end = self.data['entry_time'].max()
            delta = end - start

            # 推断时间粒度
            if delta.days >= 365:
                return {'unit': 'year', 'days': delta.days}
            elif delta.days >= 30:
                return {'unit': 'month', 'days': delta.days}
            else:
                return {'unit': 'day', 'days': delta.days}
        except Exception as e:
            raise ValueError(f"时间检测失败: {e}")
    
    def get_filtered_data(self):
        """根据聚焦日期或月份获取过滤后的数据"""
        focus_date = self.params.get('聚焦日期')
        focus_month = self.params.get('聚焦月份')
        
        # 确保数据存在且不为空
        if self.data is None or self.data.empty:
            if self.logger:
                self.logger.warning("输入数据为空")
            return pd.DataFrame()
        
        # 确保时间字段存在且格式正确
        required_fields = ['entry_time', 'exit_time']
        if not all(field in self.data.columns for field in required_fields):
            if self.logger:
                self.logger.error(f"缺少必要的时间字段: {required_fields}")
            return pd.DataFrame()
        
        # 复制数据以避免修改原始数据
        filtered_data = self.data.copy()
        
        # 确保时间字段为datetime类型
        for field in required_fields:
            if not pd.api.types.is_datetime64_any_dtype(filtered_data[field]):
                try:
                    filtered_data[field] = pd.to_datetime(filtered_data[field], errors='coerce')
                except Exception as e:
                    if self.logger:
                        self.logger.error(f"时间字段 {field} 转换失败: {str(e)}")
                    return pd.DataFrame()
        
        # 移除时间字段中的NaT值
        filtered_data = filtered_data.dropna(subset=required_fields)
        
        if focus_date:
            try:
                # 转换聚焦日期为datetime
                target_date = pd.to_datetime(focus_date).normalize()
                next_date = target_date + pd.Timedelta(days=1)
                
                # 筛选条件：
                # 1. 入场时间在目标日期之前（含），出场时间在目标日期当天或之后
                # 2. 入场时间在目标日期当天，出场时间在任何时间
                mask = (
                    ((filtered_data['entry_time'].dt.normalize() <= target_date) & 
                     (filtered_data['exit_time'].dt.normalize() >= target_date)) |
                    ((filtered_data['entry_time'].dt.normalize() == target_date))
                )
                
                filtered_data = filtered_data[mask].copy()
                
                if filtered_data.empty and self.logger:
                    self.logger.warning(f"日期 {focus_date} 筛选后数据为空")
                
            except Exception as e:
                if self.logger:
                    self.logger.error(f"日期过滤错误: {str(e)}")
                return pd.DataFrame()
                
        elif focus_month:
            try:
                # 转换聚焦月份为period
                target_month = pd.to_datetime(focus_month).to_period('M')
                
                # 筛选条件：
                # 1. 入场时间在目标月份
                # 2. 出场时间在目标月份
                # 3. 停车时间跨越目标月份（入场在之前，出场在之后）
                mask = (
                    (filtered_data['entry_time'].dt.to_period('M') == target_month) |
                    (filtered_data['exit_time'].dt.to_period('M') == target_month) |
                    ((filtered_data['entry_time'].dt.to_period('M') < target_month) & 
                     (filtered_data['exit_time'].dt.to_period('M') > target_month))
                )
                
                filtered_data = filtered_data[mask].copy()
                
                if filtered_data.empty and self.logger:
                    self.logger.warning(f"月份 {focus_month} 筛选后数据为空")
                    
            except Exception as e:
                if self.logger:
                    self.logger.error(f"月份过滤错误: {str(e)}")
                return pd.DataFrame()
        
        # 确保结果数据的时间字段仍然是datetime类型
        for field in required_fields:
            if not pd.api.types.is_datetime64_any_dtype(filtered_data[field]):
                filtered_data[field] = pd.to_datetime(filtered_data[field])
        
        return filtered_data
    
    def get_focus_month_display(self):
        """智能获取月份显示值，优先从聚焦日期提取"""
        focus_date = self.params.get('聚焦日期')
        if focus_date:
            try:
                # 严格解析日期格式
                dt = pd.to_datetime(focus_date)
                return dt.strftime('%Y-%m')
            except:
                # 容错处理非常规格式
                parts = re.split(r'[-/]', focus_date)
                if len(parts) >= 2:
                    return f"{parts[0]}-{parts[1].zfill(2)}"
                return focus_date[:7]  # 截取前7位作为保底方案
        return self.params.get('聚焦月份', '')

    def split_time_period(self, start_time="00:00", end_time="23:59", slip_time=60, interval_minutes=60):
        """
        滑动窗口式时间段分割
        
        参数:
            start_time: str - 起始时间(HH:MM格式)，默认"00:00"
            end_time: str - 结束时间(HH:MM格式)，默认"23:59"
            slip_time: int - 滑动步长(分钟)，默认60
            interval_minutes: int - 每个时间段的长度(分钟)，默认60
        """
        try:
            # 验证参数有效性
            if interval_minutes <= 0 or slip_time <= 0:
                raise ValueError("时间间隔和滑动步长必须大于0")
            if interval_minutes < slip_time:
                raise ValueError("时间间隔不能小于滑动步长")

            # 验证时间格式
            try:
                start_h, start_m = map(int, start_time.split(':'))
                end_h, end_m = map(int, end_time.split(':'))
            except ValueError:
                raise ValueError("时间格式错误，请使用HH:MM格式")
                
            # 验证时间范围有效性
            if not (0 <= start_h <= 23 and 0 <= start_m <= 59 and
                    0 <= end_h <= 23 and 0 <= end_m <= 59):
                raise ValueError("时间值无效，小时应在0-23之间，分钟应在0-59之间")
            
            # 特殊处理：如果是标准24小时时间段（整点小时）
            if slip_time == 60 and interval_minutes == 60 and start_time == "00:00" and end_time == "23:59":
                # 直接生成24个整点小时时间段
                time_slices = []
                for h in range(24):
                    if h == 23:
                        # 特殊处理23点时段，结束时间设为23:59而不是00:00
                        time_slices.append(f"23:00-23:59")
                    else:
                        time_slices.append(f"{h:02d}:00-{(h+1):02d}:00")
                return time_slices
                
            # 计算总分钟数
            total_minutes = (end_h * 60 + end_m) - (start_h * 60 + start_m)
            if total_minutes <= 0:
                total_minutes += 24 * 60  # 处理跨天情况
                
            # 计算需要的时间段数量
            num_periods = max(1, int(total_minutes / slip_time))
            
            time_slices = []
            current_min = start_h * 60 + start_m
            
            for i in range(num_periods):
                # 边界检查：处理≥24:00的起始时间点
                if current_min >= 1440:  # 1440分钟=24小时
                    current_min = 1439  # 设置为23:59

                # 计算当前时间段的结束分钟数（不使用模运算，先检查是否超过24:00）
                raw_end_min = current_min + interval_minutes

                # 转换为HH:MM格式
                start_hh = (current_min // 60) % 24
                start_mm = current_min % 60

                # 特殊处理：当结束时间等于或超过24:00时，设置为23:59
                if raw_end_min >= 1440:  # 超过24:00
                    time_slice = f"{start_hh:02d}:{start_mm:02d}-23:59"
                else:
                    # 正常情况下的结束时间
                    end_hh = (raw_end_min // 60) % 24
                    end_mm = raw_end_min % 60
                    time_slice = f"{start_hh:02d}:{start_mm:02d}-{end_hh:02d}:{end_mm:02d}"
                
                time_slices.append(time_slice)
                
                # 滑动到下一个时间段
                current_min += slip_time

                # 如果超过24小时，结束循环
                if current_min >= 24 * 60:
                    break
            
            # 确保包含23:00开头的时间段
            has_23_hour = any(period.startswith("23:00") for period in time_slices)
            if not has_23_hour and start_time == "00:00" and end_time == "23:59":
                time_slices.append("23:00-23:59")
            
            return time_slices

        except Exception as e:
            if self.logger:
                self.logger.error(f"时间段分割失败: {str(e)}")
            raise

    def create_duration_periods(self, control_points=None, period_lengths=None):
        """
        创建基于停车时长的分段时间段

        参数:
            control_points: list - 控制点列表(小时)，默认[2, 8, 24, 72]
            period_lengths: list - 各时段的长度(小时)，默认[0.5, 1, 16, 48]

        返回:
            list - 时间段标签列表，格式如["0-30分钟", "30分钟-1小时", ...]

        示例:
            默认配置生成时段：
            - 第1段：0-2小时，每0.5小时(30分钟)一个时段 (4个时段)
            - 第2段：2-8小时，每1小时一个时段 (6个时段)
            - 第3段：8-24小时，整段作为1个时段 (1个时段)
            - 第4段：24-72小时(1-3天)，整段作为1个时段 (1个时段)
            - 第5段：>72小时(>3天)，整段作为1个时段 (1个时段)
        """
        try:
            # 设置默认值（以小时为单位）
            if control_points is None:
                control_points = [2, 8, 24, 72]  # 2小时, 8小时, 1天, 3天
            if period_lengths is None:
                period_lengths = [0.5, 1, 16, 48]  # 30分钟, 1小时, 16小时, 2天

            # 转换为分钟进行内部计算
            control_points_minutes = [int(cp * 60) for cp in control_points]
            period_lengths_minutes = [int(pl * 60) for pl in period_lengths]

            # 验证参数
            if len(period_lengths) != len(control_points):
                raise ValueError("period_lengths的长度必须等于control_points的长度")

            if not all(cp > 0 for cp in control_points):
                raise ValueError("所有控制点必须大于0")

            if not all(pl > 0 for pl in period_lengths):
                raise ValueError("所有时段长度必须大于0")

            # 确保控制点按升序排列
            if control_points != sorted(control_points):
                raise ValueError("控制点必须按升序排列")

            time_periods = []
            current_start = 0

            # 处理每个控制段
            for i, (control_point_minutes, period_length_minutes) in enumerate(zip(control_points_minutes, period_lengths_minutes)):
                segment_start = current_start
                segment_end = control_point_minutes

                # 如果时段长度大于等于段长度，则整段作为一个时段
                if period_length_minutes >= (segment_end - segment_start):
                    # 使用格式化方法来生成标签
                    start_label = self._format_duration_label(segment_start)
                    end_label = self._format_duration_label(segment_end)
                    time_periods.append(f"{start_label}-{end_label}")
                else:
                    # 按指定长度分割时段
                    current_pos = segment_start
                    while current_pos < segment_end:
                        period_end = min(current_pos + period_length_minutes, segment_end)

                        # 格式化时间标签
                        start_label = self._format_duration_label(current_pos)
                        end_label = self._format_duration_label(period_end)

                        time_periods.append(f"{start_label}-{end_label}")
                        current_pos = period_end

                current_start = control_point_minutes

            # 添加最后一个时段（大于最后一个控制点）
            last_control_point_minutes = control_points_minutes[-1]
            start_label = self._format_duration_label(last_control_point_minutes)

            time_periods.append(f">{start_label}")

            return time_periods

        except Exception as e:
            if self.logger:
                self.logger.error(f"时长分段创建失败: {str(e)}")
            raise

    def _format_duration_label(self, minutes):
        """
        格式化时长标签

        参数:
            minutes: int - 分钟数

        返回:
            str - 格式化的时长标签
        """
        if minutes == 0:
            return "0分钟"
        elif minutes < 60:
            return f"{minutes}分钟"
        elif minutes < 1440:  # 小于1天
            hours = minutes // 60
            remaining_minutes = minutes % 60
            if remaining_minutes == 0:
                return f"{hours}小时"
            else:
                return f"{hours}小时{remaining_minutes}分钟"
        else:  # 大于等于1天
            days = minutes // 1440
            remaining_minutes = minutes % 1440
            hours = remaining_minutes // 60
            remaining_minutes = remaining_minutes % 60

            if remaining_minutes == 0 and hours == 0:
                if days == 1:
                    return "1天"
                else:
                    return f"{days}天"
            elif remaining_minutes == 0:
                if days == 1:
                    return f"1天{hours}小时"
                else:
                    return f"{days}天{hours}小时"
            else:
                if days == 1:
                    return f"1天{hours}小时{remaining_minutes}分钟"
                else:
                    return f"{days}天{hours}小时{remaining_minutes}分钟"