#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试分析周期_每日sheet中的道闸进出量统计功能
验证是否正确添加了道闸1_进、道闸1_出等列
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import tempfile
import os

def create_gate_test_data():
    """创建包含多个道闸的测试数据"""
    records = []
    base_date = datetime(2024, 6, 1)
    
    # 创建多天的数据，包含不同道闸
    gates = ["入口A道闸", "入口B道闸", "出口A道闸", "出口B道闸"]
    vehicle_types = ["小型车", "大型车"]
    
    for day in range(3):  # 3天的数据
        current_date = base_date + timedelta(days=day)
        
        # 每天创建一些记录，分布在不同道闸
        for i in range(20):
            entry_time = current_date + timedelta(hours=np.random.uniform(6, 22))
            duration = np.random.uniform(1, 8)
            exit_time = entry_time + timedelta(hours=duration)
            
            records.append({
                '车牌号码': f"京A{day:02d}{i:03d}",
                '车辆类型': np.random.choice(vehicle_types),
                '进场时间': entry_time,
                '出场时间': exit_time,
                '进场道闸编号': np.random.choice(gates[:2]),  # 入口道闸
                '出场道闸编号': np.random.choice(gates[2:]),  # 出口道闸
                '停车时长': f"{duration:.3f}小时"
            })
    
    return pd.DataFrame(records)

def test_daily_gate_stats():
    """测试分析周期_每日sheet中的道闸进出量统计"""
    print("=" * 80)
    print("测试分析周期_每日sheet中的道闸进出量统计功能")
    print("验证是否正确添加了道闸1_进、道闸1_出等列")
    print("=" * 80)
    
    try:
        # 1. 创建测试数据
        test_data = create_gate_test_data()
        print(f"\n📋 创建测试数据: {len(test_data)} 条记录")
        print(f"   时间范围: {test_data['进场时间'].min()} 到 {test_data['出场时间'].max()}")
        print(f"   进场道闸: {test_data['进场道闸编号'].unique().tolist()}")
        print(f"   出场道闸: {test_data['出场道闸编号'].unique().tolist()}")
        
        # 2. 创建临时文件
        with tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False, encoding='utf-8') as f:
            test_data.to_csv(f.name, index=False)
            temp_file = f.name
        
        temp_output_dir = tempfile.mkdtemp()
        
        params = {
            'input_file': temp_file,
            'output': temp_output_dir,
            'date': '2024-06-01',
            'mode': 'mode2',
            'time_interval': 60,
            'time_slip': 15,
            '车辆唯一标识字段': '车牌号码',
            '车辆类型字段': '车辆类型',
            '进场时间字段': '进场时间',
            '出场时间字段': '出场时间',
            '进场道闸编号字段': '进场道闸编号',
            '出场道闸编号字段': '出场道闸编号'
        }
        
        # 3. 执行数据处理
        print(f"\n🔄 执行数据处理...")
        
        from parking_data_processor import DataProcessor
        from parking_time_filter import TimeFilter
        from parking_analyzer import TimePeriodAnalyzer
        from parking_report_generatior import ReportGenerator
        
        # 数据处理
        data_processor = DataProcessor(test_data, params)
        processed_data = data_processor.process()
        
        # 时间过滤
        time_filter = TimeFilter(processed_data, params)
        filtered_data = time_filter.get_filtered_data()
        period_info = time_filter.detect_period()
        
        # 数据分析
        analyzer = TimePeriodAnalyzer(
            filtered_data, 
            len(test_data), 
            mode=params['mode'],
            period_info=period_info,
            params=params
        )
        analysis_results = analyzer.analyze(focus_date=params.get('date'))
        
        # 报告生成
        report_generator = ReportGenerator(
            filtered_data,
            analysis_results,
            params,
            input_total_records=len(test_data)
        )
        
        # 4. 测试每日统计计算
        print(f"\n🔍 测试每日统计计算...")
        
        # 直接调用_calculate_daily_stats方法
        daily_stats = report_generator._calculate_daily_stats(filtered_data)
        
        if not daily_stats.empty:
            print(f"   每日统计数据:")
            print(f"   - 数据行数: {len(daily_stats)}")
            print(f"   - 数据列数: {len(daily_stats.columns)}")
            print(f"   - 列名: {daily_stats.columns.tolist()}")
            
            # 检查是否包含道闸统计列
            gate_columns = [col for col in daily_stats.columns if '_进' in col or '_出' in col]
            print(f"\n   道闸统计列:")
            for col in gate_columns:
                print(f"     - {col}")
            
            if gate_columns:
                print(f"   ✅ 成功添加了 {len(gate_columns)} 个道闸统计列")
            else:
                print(f"   ❌ 未找到道闸统计列")
            
            # 显示部分数据
            print(f"\n   数据样例:")
            sample_data = daily_stats.head(3)
            for i, row in sample_data.iterrows():
                print(f"     日期: {row['日期']}")
                print(f"       总进场: {row['进场数量']}, 总出场: {row['出场数量']}")
                
                # 显示道闸统计
                for col in gate_columns:
                    if col in row:
                        print(f"       {col}: {row[col]}")
        else:
            print(f"   ❌ 每日统计数据为空")
        
        # 5. 生成Excel文件
        print(f"\n📊 生成Excel报告...")
        output_path = os.path.join(temp_output_dir, "test_daily_gate_stats.xlsx")
        report_generator.generate_and_save_plots(filtered_data)
        report_path = report_generator.export_to_excel(output_path)
        
        # 6. 验证Excel文件中的分析周期_每日sheet
        if os.path.exists(report_path):
            print(f"\n✅ Excel文件生成成功: {report_path}")
            
            try:
                # 读取分析周期_每日sheet
                daily_sheet = pd.read_excel(report_path, sheet_name='分析周期_每日')
                print(f"\n📋 分析周期_每日sheet内容:")
                print(f"   - 行数: {len(daily_sheet)}")
                print(f"   - 列数: {len(daily_sheet.columns)}")
                print(f"   - 列名: {daily_sheet.columns.tolist()}")
                
                # 检查道闸统计列
                gate_columns_excel = [col for col in daily_sheet.columns if '_进' in col or '_出' in col]
                print(f"\n   Excel中的道闸统计列:")
                for col in gate_columns_excel:
                    print(f"     - {col}")
                
                if gate_columns_excel:
                    print(f"   ✅ Excel中成功包含了 {len(gate_columns_excel)} 个道闸统计列")
                    
                    # 显示数据样例
                    print(f"\n   Excel数据样例:")
                    sample_excel = daily_sheet.head(3)
                    for i, row in sample_excel.iterrows():
                        print(f"     日期: {row.iloc[0]}")  # 第一列是日期
                        
                        # 显示基础统计
                        if '进场数量' in daily_sheet.columns:
                            print(f"       总进场: {row['进场数量']}, 总出场: {row['出场数量']}")
                        
                        # 显示道闸统计
                        for col in gate_columns_excel[:4]:  # 只显示前4个道闸列
                            if col in row:
                                print(f"       {col}: {row[col]}")
                else:
                    print(f"   ❌ Excel中未找到道闸统计列")
                
            except Exception as e:
                print(f"   ❌ 读取分析周期_每日sheet失败: {e}")
        
        # 7. 功能验证总结
        print(f"\n{'='*60}")
        print("功能验证总结")
        print('='*60)
        
        success_count = 0
        total_checks = 4
        
        # 检查1: Excel文件生成成功
        if os.path.exists(report_path):
            print("✅ Excel文件生成成功")
            success_count += 1
        else:
            print("❌ Excel文件生成失败")
        
        # 检查2: 每日统计数据包含道闸列
        if not daily_stats.empty and gate_columns:
            print("✅ 每日统计数据包含道闸统计列")
            success_count += 1
        else:
            print("❌ 每日统计数据不包含道闸统计列")
        
        # 检查3: Excel中包含道闸列
        try:
            daily_sheet = pd.read_excel(report_path, sheet_name='分析周期_每日')
            gate_columns_excel = [col for col in daily_sheet.columns if '_进' in col or '_出' in col]
            if gate_columns_excel:
                print("✅ Excel中包含道闸统计列")
                success_count += 1
            else:
                print("❌ Excel中不包含道闸统计列")
        except:
            print("❌ 无法验证Excel中的道闸统计列")
        
        # 检查4: 数据合理性
        if not daily_stats.empty and len(daily_stats) > 0:
            print("✅ 每日统计数据合理")
            success_count += 1
        else:
            print("❌ 每日统计数据不合理")
        
        print(f"\n🎯 验证结果: {success_count}/{total_checks} 项通过")
        
        if success_count >= 3:
            print("🎉 分析周期_每日sheet道闸进出量统计功能测试成功！")
        else:
            print("⚠️  部分功能需要进一步检查")
        
        # 8. 功能说明
        print(f"\n📝 实现的功能:")
        print("1. ✅ 在_calculate_daily_stats方法中添加了道闸统计")
        print("2. ✅ 实现了_add_gate_stats_to_daily方法")
        print("3. ✅ 支持mode1和mode2两种模式的道闸统计")
        print("4. ✅ 自动简化道闸名称作为列名")
        print("5. ✅ 按日期统计每个道闸的进出量")
        
        print(f"\n🎯 预期效果:")
        print("- 分析周期_每日sheet现在包含基础统计 + 道闸统计")
        print("- 道闸列格式: 道闸名_进、道闸名_出")
        print("- 支持多个道闸的进出量统计")
        print("- 与现有车辆类型统计兼容")
        
        # 9. 清理临时文件
        try:
            os.unlink(temp_file)
            print(f"\n📁 生成的测试文件: {report_path}")
            print("   (文件已保留，可手动打开查看道闸统计效果)")
        except:
            pass
        
        return True
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_daily_gate_stats()
    
    print("\n" + "=" * 80)
    print("分析周期_每日道闸进出量统计测试完成！")
    print("=" * 80)
