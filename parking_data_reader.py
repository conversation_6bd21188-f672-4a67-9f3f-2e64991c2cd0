import pandas as pd
import os
import chardet

# 文件读取配置
FILE_READING_CONFIG = {
    'supported_extensions': ['.xlsx', '.xls', '.csv', '.txt'],
    # 增强编码支持：按优先级排序，包含更多中文编码
    'encodings': [
        'utf-8', 'utf-8-sig',           # UTF-8系列（最常用）
        'gbk', 'gb2312', 'gb18030',     # 中文编码系列
        'big5', 'big5-hkscs',           # 繁体中文编码
        'latin-1', 'cp1252',            # 西文编码
        'iso-8859-1', 'windows-1252',   # Windows编码
        'ascii',                        # ASCII编码
        'cp936', 'hz-gb-2312'           # 其他中文编码
    ],
    'csv_separators': [',', ';', '\t', '|', ' '],  # 增加空格分隔符
    'excel_engines': ['openpyxl', 'xlrd', 'calamine'],  # 增加calamine引擎
    'max_file_size_mb': 500,  # 最大文件大小限制
    # 新增：编码检测配置
    'encoding_detection': {
        'sample_sizes': [10000, 50000, 100000],  # 多种采样大小
        'confidence_thresholds': [0.8, 0.6, 0.4],  # 多级置信度阈值
        'fallback_encodings': ['utf-8', 'gbk', 'latin-1']  # 最终备用编码
    }
}

def detect_file_encoding(file_path, logger=None):
    """
    增强版文件编码检测
    使用多种采样大小和置信度阈值进行检测

    Args:
        file_path: str, 文件路径
        logger: Logger, 日志记录器

    Returns:
        tuple: (检测到的编码, 置信度, 检测方法)
    """
    detection_config = FILE_READING_CONFIG['encoding_detection']

    try:
        file_size = os.path.getsize(file_path)
        if logger:
            logger.debug(f"文件大小: {file_size} 字节")

        # 尝试不同的采样大小
        for sample_size in detection_config['sample_sizes']:
            # 调整采样大小不超过文件大小
            actual_sample_size = min(sample_size, file_size)

            try:
                with open(file_path, 'rb') as f:
                    raw_data = f.read(actual_sample_size)

                if not raw_data:
                    continue

                result = chardet.detect(raw_data)
                encoding = result.get('encoding')
                confidence = result.get('confidence', 0)

                if logger:
                    logger.debug(f"采样大小 {actual_sample_size}: 编码={encoding}, 置信度={confidence:.3f}")

                # 使用多级置信度阈值
                for threshold in detection_config['confidence_thresholds']:
                    if confidence >= threshold and encoding:
                        # 标准化编码名称
                        normalized_encoding = _normalize_encoding_name(encoding)
                        if logger:
                            logger.info(f"编码检测成功: {normalized_encoding} (置信度: {confidence:.3f}, 采样: {actual_sample_size})")
                        return normalized_encoding, confidence, f"chardet_sample_{actual_sample_size}"

            except Exception as e:
                if logger:
                    logger.debug(f"采样大小 {actual_sample_size} 检测失败: {str(e)}")
                continue

        # 如果自动检测失败，尝试启发式检测
        heuristic_encoding = _heuristic_encoding_detection(file_path, logger)
        if heuristic_encoding:
            if logger:
                logger.info(f"启发式检测编码: {heuristic_encoding}")
            return heuristic_encoding, 0.5, "heuristic"

        # 最终备用方案
        fallback_encoding = detection_config['fallback_encodings'][0]
        if logger:
            logger.warning(f"编码检测失败，使用备用编码: {fallback_encoding}")
        return fallback_encoding, 0.0, "fallback"

    except Exception as e:
        if logger:
            logger.error(f"编码检测异常: {str(e)}")
        return 'utf-8', 0.0, "error_fallback"

def _normalize_encoding_name(encoding):
    """标准化编码名称"""
    if not encoding:
        return 'utf-8'

    encoding = encoding.lower()

    # 编码名称映射
    encoding_map = {
        'gb2312': 'gbk',  # GB2312是GBK的子集
        'gb18030': 'gbk',  # 通常GBK就足够了
        'windows-1252': 'cp1252',
        'iso-8859-1': 'latin-1',
        'ascii': 'utf-8'  # ASCII是UTF-8的子集
    }

    return encoding_map.get(encoding, encoding)

def _heuristic_encoding_detection(file_path, logger=None):
    """启发式编码检测"""
    try:
        # 读取文件开头的一小部分
        with open(file_path, 'rb') as f:
            sample = f.read(1000)

        # 检查BOM标记
        if sample.startswith(b'\xef\xbb\xbf'):
            return 'utf-8-sig'
        elif sample.startswith(b'\xff\xfe'):
            return 'utf-16-le'
        elif sample.startswith(b'\xfe\xff'):
            return 'utf-16-be'

        # 检查中文字符模式
        if b'\x81' in sample or b'\x82' in sample or b'\x83' in sample:
            return 'gbk'

        # 检查是否包含高位字符
        if any(b > 127 for b in sample):
            return 'utf-8'
        else:
            return 'ascii'

    except Exception as e:
        if logger:
            logger.debug(f"启发式检测失败: {str(e)}")
        return None

def read_excel_file(file_path, logger=None):
    """
    增强版Excel文件读取，支持多种引擎和错误恢复

    Args:
        file_path: str, 文件路径
        logger: Logger, 日志记录器

    Returns:
        DataFrame: 读取的数据
    """
    data = None
    last_error = None
    successful_engine = None

    # 尝试不同的引擎和参数组合
    engine_configs = [
        {'engine': 'openpyxl', 'params': {}},
        {'engine': 'openpyxl', 'params': {'data_only': True}},  # 只读取值，不读取公式
        {'engine': 'xlrd', 'params': {}},
    ]

    # 如果安装了calamine，添加到配置中
    try:
        import python_calamine
        engine_configs.append({'engine': 'calamine', 'params': {}})
    except ImportError:
        pass

    for config in engine_configs:
        engine = config['engine']
        params = config['params']

        try:
            # 尝试读取第一个工作表
            data = pd.read_excel(file_path, engine=engine, **params)

            if data is not None and not data.empty:
                successful_engine = engine
                if logger:
                    logger.info(f"使用引擎 {engine} 成功读取Excel文件 (参数: {params})")
                break

        except Exception as e:
            last_error = e
            if logger:
                logger.debug(f"使用引擎 {engine} 读取Excel失败: {str(e)}")
            continue

    # 如果单工作表读取失败，尝试读取所有工作表
    if data is None or data.empty:
        if logger:
            logger.info("尝试读取所有工作表...")

        for config in engine_configs:
            engine = config['engine']
            params = config['params']

            try:
                all_sheets = pd.read_excel(file_path, sheet_name=None, engine=engine, **params)
                if all_sheets:
                    # 选择数据最多的工作表
                    max_rows = 0
                    best_sheet = None
                    best_sheet_name = None

                    for sheet_name, sheet_data in all_sheets.items():
                        if sheet_data is not None and len(sheet_data) > max_rows:
                            max_rows = len(sheet_data)
                            best_sheet = sheet_data
                            best_sheet_name = sheet_name

                    if best_sheet is not None and not best_sheet.empty:
                        data = best_sheet
                        successful_engine = engine
                        if logger:
                            logger.info(f"使用引擎 {engine} 读取工作表 '{best_sheet_name}'，共{len(data)}行")
                        break

            except Exception as e:
                last_error = e
                if logger:
                    logger.debug(f"使用引擎 {engine} 读取所有工作表失败: {str(e)}")
                continue

    # 最后的错误恢复尝试：手动解析Excel
    if data is None or data.empty:
        if logger:
            logger.warning("尝试手动解析Excel文件...")

        try:
            # 尝试使用openpyxl的错误恢复模式
            import openpyxl
            workbook = openpyxl.load_workbook(file_path, data_only=True, read_only=True)

            # 获取第一个工作表
            worksheet = workbook.active
            data_rows = []

            for row in worksheet.iter_rows(values_only=True):
                if any(cell is not None for cell in row):  # 跳过完全空的行
                    data_rows.append(row)

            if data_rows:
                # 确定列数
                max_cols = max(len(row) for row in data_rows)

                # 补齐行数据
                normalized_rows = []
                for row in data_rows:
                    normalized_row = list(row) + [None] * (max_cols - len(row))
                    normalized_rows.append(normalized_row)

                data = pd.DataFrame(normalized_rows)
                successful_engine = "openpyxl_manual"
                if logger:
                    logger.info(f"使用手动解析模式成功读取Excel，共{len(data)}行")

            workbook.close()

        except Exception as e:
            last_error = e
            if logger:
                logger.debug(f"手动解析模式失败: {str(e)}")

    if data is None and last_error:
        if logger:
            logger.error(f"所有Excel读取方法都失败了: {str(last_error)}")
        raise last_error

    return data

def read_csv_file(file_path, logger=None):
    """
    读取CSV/TXT文件，自动检测编码和分隔符

    Args:
        file_path: str, 文件路径
        logger: Logger, 日志记录器

    Returns:
        DataFrame: 读取的数据
    """
    data = None
    last_error = None

    # 首先尝试自动检测编码
    detected_encoding, confidence, method = detect_file_encoding(file_path, logger)

    # 构建编码尝试列表：检测到的编码 + 配置中的其他编码
    encodings = [detected_encoding]
    for enc in FILE_READING_CONFIG['encodings']:
        if enc != detected_encoding:
            encodings.append(enc)

    if logger:
        logger.info(f"检测到文件编码: {detected_encoding} (置信度: {confidence:.3f}, 方法: {method})")

    for encoding in encodings:
        for separator in FILE_READING_CONFIG['csv_separators']:
            try:
                # 尝试读取文件
                read_params = {
                    'encoding': encoding,
                    'sep': separator,
                    'on_bad_lines': 'skip',  # 跳过有问题的行
                    'low_memory': False,     # 避免数据类型推断警告
                    'engine': 'python'       # 使用Python引擎，更容错
                }

                data = pd.read_csv(file_path, **read_params)

                # 检查数据质量
                if not data.empty and len(data.columns) > 1:
                    if logger:
                        logger.info(f"成功读取文件: 编码={encoding}, 分隔符='{separator}', 列数={len(data.columns)}, 行数={len(data)}")
                    return data
                elif not data.empty and len(data.columns) == 1:
                    # 只有一列，可能分隔符不对，但数据可能有效
                    if logger:
                        logger.debug(f"读取到单列数据: 编码={encoding}, 分隔符='{separator}', 行数={len(data)}")

            except UnicodeDecodeError as e:
                if logger:
                    logger.debug(f"编码 {encoding} 解码失败: {str(e)}")
                continue
            except pd.errors.EmptyDataError:
                if logger:
                    logger.debug(f"文件为空或无有效数据: 编码={encoding}, 分隔符='{separator}'")
                continue
            except Exception as e:
                last_error = e
                if logger:
                    logger.debug(f"尝试编码 {encoding} 和分隔符 '{separator}' 失败: {str(e)}")
                continue

    # 如果所有尝试都失败，尝试多种备用方案
    if logger:
        logger.warning("常规方法失败，尝试备用读取方案...")

    # 备用方案1：使用Python引擎自动检测分隔符
    for encoding in ['utf-8', 'utf-8-sig', 'gbk', 'latin-1']:
        try:
            data = pd.read_csv(
                file_path,
                encoding=encoding,
                sep=None,
                engine='python',
                on_bad_lines='skip'
            )
            if not data.empty:
                if logger:
                    logger.warning(f"备用方案1成功: 编码={encoding}, 自动检测分隔符")
                return data
        except Exception as e:
            if logger:
                logger.debug(f"备用方案1失败 (编码={encoding}): {str(e)}")
            continue

    # 备用方案2：尝试读取为纯文本，然后手动解析
    try:
        with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
            lines = f.readlines()

        if lines:
            # 尝试检测分隔符
            first_line = lines[0].strip()
            separators = [',', '\t', ';', '|', ' ']
            best_sep = ','
            max_cols = 0

            for sep in separators:
                cols = len(first_line.split(sep))
                if cols > max_cols:
                    max_cols = cols
                    best_sep = sep

            # 手动解析数据
            data_rows = []
            for line in lines:
                row = line.strip().split(best_sep)
                data_rows.append(row)

            if data_rows:
                data = pd.DataFrame(data_rows)
                if logger:
                    logger.warning(f"备用方案2成功: 手动解析，分隔符='{best_sep}', 行数={len(data)}")
                return data

    except Exception as e:
        if logger:
            logger.debug(f"备用方案2失败: {str(e)}")

    # 备用方案3：尝试以二进制模式读取并转换
    try:
        with open(file_path, 'rb') as f:
            raw_content = f.read()

        # 尝试不同编码解码
        for encoding in ['utf-8', 'gbk', 'latin-1']:
            try:
                text_content = raw_content.decode(encoding, errors='ignore')
                lines = text_content.split('\n')

                if lines and len(lines) > 1:
                    # 简单的CSV解析
                    data_rows = []
                    for line in lines:
                        if line.strip():
                            row = line.split(',')  # 假设是逗号分隔
                            data_rows.append(row)

                    if data_rows:
                        data = pd.DataFrame(data_rows)
                        if logger:
                            logger.warning(f"备用方案3成功: 二进制读取+{encoding}解码")
                        return data

            except Exception:
                continue

    except Exception as e:
        if logger:
            logger.debug(f"备用方案3失败: {str(e)}")

    # 所有方案都失败
    if last_error:
        if logger:
            logger.error(f"所有读取方案都失败，最后错误: {str(last_error)}")
        raise last_error
    else:
        error_msg = "无法读取文件：所有编码和分隔符组合都失败"
        if logger:
            logger.error(error_msg)
        raise ValueError(error_msg)

def validate_file_size(file_path, max_size_mb=None):
    """
    验证文件大小

    Args:
        file_path: str, 文件路径
        max_size_mb: int, 最大文件大小（MB）

    Returns:
        bool: 文件大小是否合适
    """
    if max_size_mb is None:
        max_size_mb = FILE_READING_CONFIG['max_file_size_mb']

    try:
        file_size_mb = os.path.getsize(file_path) / (1024 * 1024)
        return file_size_mb <= max_size_mb
    except Exception:
        return True  # 如果无法获取文件大小，假设合适

def read_data_file(file_path, config_manager=None):
    """
    增强版文件读取函数，支持xlsx、csv、txt格式，具有强健壮性

    Args:
        file_path: str, 文件路径
        config_manager: ConfigManager, 配置管理器（可选）

    Returns:
        DataFrame: 读取的数据

    Raises:
        FileNotFoundError: 文件不存在
        ValueError: 不支持的文件格式或文件为空
        IOError: 文件读取失败
    """
    # 验证文件存在
    if not os.path.exists(file_path):
        raise FileNotFoundError(f"文件不存在: {file_path}")

    # 获取配置和日志
    config = config_manager.get_data_processing_config() if config_manager else {}
    logger = config_manager.get_logger() if config_manager else None

    # 验证文件大小
    if not validate_file_size(file_path):
        max_size = FILE_READING_CONFIG['max_file_size_mb']
        raise ValueError(f"文件过大，超过{max_size}MB限制: {file_path}")

    # 获取文件扩展名
    file_ext = os.path.splitext(file_path)[1].lower()

    if logger:
        logger.info(f"开始读取文件: {file_path} (格式: {file_ext})")

    # 验证文件格式
    if file_ext not in FILE_READING_CONFIG['supported_extensions']:
        supported = ', '.join(FILE_READING_CONFIG['supported_extensions'])
        raise ValueError(f"不支持的文件格式: {file_ext}。支持的格式: {supported}")

    try:
        data = None

        # 根据文件类型选择读取方法
        if file_ext in ['.xlsx', '.xls']:
            data = read_excel_file(file_path, logger)
        elif file_ext in ['.csv', '.txt']:
            data = read_csv_file(file_path, logger)

        # 验证读取结果
        if data is None or data.empty:
            raise ValueError(f"无法读取文件或文件为空: {file_path}")

        # 基础数据清洗
        original_rows = len(data)
        original_cols = len(data.columns)

        if config.get('clean_data', True):
            # 1. 删除全为空的行和列
            data = data.dropna(how='all').dropna(axis=1, how='all')

            # 2. 规范化列名（去除前后空格，替换特殊字符）
            data.columns = data.columns.astype(str).str.strip()

            # 3. 移除重复的列名
            if len(data.columns) != len(set(data.columns)):
                if logger:
                    logger.warning("发现重复列名，正在处理...")
                data = data.loc[:, ~data.columns.duplicated()]

        cleaned_rows = len(data)
        cleaned_cols = len(data.columns)

        if logger:
            logger.info(f"文件读取成功: {file_path}")
            logger.info(f"原始数据: {original_rows}行 x {original_cols}列")
            logger.info(f"清洗后数据: {cleaned_rows}行 x {cleaned_cols}列")
            logger.info(f"列名: {list(data.columns)}")

        return data

    except Exception as e:
        error_msg = f"读取文件失败: {str(e)}"
        if logger:
            logger.error(error_msg)
        raise IOError(error_msg)

def process_output_path(input_file, output_file=None, config_manager=None):
    """处理输出文件路径"""
    from datetime import datetime

    # 优先使用config_manager中的output_dir
    output_dir = ''
    if config_manager:
        output_dir = config_manager.get_system_config().get('output_dir', '')
        if not output_dir:
            output_dir = config_manager.get_data_processing_config().get('output_dir', '')

    input_dir = os.path.dirname(input_file)
    input_filename = os.path.basename(input_file)
    input_name, _ = os.path.splitext(input_filename)

    # 生成带时间戳的文件名，避免文件冲突
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    analysis_filename = f"{input_name}_analysis_{timestamp}.xlsx"

    # 如果配置了输出目录且未提供输出文件
    if output_dir and not output_file:
        return os.path.join(output_dir, analysis_filename)

    # 如果未提供输出路径，使用输入文件路径
    if not output_file:
        return os.path.join(input_dir, analysis_filename)

    # 如果提供的是目录，使用输入文件名
    if os.path.isdir(output_file):
        return os.path.join(output_file, analysis_filename)

    # 如果提供的只是文件名，使用输入文件目录
    if not os.path.dirname(output_file):
        return os.path.join(input_dir, output_file)

    # 如果提供的是完整路径，直接使用
    return output_file