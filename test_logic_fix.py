#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试逻辑修复
验证"结构不兼容但被标记为重复"的问题是否修复
"""

import pandas as pd
import tempfile
import os
import shutil

def test_logic_fix():
    """测试逻辑修复"""
    print("🧪 测试逻辑修复")
    print("=" * 60)
    
    # 创建临时目录
    temp_dir = tempfile.mkdtemp()
    print(f"测试目录: {temp_dir}")
    
    try:
        # 创建两个内容相同但列名不同的文件（模拟您的场景）
        # 这样第一个文件会因为"结构不兼容"被跳过
        # 第二个文件不应该被标记为"重复"
        
        data_content = pd.DataFrame({
            'col1': [1, 2, 3, 4, 5],
            'col2': ['A', 'B', 'C', 'D', 'E'],
            'col3': [10, 20, 30, 40, 50]
        })
        
        # 第一个文件：使用不同的列名（会导致结构不兼容）
        data1 = data_content.copy()
        data1.columns = ['不同列名1', '不同列名2', '不同列名3']
        
        # 第二个文件：使用相同的数据但不同的列名
        data2 = data_content.copy()
        data2.columns = ['另一列名1', '另一列名2', '另一列名3']
        
        # 第三个文件：作为参考结构（兼容的列名）
        data3 = pd.DataFrame({
            'field1': [100, 200, 300],
            'field2': ['X', 'Y', 'Z'],
            'field3': [1000, 2000, 3000]
        })
        
        # 创建文件
        file1 = os.path.join(temp_dir, 'incompatible1.csv')
        file2 = os.path.join(temp_dir, 'incompatible2.csv')
        file3 = os.path.join(temp_dir, 'reference.csv')
        
        data1.to_csv(file1, index=False)
        data2.to_csv(file2, index=False)
        data3.to_csv(file3, index=False)
        
        print(f"\n📁 创建的测试文件:")
        print(f"  incompatible1.csv: {len(data1)} 行 (不兼容列名)")
        print(f"  incompatible2.csv: {len(data2)} 行 (不兼容列名，内容与第一个相同)")
        print(f"  reference.csv: {len(data3)} 行 (参考结构)")
        
        # 导入合并器
        from excel_data_merger import IntegratedDataMerger
        
        # 创建合并器
        merger = IntegratedDataMerger()
        
        # 执行合并
        output_file = os.path.join(temp_dir, 'result.csv')
        result = merger.smart_merge_directory(
            input_path=temp_dir,
            output_path=output_file,
            file_pattern="*",
            recursive=False
        )
        
        # 分析结果
        stats = merger.merge_stats
        
        print(f"\n📊 合并结果:")
        print(f"总数据源: {stats['total_sources']}")
        print(f"成功合并: {stats['merged_sources']}")
        print(f"重复数量: {stats['duplicate_sources']}")
        print(f"跳过数量: {stats['skipped_sources']}")
        print(f"合并行数: {stats['merged_rows']}")
        print(f"实际行数: {len(result)}")
        
        # 显示详细信息
        if stats['merged_details']:
            print(f"\n✅ 成功合并的文件:")
            for detail in stats['merged_details']:
                print(f"   - {detail['file_name']}: {detail['rows']} 行 ({detail['role']})")
        
        if stats['duplicate_details']:
            print(f"\n🔄 重复检测详情:")
            for detail in stats['duplicate_details']:
                print(f"   - {detail['source']} → 重复于 {detail['duplicate_of']}")
        
        if stats['skipped_details']:
            print(f"\n⚠️ 跳过详情:")
            for detail in stats['skipped_details']:
                print(f"   - {detail['source']}: {detail['reason']}")
        
        # 验证修复结果
        print(f"\n🔍 修复验证:")
        
        # 预期结果：
        # 1. reference.csv 被成功合并（作为参考结构）
        # 2. incompatible1.csv 和 incompatible2.csv 都因为结构不兼容被跳过
        # 3. 不应该有重复检测（因为不兼容的文件不会记录哈希）
        
        expected_merged = 1  # 只有reference.csv
        expected_duplicates = 0  # 不应该有重复
        expected_skipped = 2  # incompatible1.csv 和 incompatible2.csv
        
        print(f"  预期成功合并: {expected_merged}")
        print(f"  预期重复数量: {expected_duplicates}")
        print(f"  预期跳过数量: {expected_skipped}")
        
        is_fixed = (
            stats['merged_sources'] == expected_merged and
            stats['duplicate_sources'] == expected_duplicates and
            stats['skipped_sources'] == expected_skipped
        )
        
        print(f"\n📋 修复结果:")
        if is_fixed:
            print("✅ 逻辑修复成功！")
            print("   - 结构不兼容的文件被正确跳过")
            print("   - 没有出现错误的重复检测")
            print("   - 哈希只在成功合并时记录")
        else:
            print("❌ 逻辑仍有问题！")
            print(f"   实际成功合并: {stats['merged_sources']} (预期: {expected_merged})")
            print(f"   实际重复数量: {stats['duplicate_sources']} (预期: {expected_duplicates})")
            print(f"   实际跳过数量: {stats['skipped_sources']} (预期: {expected_skipped})")
            
            if stats['duplicate_sources'] > 0:
                print("   ⚠️ 仍然出现了错误的重复检测！")
        
        return is_fixed
        
    except Exception as e:
        print(f"❌ 测试异常: {str(e)}")
        import traceback
        traceback.print_exc()
        return False
        
    finally:
        # 清理
        try:
            shutil.rmtree(temp_dir)
            print(f"\n🧹 清理测试目录")
        except:
            pass

def main():
    """主函数"""
    print("🔧 测试逻辑修复")
    print("=" * 80)
    
    result = test_logic_fix()
    
    print(f"\n📊 测试结果: {'✅ 修复成功' if result else '❌ 仍有问题'}")
    
    if result:
        print(f"\n🎉 问题已修复！")
        print(f"   - 不会再出现'结构不兼容但被标记为重复'的矛盾")
        print(f"   - 只有成功合并的文件才会记录哈希用于重复检测")
        print(f"   - 逻辑现在是一致的")
    else:
        print(f"\n⚠️ 问题仍然存在，需要进一步调试")

if __name__ == "__main__":
    main()
