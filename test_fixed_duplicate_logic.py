#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修复后的重复检测逻辑
验证Excel文件和CSV文件的平等处理
"""

import pandas as pd
import tempfile
import os
import shutil

def test_fixed_duplicate_logic():
    """测试修复后的重复检测逻辑"""
    print("🧪 测试修复后的重复检测逻辑")
    print("=" * 60)
    
    # 创建临时目录
    temp_dir = tempfile.mkdtemp()
    print(f"测试目录: {temp_dir}")
    
    try:
        # 创建测试数据
        data_a = pd.DataFrame({
            'col1': [1, 2, 3, 4, 5],
            'col2': ['A', 'B', 'C', 'D', 'E']
        })
        
        data_b = pd.DataFrame({
            'col1': [6, 7, 8, 9, 10],
            'col2': ['F', 'G', 'H', 'I', 'J']
        })
        
        # data_c 与 data_a 相同（重复）
        data_c = data_a.copy()
        
        # 创建文件：Excel文件和CSV文件混合，包含重复数据
        file1_xlsx = os.path.join(temp_dir, 'file1.xlsx')  # Excel文件
        file2_csv = os.path.join(temp_dir, 'file2.csv')    # CSV文件（唯一）
        file3_csv = os.path.join(temp_dir, 'file3.csv')    # CSV文件（与Excel重复）
        
        # 保存文件
        data_a.to_excel(file1_xlsx, index=False)  # Excel文件
        data_b.to_csv(file2_csv, index=False)     # 唯一CSV文件
        data_c.to_csv(file3_csv, index=False)     # 重复CSV文件
        
        print(f"\n📁 创建的文件:")
        print(f"  file1.xlsx: {len(data_a)} 行 (Excel文件)")
        print(f"  file2.csv: {len(data_b)} 行 (唯一CSV文件)")
        print(f"  file3.csv: {len(data_c)} 行 (与file1.xlsx重复的CSV文件)")
        
        # 导入合并器
        from excel_data_merger import IntegratedDataMerger
        
        # 创建合并器
        merger = IntegratedDataMerger()
        
        # 执行合并
        output_file = os.path.join(temp_dir, 'result.xlsx')
        result = merger.smart_merge_directory(
            input_path=temp_dir,
            output_path=output_file,
            file_pattern="*",
            recursive=False
        )
        
        # 分析结果
        stats = merger.merge_stats
        
        print(f"\n📊 合并结果:")
        print(f"总数据源: {stats['total_sources']}")
        print(f"成功合并: {stats['merged_sources']}")
        print(f"重复数量: {stats['duplicate_sources']}")
        print(f"跳过数量: {stats['skipped_sources']}")
        print(f"合并行数: {stats['merged_rows']}")
        print(f"实际行数: {len(result)}")
        
        # 显示详细信息
        if stats['merged_details']:
            print(f"\n✅ 成功合并的文件:")
            for detail in stats['merged_details']:
                print(f"   - {detail['file_name']}: {detail['rows']} 行 ({detail['role']})")
        
        if stats['duplicate_details']:
            print(f"\n🔄 重复检测详情:")
            for detail in stats['duplicate_details']:
                print(f"   - {detail['source']} → 重复于 {detail['duplicate_of']}")
        
        # 验证结果
        print(f"\n🔍 预期结果:")
        print(f"  - 应该保留: file1.xlsx 或 file3.csv (其中一个)")
        print(f"  - 应该保留: file2.csv (唯一文件)")
        print(f"  - 应该跳过: file1.xlsx 或 file3.csv (其中一个，重复)")
        print(f"  - 预期成功合并: 2")
        print(f"  - 预期重复数量: 1")
        
        # 检查结果
        expected_merged = 2
        expected_duplicates = 1
        
        is_correct = (
            stats['merged_sources'] == expected_merged and
            stats['duplicate_sources'] == expected_duplicates
        )
        
        print(f"\n📋 结果评估:")
        if is_correct:
            print("✅ 修复成功！Excel和CSV文件平等处理！")
            print("   - 重复检测正确工作")
            print("   - 保留了一个重复文件，跳过了另一个")
            print("   - 唯一文件被正常合并")
        else:
            print("❌ 修复失败！仍有问题！")
            print(f"   实际成功合并: {stats['merged_sources']} (预期: {expected_merged})")
            print(f"   实际重复数量: {stats['duplicate_sources']} (预期: {expected_duplicates})")
        
        return is_correct
        
    except Exception as e:
        print(f"❌ 测试异常: {str(e)}")
        import traceback
        traceback.print_exc()
        return False
        
    finally:
        # 清理
        try:
            shutil.rmtree(temp_dir)
            print(f"\n🧹 清理测试目录")
        except:
            pass

def test_csv_not_skipped():
    """测试CSV文件不会被错误跳过"""
    print("\n🧪 测试CSV文件不会被错误跳过")
    print("=" * 60)
    
    # 创建临时目录
    temp_dir = tempfile.mkdtemp()
    print(f"测试目录: {temp_dir}")
    
    try:
        # 创建完全不同的数据
        data_excel = pd.DataFrame({
            'field1': [100, 200, 300],
            'field2': ['Excel1', 'Excel2', 'Excel3']
        })
        
        data_csv = pd.DataFrame({
            'field1': [400, 500, 600],
            'field2': ['CSV1', 'CSV2', 'CSV3']
        })
        
        # 创建文件
        excel_file = os.path.join(temp_dir, 'data.xlsx')
        csv_file = os.path.join(temp_dir, 'data.csv')
        
        data_excel.to_excel(excel_file, index=False)
        data_csv.to_csv(csv_file, index=False)
        
        print(f"\n📁 创建的文件:")
        print(f"  data.xlsx: {len(data_excel)} 行 (Excel文件)")
        print(f"  data.csv: {len(data_csv)} 行 (CSV文件)")
        print(f"  注意: 两个文件内容完全不同")
        
        # 导入合并器
        from excel_data_merger import IntegratedDataMerger
        
        # 创建合并器
        merger = IntegratedDataMerger()
        
        # 执行合并
        output_file = os.path.join(temp_dir, 'result.xlsx')
        result = merger.smart_merge_directory(
            input_path=temp_dir,
            output_path=output_file,
            file_pattern="*",
            recursive=False
        )
        
        # 分析结果
        stats = merger.merge_stats
        
        print(f"\n📊 合并结果:")
        print(f"总数据源: {stats['total_sources']}")
        print(f"成功合并: {stats['merged_sources']}")
        print(f"重复数量: {stats['duplicate_sources']}")
        print(f"合并行数: {stats['merged_rows']}")
        print(f"实际行数: {len(result)}")
        
        # 显示详细信息
        if stats['merged_details']:
            print(f"\n✅ 成功合并的文件:")
            for detail in stats['merged_details']:
                print(f"   - {detail['file_name']}: {detail['rows']} 行")
        
        if stats['duplicate_details']:
            print(f"\n🔄 重复检测详情:")
            for detail in stats['duplicate_details']:
                print(f"   - {detail['source']} → 重复于 {detail['duplicate_of']}")
        
        # 验证结果
        expected_merged = 2  # 两个不同的文件都应该被合并
        expected_duplicates = 0  # 没有重复
        expected_rows = len(data_excel) + 1 + len(data_csv) + 1  # 包含标题行
        
        print(f"\n🔍 预期结果:")
        print(f"  - 两个文件都应该被合并")
        print(f"  - 预期成功合并: {expected_merged}")
        print(f"  - 预期重复数量: {expected_duplicates}")
        print(f"  - 预期总行数: {expected_rows}")
        
        is_correct = (
            stats['merged_sources'] == expected_merged and
            stats['duplicate_sources'] == expected_duplicates and
            len(result) == expected_rows
        )
        
        print(f"\n📋 结果评估:")
        if is_correct:
            print("✅ CSV文件处理正常！")
            print("   - CSV文件没有被错误跳过")
            print("   - Excel和CSV文件都被正确合并")
        else:
            print("❌ CSV文件仍被错误处理！")
            print(f"   实际成功合并: {stats['merged_sources']} (预期: {expected_merged})")
            print(f"   实际重复数量: {stats['duplicate_sources']} (预期: {expected_duplicates})")
            print(f"   实际总行数: {len(result)} (预期: {expected_rows})")
        
        return is_correct
        
    except Exception as e:
        print(f"❌ 测试异常: {str(e)}")
        import traceback
        traceback.print_exc()
        return False
        
    finally:
        # 清理
        try:
            shutil.rmtree(temp_dir)
            print(f"\n🧹 清理测试目录")
        except:
            pass

def main():
    """主函数"""
    print("🔧 测试修复后的重复检测逻辑")
    print("=" * 80)
    
    # 测试1: 修复后的重复检测逻辑
    result1 = test_fixed_duplicate_logic()
    
    # 测试2: CSV文件不会被错误跳过
    result2 = test_csv_not_skipped()
    
    print(f"\n📊 测试总结:")
    print("=" * 80)
    print(f"重复检测修复: {'✅ 成功' if result1 else '❌ 失败'}")
    print(f"CSV文件处理: {'✅ 正常' if result2 else '❌ 仍有问题'}")
    
    if result1 and result2:
        print(f"\n🎉 所有测试通过！问题已修复！")
        print(f"   - Excel文件和CSV文件现在平等处理")
        print(f"   - 重复检测按文件处理顺序进行")
        print(f"   - 第一个文件被保留，重复文件被跳过")
    else:
        print(f"\n⚠️ 仍有问题需要进一步修复")

if __name__ == "__main__":
    main()
