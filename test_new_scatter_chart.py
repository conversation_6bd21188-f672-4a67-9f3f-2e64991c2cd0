#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试新的停车时长频率分布散点图
"""

import pandas as pd
import numpy as np
import os
from parking_report_generator import ReportGenerator
from parking_chart_generator import ParkingChartGenerator

def create_test_data_with_frequency():
    """创建包含频率分布的测试数据"""
    print("=== 创建测试数据 ===\n")
    
    # 创建丰富的测试数据
    np.random.seed(42)
    
    # 生成多样化的停车时长数据
    durations = []
    vehicle_types = []
    
    # 短时停车 (0-2小时) - 购物、办事
    short_durations = np.random.exponential(0.8, 60)
    short_durations = np.clip(short_durations, 0.1, 2.0)
    durations.extend(short_durations)
    vehicle_types.extend(['小型车'] * len(short_durations))
    
    # 中时停车 (2-8小时) - 工作、会议
    medium_durations = np.random.normal(4.5, 1.2, 40)
    medium_durations = np.clip(medium_durations, 2.0, 8.0)
    durations.extend(medium_durations)
    vehicle_types.extend(['中型车'] * len(medium_durations))
    
    # 长时停车 (8小时-3天) - 过夜、住宿
    long_durations = np.random.lognormal(2.5, 0.8, 25)
    long_durations = np.clip(long_durations, 8.0, 72.0)
    durations.extend(long_durations)
    vehicle_types.extend(['大型车'] * len(long_durations))
    
    # 超长时停车 (>3天) - 长期停放
    very_long_durations = np.random.uniform(72, 168, 15)
    durations.extend(very_long_durations)
    vehicle_types.extend(['特种车'] * len(very_long_durations))
    
    # 创建完整数据
    n_records = len(durations)
    base_time = pd.Timestamp('2024-01-01 08:00:00')
    
    entry_times = [base_time + pd.Timedelta(hours=i*0.3) for i in range(n_records)]
    exit_times = [entry_times[i] + pd.Timedelta(hours=durations[i]) for i in range(n_records)]
    
    test_data = pd.DataFrame({
        'entry_time': entry_times,
        'exit_time': exit_times,
        'duration': durations,
        'vtype': vehicle_types,
        'vehicle_id': [f'车{i+1:04d}' for i in range(n_records)],
        'entry_gate': np.random.choice(['A口', 'B口', 'C口'], n_records),
        'exit_gate': np.random.choice(['A口', 'B口', 'C口'], n_records)
    })
    
    print(f"创建测试数据: {len(test_data)} 条记录")
    print(f"停车时长范围: {test_data['duration'].min():.2f} - {test_data['duration'].max():.2f} 小时")
    print(f"车辆类型分布: {test_data['vtype'].value_counts().to_dict()}")
    print(f"唯一时长值: {test_data['duration'].nunique()} 个")
    
    return test_data

def generate_report_with_frequency_data():
    """生成包含频率分布数据的报告"""
    print("\n=== 生成包含频率分布数据的报告 ===\n")
    
    # 创建测试数据
    test_data = create_test_data_with_frequency()
    
    # 配置参数
    params = {
        'mode': 'mode2',
        '聚焦日期': '2024-01-01',
        '聚焦月份': '2024-01'
    }
    
    # 创建分析结果（模拟）
    analysis_results = {
        'peak_flow': pd.DataFrame({
            '时段': ['08:00-09:00', '09:00-10:00', '10:00-11:00'],
            '进场': [10, 15, 8],
            '出场': [5, 12, 10],
            '总量': [15, 27, 18]
        })
    }
    
    # 生成报告
    generator = ReportGenerator(test_data, analysis_results, params)
    generator.processed_data = test_data
    
    output_path = os.path.join(os.getcwd(), "频率分布散点图测试报告.xlsx")
    if os.path.exists(output_path):
        os.remove(output_path)
    
    result_path = generator.export_to_excel(output_path)
    
    if result_path and os.path.exists(result_path):
        print(f"✅ 报告生成成功: {result_path}")
        
        # 验证工作表
        with pd.ExcelFile(result_path) as xls:
            sheet_names = xls.sheet_names
            
        print(f"✅ 包含 {len(sheet_names)} 个工作表:")
        for i, sheet in enumerate(sheet_names, 1):
            print(f"   {i:2d}. {sheet}")
        
        # 检查新的工作表
        if '停车时长频率分布' in sheet_names:
            freq_data = pd.read_excel(result_path, sheet_name='停车时长频率分布')
            print(f"\n✅ 停车时长频率分布数据: {len(freq_data)} 行 x {len(freq_data.columns)} 列")
            print(f"✅ 数据列: {list(freq_data.columns)}")
            
            # 显示前几行数据
            print(f"\n📊 前5行数据:")
            print(freq_data.head().to_string())
        else:
            print(f"\n❌ 未找到停车时长频率分布工作表")
        
        return result_path
    else:
        print("❌ 报告生成失败")
        return None

def test_new_scatter_chart(excel_file):
    """测试新的散点图生成"""
    print(f"\n=== 测试新的散点图生成 ===\n")
    
    if not excel_file or not os.path.exists(excel_file):
        print("❌ Excel文件不存在，无法测试散点图")
        return None
    
    try:
        # 创建图表生成器
        chart_generator = ParkingChartGenerator(excel_file)
        
        print(f"📊 Excel文件: {excel_file}")
        print(f"📋 可用工作表: {list(chart_generator.excel_data.keys())}")
        
        # 检查是否有停车时长频率分布数据
        if '停车时长频率分布' in chart_generator.excel_data:
            freq_data = chart_generator.excel_data['停车时长频率分布']
            print(f"✅ 找到停车时长频率分布数据: {len(freq_data)} 行 x {len(freq_data.columns)} 列")
            print(f"📋 数据列: {list(freq_data.columns)}")
            
            # 显示数据统计
            print(f"\n📊 数据统计:")
            print(f"   - 唯一时长值: {freq_data['停车时长(小时)'].nunique()} 个")
            print(f"   - 时长范围: {freq_data['停车时长(小时)'].min():.3f} - {freq_data['停车时长(小时)'].max():.3f} 小时")
            print(f"   - 总频率: {freq_data['频率'].sum()}")
            print(f"   - 时段区间数: {freq_data['时段区间'].nunique()} 个")
            
            # 显示前几行数据
            print(f"\n📊 前3行数据:")
            display_cols = ['停车时长(小时)', '频率', '时段区间']
            if all(col in freq_data.columns for col in display_cols):
                print(freq_data[display_cols].head(3).to_string())
            
            # 生成散点图
            print(f"\n📈 开始生成新的散点图...")
            scatter_file = chart_generator.generate_duration_frequency_scatter()
            
            if scatter_file and os.path.exists(scatter_file):
                print(f"✅ 新散点图生成成功: {scatter_file}")
                
                # 验证文件大小
                file_size = os.path.getsize(scatter_file)
                print(f"📄 文件大小: {file_size:,} 字节")
                
                # 重命名为指定名称
                target_file = "停车时长频率散点图.html"
                if os.path.exists(target_file):
                    os.remove(target_file)
                
                os.rename(scatter_file, target_file)
                print(f"✅ 文件已重命名为: {target_file}")
                
                return target_file
            else:
                print(f"❌ 新散点图生成失败")
                return None
        else:
            print(f"❌ 未找到停车时长频率分布数据")
            return None
            
    except Exception as e:
        print(f"❌ 新散点图测试异常: {str(e)}")
        import traceback
        traceback.print_exc()
        return None

def main():
    """主函数"""
    print("🎯 停车时长频率分布散点图测试\n")
    
    # 步骤1：生成包含频率分布数据的报告
    excel_file = generate_report_with_frequency_data()
    
    if excel_file:
        # 步骤2：测试新的散点图生成
        scatter_file = test_new_scatter_chart(excel_file)
        
        print(f"\n{'='*60}")
        print(f"测试总结:")
        print(f"1. ✅ 测试数据和报告生成")
        print(f"2. {'✅' if scatter_file else '❌'} 新散点图生成测试")
        
        if scatter_file:
            print(f"\n🎉 停车时长频率分布散点图功能测试成功！")
            print(f"📊 散点图文件: {scatter_file}")
            print(f"💡 可以在浏览器中打开查看效果")
            print(f"📄 文件位置: {os.path.abspath(scatter_file)}")
        else:
            print(f"\n⚠️  散点图功能需要进一步检查")
    else:
        print(f"\n❌ 测试失败：无法生成基础报告")

if __name__ == "__main__":
    main()
