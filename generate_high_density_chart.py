#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
生成高密度概率密度图
"""

import os
import pandas as pd
import numpy as np
from parking_chart_generator import ParkingChartGenerator

def analyze_density_options():
    """分析不同密度选项的效果"""
    print("🔍 分析不同密度选项的效果\n")
    
    # 查找数据文件
    excel_files = [f for f in os.listdir('.') if f.endswith('.xlsx') and not f.startswith('~')]
    
    best_file = None
    best_data_count = 0
    
    for excel_file in excel_files:
        try:
            with pd.ExcelFile(excel_file) as xls:
                if '数据_总量' in xls.sheet_names:
                    df = pd.read_excel(excel_file, sheet_name='数据_总量')
                    if 'duration' in df.columns:
                        valid_duration = df['duration'].dropna()
                        valid_duration = valid_duration[valid_duration >= 0]
                        
                        if len(valid_duration) > best_data_count:
                            best_data_count = len(valid_duration)
                            best_file = excel_file
        except:
            continue
    
    if not best_file:
        print("❌ 未找到数据文件")
        return None
    
    print(f"📊 使用数据文件: {best_file}")
    
    # 读取数据
    df = pd.read_excel(best_file, sheet_name='数据_总量')
    duration_data = df['duration'].dropna()
    duration_data = duration_data[duration_data >= 0]
    
    print(f"📋 数据分析:")
    print(f"   - 总数据点: {len(duration_data)}")
    print(f"   - 唯一值数量: {duration_data.nunique()}")
    print(f"   - 时长范围: {duration_data.min():.3f} - {duration_data.max():.3f} 小时")
    
    # 分析不同密度模式的效果
    print(f"\n📊 不同密度模式对比:")
    
    # 1. 当前标准模式（约14个区间）
    n_bins_standard = min(50, max(10, int(np.sqrt(len(duration_data)))))
    print(f"   1. 标准模式: {n_bins_standard} 个区间")
    
    # 2. 高密度模式（更多区间）
    n_bins_high = min(200, max(50, len(duration_data) // 2))
    print(f"   2. 高密度模式: {n_bins_high} 个区间")
    
    # 3. 超高密度模式（每个唯一值）
    unique_count = duration_data.nunique()
    print(f"   3. 超高密度模式: {unique_count} 个唯一值点")
    
    # 显示唯一值的分布
    value_counts = duration_data.value_counts().sort_index()
    print(f"\n📊 唯一值频率分布（前10个）:")
    for i, (value, count) in enumerate(value_counts.head(10).items(), 1):
        print(f"   {i:2d}. {value:.3f}小时: {count} 次")
    
    if len(value_counts) > 10:
        print(f"   ... 还有 {len(value_counts) - 10} 个唯一值")
    
    return best_file, duration_data

def generate_multiple_density_charts():
    """生成多种密度的图表"""
    print(f"\n🎯 生成多种密度的概率密度图\n")
    
    # 分析数据
    result = analyze_density_options()
    if not result:
        return []
    
    excel_file, duration_data = result
    
    try:
        # 创建图表生成器
        chart_generator = ParkingChartGenerator(excel_file)
        
        print(f"📁 输出目录: {chart_generator.output_dir}")
        
        generated_files = []
        
        # 生成不同密度模式的图表
        density_modes = [
            ('ultra', '超高密度（每个唯一值一个点）'),
            ('high', '高密度（更多区间）'),
            ('custom', '自定义密度（100个区间）')
        ]
        
        for mode, description in density_modes:
            print(f"\n📈 生成{description}...")
            
            try:
                file_path = chart_generator.generate_high_density_probability_chart(
                    sheet_name='数据_总量', 
                    density_mode=mode
                )
                
                if file_path and os.path.exists(file_path):
                    file_size = os.path.getsize(file_path)
                    generated_files.append((file_path, mode, description))
                    print(f"   ✅ 生成成功: {os.path.basename(file_path)} ({file_size:,} 字节)")
                else:
                    print(f"   ❌ 生成失败")
                    
            except Exception as e:
                print(f"   ❌ 生成{mode}模式时出错: {str(e)}")
        
        return generated_files
        
    except Exception as e:
        print(f"❌ 生成图表时出错: {str(e)}")
        import traceback
        traceback.print_exc()
        return []

def compare_chart_characteristics(generated_files):
    """比较不同图表的特征"""
    print(f"\n📊 图表特征对比\n")
    
    if not generated_files:
        print("❌ 没有生成的图表可供比较")
        return
    
    print(f"{'模式':<15} {'文件大小':<12} {'描述'}")
    print(f"{'-'*60}")
    
    for file_path, mode, description in generated_files:
        file_size = os.path.getsize(file_path)
        file_name = os.path.basename(file_path)
        print(f"{mode:<15} {file_size:>8,} 字节  {description}")
    
    print(f"\n🎯 使用建议:")
    print(f"   📊 超高密度模式:")
    print(f"      - 优点: 显示每个具体的时长值")
    print(f"      - 缺点: 点可能过于密集，需要缩放查看")
    print(f"      - 适用: 精确分析具体时长分布")
    
    print(f"   📊 高密度模式:")
    print(f"      - 优点: 平衡了密度和可读性")
    print(f"      - 缺点: 仍然是区间统计，不是原始值")
    print(f"      - 适用: 详细的分布形状分析")
    
    print(f"   📊 自定义密度模式:")
    print(f"      - 优点: 可以调整区间数量")
    print(f"      - 缺点: 需要根据数据特点调整")
    print(f"      - 适用: 特定分析需求")

def explain_high_density_concept():
    """解释高密度概念"""
    print(f"\n📚 高密度概率密度图解释\n")
    
    print(f"🎯 什么是高密度概率密度图？")
    print(f"   ")
    print(f"   传统概率密度图:")
    print(f"   - 将数据分成较少的区间（如10-20个）")
    print(f"   - 每个区间用一个点表示")
    print(f"   - 适合查看整体分布形状")
    print(f"   ")
    print(f"   高密度概率密度图:")
    print(f"   - 使用更多的区间（如50-200个）")
    print(f"   - 或者每个唯一值一个点")
    print(f"   - 能看到更细致的分布细节")
    
    print(f"\n📊 三种密度模式对比:")
    print(f"   ")
    print(f"   🔸 超高密度模式 (ultra):")
    print(f"      - 每个唯一的duration值对应一个点")
    print(f"      - 点数 = 唯一值数量")
    print(f"      - Y轴显示该值的相对频率")
    print(f"      - 最接近原始数据的表示")
    print(f"   ")
    print(f"   🔸 高密度模式 (high):")
    print(f"      - 使用更多区间进行分组")
    print(f"      - 点数 = 有数据的区间数")
    print(f"      - Y轴显示真正的概率密度")
    print(f"      - 平衡了细节和统计意义")
    print(f"   ")
    print(f"   🔸 自定义密度模式 (custom):")
    print(f"      - 可以指定区间数量")
    print(f"      - 灵活调整密度级别")
    print(f"      - 适合特定分析需求")
    
    print(f"\n💡 如何选择密度模式？")
    print(f"   - 数据探索: 使用超高密度模式")
    print(f"   - 分布分析: 使用高密度模式")
    print(f"   - 报告展示: 使用标准密度模式")
    print(f"   - 特定需求: 使用自定义密度模式")

def main():
    """主函数"""
    print("🎯 生成高密度概率密度图\n")
    
    # 1. 解释高密度概念
    explain_high_density_concept()
    
    # 2. 生成多种密度图表
    generated_files = generate_multiple_density_charts()
    
    # 3. 比较图表特征
    compare_chart_characteristics(generated_files)
    
    # 4. 总结
    print(f"\n{'='*60}")
    print(f"🎉 高密度概率密度图生成完成!")
    
    if generated_files:
        print(f"✅ 成功生成 {len(generated_files)} 个图表:")
        for file_path, mode, description in generated_files:
            file_name = os.path.basename(file_path)
            print(f"   📊 {file_name}")
        
        print(f"\n📁 文件位置: C:\\Users\\<USER>\\Desktop\\停车分析")
        print(f"💡 可以在浏览器中打开查看不同密度效果")
        print(f"🔍 建议先查看超高密度模式，体验最密集的点分布")
    else:
        print(f"❌ 未生成任何图表")
    
    print(f"{'='*60}")

if __name__ == "__main__":
    main()
