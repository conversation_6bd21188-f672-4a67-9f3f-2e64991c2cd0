#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试完整报告生成，包含新的延停时长概率密度sheet
"""

import pandas as pd
import numpy as np
import os
from parking_report_generator import ReportGenerator

def create_comprehensive_test_data():
    """创建综合测试数据"""
    np.random.seed(42)
    
    # 生成更多样化的停车记录
    durations = []
    vehicle_types = []
    entry_gates = []
    exit_gates = []
    
    # 短时停车 (0-2小时) - 模拟购物、办事等
    short_durations = np.random.exponential(0.5, 80)
    short_durations = np.clip(short_durations, 0.1, 2.0)
    durations.extend(short_durations)
    vehicle_types.extend(['小型车'] * len(short_durations))
    entry_gates.extend(np.random.choice(['A口', 'B口'], len(short_durations)))
    exit_gates.extend(np.random.choice(['A口', 'B口'], len(short_durations)))
    
    # 中时停车 (2-8小时) - 模拟工作、会议等
    medium_durations = np.random.normal(5, 1.5, 60)
    medium_durations = np.clip(medium_durations, 2.0, 8.0)
    durations.extend(medium_durations)
    vehicle_types.extend(['中型车'] * len(medium_durations))
    entry_gates.extend(np.random.choice(['B口', 'C口'], len(medium_durations)))
    exit_gates.extend(np.random.choice(['B口', 'C口'], len(medium_durations)))
    
    # 长时停车 (8小时-3天) - 模拟过夜、住宿等
    long_durations = np.random.exponential(12, 40)
    long_durations = np.clip(long_durations, 8.0, 72.0)
    durations.extend(long_durations)
    vehicle_types.extend(['大型车'] * len(long_durations))
    entry_gates.extend(np.random.choice(['A口', 'C口'], len(long_durations)))
    exit_gates.extend(np.random.choice(['A口', 'C口'], len(long_durations)))
    
    # 超长时停车 (>3天) - 模拟长期停放
    very_long_durations = np.random.uniform(72, 168, 20)
    durations.extend(very_long_durations)
    vehicle_types.extend(['特种车'] * len(very_long_durations))
    entry_gates.extend(np.random.choice(['A口', 'B口', 'C口'], len(very_long_durations)))
    exit_gates.extend(np.random.choice(['A口', 'B口', 'C口'], len(very_long_durations)))
    
    # 创建完整的停车数据
    n_records = len(durations)
    base_time = pd.Timestamp('2024-01-01 08:00:00')
    
    # 生成入场时间（分布在一天内）
    entry_times = []
    for i in range(n_records):
        # 随机分布在24小时内
        hour_offset = np.random.uniform(0, 24)
        entry_times.append(base_time + pd.Timedelta(hours=hour_offset))
    
    # 根据停车时长计算出场时间
    exit_times = [entry_times[i] + pd.Timedelta(hours=durations[i]) for i in range(n_records)]
    
    # 创建DataFrame
    test_data = pd.DataFrame({
        'entry_time': entry_times,
        'exit_time': exit_times,
        'duration': durations,  # 停车时长（小时）
        'vtype': vehicle_types,
        'vehicle_id': [f'车{i+1:04d}' for i in range(n_records)],
        'entry_gate': entry_gates,
        'exit_gate': exit_gates,
        'license_plate': [f'京A{i+1:05d}' for i in range(n_records)]
    })
    
    return test_data

def test_density_sheet_generation():
    """测试延停时长概率密度sheet生成"""
    print("=== 测试延停时长概率密度sheet生成 ===\n")
    
    # 创建测试数据
    test_data = create_comprehensive_test_data()
    print(f"创建测试数据: {len(test_data)} 条记录")
    print(f"停车时长范围: {test_data['duration'].min():.2f} - {test_data['duration'].max():.2f} 小时")
    print(f"车辆类型分布: {test_data['vtype'].value_counts().to_dict()}")
    print()
    
    # 配置参数
    params = {
        'mode': 'mode2',
        '聚焦日期': '2024-01-01',
        '聚焦月份': '2024-01',
        # 自定义时长分段配置
        'duration_control_points': [2, 8, 24, 72],  # 2小时, 8小时, 1天, 3天
        'duration_period_lengths': [0.5, 1, 16, 48]  # 30分钟, 1小时, 16小时, 2天
    }
    
    # 创建报告生成器
    generator = ReportGenerator(test_data, params)
    generator.processed_data = test_data  # 设置处理后的数据
    
    # 测试单独的延停时长概率密度计算
    print("1. 测试延停时长概率密度计算:")
    try:
        density_stats = generator._calculate_duration_probability_density()
        if not density_stats.empty:
            print(f"   ✅ 成功计算，共 {len(density_stats)} 个时段")
            print(f"   ✅ 总记录数: {density_stats['频数'].sum()}")
            print(f"   ✅ 包含列: {list(density_stats.columns)}")
            
            # 显示前几个时段的统计
            print("\n   前5个时段统计:")
            for i in range(min(5, len(density_stats))):
                row = density_stats.iloc[i]
                print(f"     {row['时长区间']:20} | 频数: {row['频数']:3d} | 百分比: {row['百分比(%)']:6.2f}%")
        else:
            print("   ❌ 计算失败，返回空DataFrame")
    except Exception as e:
        print(f"   ❌ 计算失败: {str(e)}")
        import traceback
        traceback.print_exc()
    
    print("\n" + "="*60 + "\n")
    
    # 测试导出到Excel
    print("2. 测试导出延停时长概率密度sheet到Excel:")
    try:
        output_path = os.path.join(os.getcwd(), "测试报告_延停时长概率密度.xlsx")
        
        # 删除已存在的文件
        if os.path.exists(output_path):
            os.remove(output_path)
        
        with pd.ExcelWriter(output_path, engine='xlsxwriter') as writer:
            workbook = writer.book
            
            # 只导出延停时长概率密度sheet
            generator._export_duration_probability_density(writer, workbook)
            
        print(f"   ✅ 成功导出到: {output_path}")
        
        # 验证文件内容
        if os.path.exists(output_path):
            with pd.ExcelFile(output_path) as xls:
                sheet_names = xls.sheet_names
                print(f"   ✅ 包含工作表: {sheet_names}")
                
                if '延停时长概率密度' in sheet_names:
                    df = pd.read_excel(output_path, sheet_name='延停时长概率密度')
                    print(f"   ✅ 工作表数据: {len(df)} 行 x {len(df.columns)} 列")
                    print(f"   ✅ 列名: {list(df.columns)}")
        
    except Exception as e:
        print(f"   ❌ 导出失败: {str(e)}")
        import traceback
        traceback.print_exc()

def test_integration_with_full_report():
    """测试与完整报告的集成"""
    print("\n=== 测试与完整报告的集成 ===\n")
    
    # 创建测试数据
    test_data = create_comprehensive_test_data()
    
    # 配置参数
    params = {
        'mode': 'mode2',
        '聚焦日期': '2024-01-01',
        '聚焦月份': '2024-01'
    }
    
    # 创建报告生成器
    generator = ReportGenerator(test_data, params)
    
    try:
        # 生成完整报告
        output_path = os.path.join(os.getcwd(), "完整测试报告_含延停时长概率密度.xlsx")
        
        # 删除已存在的文件
        if os.path.exists(output_path):
            os.remove(output_path)
        
        print("开始生成完整报告...")
        result_path = generator.export_to_excel(output_path)
        
        if result_path and os.path.exists(result_path):
            print(f"✅ 成功生成完整报告: {result_path}")
            
            # 验证是否包含延停时长概率密度sheet
            with pd.ExcelFile(result_path) as xls:
                sheet_names = xls.sheet_names
                print(f"✅ 报告包含 {len(sheet_names)} 个工作表:")
                for i, sheet in enumerate(sheet_names, 1):
                    print(f"   {i:2d}. {sheet}")
                
                if '延停时长概率密度' in sheet_names:
                    print(f"\n✅ 成功包含'延停时长概率密度'工作表")
                    
                    # 读取并验证数据
                    df = pd.read_excel(result_path, sheet_name='延停时长概率密度')
                    print(f"✅ 延停时长概率密度数据: {len(df)} 行 x {len(df.columns)} 列")
                    
                    # 显示数据摘要
                    if not df.empty:
                        total_records = df['频数'].sum() if '频数' in df.columns else 0
                        print(f"✅ 总记录数: {total_records}")
                        print(f"✅ 时段数量: {len(df)}")
                else:
                    print(f"❌ 未找到'延停时长概率密度'工作表")
        else:
            print(f"❌ 报告生成失败")
            
    except Exception as e:
        print(f"❌ 完整报告生成失败: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    # 运行测试
    test_density_sheet_generation()
    test_integration_with_full_report()
    
    print(f"\n{'='*60}")
    print("测试完成！")
    print("如果所有测试都通过，说明延停时长概率密度功能已成功集成到报告生成系统中。")
