# 🚗 车辆类型进出量时间分布图表功能说明

## 📊 功能概览

新增了车辆类型进出量时间分布图表生成功能，能够为进出量时间分布数据中的各类车辆类型生成独立的柱状图，并将所有图表整合到一个页面中。

## 🎯 功能特点

### 1. 自动识别车辆类型
- 自动扫描数据列，识别包含车辆类型信息的列
- 支持的车辆类型：私家车、网约车、出租车、货车、客车、摩托车、电动车
- 智能匹配列名中的"进场"、"出场"等关键词

### 2. 独立图表生成
- 为每种车辆类型生成独立的柱状图
- 每个图表包含该车辆类型的进场和出场数据
- 使用专业演讲风格的颜色配置

### 3. 页面整合
- 使用pyecharts的Page布局
- 所有车辆类型图表整合到一个HTML文件
- 文件名：`进出量时间分布_车型.html`

## 🔧 技术实现

### 核心方法
```python
def generate_vehicle_type_traffic_charts(self, sheet_name='进出量时间分布'):
    """
    生成各类车辆类型的进出量时间分布图表（整合到一个页面）
    
    Args:
        sheet_name: str, 工作表名称
        
    Returns:
        str: 生成的HTML文件路径
    """
```

### 数据识别逻辑
```python
# 寻找车辆类型相关的列
for i, col in enumerate(columns[1:], 1):
    col_str = str(col).lower()
    if any(keyword in col_str for keyword in ['进场', '出场', '进入', '离开', '入场', '出入']):
        # 提取车辆类型名称
        for vehicle_type in ['私家车', '网约车', '出租车', '货车', '客车', '摩托车', '电动车']:
            if vehicle_type in str(col):
                vehicle_types.add(vehicle_type)
                # 记录列信息
```

### 颜色配置
```python
vehicle_colors = {
    '私家车': {'进场': '#2E86AB', '出场': '#A23B72'},
    '网约车': {'进场': '#1B4F72', '出场': '#922B21'},
    '出租车': {'进场': '#148F77', '出场': '#B7950B'},
    '货车': {'进场': '#7D3C98', '出场': '#D35400'},
    '客车': {'进场': '#2874A6', '出场': '#C0392B'},
    '摩托车': {'进场': '#117A65', '出场': '#E67E22'},
    '电动车': {'进场': '#5B2C6F', '出场': '#F39C12'}
}
```

## 📋 数据要求

### 列名格式
数据列名应包含以下信息：
- **车辆类型名称**：私家车、网约车、出租车等
- **方向关键词**：进场、出场、进入、离开、入场、出入

### 示例列名
```
✅ 正确格式：
- 私家车进场数量
- 网约车出场数量
- 出租车进入数量
- 货车离开数量

❌ 错误格式：
- 私家车数量 (缺少方向)
- 进场车辆 (缺少车辆类型)
- 车辆统计 (信息不明确)
```

### 数据结构示例
```
时间段          | 私家车进场数量 | 私家车出场数量 | 网约车进场数量 | 网约车出场数量
08:00-09:00    | 80           | 50           | 30           | 20
09:00-10:00    | 120          | 80           | 45           | 30
10:00-11:00    | 100          | 110          | 35           | 35
```

## 🎨 视觉设计

### 专业演讲风格
- **高对比度颜色**：确保远距离观看清晰
- **统一色彩体系**：每种车辆类型有专属颜色
- **专业外观**：适合商务演示和学术报告

### 图表特性
- **标题优化**：每个图表有清晰的标题和副标题
- **Tooltip优化**：白色背景，深色文字，高可读性
- **交互功能**：支持缩放、平移等交互操作
- **响应式设计**：适配不同屏幕尺寸

## 🚀 使用方法

### 1. 自动生成（推荐）
```python
from parking_chart_generator import ParkingChartGenerator

# 创建图表生成器
chart_generator = ParkingChartGenerator('your_data.xlsx', 'output_dir')

# 生成所有图表（包括车辆类型图表）
chart_generator.generate_all_charts()
```

### 2. 单独生成
```python
# 只生成车辆类型图表
chart_file = chart_generator.generate_vehicle_type_traffic_charts()
print(f"图表已生成: {chart_file}")
```

### 3. 自定义工作表
```python
# 指定特定的工作表
chart_file = chart_generator.generate_vehicle_type_traffic_charts('自定义工作表名')
```

## 📊 输出结果

### 文件信息
- **文件名**：`进出量时间分布_车型.html`
- **文件类型**：HTML格式，可在浏览器中打开
- **文件大小**：根据数据量和图表数量而定

### 图表内容
- 每种车辆类型一个独立图表
- 每个图表包含进场和出场两个数据系列
- 所有图表在同一页面中垂直排列
- 支持独立的缩放和交互操作

## 🔍 故障排除

### 常见问题

#### 1. 未找到车辆类型数据
**问题**：显示"❌ 未找到车辆类型相关的数据列"
**解决**：
- 检查列名是否包含车辆类型名称
- 确保列名包含"进场"或"出场"关键词
- 验证数据格式是否正确

#### 2. 图表为空
**问题**：生成的图表没有数据显示
**解决**：
- 检查数据列是否包含有效数值
- 确认数据不全为0或空值
- 验证数据类型是否为数值型

#### 3. 颜色显示异常
**问题**：图表颜色不符合预期
**解决**：
- 检查车辆类型名称是否在预定义列表中
- 确认颜色配置是否正确加载
- 验证浏览器是否支持CSS颜色格式

### 调试信息
运行时会输出详细的调试信息：
```
📋 数据列名: ['时间段', '私家车进场数量', '私家车出场数量', ...]
🚗 发现车辆类型: ['私家车', '网约车', '出租车']
📊 相关数据列: 6 个
📊 包含 3 个车辆类型的图表
```

## 🔄 版本更新

### v2.2 新增功能
- ✅ 车辆类型自动识别
- ✅ 独立图表生成
- ✅ Page布局整合
- ✅ 专业颜色配置
- ✅ 优化的Tooltip样式

### 兼容性
- 兼容现有的所有图表生成功能
- 不影响原有的进出量时间分布图表
- 可与其他图表功能同时使用

---

*功能开发完成时间: 2025-06-20*  
*适用版本: parking_chart_generator.py v2.2+*
