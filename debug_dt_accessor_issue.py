import pandas as pd
from parking_data_base import ParkingDataBase
from parking_analyzer import ParkingAnalyzer
import logging

# 设置日志
logging.basicConfig(level=logging.DEBUG, format='%(levelname)s:%(name)s:%(message)s')
logger = logging.getLogger(__name__)

# 读取实际数据
csv_file = r'C:\Users\<USER>\Desktop\停车分析\数据\正泰\义乌正泰_北门_合并_20250623_230242.csv'

try:
    data = pd.read_csv(csv_file, encoding='utf-8')
except:
    try:
        data = pd.read_csv(csv_file, encoding='gbk')
    except:
        data = pd.read_csv(csv_file, encoding='gb2312')

print(f"原始数据: {len(data)} 条记录")

# 设置参数
params = {
    'mode': 'mode1',
    'input_file': csv_file,
    'output': r'C:\Users\<USER>\Desktop\停车分析',
    'date': '',
    'month': '',
    'time_interval': 60,
    'time_slip': 30,
}

MODE_CONFIG = {
    'mode1': {
        '车辆唯一标识字段': '车牌',
        '车辆类型字段': '车辆类型',
        '时间记录字段': '时间',
        '进出类型字段': '方向',
        '进出标识值': ('进', '出'),
        '道闸编号字段': '出入口'
    }
}

# 合并配置
params.update(MODE_CONFIG)

# 创建处理器
processor = ParkingDataBase(data, params, logger)

# 处理数据
try:
    processor.process()
    print(f"\n处理后数据: {len(processor.processed_data)} 条记录")
    
    if len(processor.processed_data) > 0:
        print("处理后的数据类型:")
        print(processor.processed_data.dtypes)
        
        print("\n检查时间字段:")
        for col in ['entry_time', 'exit_time', 'timestamp']:
            if col in processor.processed_data.columns:
                print(f"{col}: {processor.processed_data[col].dtype}")
                print(f"  前5个值: {processor.processed_data[col].head().tolist()}")
                print(f"  是否有NaT: {processor.processed_data[col].isna().sum()}")
        
        # 创建分析器
        print("\n创建分析器...")
        analyzer = ParkingAnalyzer(data, params, logger)
        
        # 尝试分析
        print("开始分析...")
        try:
            results = analyzer.analyze()
            print(f"分析成功: {len(results)} 个结果")
        except Exception as e:
            print(f"分析失败: {e}")
            import traceback
            traceback.print_exc()
            
            # 检查分析器中的数据
            print("\n检查分析器中的数据:")
            if hasattr(analyzer, 'processed_data') and analyzer.processed_data is not None:
                print(f"分析器数据行数: {len(analyzer.processed_data)}")
                print("分析器数据类型:")
                print(analyzer.processed_data.dtypes)
                
                # 检查时间字段
                for col in ['entry_time', 'exit_time', 'timestamp']:
                    if col in analyzer.processed_data.columns:
                        print(f"{col}: {analyzer.processed_data[col].dtype}")
                        print(f"  是否有NaT: {analyzer.processed_data[col].isna().sum()}")
                        # 检查是否有非datetime类型的值
                        non_datetime = analyzer.processed_data[col].apply(lambda x: not pd.api.types.is_datetime64_any_dtype(type(x)))
                        if non_datetime.any():
                            print(f"  发现非datetime值: {analyzer.processed_data[col][non_datetime].head()}")
            else:
                print("分析器中没有processed_data")
    else:
        print("❌ 处理后数据为空")
        
except Exception as e:
    print(f"处理失败: {e}")
    import traceback
    traceback.print_exc()
