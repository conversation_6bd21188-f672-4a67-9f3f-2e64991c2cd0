#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试出入口Timeline动态图表生成功能
"""

import os

def test_gate_timeline():
    """测试出入口Timeline动态图表生成"""
    print("🚪 测试出入口Timeline动态图表生成")
    print("=" * 50)
    
    # Excel文件路径
    excel_file = r'C:\Users\<USER>\Desktop\停车分析\义乌火车站道闸_私家车_合并_20250617_083509_analysis_20250618_211554.xlsx'
    
    if not os.path.exists(excel_file):
        print(f"❌ Excel文件不存在: {excel_file}")
        return False
    
    try:
        # 导入图表生成器
        from parking_chart_generator import ParkingChartGenerator
        
        # 创建图表生成器
        print("📊 创建图表生成器...")
        chart_generator = ParkingChartGenerator(excel_file, None)
        
        # 检查是否有进出量时间分布(按道闸)工作表
        target_sheet = '进出量时间分布(按道闸)'
        if target_sheet not in chart_generator.excel_data:
            print(f"❌ 未找到'{target_sheet}'工作表")
            available_sheets = list(chart_generator.excel_data.keys())
            print(f"可用工作表: {available_sheets}")
            return False
        
        # 获取数据并分析结构
        data = chart_generator.excel_data[target_sheet]
        columns = list(data.columns)
        total_cols = len(columns)
        
        print(f"📋 数据结构分析:")
        print(f"   总列数: {total_cols}")
        print(f"   第1列: {columns[0]} (时间段)")
        
        if total_cols > 1:
            gate_cols = columns[1:]
            print(f"   出入口相关列: {gate_cols}")
            
            # 分析出入口数据结构
            gate_cols_count = len(gate_cols)
            estimated_gates = gate_cols_count // 3
            print(f"   出入口相关列数: {gate_cols_count}")
            print(f"   预估出入口数量: {estimated_gates}")
            
            # 显示每个出入口的列结构
            for i in range(min(estimated_gates, 3)):  # 最多显示前3个出入口的结构
                start_idx = 1 + i * 3
                if start_idx + 2 < total_cols:
                    print(f"   出入口{i+1}: {columns[start_idx]} | {columns[start_idx+1]} | {columns[start_idx+2]}")
        
        # 尝试生成Timeline图表
        print(f"\n🎬 尝试生成出入口Timeline动态图表...")
        result = chart_generator.generate_gate_traffic_timeline()
        
        if result:
            print(f"✅ 成功生成: {os.path.basename(result)}")
            
            # 检查文件是否存在
            if os.path.exists(result):
                file_size = os.path.getsize(result) / 1024
                print(f"📄 文件大小: {file_size:.1f}KB")
                print(f"📁 文件路径: {result}")
                
                # 读取HTML内容检查Timeline特征
                with open(result, 'r', encoding='utf-8') as f:
                    html_content = f.read()
                
                # 检查Timeline相关特征
                timeline_checks = {
                    'Timeline组件': 'Timeline' in html_content,
                    '动态播放': 'play_interval' in html_content or 'auto_play' in html_content,
                    '多个时间段': html_content.count('add(') > 1,  # 多个时间段
                    '柱状图': 'Bar(' in html_content,
                    '出入口数据': '出入口' in html_content or '道闸' in html_content,
                }
                
                print(f"\n📊 Timeline图表验证:")
                all_checks_passed = True
                for check_name, is_passed in timeline_checks.items():
                    status = "✅" if is_passed else "❌"
                    print(f"   {status} {check_name}: {'通过' if is_passed else '未通过'}")
                    if not is_passed:
                        all_checks_passed = False
                
                # 检查时间段数量
                time_segments = html_content.count('add(')
                if time_segments > 0:
                    print(f"\n🕐 Timeline信息:")
                    print(f"   检测到时间段数量: {time_segments}")
                    print(f"   支持动态播放切换")
                    print(f"   可查看各时间段的出入口流量对比")
                
                if all_checks_passed:
                    print("\n✅ Timeline动态图表生成成功！")
                    print("   - 支持时间段动态切换")
                    print("   - 显示各出入口总量对比")
                    print("   - 可播放查看流量变化趋势")
                    print("   - 文件名: 进出量出入口分布.html")
                    return True
                else:
                    print("\n⚠️ Timeline图表生成但可能不是预期格式")
                    return False
            else:
                print(f"❌ 文件未生成: {result}")
                return False
        else:
            print("❌ 未能生成Timeline图表")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def demo_timeline_features():
    """演示Timeline功能特点"""
    print("\n🎬 Timeline动态图表特点")
    print("=" * 50)
    
    print("🎯 功能特色:")
    print("   ✅ 动态时间轴：可切换不同时间段查看")
    print("   ✅ 出入口对比：显示各出入口总量对比")
    print("   ✅ 播放控制：支持自动播放和手动控制")
    print("   ✅ 数据聚焦：仅显示总量，突出关键信息")
    
    print("\n📊 数据结构要求:")
    print("   - 第1列：时间段")
    print("   - 后续列：出入口A-进、出入口A-出、出入口A-总量")
    print("   - 继续：出入口B-进、出入口B-出、出入口B-总量")
    print("   - 以此类推...")
    
    print("\n🎨 视觉效果:")
    print("   - 每个时间段一个柱状图")
    print("   - X轴：各个出入口名称")
    print("   - Y轴：总流量数值")
    print("   - 颜色：使用专业演讲风格橙色")
    
    print("\n🎮 交互功能:")
    print("   - 时间轴滑块：手动选择时间段")
    print("   - 播放按钮：自动播放所有时间段")
    print("   - 播放间隔：2秒/段，可循环播放")
    print("   - Tooltip：鼠标悬停查看详细数值")
    
    print("\n📋 应用场景:")
    print("   - 流量趋势分析：观察各出入口流量随时间变化")
    print("   - 瓶颈识别：找出高峰时段的流量瓶颈出入口")
    print("   - 演示汇报：动态展示停车场运营状况")
    print("   - 决策支持：为出入口管理提供数据支持")

def main():
    """主函数"""
    # 演示功能特点
    demo_timeline_features()
    
    # 测试功能
    success = test_gate_timeline()
    
    if success:
        print("\n🎉 测试成功！Timeline动态图表已生成！")
        print("📁 文件名: 进出量出入口分布.html")
        print("💡 打开文件后可以:")
        print("   1. 使用时间轴滑块切换时间段")
        print("   2. 点击播放按钮观看动态变化")
        print("   3. 对比各出入口在不同时间段的流量")
        print("🔍 建议在浏览器中查看动态效果")
    else:
        print("\n⚠️ 测试失败")
        print("💡 可能的原因:")
        print("   1. Excel文件中没有'进出量时间分布(按道闸)'工作表")
        print("   2. 数据列结构不符合预期（不是3的倍数）")
        print("   3. 数据格式不正确或为空")
    
    print("\n" + "="*50)
    input("按回车键退出...")

if __name__ == "__main__":
    main()
