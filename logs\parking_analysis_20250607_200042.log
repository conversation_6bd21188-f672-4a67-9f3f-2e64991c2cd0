2025-06-07 20:00:42,012 - main - INFO - 开始数据分析任务
2025-06-07 20:00:42,013 - main - INFO - 使用处理模式: mode_p2
2025-06-07 20:00:42,013 - main - INFO - 读取数据文件: C:\Users\<USER>\Desktop\停车分析\数据\火车站\5.31-6.2私家车.csv
2025-06-07 20:01:00,697 - DataReader - INFO - 检测到文件编码: GB2312
2025-06-07 20:01:00,841 - DataReader - INFO - 成功读取数据文件，共 15479 行
2025-06-07 20:01:00,866 - DataReader - INFO - 数据结构验证通过
2025-06-07 20:01:00,866 - main - INFO - 开始数据处理
2025-06-07 20:01:00,871 - DataProcessor - INFO - 开始数据处理
2025-06-07 20:01:01,000 - DataProcessor - INFO - 数据处理完成，统计信息: {'total_records': 15479, 'vehicle_type_counts': {'临时用户A': 15168, '免费用户A': 311}, 'entry_gate_counts': {'2号进口1': 10783, '地下3号入口2': 3161, '地下3号入口1': 1291, '地面入口': 139, '地面入口2': 37, '地下室2号出口-2': 31, '停车场1号西出口2': 25, '停车场1号西出口': 9, '地面出口': 3}, 'exit_gate_counts': {'地下室2号出口-2': 8948, '停车场1号西出口2': 3960, '停车场1号西出口': 2392, '地面出口': 142, '地面出口2': 37}, 'avg_duration': 1.955036931757004, 'max_duration': 256.15777777777777}
2025-06-07 20:01:01,007 - DataProcessor - WARNING - 过滤掉 3200 条异常停车时长记录
2025-06-07 20:01:01,031 - main - INFO - 开始数据分析
2025-06-07 20:01:01,085 - main - INFO - 生成分析报告
2025-06-07 20:01:01,086 - DataReader - INFO - 输出文件路径: reports\5.31-6.2私家车_分析结果_20250607_200101.xlsx
2025-06-07 20:01:02,396 - ReportGenerator - ERROR - 生成Excel报告失败: 'NoneType' object is not iterable
Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\停车分析\pythoncode\1.开发中\p_parking_report_generator.py", line 228, in generate_excel_report
    self.generate_pattern_charts() +
  File "c:\Users\<USER>\Desktop\停车分析\pythoncode\1.开发中\p_parking_report_generator.py", line 123, in generate_pattern_charts
    sns.histplot(
  File "D:\anaconda\lib\site-packages\seaborn\distributions.py", line 1423, in histplot
    p.plot_univariate_histogram(
  File "D:\anaconda\lib\site-packages\seaborn\distributions.py", line 424, in plot_univariate_histogram
    for sub_vars, sub_data in self.iter_data("hue", from_comp_data=True):
  File "D:\anaconda\lib\site-packages\seaborn\_core.py", line 996, in iter_data
    iter_keys = itertools.product(*grouping_keys)
TypeError: 'NoneType' object is not iterable
2025-06-07 20:01:02,401 - main - ERROR - 处理过程中出现错误: 'NoneType' object is not iterable
Traceback (most recent call last):
  File "c:/Users/<USER>/Desktop/停车分析/pythoncode/1.开发中/p_parking_main.py", line 108, in main
    report_generator.generate_report(output_path, report_format)
  File "c:\Users\<USER>\Desktop\停车分析\pythoncode\1.开发中\p_parking_report_generator.py", line 341, in generate_report
    self.generate_excel_report(output_path)
  File "c:\Users\<USER>\Desktop\停车分析\pythoncode\1.开发中\p_parking_report_generator.py", line 228, in generate_excel_report
    self.generate_pattern_charts() +
  File "c:\Users\<USER>\Desktop\停车分析\pythoncode\1.开发中\p_parking_report_generator.py", line 123, in generate_pattern_charts
    sns.histplot(
  File "D:\anaconda\lib\site-packages\seaborn\distributions.py", line 1423, in histplot
    p.plot_univariate_histogram(
  File "D:\anaconda\lib\site-packages\seaborn\distributions.py", line 424, in plot_univariate_histogram
    for sub_vars, sub_data in self.iter_data("hue", from_comp_data=True):
  File "D:\anaconda\lib\site-packages\seaborn\_core.py", line 996, in iter_data
    iter_keys = itertools.product(*grouping_keys)
TypeError: 'NoneType' object is not iterable
