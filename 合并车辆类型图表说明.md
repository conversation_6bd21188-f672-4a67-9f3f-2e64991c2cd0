# 📊 合并车辆类型图表功能说明

## 🎯 功能概览

将原本分别显示的多个车辆类型图表合并到一张图中，使用统一的图表尺寸，便于对比分析不同车辆类型的进出量数据。

## 🔄 改进对比

### 修改前（Page布局）
- **多个独立图表**：每种车辆类型一个单独的图表
- **垂直排列**：图表在页面中垂直堆叠显示
- **分散对比**：需要上下滚动才能对比不同车辆类型
- **文件结构**：使用pyecharts的Page布局

### 修改后（合并图表）
- **单一图表**：所有车辆类型在一张图中显示
- **统一尺寸**：1200x600px的标准图表尺寸
- **直观对比**：所有数据在同一时间轴上直接对比
- **文件结构**：单一Bar图表，包含多个数据系列

## 🎨 视觉设计

### 1. 图表尺寸
```python
width="1200px"   # 标准宽度
height="600px"   # 增加高度以容纳更多数据系列
```

### 2. 颜色配置
每种车辆类型使用配对的颜色：
```python
vehicle_colors = {
    '私家车': {'进场': '#2E86AB', '出场': '#A23B72'},
    '网约车': {'进场': '#1B4F72', '出场': '#922B21'},
    '出租车': {'进场': '#148F77', '出场': '#B7950B'},
    '货车': {'进场': '#7D3C98', '出场': '#D35400'},
    '客车': {'进场': '#2874A6', '出场': '#C0392B'},
    '摩托车': {'进场': '#117A65', '出场': '#E67E22'},
    '电动车': {'进场': '#5B2C6F', '出场': '#F39C12'}
}
```

### 3. 数据系列命名
- **格式**：`[车辆类型]-进场` / `[车辆类型]-出场`
- **示例**：`私家车-进场`、`私家车-出场`、`网约车-进场`、`网约车-出场`

### 4. 智能图例
```python
legend_opts=opts.LegendOpts(
    pos_top="8%",
    type_="scroll" if series_count > 8 else "plain"  # 超过8个系列时自动滚动
)
```

## 🔧 技术实现

### 核心代码结构
```python
# 创建单一合并图表
combined_chart = Bar(init_opts=opts.InitOpts(
    theme=self.chart_config['theme'],
    width="1200px",
    height="600px"
))

# 为每种车辆类型添加数据系列
for vehicle_type in sorted(vehicle_types):
    # 添加进场数据系列
    combined_chart.add_yaxis(
        series_name=f"{vehicle_type}-进场",
        y_axis=entry_data,
        color=colors['进场']
    )
    
    # 添加出场数据系列
    combined_chart.add_yaxis(
        series_name=f"{vehicle_type}-出场", 
        y_axis=exit_data,
        color=colors['出场']
    )
```

### 数据处理流程
1. **数据收集**：从Excel中提取各车辆类型的进出场数据
2. **数据验证**：跳过空数据的车辆类型
3. **系列创建**：为每种车辆类型创建进场和出场两个数据系列
4. **颜色分配**：根据车辆类型分配专业演讲风格的颜色
5. **图表渲染**：生成单一HTML文件

## 📈 功能特点

### 1. 数据对比优势
- **时间轴统一**：所有车辆类型使用相同的时间段
- **数值对比**：可直接比较不同车辆类型的流量
- **趋势分析**：容易识别各车辆类型的流量变化模式

### 2. 交互功能
- **缩放支持**：支持滑块和内置缩放
- **图例控制**：点击图例可显示/隐藏特定车辆类型
- **Tooltip优化**：鼠标悬停显示详细数据

### 3. 专业外观
- **演讲适配**：适合大屏幕演示
- **颜色区分**：高对比度的专业颜色配置
- **字体优化**：清晰的标题和标签

## 🎯 使用场景

### 1. 数据分析
- **流量对比**：比较不同车辆类型的进出场流量
- **高峰识别**：识别各车辆类型的高峰时段
- **趋势分析**：分析车辆类型流量的时间变化趋势

### 2. 演示报告
- **管理汇报**：向管理层展示停车场使用情况
- **客户演示**：向客户展示停车场运营数据
- **学术研究**：用于交通流量研究和分析

### 3. 运营决策
- **资源配置**：根据车辆类型流量配置停车资源
- **收费策略**：制定差异化的停车收费策略
- **设施规划**：规划不同车辆类型的停车区域

## 📊 输出效果

### 文件信息
- **文件名**：`进出量时间分布_车型.html`
- **文件类型**：HTML格式，可在浏览器中打开
- **文件大小**：根据数据量和车辆类型数量而定

### 图表内容
- **标题**：`各类车辆进出量时间分布`
- **副标题**：显示包含的车辆类型数量
- **数据系列**：每种车辆类型2个系列（进场+出场）
- **图例**：显示所有车辆类型和方向

## 💡 使用建议

### 1. 数据准备
- 确保Excel数据包含多种车辆类型的列
- 列名格式：前4列（基础数据）+ 车辆类型列 + 后3列（可选）
- 数据完整性：避免大量空值或零值

### 2. 显示优化
- **车辆类型数量**：建议不超过6种，以保持图表清晰
- **时间段数量**：建议不超过24个时间段
- **屏幕尺寸**：建议在1920x1080或更高分辨率下查看

### 3. 分析技巧
- **图例控制**：点击图例项目可隐藏/显示特定数据系列
- **缩放功能**：使用滑块聚焦特定时间段
- **数据导出**：可通过浏览器打印功能导出为PDF

## 🔄 版本更新

### v2.3 新功能
- ✅ 合并图表显示
- ✅ 统一图表尺寸
- ✅ 智能图例布局
- ✅ 优化的颜色配置
- ✅ 增强的交互功能

### 兼容性
- 完全兼容现有的数据格式
- 保持原有的文件命名规则
- 不影响其他图表生成功能

---

*功能更新完成时间: 2025-06-20*  
*适用版本: parking_chart_generator.py v2.3+*
