"""
简化模式报告生成器模块
专门处理Mode1缺少车牌号的数据源，只生成可行的报告sheet
"""

import os
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import xlsxwriter
from field_mapping_config import STANDARDIZED_FIELDS

class ReportGeneratorSimple:
    """简化模式报告生成器"""
    
    def __init__(self, data, analysis_results, params, input_total_records=None):
        """
        初始化简化报告生成器
        
        Args:
            data: DataFrame, 处理后的数据
            analysis_results: dict, 分析结果
            params: dict, 参数配置
            input_total_records: int, 原始输入记录数
        """
        self.data = data
        self.analysis_results = analysis_results
        self.params = params
        self.input_total_records = input_total_records or len(data) if data is not None else 0
        self._filtered_data_cache = None
    
    def export_to_excel(self, output_path):
        """
        导出简化报告到Excel
        
        Args:
            output_path: str, 输出文件路径
            
        Returns:
            str: 生成的Excel文件路径
        """
        writer = None
        try:
            # 前置检查
            if self.data is None or self.data.empty:
                raise RuntimeError("数据为空，无法导出Excel")
            if self.analysis_results is None:
                raise RuntimeError("分析结果为空，无法导出Excel")
    
            # 确保输出目录存在
            os.makedirs(os.path.dirname(output_path), exist_ok=True)
            
            # 检查文件是否已存在并尝试删除
            if os.path.exists(output_path):
                try:
                    os.remove(output_path)
                except PermissionError:
                    raise PermissionError(f"无法覆盖文件，请关闭已打开的Excel文件: {output_path}")
                except Exception as e:
                    raise RuntimeError(f"删除旧文件失败: {str(e)}")
    
            # 创建Excel工作簿
            writer = pd.ExcelWriter(output_path, engine='xlsxwriter')
            workbook = writer.book
                
            # 获取筛选后的数据
            filtered_data = self._get_filtered_data()
            if filtered_data.empty:
                self._log_warning("筛选后数据为空，仅导出总量数据")
                
            # 定义简化模式的工作表创建顺序
            sheet_creation_order = [
                ('概览', self._create_overview_sheet_simple),
                ('数据_总量', self._export_cleaned_data_simple),
                ('数据_分析日', self._export_focus_date_data_simple),
                ('进出量时间分布', self._export_peak_flow_simple),
                ('出入口流量占比', self._export_gate_flow_distribution_simple),
                ('分析周期_每日', self._export_daily_stats_simple),
                ('分析图', self._create_charts_sheet_simple)
            ]

            # 开始生成报告提示
            print("📊 生成简化报告工作表:")
            
            # 按顺序创建工作表
            for sheet_name, export_func in sheet_creation_order:
                try:
                    # 调用导出函数
                    export_func(writer, workbook)
                    
                    # 输出完成提示
                    print(f"  ✅ {sheet_name}")

                except Exception as e:
                    self._log_warning(f"创建'{sheet_name}'工作表失败: {str(e)}")
                    print(f"  ❌ {sheet_name} (失败)")
                    # 创建一个空的工作表，确保工作表名称存在
                    try:
                        workbook.add_worksheet(sheet_name)
                    except:
                        pass
                    continue
            
            # 显式保存并关闭writer
            writer.close()
            return output_path
            
        except Exception as e:
            if writer:
                try:
                    writer.close()
                except:
                    pass
            raise RuntimeError(f"导出Excel失败: {str(e)}")
    
    def generate_and_save_plots(self, data):
        """生成并保存图表（简化版）"""
        # 简化模式暂时跳过图表生成
        pass
    
    def _get_filtered_data(self):
        """获取筛选后的数据"""
        # 如果已有缓存，直接返回
        if self._filtered_data_cache is not None:
            return self._filtered_data_cache
            
        # 如果没有聚焦日期，返回所有数据
        if '聚焦日期' not in self.params or not self.params['聚焦日期']:
            self._filtered_data_cache = self.data
            return self.data
        
        # 根据聚焦日期筛选数据
        focus_date = pd.to_datetime(self.params['聚焦日期']).normalize()
        focus_date_end = focus_date + pd.Timedelta(days=1)
        
        # 筛选条件：记录时间在分析日期范围内
        if 'timestamp' in self.data.columns:
            mask = (
                (self.data['timestamp'] >= focus_date) & 
                (self.data['timestamp'] < focus_date_end)
            )
            self._filtered_data_cache = self.data[mask].copy()
        else:
            self._filtered_data_cache = self.data
            
        return self._filtered_data_cache
    
    def _create_overview_sheet_simple(self, writer, workbook):
        """创建简化概览工作表"""
        try:
            # 设置格式
            formats = self._create_excel_formats(workbook)
            
            # 创建工作表
            worksheet = workbook.add_worksheet('概览')
            
            # 设置列宽
            worksheet.set_column('A:A', 25)
            worksheet.set_column('B:B', 40)
            
            # 获取概览数据
            overview_data = self.analysis_results.get('overview', {})
            
            # 写入基础信息
            row = 0
            
            # 基础统计信息
            basic_metrics = [
                ('分析模式', 'mode', lambda x: f"{x} (简化模式)"),
                ('原始记录总数', 'total_records', str),
                ('有效记录数', 'processed_records', str),
                ('进场记录数', 'entry_records', str),
                ('出场记录数', 'exit_records', str),
            ]
            
            for display_name, key, formatter in basic_metrics:
                value = overview_data.get(key)
                if value is not None:
                    worksheet.write(row, 0, display_name, formats['header'])
                    worksheet.write(row, 1, formatter(value), formats['cell'])
                    row += 1
            
            # 车辆类型分布
            vehicle_dist = overview_data.get('vehicle_distribution', {})
            if vehicle_dist:
                worksheet.write(row, 0, '车辆类型分布', formats['header'])
                dist_text = ', '.join(f"{k}:{v}条" for k, v in vehicle_dist.items())
                worksheet.write(row, 1, dist_text, formats['cell'])
                row += 1
            
            # 时间范围
            date_range = overview_data.get('date_range')
            if date_range:
                worksheet.write(row, 0, '数据时间范围', formats['header'])
                range_text = f"{date_range['start'].strftime('%Y-%m-%d')} 至 {date_range['end'].strftime('%Y-%m-%d')} ({date_range['days']}天)"
                worksheet.write(row, 1, range_text, formats['cell'])
                row += 1
            
            # 添加说明
            row += 1
            worksheet.write(row, 0, '说明', formats['header'])
            worksheet.write(row, 1, '简化模式：由于缺少车辆标识，无法提供停车时长和车辆追踪相关统计', formats['cell'])
            
        except Exception as e:
            self._log_error(f"创建概览工作表失败: {str(e)}")
            raise
    
    def _export_cleaned_data_simple(self, writer, workbook):
        """导出原始数据到'数据_总量'工作表（简化版）"""
        try:
            # 设置格式
            formats = self._create_excel_formats(workbook)
            
            # 创建工作表
            worksheet = workbook.add_worksheet('数据_总量')
            
            # 写入数据
            if not self.data.empty:
                # 过滤掉不需要的字段
                exclude_columns = ['data_quality']
                filtered_data = self.data.drop(columns=[col for col in exclude_columns if col in self.data.columns])
                
                # 获取剩余列
                columns = list(filtered_data.columns)

                # 写入列名
                for col_num, column in enumerate(columns):
                    worksheet.write(0, col_num, column, formats['header'])
                
                # 写入数据行
                for row_num, row in enumerate(filtered_data.itertuples(index=False), start=1):
                    for col_num, value in enumerate(row):
                        # 处理日期时间类型
                        if pd.api.types.is_datetime64_any_dtype(filtered_data.dtypes[col_num]):
                            value = value.strftime('%Y-%m-%d %H:%M:%S') if not pd.isna(value) else ''
                        
                        worksheet.write(row_num, col_num, value, formats['cell'])
                
                # 自动调整列宽
                for i, col in enumerate(columns):
                    max_len = max(
                        len(str(col)),
                        filtered_data[col].astype(str).str.len().max() if len(filtered_data) > 0 else 0
                    )
                    worksheet.set_column(i, i, min(max_len + 2, 30))
            
        except Exception as e:
            self._log_error(f"导出原始数据失败: {str(e)}")
            raise
    
    def _export_focus_date_data_simple(self, writer, workbook):
        """导出聚焦日期数据（简化版）"""
        try:
            filtered_data = self._get_filtered_data()
            
            # 设置格式
            formats = self._create_excel_formats(workbook)
            
            # 创建工作表
            worksheet = workbook.add_worksheet('数据_分析日')
            
            if not filtered_data.empty:
                columns = list(filtered_data.columns)
                
                # 写入列名
                for col_num, column in enumerate(columns):
                    worksheet.write(0, col_num, column, formats['header'])
                
                # 写入数据行
                for row_num, row in enumerate(filtered_data.itertuples(index=False), start=1):
                    for col_num, value in enumerate(row):
                        if pd.api.types.is_datetime64_any_dtype(filtered_data.dtypes[col_num]):
                            value = value.strftime('%Y-%m-%d %H:%M:%S') if not pd.isna(value) else ''
                        worksheet.write(row_num, col_num, value, formats['cell'])
                
                # 自动调整列宽
                for i, col in enumerate(columns):
                    max_len = max(
                        len(str(col)),
                        filtered_data[col].astype(str).str.len().max() if len(filtered_data) > 0 else 0
                    )
                    worksheet.set_column(i, i, min(max_len + 2, 30))
            
        except Exception as e:
            self._log_error(f"导出聚焦日期数据失败: {str(e)}")
            raise
    
    def _export_peak_flow_simple(self, writer, workbook):
        """导出进出量时间分布（简化版）"""
        try:
            # 设置格式
            formats = self._create_excel_formats(workbook)

            # 创建工作表
            worksheet = workbook.add_worksheet('进出量时间分布')

            # 获取时间分布数据
            time_dist = self.analysis_results.get('time_distribution', {})
            entry_by_hour = time_dist.get('entry_by_hour', {})
            exit_by_hour = time_dist.get('exit_by_hour', {})

            # 写入标题
            worksheet.write(0, 0, '小时', formats['header'])
            worksheet.write(0, 1, '进场记录数', formats['header'])
            worksheet.write(0, 2, '出场记录数', formats['header'])

            # 写入数据
            for hour in range(24):
                row = hour + 1
                worksheet.write(row, 0, f"{hour:02d}:00", formats['cell'])
                worksheet.write(row, 1, entry_by_hour.get(hour, 0), formats['cell'])
                worksheet.write(row, 2, exit_by_hour.get(hour, 0), formats['cell'])

            # 设置列宽
            worksheet.set_column('A:A', 10)
            worksheet.set_column('B:C', 15)

        except Exception as e:
            self._log_error(f"导出进出量时间分布失败: {str(e)}")
            raise

    def _export_gate_flow_distribution_simple(self, writer, workbook):
        """导出出入口流量占比（简化版）"""
        try:
            # 设置格式
            formats = self._create_excel_formats(workbook)

            # 创建工作表
            worksheet = workbook.add_worksheet('出入口流量占比')

            # 获取道闸使用数据
            gate_usage = self.analysis_results.get('gate_usage', {}).get('gate_usage', {})

            # 写入标题
            worksheet.write(0, 0, '道闸', formats['header'])
            worksheet.write(0, 1, '通行记录数', formats['header'])
            worksheet.write(0, 2, '占比', formats['header'])

            # 计算总数
            total_records = sum(gate_usage.values()) if gate_usage else 0

            # 写入数据
            row = 1
            for gate, count in gate_usage.items():
                percentage = (count / total_records * 100) if total_records > 0 else 0
                worksheet.write(row, 0, gate, formats['cell'])
                worksheet.write(row, 1, count, formats['cell'])
                worksheet.write(row, 2, f"{percentage:.1f}%", formats['cell'])
                row += 1

            # 设置列宽
            worksheet.set_column('A:A', 15)
            worksheet.set_column('B:C', 12)

        except Exception as e:
            self._log_error(f"导出出入口流量占比失败: {str(e)}")
            raise

    def _export_daily_stats_simple(self, writer, workbook):
        """导出每日统计（简化版）"""
        try:
            # 设置格式
            formats = self._create_excel_formats(workbook)

            # 创建工作表
            worksheet = workbook.add_worksheet('分析周期_每日')

            # 获取每日流量数据
            daily_flow = self.analysis_results.get('time_distribution', {}).get('daily_flow', {})

            # 写入标题
            worksheet.write(0, 0, '日期', formats['header'])
            worksheet.write(0, 1, '记录总数', formats['header'])

            # 写入数据
            row = 1
            for date, count in sorted(daily_flow.items()):
                worksheet.write(row, 0, str(date), formats['cell'])
                worksheet.write(row, 1, count, formats['cell'])
                row += 1

            # 设置列宽
            worksheet.set_column('A:A', 12)
            worksheet.set_column('B:B', 12)

        except Exception as e:
            self._log_error(f"导出每日统计失败: {str(e)}")
            raise

    def _create_charts_sheet_simple(self, writer, workbook):
        """创建图表工作表（简化版）"""
        try:
            # 创建工作表
            worksheet = workbook.add_worksheet('分析图')

            # 设置格式
            formats = self._create_excel_formats(workbook)

            # 写入说明
            worksheet.write(0, 0, '简化模式图表', formats['header'])
            worksheet.write(1, 0, '由于缺少车辆标识，暂不提供复杂图表分析', formats['cell'])

            # 设置列宽
            worksheet.set_column('A:A', 30)

        except Exception as e:
            self._log_error(f"创建图表工作表失败: {str(e)}")
            raise
    
    def _create_excel_formats(self, workbook):
        """创建Excel格式"""
        return {
            'header': workbook.add_format({
                'bold': True,
                'bg_color': '#D7E4BC',
                'border': 1,
                'text_wrap': True,
                'valign': 'vcenter'
            }),
            'cell': workbook.add_format({
                'border': 1,
                'valign': 'vcenter'
            })
        }
    
    def _log_error(self, message):
        """记录错误信息"""
        print(f"错误: {message}")
    
    def _log_warning(self, message):
        """记录警告信息"""
        print(f"警告: {message}")
