#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Excel数据合并器 - 完整集成版
所有功能集成在一个文件中，配置集中管理
"""

import pandas as pd
import logging
from pathlib import Path
from typing import List, Dict, Tuple
import chardet
import hashlib
from datetime import datetime

# ========================================
# 📋 配置区域 - 在这里设置您的合并任务
# ========================================

# 任务配置 - 您可以在这里设置多个任务
MERGE_TASKS = {
    # # 示例任务 - 网约车数据合并
    # "merge_data": {
    #     "task_name": "义乌火车站道闸合并",  # 任务显示名称
    #     "input_directory": r"C:\Users\<USER>\Desktop\停车分析\数据\正泰",  # 输入数据目录
    #     "output_directory": r"C:\Users\<USER>\Desktop\停车分析\数据\正泰",      # 输出文件目录
    #     "output_filename": "义乌正泰_北门_合并.csv",              # 输出文件名（会自动添加时间戳）
    #     "file_pattern": "*",        # 文件名匹配模式，不包含扩展名（程序自动匹配 .xlsx, .xls, .csv, .txt）
    #     "recursive": True,          # 是否递归搜索子目录
    #     "merge_strategy": "smart",  # 合并策略: "smart" 或 "traditional"
    #     "enabled": True             # 是否启用此任务，设置为 True 启用
    # }

    # 您可以添加更多任务，例如：
    "private_cars": {
        "task_name": "义乌火车站道闸_合并",
        "input_directory": r"C:\Users\<USER>\Desktop\停车分析\数据\火车站\网约车",
        "output_directory": r"C:\Users\<USER>\Desktop\停车分析\数据\火车站",
        "output_filename": "义乌火车站道闸_网约车_合并.csv",
        "file_pattern": "*",
        "recursive": True,
        "merge_strategy": "smart",
        "enabled": True  # 是否启用此任务，设置为 True 启用
    }
}

# 全局设置
GLOBAL_SETTINGS = {
    "log_directory": r"C:\Users\<USER>\Desktop\停车分析\数据\火车站\logs\excel_merger",  # 日志保存目录
    "save_log": True,                          # 是否保存详细日志到文件
    "continue_on_error": True,                 # 遇到错误是否继续处理其他文件
    "supported_extensions": ['.xlsx', '.xls', '.csv', '.txt'],  # 支持的输入文件格式
    "output_format": "auto",                   # 输出格式: auto(自动), csv, xlsx, excel
    "encoding": "utf-8-sig",                   # CSV/TXT文件编码(推荐utf-8-sig以支持Excel中文显示)
    "add_timestamp_to_filename": True,         # 是否在输出文件名中添加时间戳(格式:文件名_YYYYMMDD_HHMMSS.扩展名)
    "show_detailed_progress": False            # 是否在控制台显示详细的处理进度信息
}

# ========================================
# 🔧 核心合并器类
# ========================================

class IntegratedDataMerger:
    """集成数据合并器类"""
    
    def __init__(self, settings: Dict = None):
        """
        初始化合并器
        
        Args:
            settings: Dict, 全局设置，如果不提供则使用默认设置
        """
        self.settings = settings or GLOBAL_SETTINGS
        self.timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # 设置日志
        self.logger = self._setup_logger()
        
        # 支持的文件格式
        self.supported_extensions = self.settings['supported_extensions']
        
        # 合并统计
        self.merge_stats = {
            'total_sources': 0,
            'merged_sources': 0,
            'skipped_sources': 0,
            'duplicate_sources': 0,
            'total_rows': 0,
            'merged_rows': 0,
            'skipped_details': [],
            'duplicate_details': [],
            'merged_details': [],  # 新增：详细的合并信息
            'file_summary': {},    # 新增：文件级别的汇总
            'processing_time': 0   # 新增：处理时间
        }
        
        # 重复检测缓存
        self.data_hashes = {}
        self.processed_sources = set()
        
        # 任务执行历史
        self.execution_history = []
    
    def _setup_logger(self):
        """设置日志记录器"""
        logger = logging.getLogger('IntegratedDataMerger')

        # 根据设置决定日志级别
        if self.settings.get('show_detailed_progress', False):
            logger.setLevel(logging.INFO)
        else:
            logger.setLevel(logging.WARNING)  # 只显示警告和错误

        # 清除现有处理器
        for handler in logger.handlers[:]:
            logger.removeHandler(handler)

        # 控制台处理器
        console_handler = logging.StreamHandler()
        if self.settings.get('show_detailed_progress', False):
            console_handler.setLevel(logging.INFO)
        else:
            console_handler.setLevel(logging.WARNING)
        formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
        console_handler.setFormatter(formatter)
        logger.addHandler(console_handler)
        
        # 文件处理器（如果启用日志保存）
        if self.settings.get('save_log', False):
            try:
                log_dir = Path(self.settings['log_directory'])
                log_dir.mkdir(parents=True, exist_ok=True)
                
                log_file = log_dir / f"merger_log_{self.timestamp}.txt"
                file_handler = logging.FileHandler(log_file, encoding='utf-8')
                file_handler.setLevel(logging.INFO)
                file_handler.setFormatter(formatter)
                logger.addHandler(file_handler)
                
                print(f"📝 日志将保存到: {log_file}")
            except Exception as e:
                print(f"⚠️  日志设置失败: {str(e)}")
        
        return logger
    
    def _reset_stats(self):
        """重置统计信息"""
        self.merge_stats = {
            'total_sources': 0,
            'merged_sources': 0,
            'skipped_sources': 0,
            'duplicate_sources': 0,
            'total_rows': 0,
            'merged_rows': 0,
            'skipped_details': [],
            'duplicate_details': [],
            'merged_details': [],  # 详细的合并信息
            'file_summary': {},    # 文件级别的汇总
            'processing_time': 0   # 处理时间
        }
        self.data_hashes = {}
        self.processed_sources = set()
    
    def calculate_data_hash(self, df: pd.DataFrame) -> str:
        """
        计算数据框的哈希值，用于重复检测
        基于数据内容而非列名，确保相同数据能被正确识别
        """
        if df.empty:
            return ""

        try:
            # 1. 创建副本并重置列名为统一格式（避免列名差异影响重复检测）
            df_normalized = df.copy()
            # 使用位置索引作为列名，确保相同结构的数据有相同的列名
            df_normalized.columns = [f'col_{i}' for i in range(len(df_normalized.columns))]

            # 2. 统一数据类型
            df_normalized = df_normalized.astype(str)

            # 3. 标准化空值
            df_normalized = df_normalized.fillna('')

            # 4. 标准化数据内容（去除前后空格）
            for col in df_normalized.columns:
                df_normalized[col] = df_normalized[col].str.strip()

            # 5. 按所有列排序确保行顺序一致
            df_normalized = df_normalized.sort_values(by=list(df_normalized.columns)).reset_index(drop=True)

            # 6. 生成CSV字符串并计算哈希（不包含列名）
            content = df_normalized.to_csv(index=False, header=False)
            hash_value = hashlib.md5(content.encode('utf-8')).hexdigest()

            self.logger.debug(f"计算数据哈希: 形状{df.shape} -> {hash_value[:8]}...")
            return hash_value

        except Exception as e:
            self.logger.error(f"计算数据哈希失败: {str(e)}")
            return ""
    
    def is_duplicate_data(self, data: pd.DataFrame, source_name: str) -> Tuple[bool, str]:
        """检查数据是否重复"""
        data_hash = self.calculate_data_hash(data)

        if not data_hash:
            return False, ""

        # 检查是否已存在相同哈希
        for existing_source, existing_hash in self.data_hashes.items():
            if data_hash == existing_hash:
                self.logger.info(f"发现重复数据: {source_name} 与 {existing_source} 内容相同")
                return True, existing_source

        # 临时记录哈希值用于重复检测
        # 如果后续文件因为结构不兼容被跳过，会通过 cleanup_failed_merge 清理
        self.data_hashes[source_name] = data_hash
        self.processed_sources.add(source_name)

        return False, ""

    def record_successful_merge(self, data: pd.DataFrame, source_name: str):
        """记录成功合并的文件信息（用于重复检测）"""
        # 哈希已经在 is_duplicate_data 中记录了，这里只需要确认
        data_hash = self.calculate_data_hash(data)
        if data_hash:
            self.data_hashes[source_name] = data_hash
            self.processed_sources.add(source_name)
            self.logger.debug(f"确认成功合并文件的哈希: {source_name}")

    def cleanup_failed_merge(self, source_name: str):
        """清理失败合并的文件信息（从重复检测中移除）"""
        if source_name in self.data_hashes:
            del self.data_hashes[source_name]
            self.logger.debug(f"清理失败合并文件的哈希: {source_name}")

        if source_name in self.processed_sources:
            self.processed_sources.remove(source_name)
    
    def detect_header_row(self, df: pd.DataFrame) -> Tuple[bool, int]:
        """
        智能检测数据框中的标题行位置
        基于字段名和数据行的格式差异性及数量特征进行判断
        """
        if df.empty:
            return False, -1

        # 读取前30行进行分析
        max_check_rows = min(30, len(df))
        if max_check_rows < 2:
            return True, 0  # 数据太少，假设第一行是标题

        self.logger.info(f"开始分析前 {max_check_rows} 行数据以检测标题行位置")

        # 分析每一行作为标题行的可能性
        header_scores = []

        for i in range(max_check_rows):
            score = self._calculate_header_probability(df, i, max_check_rows)
            header_scores.append((i, score))
            self.logger.debug(f"第 {i+1} 行作为标题行的得分: {score:.3f}")

        # 找到得分最高的行
        best_row, best_score = max(header_scores, key=lambda x: x[1])

        # 判断是否找到了明显的标题行
        if best_score >= 0.6:
            self.logger.info(f"检测到标题行在第 {best_row+1} 行 (得分: {best_score:.3f})")
            return True, best_row
        elif best_score >= 0.4:
            self.logger.info(f"可能的标题行在第 {best_row+1} 行 (得分: {best_score:.3f})")
            return True, best_row
        else:
            self.logger.warning(f"未检测到明显标题行，使用第一行 (最高得分: {best_score:.3f})")
            return True, 0

    def _calculate_header_probability(self, df: pd.DataFrame, row_index: int, max_rows: int) -> float:
        """
        计算指定行作为标题行的概率
        基于字段名特征和与数据行的差异性分析
        """
        if row_index >= len(df):
            return 0.0

        candidate_row = df.iloc[row_index]

        # 1. 字段名特征分析 (权重: 35%)
        field_name_score = self._analyze_field_name_characteristics(candidate_row)

        # 2. 与数据行的差异性分析 (权重: 40%)
        data_difference_score = self._analyze_difference_with_data_rows(df, row_index, max_rows)

        # 3. 数据类型一致性分析 (权重: 15%)
        type_consistency_score = self._analyze_type_consistency(candidate_row)

        # 4. 位置合理性分析 (权重: 10%)
        position_score = self._analyze_position_reasonableness(row_index, max_rows)

        # 计算综合得分
        total_score = (
            field_name_score * 0.35 +
            data_difference_score * 0.40 +
            type_consistency_score * 0.15 +
            position_score * 0.10
        )

        self.logger.debug(f"第 {row_index+1} 行得分详情: 字段名={field_name_score:.3f}, "
                         f"差异性={data_difference_score:.3f}, 类型一致性={type_consistency_score:.3f}, "
                         f"位置合理性={position_score:.3f}, 总分={total_score:.3f}")

        return total_score

    def _analyze_field_name_characteristics(self, row: pd.Series) -> float:
        """分析字段名特征"""
        if len(row) == 0:
            return 0.0

        score = 0.0
        valid_cells = 0

        for cell in row:
            if pd.notna(cell):
                valid_cells += 1
                cell_str = str(cell).strip()

                if not cell_str:
                    continue

                # 字段名特征检查
                cell_score = 0.0

                # 1. 是否为字符串类型
                if isinstance(cell, str):
                    cell_score += 0.3

                # 2. 长度合理性 (字段名通常不会太长也不会太短)
                if 2 <= len(cell_str) <= 50:
                    cell_score += 0.2
                elif 1 <= len(cell_str) <= 100:
                    cell_score += 0.1

                # 3. 包含字母 (字段名通常包含字母)
                if any(c.isalpha() for c in cell_str):
                    cell_score += 0.2

                # 4. 不是纯数字 (字段名通常不是纯数字)
                if not cell_str.replace('.', '').replace('-', '').replace('+', '').isdigit():
                    cell_score += 0.2

                # 5. 常见字段名模式
                field_patterns = [
                    'id', 'name', 'date', 'time', 'code', 'type', 'status', 'amount', 'price', 'count',
                    'number', 'value', 'description', 'category', 'group', 'level', 'rate', 'percent',
                    '编号', '名称', '日期', '时间', '代码', '类型', '状态', '金额', '价格', '数量',
                    '数值', '描述', '分类', '组别', '等级', '比率', '百分比', '车牌', '方向', '道闸'
                ]

                cell_lower = cell_str.lower()
                if any(pattern in cell_lower for pattern in field_patterns):
                    cell_score += 0.1

                score += min(cell_score, 1.0)  # 单个字段最高1分

        # 计算平均得分
        if valid_cells > 0:
            avg_score = score / valid_cells

            # 非空字段比例奖励
            non_empty_ratio = valid_cells / len(row)
            if non_empty_ratio >= 0.8:
                avg_score *= 1.2
            elif non_empty_ratio >= 0.6:
                avg_score *= 1.1

            return min(avg_score, 1.0)

        return 0.0

    def _analyze_difference_with_data_rows(self, df: pd.DataFrame, header_row_index: int, max_rows: int) -> float:
        """分析候选标题行与数据行的差异性"""
        if header_row_index >= len(df) - 1:
            return 0.0  # 没有后续数据行可比较

        header_row = df.iloc[header_row_index]

        # 选择后续的数据行进行比较 (最多比较10行)
        data_start = header_row_index + 1
        data_end = min(data_start + 10, len(df), max_rows)

        if data_start >= data_end:
            return 0.0

        data_rows = df.iloc[data_start:data_end]

        differences = []

        for _, data_row in data_rows.iterrows():
            diff_score = self._calculate_row_difference_score(header_row, data_row)
            differences.append(diff_score)

        if not differences:
            return 0.0

        # 计算平均差异性
        avg_difference = sum(differences) / len(differences)

        # 差异性越大，越可能是标题行
        return min(avg_difference, 1.0)

    def _calculate_row_difference_score(self, header_row: pd.Series, data_row: pd.Series) -> float:
        """计算两行之间的差异得分"""
        if len(header_row) != len(data_row):
            return 0.5  # 长度不同，给中等差异分

        total_score = 0.0
        valid_comparisons = 0

        for header_val, data_val in zip(header_row, data_row):
            if pd.notna(header_val) and pd.notna(data_val):
                valid_comparisons += 1

                header_str = str(header_val).strip()
                data_str = str(data_val).strip()

                if not header_str or not data_str:
                    continue

                cell_diff = 0.0

                # 1. 数据类型差异
                if type(header_val) != type(data_val):
                    cell_diff += 0.3

                # 2. 内容差异
                if header_str != data_str:
                    cell_diff += 0.2

                # 3. 字符串 vs 数字模式差异
                header_is_numeric = self._is_numeric_string(header_str)
                data_is_numeric = self._is_numeric_string(data_str)

                if header_is_numeric != data_is_numeric:
                    cell_diff += 0.3

                # 4. 长度差异
                len_diff = abs(len(header_str) - len(data_str))
                if len_diff > 5:
                    cell_diff += 0.2

                total_score += min(cell_diff, 1.0)

        if valid_comparisons > 0:
            return total_score / valid_comparisons

        return 0.0

    def _is_numeric_string(self, s: str) -> bool:
        """判断字符串是否表示数字"""
        try:
            float(s.replace(',', '').replace(' ', ''))
            return True
        except ValueError:
            return False

    def _analyze_type_consistency(self, row: pd.Series) -> float:
        """分析行内数据类型一致性"""
        if len(row) == 0:
            return 0.0

        type_counts = {}
        valid_count = 0

        for val in row:
            if pd.notna(val):
                valid_count += 1
                val_type = type(val).__name__
                type_counts[val_type] = type_counts.get(val_type, 0) + 1

        if valid_count == 0:
            return 0.0

        # 字段名通常类型比较一致（多为字符串）
        if 'str' in type_counts:
            str_ratio = type_counts['str'] / valid_count
            if str_ratio >= 0.8:
                return 1.0
            elif str_ratio >= 0.6:
                return 0.8
            elif str_ratio >= 0.4:
                return 0.6

        # 计算类型一致性
        max_type_count = max(type_counts.values()) if type_counts else 0
        consistency = max_type_count / valid_count if valid_count > 0 else 0

        return consistency

    def _analyze_position_reasonableness(self, row_index: int, max_rows: int) -> float:
        """分析位置合理性"""
        # 标题行通常在前几行
        if row_index == 0:
            return 1.0  # 第一行最可能
        elif row_index <= 2:
            return 0.8  # 前3行比较可能
        elif row_index <= 5:
            return 0.6  # 前6行有可能
        elif row_index <= 10:
            return 0.4  # 前11行可能性较低
        else:
            return 0.2  # 更后面的行可能性很低
    


    def standardize_dataframe(self, df: pd.DataFrame, source_name: str) -> pd.DataFrame:
        """标准化数据框（处理标题行、清理数据）"""
        if df.empty:
            self.logger.warning(f"{source_name}: 数据为空")
            return df

        # 检测标题行
        has_header, header_row = self.detect_header_row(df)

        if has_header and header_row > 0:
            # 如果标题行不在第一行，重新设置
            df.columns = df.iloc[header_row]
            df = df.iloc[header_row + 1:].reset_index(drop=True)
            self.logger.info(f"{source_name}: 使用第 {header_row+1} 行作为列名")
        elif has_header and header_row == 0:
            # 标题行在第一行，保持不变
            pass
        else:
            # 没有标题行，使用默认列名
            df.columns = [f'Column_{i+1}' for i in range(len(df.columns))]
            self.logger.info(f"{source_name}: 未找到标题行，使用默认列名")

        # 清理列名
        df.columns = df.columns.astype(str).str.strip()

        # 移除全为空的行
        df = df.dropna(how='all')

        # 移除全为空的列
        df = df.dropna(axis=1, how='all')

        self.logger.info(f"{source_name}: 标准化后数据形状 {df.shape}")
        return df

    def compare_data_structure(self, df1: pd.DataFrame, df2: pd.DataFrame,
                             name1: str, name2: str) -> Tuple[bool, str]:
        """比较两个数据框的结构是否一致"""
        if df1.empty or df2.empty:
            return False, "其中一个数据源为空"

        # 1. 比较列数
        if len(df1.columns) != len(df2.columns):
            return False, f"列数不匹配: {name1}({len(df1.columns)}列) vs {name2}({len(df2.columns)}列)"

        # 2. 标准化列名进行比较
        cols1_normalized = self._normalize_column_names(df1.columns)
        cols2_normalized = self._normalize_column_names(df2.columns)

        # 3. 计算相似度
        similarity_metrics = self._calculate_column_similarity(cols1_normalized, cols2_normalized)

        # 4. 综合判断兼容性
        is_compatible, reason = self._evaluate_compatibility(similarity_metrics, name1, name2)

        return is_compatible, reason

    def _normalize_column_names(self, columns) -> list:
        """标准化列名"""
        normalized = []
        for col in columns:
            col_str = str(col).strip().lower()
            col_str = col_str.replace('unnamed:', '').replace('unnamed:', '')

            import re
            col_str = re.sub(r'\s+', '', col_str)
            col_str = re.sub(r'[^\w\u4e00-\u9fff]', '', col_str)

            normalized.append(col_str)

        return normalized

    def _calculate_column_similarity(self, cols1: list, cols2: list) -> dict:
        """计算列名相似度的多个指标"""
        metrics = {
            'exact_match_ratio': 0,
            'fuzzy_match_ratio': 0,
            'position_consistency': 0,
            'length_similarity': 0
        }

        if len(cols1) != len(cols2) or len(cols1) == 0:
            return metrics

        # 1. 精确匹配比例
        exact_matches = sum(1 for c1, c2 in zip(cols1, cols2) if c1 == c2)
        metrics['exact_match_ratio'] = exact_matches / len(cols1)

        # 2. 模糊匹配比例
        fuzzy_matches = 0
        for c1, c2 in zip(cols1, cols2):
            similarity = self._calculate_string_similarity(c1, c2)
            if similarity >= 0.7:
                fuzzy_matches += 1
        metrics['fuzzy_match_ratio'] = fuzzy_matches / len(cols1)

        # 3. 位置一致性
        position_score = 0
        for i, (c1, c2) in enumerate(zip(cols1, cols2)):
            if c1 in cols2 and c2 in cols1:
                if c1 == c2:
                    position_score += 1
                else:
                    c1_pos_in_2 = cols2.index(c1) if c1 in cols2 else -1
                    c2_pos_in_1 = cols1.index(c2) if c2 in cols1 else -1
                    if abs(i - c1_pos_in_2) <= 1 or abs(i - c2_pos_in_1) <= 1:
                        position_score += 0.5
        metrics['position_consistency'] = position_score / len(cols1)

        # 4. 长度相似性
        length_diffs = [abs(len(c1) - len(c2)) for c1, c2 in zip(cols1, cols2)]
        avg_length = sum(len(c1) + len(c2) for c1, c2 in zip(cols1, cols2)) / (2 * len(cols1))
        if avg_length > 0:
            length_similarity = 1 - (sum(length_diffs) / len(length_diffs)) / avg_length
            metrics['length_similarity'] = max(0, min(1, length_similarity))

        return metrics

    def _calculate_string_similarity(self, s1: str, s2: str) -> float:
        """计算两个字符串的相似度"""
        if s1 == s2:
            return 1.0

        if len(s1) == 0 or len(s2) == 0:
            return 0.0

        # 使用最长公共子序列计算相似度
        def lcs_length(x, y):
            m, n = len(x), len(y)
            dp = [[0] * (n + 1) for _ in range(m + 1)]

            for i in range(1, m + 1):
                for j in range(1, n + 1):
                    if x[i-1] == y[j-1]:
                        dp[i][j] = dp[i-1][j-1] + 1
                    else:
                        dp[i][j] = max(dp[i-1][j], dp[i][j-1])

            return dp[m][n]

        lcs_len = lcs_length(s1, s2)
        max_len = max(len(s1), len(s2))

        return lcs_len / max_len if max_len > 0 else 0

    def _evaluate_compatibility(self, metrics: dict, name1: str, name2: str) -> Tuple[bool, str]:
        """基于多个指标评估数据结构兼容性"""
        # 权重配置
        weights = {
            'exact_match_ratio': 0.4,
            'fuzzy_match_ratio': 0.3,
            'position_consistency': 0.2,
            'length_similarity': 0.1
        }

        # 计算综合得分
        total_score = sum(metrics[key] * weights[key] for key in weights)

        # 特殊处理：如果是合并结果文件且列数相同，直接认为兼容
        is_merge_result_file = self._is_merge_result_file(name2)
        # 从metrics中获取列数信息（通过检查exact_match_ratio是否有意义来判断列数是否相同）
        columns_match = 'exact_match_ratio' in metrics and metrics.get('exact_match_ratio', -1) >= 0
        if is_merge_result_file and columns_match:
            self.logger.info(f"检测到合并结果文件 {name2}，列数匹配，强制兼容")
            return True, f"合并结果文件兼容 (列数匹配)"

        # 兼容性阈值（针对混合标题行情况适当降低）
        high_compatibility_threshold = 0.6  # 从0.7降低到0.6
        medium_compatibility_threshold = 0.3  # 从0.4降低到0.3

        # 如果是合并结果文件，进一步降低阈值
        if is_merge_result_file:
            high_compatibility_threshold = 0.4
            medium_compatibility_threshold = 0.1
            self.logger.debug(f"检测到合并结果文件 {name2}，降低兼容性阈值")

        if total_score >= high_compatibility_threshold:
            if metrics['exact_match_ratio'] == 1.0:
                return True, "列名完全匹配"
            else:
                return True, f"高度兼容 (综合得分: {total_score:.1%})"

        elif total_score >= medium_compatibility_threshold:
            if metrics['exact_match_ratio'] >= 0.4 or metrics['fuzzy_match_ratio'] >= 0.6:
                return True, f"中等兼容 (综合得分: {total_score:.1%})"
            else:
                return False, f"兼容性不足 (综合得分: {total_score:.1%})"

        else:
            reasons = []
            if metrics['exact_match_ratio'] < 0.3:
                reasons.append(f"精确匹配率过低 ({metrics['exact_match_ratio']:.1%})")
            if metrics['fuzzy_match_ratio'] < 0.5:
                reasons.append(f"模糊匹配率过低 ({metrics['fuzzy_match_ratio']:.1%})")

            reason_text = "; ".join(reasons) if reasons else f"综合得分过低 ({total_score:.1%})"
            return False, f"结构不兼容: {reason_text}"

    def _is_merge_result_file(self, filename: str) -> bool:
        """判断是否为合并结果文件"""
        # 合并结果文件的常见命名模式
        merge_patterns = [
            '_合并_',
            '_merge_',
            '_merged_',
            '_combined_',
            '_concat_',
            '合并',
            'merge',
            'combined'
        ]

        filename_lower = filename.lower()

        # 检查文件名是否包含合并相关的关键词
        for pattern in merge_patterns:
            if pattern in filename_lower:
                return True

        # 检查是否包含时间戳模式（合并文件通常有时间戳）
        import re
        timestamp_patterns = [
            r'\d{8}_\d{6}',  # 20250618_224630
            r'\d{4}-\d{2}-\d{2}',  # 2025-06-18
            r'\d{4}\d{2}\d{2}',  # 20250618
        ]

        for pattern in timestamp_patterns:
            if re.search(pattern, filename):
                return True

        return False

    def read_file_with_encoding(self, file_path: Path) -> pd.DataFrame:
        """智能读取文件（自动检测编码）"""
        file_ext = file_path.suffix.lower()

        try:
            if file_ext in ['.xlsx', '.xls']:
                # Excel文件
                df = pd.read_excel(file_path, header=None)
                self.logger.info(f"成功读取Excel文件: {file_path.name}")
                return df

            elif file_ext in ['.csv', '.txt']:
                # CSV/TXT文件 - 自动检测编码和分隔符
                return self._read_csv_with_detection(file_path)

            else:
                self.logger.warning(f"不支持的文件格式: {file_ext}")
                return pd.DataFrame()

        except Exception as e:
            self.logger.error(f"读取文件失败 {file_path.name}: {str(e)}")
            return pd.DataFrame()

    def _read_csv_with_detection(self, file_path: Path) -> pd.DataFrame:
        """读取CSV/TXT文件，自动检测编码和分隔符"""
        # 检测编码
        encoding = self._detect_encoding(file_path)

        # 检测分隔符
        separator = self._detect_separator(file_path, encoding)

        try:
            df = pd.read_csv(
                file_path,
                sep=separator,
                encoding=encoding,
                header=None,
                on_bad_lines='skip',  # 跳过有问题的行
                engine='python'       # 使用Python引擎，更容错
            )
            self.logger.info(f"成功读取 {file_path.name}: 编码={encoding}, 分隔符='{separator}'")
            return df
        except Exception as e:
            # 增强的备用方案
            self.logger.warning(f"使用检测参数读取失败，尝试备用方案: {str(e)}")

            # 备用方案1：尝试多种编码
            backup_encodings = ['utf-8-sig', 'utf-8', 'gbk', 'latin-1']
            for backup_encoding in backup_encodings:
                try:
                    df = pd.read_csv(
                        file_path,
                        encoding=backup_encoding,
                        header=None,
                        on_bad_lines='skip',
                        engine='python'
                    )
                    if not df.empty:
                        self.logger.info(f"备用方案成功读取: {file_path.name} (编码={backup_encoding})")
                        return df
                except Exception:
                    continue

            # 备用方案2：手动解析
            try:
                with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                    lines = f.readlines()

                if lines:
                    # 检测分隔符
                    first_line = lines[0].strip()
                    separators = [',', '\t', ';', '|', ' ']
                    best_sep = ','
                    max_cols = 0

                    for sep in separators:
                        cols = len(first_line.split(sep))
                        if cols > max_cols:
                            max_cols = cols
                            best_sep = sep

                    # 手动解析数据
                    data_rows = []
                    for line in lines:
                        row = line.strip().split(best_sep)
                        data_rows.append(row)

                    if data_rows:
                        df = pd.DataFrame(data_rows)
                        self.logger.warning(f"手动解析成功读取: {file_path.name} (分隔符='{best_sep}')")
                        return df

            except Exception as e3:
                self.logger.debug(f"手动解析失败: {str(e3)}")

            self.logger.error(f"所有读取方案都失败: {file_path.name}")
            return pd.DataFrame()

    def _detect_encoding(self, file_path: Path) -> str:
        """
        增强版文件编码检测
        使用多种采样大小和置信度阈值进行检测
        """
        try:
            file_size = file_path.stat().st_size
            self.logger.debug(f"文件大小: {file_size} 字节")

            # 多种采样大小
            sample_sizes = [10000, 50000, 100000]
            confidence_thresholds = [0.8, 0.6, 0.4]

            # 尝试不同的采样大小
            for sample_size in sample_sizes:
                # 调整采样大小不超过文件大小
                actual_sample_size = min(sample_size, file_size)

                try:
                    with open(file_path, 'rb') as f:
                        raw_data = f.read(actual_sample_size)

                    if not raw_data:
                        continue

                    result = chardet.detect(raw_data)
                    encoding = result.get('encoding')
                    confidence = result.get('confidence', 0)

                    self.logger.debug(f"采样大小 {actual_sample_size}: 编码={encoding}, 置信度={confidence:.3f}")

                    # 使用多级置信度阈值
                    for threshold in confidence_thresholds:
                        if confidence >= threshold and encoding:
                            # 标准化编码名称
                            normalized_encoding = self._normalize_encoding_name(encoding)
                            self.logger.info(f"编码检测成功: {normalized_encoding} (置信度: {confidence:.3f}, 采样: {actual_sample_size})")
                            return normalized_encoding

                except Exception as e:
                    self.logger.debug(f"采样大小 {actual_sample_size} 检测失败: {str(e)}")
                    continue

            # 如果自动检测失败，尝试启发式检测
            heuristic_encoding = self._heuristic_encoding_detection(file_path)
            if heuristic_encoding:
                self.logger.info(f"启发式检测编码: {heuristic_encoding}")
                return heuristic_encoding

            # 最终备用方案
            default_encoding = self.settings.get('encoding', 'utf-8-sig')
            self.logger.warning(f"编码检测失败，使用默认编码: {default_encoding}")
            return default_encoding

        except Exception as e:
            default_encoding = self.settings.get('encoding', 'utf-8-sig')
            self.logger.error(f"编码检测异常: {str(e)}，使用默认编码: {default_encoding}")
            return default_encoding

    def _normalize_encoding_name(self, encoding):
        """标准化编码名称"""
        if not encoding:
            return 'utf-8'

        encoding = encoding.lower()

        # 编码名称映射
        encoding_map = {
            'gb2312': 'gbk',  # GB2312是GBK的子集
            'gb18030': 'gbk',  # 通常GBK就足够了
            'windows-1252': 'cp1252',
            'iso-8859-1': 'latin-1',
            'ascii': 'utf-8'  # ASCII是UTF-8的子集
        }

        return encoding_map.get(encoding, encoding)

    def _heuristic_encoding_detection(self, file_path: Path):
        """启发式编码检测"""
        try:
            # 读取文件开头的一小部分
            with open(file_path, 'rb') as f:
                sample = f.read(1000)

            # 检查BOM标记
            if sample.startswith(b'\xef\xbb\xbf'):
                return 'utf-8-sig'
            elif sample.startswith(b'\xff\xfe'):
                return 'utf-16-le'
            elif sample.startswith(b'\xfe\xff'):
                return 'utf-16-be'

            # 检查中文字符模式
            if b'\x81' in sample or b'\x82' in sample or b'\x83' in sample:
                return 'gbk'

            # 检查是否包含高位字符
            if any(b > 127 for b in sample):
                return 'utf-8'
            else:
                return 'ascii'

        except Exception as e:
            self.logger.debug(f"启发式检测失败: {str(e)}")
            return None

    def _detect_separator(self, file_path: Path, encoding: str) -> str:
        """检测CSV分隔符"""
        try:
            with open(file_path, 'r', encoding=encoding) as f:
                first_line = f.readline()

            # 常见分隔符
            separators = [',', '\t', ';', '|', ' ']
            separator_counts = {}

            for sep in separators:
                count = first_line.count(sep)
                if count > 0:
                    separator_counts[sep] = count

            if separator_counts:
                # 返回出现次数最多的分隔符
                best_separator = max(separator_counts, key=separator_counts.get)
                return best_separator
            else:
                return ','  # 默认逗号

        except Exception:
            return ','

    def merge_excel_sheets(self, excel_path: str, output_path: str = None) -> pd.DataFrame:
        """合并Excel文件中的所有工作表"""
        self.logger.info(f"开始合并Excel文件: {excel_path}")
        self._reset_stats()

        try:
            # 读取所有工作表
            all_sheets = pd.read_excel(excel_path, sheet_name=None)

            if not all_sheets:
                self.logger.warning("Excel文件中没有工作表")
                return pd.DataFrame()

            self.logger.info(f"找到 {len(all_sheets)} 个工作表: {list(all_sheets.keys())}")

            merged_data = pd.DataFrame()
            reference_structure = None
            local_hashes = {}  # 本文件内的重复检测

            self.merge_stats['total_sources'] = len(all_sheets)

            for sheet_name, sheet_data in all_sheets.items():
                self.logger.info(f"处理工作表: {sheet_name}")

                # 标准化数据
                standardized_data = self.standardize_dataframe(sheet_data, f"工作表_{sheet_name}")

                if standardized_data.empty:
                    self.logger.warning(f"跳过空工作表: {sheet_name}")
                    self.merge_stats['skipped_sources'] += 1
                    self.merge_stats['skipped_details'].append({
                        'source': f"工作表_{sheet_name}",
                        'reason': '数据为空'
                    })
                    continue

                # 检查本文件内的工作表重复
                data_hash = self.calculate_data_hash(standardized_data)
                if data_hash in local_hashes:
                    duplicate_sheet = local_hashes[data_hash]
                    self.logger.warning(f"跳过重复工作表: {sheet_name} (与 {duplicate_sheet} 重复)")
                    self.merge_stats['duplicate_sources'] += 1
                    self.merge_stats['duplicate_details'].append({
                        'source': f"工作表_{sheet_name}",
                        'duplicate_of': f"工作表_{duplicate_sheet}",
                        'reason': '文件内工作表数据重复'
                    })
                    continue

                local_hashes[data_hash] = sheet_name

                # 检查数据结构一致性
                if reference_structure is None:
                    reference_structure = standardized_data
                    merged_data = standardized_data.copy()
                    self.merge_stats['merged_sources'] += 1
                    self.merge_stats['merged_rows'] += len(standardized_data)

                    # 记录详细合并信息
                    self.merge_stats['merged_details'].append({
                        'source_type': 'excel_sheet',
                        'file_name': Path(excel_path).name,
                        'sheet_name': sheet_name,
                        'rows': len(standardized_data),
                        'columns': len(standardized_data.columns),
                        'role': '参考结构',
                        'reason': '作为合并基准'
                    })

                    self.logger.info(f"使用 {sheet_name} 作为参考结构")
                else:
                    is_compatible, reason = self.compare_data_structure(
                        reference_structure, standardized_data,
                        "参考结构", f"工作表_{sheet_name}"
                    )

                    if is_compatible:
                        merged_data = pd.concat([merged_data, standardized_data],
                                             ignore_index=True, sort=False)
                        self.merge_stats['merged_sources'] += 1
                        self.merge_stats['merged_rows'] += len(standardized_data)

                        # 记录详细合并信息
                        self.merge_stats['merged_details'].append({
                            'source_type': 'excel_sheet',
                            'file_name': Path(excel_path).name,
                            'sheet_name': sheet_name,
                            'rows': len(standardized_data),
                            'columns': len(standardized_data.columns),
                            'role': '数据合并',
                            'reason': reason
                        })

                        self.logger.info(f"成功合并工作表: {sheet_name} ({reason})")
                    else:
                        self.merge_stats['skipped_sources'] += 1
                        self.merge_stats['skipped_details'].append({
                            'source': f"工作表_{sheet_name}",
                            'reason': reason
                        })
                        self.logger.warning(f"跳过不兼容工作表: {sheet_name} - {reason}")

            # 保存结果（检查是否有实质性合并）
            if output_path and not merged_data.empty:
                # 检查是否有多个工作表被合并
                merged_sheets_count = self.merge_stats['merged_sources']
                if merged_sheets_count > 1:
                    self._save_merged_data(merged_data, output_path)
                else:
                    print(f"📝 Excel文件只有一个工作表，无实质性合并行为，未生成输出文件: {output_path}")
            elif output_path and merged_data.empty:
                print(f"📝 Excel文件无有效数据，未生成输出文件: {output_path}")

            self._print_merge_summary("Excel工作表合并")
            return merged_data

        except Exception as e:
            self.logger.error(f"合并Excel文件失败: {str(e)}")
            return pd.DataFrame()

    def smart_merge_directory(self, input_path: str, output_path: str = None,
                            file_pattern: str = "*", recursive: bool = False) -> pd.DataFrame:
        """智能合并目录中的文件（三阶段策略）"""
        self.logger.info(f"开始智能合并目录: {input_path}")
        self._reset_stats()

        input_dir = Path(input_path)
        if not input_dir.exists() or not input_dir.is_dir():
            self.logger.error(f"输入路径不存在或不是目录: {input_path}")
            return pd.DataFrame()

        # 确定要排除的输出文件路径
        exclude_paths = set()
        if output_path:
            output_file = Path(output_path)
            exclude_paths.add(output_file.resolve())
            # 同时排除可能的同名不同扩展名文件
            exclude_paths.add(output_file.with_suffix('.csv').resolve())
            exclude_paths.add(output_file.with_suffix('.xlsx').resolve())
            exclude_paths.add(output_file.with_suffix('.xls').resolve())
            self.logger.debug(f"排除输出文件路径: {[str(p) for p in exclude_paths]}")

        # 查找并分类文件
        excel_files, other_files = self._categorize_files(input_dir, file_pattern, recursive, exclude_paths)

        if not excel_files and not other_files:
            self.logger.warning(f"未找到匹配的文件: {file_pattern}")
            return pd.DataFrame()

        self.logger.info(f"找到 {len(excel_files)} 个Excel文件, {len(other_files)} 个其他格式文件")

        # 第一阶段：处理Excel文件内部工作表合并
        excel_merged_results = self._process_excel_files(excel_files)

        # 第二阶段：处理其他格式文件
        other_file_results = self._process_other_files(other_files)

        # 第三阶段：跨文件合并
        final_result = self._cross_file_merge(excel_merged_results + other_file_results)

        # 保存结果（只有在有实质性合并时才保存）
        if output_path and not final_result.empty:
            self._save_merged_data(final_result, output_path)
        elif output_path and final_result.empty:
            print(f"📝 由于无实质性合并行为，未生成输出文件: {output_path}")

        self._print_merge_summary("智能目录合并")
        return final_result

    def _categorize_files(self, input_dir: Path, file_pattern: str, recursive: bool, exclude_paths: set = None) -> Tuple[List[Path], List[Path]]:
        """分类文件：Excel文件和其他格式文件，排除指定的输出文件"""
        excel_extensions = ['.xlsx', '.xls']
        other_extensions = ['.csv', '.txt']

        excel_files = []
        other_files = []

        if exclude_paths is None:
            exclude_paths = set()

        # 查找文件
        for ext in excel_extensions + other_extensions:
            if file_pattern == "*":
                # 特殊处理通配符模式
                pattern = f"**/*{ext}" if recursive else f"*{ext}"
            else:
                pattern = f"**/{file_pattern}{ext}" if recursive else f"{file_pattern}{ext}"
            found_files = list(input_dir.glob(pattern))

            for file_path in found_files:
                # 检查是否为要排除的文件
                if file_path.resolve() in exclude_paths:
                    # 静默排除输出文件，不产生任何日志提示
                    continue

                if ext in excel_extensions:
                    excel_files.append(file_path)
                else:
                    other_files.append(file_path)

        # 去重并排序
        excel_files = sorted(list(set(excel_files)))
        other_files = sorted(list(set(other_files)))

        return excel_files, other_files

    def _process_excel_files(self, excel_files: List[Path]) -> List[Dict]:
        """处理Excel文件，返回每个文件的合并结果"""
        excel_results = []

        for excel_file in excel_files:
            self.logger.info(f"处理Excel文件: {excel_file.name}")

            try:
                # 合并Excel文件内的工作表
                merged_data = self._merge_single_excel_file(excel_file)

                if not merged_data.empty:
                    excel_results.append({
                        'source': excel_file.name,
                        'data': merged_data,
                        'type': 'excel_merged'
                    })

                    # 注意：不在这里记录Excel文件的数据哈希
                    # Excel文件的重复检测将在跨文件合并阶段与其他文件一起进行
                    # 这样确保Excel文件和CSV文件在重复检测中地位平等

                    self.logger.info(f"Excel文件 {excel_file.name} 内部合并完成: {merged_data.shape}")
                else:
                    self.logger.warning(f"Excel文件 {excel_file.name} 合并结果为空")

            except Exception as e:
                self.logger.error(f"处理Excel文件失败 {excel_file.name}: {str(e)}")

        return excel_results

    def _merge_single_excel_file(self, excel_file: Path) -> pd.DataFrame:
        """合并单个Excel文件内的所有工作表"""
        try:
            # 读取所有工作表
            all_sheets = pd.read_excel(excel_file, sheet_name=None)

            if not all_sheets:
                return pd.DataFrame()

            merged_data = pd.DataFrame()
            reference_structure = None
            reference_columns = None  # 参考列名
            local_hashes = {}  # 本文件内的重复检测

            # 第一步：智能分析所有工作表，确定参考列名和数据模式
            sheet_items = list(all_sheets.items())
            reference_columns = None

            # 分析每个工作表的标题行情况
            sheet_analysis = []
            for sheet_name, sheet_data in sheet_items:
                if sheet_data.empty:
                    continue

                # 检测是否有标题行
                has_header, header_row = self.detect_header_row(sheet_data)

                analysis = {
                    'sheet_name': sheet_name,
                    'sheet_data': sheet_data,
                    'has_header': has_header,
                    'header_row': header_row,
                    'columns_count': len(sheet_data.columns),
                    'rows_count': len(sheet_data)
                }
                sheet_analysis.append(analysis)

                self.logger.debug(f"工作表 {sheet_name}: 标题行={has_header}, 位置={header_row}, 列数={len(sheet_data.columns)}")

            # 优先选择有标题行的工作表作为参考
            for analysis in sheet_analysis:
                if analysis['has_header'] and analysis['header_row'] >= 0:
                    sheet_data = analysis['sheet_data']
                    header_row = analysis['header_row']

                    if header_row > 0:
                        reference_columns = sheet_data.iloc[header_row].astype(str).str.strip().tolist()
                    else:
                        reference_columns = sheet_data.columns.astype(str).str.strip().tolist()

                    self.logger.info(f"使用工作表 {analysis['sheet_name']} 的列名作为参考: {reference_columns}")
                    break

            # 如果没有找到有标题行的工作表，检查是否所有工作表列数一致
            if reference_columns is None and sheet_analysis:
                # 检查列数一致性
                column_counts = [analysis['columns_count'] for analysis in sheet_analysis]
                if len(set(column_counts)) == 1:
                    # 所有工作表列数一致，可能都没有标题行，使用默认列名
                    columns_count = column_counts[0]
                    reference_columns = [f'Column_{i+1}' for i in range(columns_count)]
                    self.logger.info(f"所有工作表列数一致({columns_count}列)但无标题行，使用默认列名: {reference_columns}")
                else:
                    # 列数不一致，使用第一个工作表的列数
                    first_analysis = sheet_analysis[0]
                    reference_columns = [f'Column_{i+1}' for i in range(first_analysis['columns_count'])]
                    self.logger.warning(f"工作表列数不一致，使用第一个工作表的列数生成默认列名: {reference_columns}")

            # 第二步：使用统一的列名处理所有工作表
            for sheet_name, sheet_data in sheet_items:
                if sheet_data.empty:
                    continue

                # 智能标准化数据（使用参考列名）
                standardized_data = self._smart_standardize_sheet(
                    sheet_data, f"{excel_file.name}_{sheet_name}", reference_columns
                )

                if standardized_data.empty:
                    continue

                # 检查本文件内的工作表重复
                data_hash = self.calculate_data_hash(standardized_data)
                if data_hash in local_hashes:
                    duplicate_sheet = local_hashes[data_hash]
                    self.logger.warning(f"跳过重复工作表: {sheet_name} (与 {duplicate_sheet} 重复)")
                    self.merge_stats['duplicate_sources'] += 1
                    self.merge_stats['duplicate_details'].append({
                        'source': f"{excel_file.name}_{sheet_name}",
                        'duplicate_of': f"{excel_file.name}_{duplicate_sheet}",
                        'reason': '文件内工作表数据重复'
                    })
                    continue

                local_hashes[data_hash] = sheet_name

                # 检查数据结构一致性
                if reference_structure is None:
                    reference_structure = standardized_data
                    merged_data = standardized_data.copy()
                    self.merge_stats['merged_sources'] += 1
                    self.merge_stats['merged_rows'] += len(standardized_data)

                    # 记录详细合并信息
                    self.merge_stats['merged_details'].append({
                        'source_type': 'excel_sheet',
                        'file_name': excel_file.name,
                        'sheet_name': sheet_name,
                        'rows': len(standardized_data),
                        'columns': len(standardized_data.columns),
                        'role': '参考结构',
                        'reason': '作为合并基准'
                    })

                    self.logger.info(f"使用 {sheet_name} 作为参考结构")
                else:
                    is_compatible, reason = self.compare_data_structure(
                        reference_structure, standardized_data,
                        "参考结构", f"{excel_file.name}_{sheet_name}"
                    )

                    if is_compatible:
                        merged_data = pd.concat([merged_data, standardized_data],
                                             ignore_index=True, sort=False)
                        self.merge_stats['merged_sources'] += 1
                        self.merge_stats['merged_rows'] += len(standardized_data)

                        # 记录详细合并信息
                        self.merge_stats['merged_details'].append({
                            'source_type': 'excel_sheet',
                            'file_name': excel_file.name,
                            'sheet_name': sheet_name,
                            'rows': len(standardized_data),
                            'columns': len(standardized_data.columns),
                            'role': '数据合并',
                            'reason': reason
                        })

                        self.logger.info(f"成功合并工作表: {sheet_name} ({reason})")
                    else:
                        self.merge_stats['skipped_sources'] += 1
                        self.merge_stats['skipped_details'].append({
                            'source': f"{excel_file.name}_{sheet_name}",
                            'reason': reason
                        })
                        self.logger.warning(f"跳过不兼容工作表: {sheet_name} - {reason}")

            return merged_data

        except Exception as e:
            self.logger.error(f"合并Excel文件失败 {excel_file}: {str(e)}")
            return pd.DataFrame()

    def _smart_standardize_sheet(self, df: pd.DataFrame, source_name: str, reference_columns: List[str]) -> pd.DataFrame:
        """
        智能标准化工作表数据
        专门处理Excel文件中混合标题行的情况
        """
        if df.empty:
            self.logger.warning(f"{source_name}: 数据为空")
            return df

        # 检测标题行，但对于Excel内部工作表使用更宽松的策略
        has_header, header_row = self.detect_header_row(df)

        # 特殊处理：如果有参考列名，优先考虑数据一致性
        if reference_columns and len(reference_columns) == len(df.columns):
            # 检查第一行是否看起来像数据而不是标题
            first_row_is_data = self._is_likely_data_row(df.iloc[0], reference_columns)

            if first_row_is_data:
                # 第一行看起来是数据，直接应用参考列名
                df.columns = reference_columns
                self.logger.info(f"{source_name}: 第一行为数据行，应用参考列名 {reference_columns}")
            elif has_header and header_row >= 0:
                # 有标题行，但需要验证是否与参考列名兼容
                if header_row > 0:
                    actual_headers = df.iloc[header_row].astype(str).str.strip().tolist()
                    df = df.iloc[header_row + 1:].reset_index(drop=True)
                else:
                    actual_headers = df.columns.astype(str).str.strip().tolist()

                # 检查标题兼容性
                if self._are_headers_compatible(actual_headers, reference_columns):
                    df.columns = reference_columns  # 使用统一的参考列名
                    self.logger.info(f"{source_name}: 标题行兼容，统一使用参考列名")
                else:
                    df.columns = reference_columns  # 强制使用参考列名保持一致性
                    self.logger.warning(f"{source_name}: 标题行不兼容，强制使用参考列名以保持一致性")
            else:
                # 没有明确标题行，直接使用参考列名
                df.columns = reference_columns
                self.logger.info(f"{source_name}: 无明确标题行，应用参考列名")

        elif has_header and header_row >= 0:
            # 有标题行但没有参考列名的情况
            if header_row > 0:
                df.columns = df.iloc[header_row]
                df = df.iloc[header_row + 1:].reset_index(drop=True)
                self.logger.info(f"{source_name}: 使用第 {header_row+1} 行作为列名")
            else:
                self.logger.info(f"{source_name}: 使用第一行作为列名")

            # 清理列名
            df.columns = df.columns.astype(str).str.strip()

        else:
            # 既没有标题行也没有参考列名，使用默认列名
            df.columns = [f'Column_{i+1}' for i in range(len(df.columns))]
            self.logger.warning(f"{source_name}: 无标题行和参考列名，使用默认列名")

        # 移除全为空的行
        df = df.dropna(how='all')

        # 移除全为空的列
        df = df.dropna(axis=1, how='all')

        self.logger.info(f"{source_name}: 标准化后数据形状 {df.shape}, 列名 {list(df.columns)}")
        return df

    def _is_likely_data_row(self, row: pd.Series, reference_columns: List[str]) -> bool:
        """
        判断一行数据是否更像数据行而不是标题行
        """
        if len(row) != len(reference_columns):
            return False

        data_indicators = 0
        total_cells = 0

        for cell_value in row:
            if pd.notna(cell_value):
                total_cells += 1
                cell_str = str(cell_value).strip()

                # 数据行的特征
                # 1. 是数字
                if self._is_numeric_string(cell_str):
                    data_indicators += 1
                # 2. 是日期时间格式
                elif self._is_datetime_string(cell_str):
                    data_indicators += 1
                # 3. 是特定的数据模式（如车牌号）
                elif self._is_data_pattern(cell_str):
                    data_indicators += 1

        # 如果超过50%的非空单元格看起来像数据，则认为是数据行
        if total_cells > 0:
            data_ratio = data_indicators / total_cells
            return data_ratio > 0.5

        return False

    def _is_datetime_string(self, s: str) -> bool:
        """判断字符串是否是日期时间格式"""
        datetime_patterns = [
            r'\d{4}-\d{1,2}-\d{1,2}',  # 2024-12-15
            r'\d{4}/\d{1,2}/\d{1,2}',  # 2024/12/15
            r'\d{1,2}:\d{1,2}:\d{1,2}', # 08:30:00
            r'\d{4}-\d{1,2}-\d{1,2}\s+\d{1,2}:\d{1,2}:\d{1,2}',  # 2024-12-15 08:30:00
        ]

        import re
        for pattern in datetime_patterns:
            if re.search(pattern, s):
                return True
        return False

    def _is_data_pattern(self, s: str) -> bool:
        """判断字符串是否符合特定的数据模式"""
        import re

        # 车牌号模式
        plate_pattern = r'^[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领][A-Z][A-Z0-9]{4,5}[A-Z0-9挂学警港澳]?$'
        if re.match(plate_pattern, s):
            return True

        # 其他数据模式可以在这里添加

        return False

    def _are_headers_compatible(self, headers1: List[str], headers2: List[str]) -> bool:
        """
        判断两组标题是否兼容
        """
        if len(headers1) != len(headers2):
            return False

        # 简单的相似度检查
        matches = 0
        for h1, h2 in zip(headers1, headers2):
            if h1.lower().strip() == h2.lower().strip():
                matches += 1
            elif self._are_similar_headers(h1, h2):
                matches += 1

        # 如果80%以上的标题匹配或相似，认为兼容
        compatibility_ratio = matches / len(headers1)
        return compatibility_ratio >= 0.8

    def _are_similar_headers(self, h1: str, h2: str) -> bool:
        """
        判断两个标题是否相似
        """
        h1_clean = h1.lower().strip().replace(' ', '').replace('_', '')
        h2_clean = h2.lower().strip().replace(' ', '').replace('_', '')

        # 包含关系
        if h1_clean in h2_clean or h2_clean in h1_clean:
            return True

        # 可以添加更多相似度判断逻辑

        return False

    def _process_other_files(self, other_files: List[Path]) -> List[Dict]:
        """处理其他格式文件（CSV、TXT等）"""
        other_results = []

        for file_path in other_files:
            self.logger.info(f"处理文件: {file_path.name}")

            # 读取文件
            file_data = self.read_file_with_encoding(file_path)

            if file_data.empty:
                self.logger.warning(f"跳过空文件: {file_path.name}")
                self.merge_stats['skipped_sources'] += 1
                self.merge_stats['skipped_details'].append({
                    'source': file_path.name,
                    'reason': '文件为空或读取失败'
                })
                continue

            # 标准化数据
            standardized_data = self.standardize_dataframe(file_data, file_path.name)

            if standardized_data.empty:
                self.logger.warning(f"跳过标准化后为空的文件: {file_path.name}")
                self.merge_stats['skipped_sources'] += 1
                self.merge_stats['skipped_details'].append({
                    'source': file_path.name,
                    'reason': '标准化后数据为空'
                })
                continue

            other_results.append({
                'source': file_path.name,
                'data': standardized_data,
                'type': 'single_file'
            })

        return other_results

    def _cross_file_merge(self, all_results: List[Dict]) -> pd.DataFrame:
        """跨文件合并所有结果 - 支持任意两个兼容文件的合并"""
        if not all_results:
            return pd.DataFrame()

        # 更新总数据源数量（包括Excel文件内部已合并的工作表）
        total_excel_sheets = sum(1 for detail in self.merge_stats['merged_details'] if detail['source_type'] == 'excel_sheet')
        total_skipped_sheets = sum(1 for detail in self.merge_stats['skipped_details'] if '_' in detail['source'])
        self.merge_stats['total_sources'] = len(all_results) + total_excel_sheets + total_skipped_sheets

        # 第一步：去重检测，移除重复文件
        unique_results = self._remove_duplicate_files(all_results)

        if not unique_results:
            self.logger.warning("去重后没有可用的文件进行合并")
            return pd.DataFrame()

        # 第二步：分组兼容的文件
        compatible_groups = self._group_compatible_files(unique_results)

        if not compatible_groups:
            self.logger.warning("没有找到兼容的文件组进行合并")
            return pd.DataFrame()

        # 第三步：合并最大的兼容组
        largest_group = max(compatible_groups, key=lambda g: len(g['files']))

        print(f"\n🔍 兼容性分析结果:")
        print(f"   找到 {len(compatible_groups)} 个兼容组")
        print(f"   选择最大组进行合并（包含 {len(largest_group['files'])} 个文件）")

        # 显示所有兼容组的信息
        for i, group in enumerate(compatible_groups, 1):
            file_names = [f['source'] for f in group['files']]
            status = "✅ 已合并" if group == largest_group else "⏸️  未合并"
            print(f"   兼容组 {i} ({status}): {len(group['files'])} 个文件")
            for file_name in file_names:
                print(f"      - {file_name}")

        # 检查是否有实质性的合并行为
        has_substantial_merge = self._check_substantial_merge(largest_group)

        if has_substantial_merge:
            # 合并最大兼容组
            merged_data = self._merge_compatible_group(largest_group)

            # 记录未合并的文件（属于其他兼容组的文件）
            merged_file_names = {f['source'] for f in largest_group['files']}
            unmerged_files = [f for f in unique_results if f['source'] not in merged_file_names]

            for unmerged_file in unmerged_files:
                self.merge_stats['skipped_sources'] += 1
                self.merge_stats['skipped_details'].append({
                    'source': unmerged_file['source'],
                    'reason': '属于其他兼容组，未选择合并'
                })

            return merged_data
        else:
            # 没有实质性合并，返回空数据框
            print(f"\n⚠️  未检测到实质性合并行为，不生成合并文件")
            print(f"   最大兼容组只包含 {len(largest_group['files'])} 个数据源")
            print(f"   需要至少2个不同的文件或工作表才会生成合并结果")

            # 将所有文件标记为跳过
            for result in unique_results:
                self.merge_stats['skipped_sources'] += 1
                self.merge_stats['skipped_details'].append({
                    'source': result['source'],
                    'reason': '无实质性合并行为'
                })

            return pd.DataFrame()

    def _remove_duplicate_files(self, all_results: List[Dict]) -> List[Dict]:
        """移除重复文件，返回去重后的文件列表"""
        unique_results = []

        for result in all_results:
            source_name = result['source']
            data = result['data']
            result_type = result.get('type', 'unknown')

            # 统一的跨文件重复检测：Excel文件和其他文件平等对待
            is_duplicate, duplicate_source = self.is_duplicate_data(data, source_name)

            if is_duplicate:
                self.logger.warning(f"跳过重复文件: {source_name} (与 {duplicate_source} 重复)")
                self.merge_stats['duplicate_sources'] += 1
                self.merge_stats['duplicate_details'].append({
                    'source': source_name,
                    'duplicate_of': duplicate_source,
                    'reason': '跨文件/跨格式数据重复'
                })

                # 如果是Excel文件被跳过，需要清理之前记录的统计信息
                if result_type == 'excel_merged':
                    self._cleanup_excel_stats(source_name)
            else:
                # 记录成功的文件哈希（用于后续重复检测）
                self.record_successful_merge(data, source_name)
                unique_results.append(result)

        return unique_results

    def _group_compatible_files(self, unique_results: List[Dict]) -> List[Dict]:
        """将文件按兼容性分组"""
        if not unique_results:
            return []

        compatible_groups = []
        processed_files = set()

        for i, base_result in enumerate(unique_results):
            if base_result['source'] in processed_files:
                continue

            # 创建新的兼容组，以当前文件为基准
            current_group = {
                'reference_file': base_result['source'],
                'reference_data': base_result['data'],
                'files': [base_result]
            }

            processed_files.add(base_result['source'])

            # 查找与当前文件兼容的其他文件
            for j, other_result in enumerate(unique_results):
                if i != j and other_result['source'] not in processed_files:
                    is_compatible, reason = self.compare_data_structure(
                        base_result['data'], other_result['data'],
                        base_result['source'], other_result['source']
                    )

                    if is_compatible:
                        current_group['files'].append(other_result)
                        processed_files.add(other_result['source'])
                        self.logger.info(f"文件 {other_result['source']} 与 {base_result['source']} 兼容: {reason}")

            # 只有包含多个文件的组才有合并价值，单文件组也保留（可能是唯一的数据源）
            compatible_groups.append(current_group)

        return compatible_groups

    def _check_substantial_merge(self, group: Dict) -> bool:
        """
        检查是否有实质性的合并行为

        实质性合并的定义：
        1. 多个不同的文件合并
        2. 单个Excel文件内多个工作表合并
        3. 排除：单个文件单个工作表的情况
        """
        if not group or not group.get('files'):
            return False

        files = group['files']

        # 情况1：多个文件 - 这肯定是实质性合并
        if len(files) > 1:
            return True

        # 情况2：单个文件 - 需要检查是否是Excel文件的多工作表合并
        if len(files) == 1:
            file_result = files[0]
            result_type = file_result.get('type', 'unknown')

            # 如果是Excel合并结果，说明内部有多个工作表被合并了
            if result_type == 'excel_merged':
                # 检查这个Excel文件是否真的合并了多个工作表
                file_name = file_result['source']
                excel_merge_count = sum(
                    1 for detail in self.merge_stats['merged_details']
                    if detail.get('file_name') == file_name and detail.get('source_type') == 'excel_sheet'
                )

                # 如果有多个工作表被合并，则认为是实质性合并
                if excel_merge_count > 1:
                    return True

            # 单个文件单个工作表/数据源，不是实质性合并
            return False

        return False

    def _merge_compatible_group(self, group: Dict) -> pd.DataFrame:
        """合并一个兼容组中的所有文件"""
        if not group['files']:
            return pd.DataFrame()

        merged_data = pd.DataFrame()

        for i, file_result in enumerate(group['files']):
            source_name = file_result['source']
            data = file_result['data']
            result_type = file_result.get('type', 'unknown')

            if i == 0:
                # 第一个文件作为基准
                merged_data = data.copy()

                # 统计信息
                if result_type != 'excel_merged':
                    self.merge_stats['merged_sources'] += 1
                    self.merge_stats['merged_rows'] += len(data)
                else:
                    self.merge_stats['merged_rows'] += len(data)

                # 记录详细合并信息
                if result_type != 'excel_merged':
                    self.merge_stats['merged_details'].append({
                        'source_type': 'file',
                        'file_name': source_name,
                        'sheet_name': None,
                        'rows': len(data),
                        'columns': len(data.columns),
                        'role': '参考结构',
                        'reason': '作为合并基准'
                    })

                self.logger.info(f"使用 {source_name} 作为参考结构")
            else:
                # 后续文件进行合并
                merged_data = pd.concat([merged_data, data], ignore_index=True, sort=False)

                # 统计信息
                if result_type != 'excel_merged':
                    self.merge_stats['merged_sources'] += 1
                    self.merge_stats['merged_rows'] += len(data)
                else:
                    self.merge_stats['merged_rows'] += len(data)

                # 记录详细合并信息
                if result_type != 'excel_merged':
                    self.merge_stats['merged_details'].append({
                        'source_type': 'file',
                        'file_name': source_name,
                        'sheet_name': None,
                        'rows': len(data),
                        'columns': len(data.columns),
                        'role': '数据合并',
                        'reason': '兼容文件合并'
                    })

                self.logger.info(f"成功合并文件: {source_name}")

        return merged_data

    def _cleanup_excel_stats(self, excel_file_name: str):
        """
        清理被跳过的Excel文件的统计信息
        当Excel文件在跨文件合并时被跳过时，需要清理之前记录的统计信息
        """
        # 清理merged_details中该Excel文件的记录
        original_details = self.merge_stats['merged_details'].copy()
        self.merge_stats['merged_details'] = [
            detail for detail in original_details
            if detail.get('file_name') != excel_file_name
        ]

        # 计算被清理的统计数据
        cleaned_details = [
            detail for detail in original_details
            if detail.get('file_name') == excel_file_name
        ]

        if cleaned_details:
            # 减少相应的统计计数
            cleaned_sources = len(cleaned_details)
            cleaned_rows = sum(detail.get('rows', 0) for detail in cleaned_details)

            self.merge_stats['merged_sources'] -= cleaned_sources
            self.merge_stats['merged_rows'] -= cleaned_rows

            self.logger.info(f"清理Excel文件 {excel_file_name} 的统计信息: {cleaned_sources}个源, {cleaned_rows}行")

    def _save_merged_data(self, data: pd.DataFrame, output_path: str):
        """
        保存合并后的数据
        支持多种输出格式和自动判断
        """
        try:
            output_file = Path(output_path)

            # 确保输出目录存在
            output_file.parent.mkdir(parents=True, exist_ok=True)

            # 获取输出格式设置
            output_format = self.settings.get('output_format', 'auto').lower()

            # 确定最终的保存格式和文件路径
            final_format, final_path = self._determine_output_format(output_file, output_format)

            # 根据确定的格式保存文件
            if final_format == 'excel':
                data.to_excel(final_path, index=False, engine='openpyxl')
                self.logger.info(f"合并结果已保存为Excel格式: {final_path}")
            elif final_format == 'csv':
                encoding = self.settings.get('encoding', 'utf-8-sig')
                data.to_csv(final_path, index=False, encoding=encoding)
                self.logger.info(f"合并结果已保存为CSV格式: {final_path}")
            else:
                # 默认保存为CSV
                encoding = self.settings.get('encoding', 'utf-8-sig')
                data.to_csv(final_path, index=False, encoding=encoding)
                self.logger.info(f"合并结果已保存为CSV格式: {final_path}")

        except Exception as e:
            self.logger.error(f"保存文件失败: {str(e)}")

    def _determine_output_format(self, output_file: Path, output_format: str) -> tuple:
        """
        确定输出格式和最终文件路径

        Args:
            output_file: 输出文件路径对象
            output_format: 输出格式设置

        Returns:
            tuple: (最终格式, 最终文件路径)
        """

        if output_format == 'auto':
            # 自动判断：根据文件扩展名决定
            file_ext = output_file.suffix.lower()

            if file_ext in ['.xlsx', '.xls']:
                return 'excel', str(output_file)
            elif file_ext in ['.csv']:
                return 'csv', str(output_file)
            else:
                # 没有扩展名或不支持的扩展名，默认使用CSV
                if not file_ext:
                    new_path = output_file.with_suffix('.csv')
                    self.logger.info(f"文件无扩展名，自动添加.csv扩展名: {new_path}")
                    return 'csv', str(new_path)
                else:
                    # 保持原扩展名但使用CSV格式
                    self.logger.warning(f"不支持的文件扩展名 {file_ext}，将使用CSV格式保存")
                    return 'csv', str(output_file)

        elif output_format in ['csv']:
            # 强制使用CSV格式
            if output_file.suffix.lower() not in ['.csv']:
                new_path = output_file.with_suffix('.csv')
                self.logger.info(f"根据output_format设置，修改文件扩展名为.csv: {new_path}")
                return 'csv', str(new_path)
            else:
                return 'csv', str(output_file)

        elif output_format in ['xlsx', 'excel']:
            # 强制使用Excel格式
            if output_file.suffix.lower() not in ['.xlsx', '.xls']:
                new_path = output_file.with_suffix('.xlsx')
                self.logger.info(f"根据output_format设置，修改文件扩展名为.xlsx: {new_path}")
                return 'excel', str(new_path)
            else:
                return 'excel', str(output_file)

        else:
            # 不支持的格式，默认使用CSV
            self.logger.warning(f"不支持的output_format设置: {output_format}，将使用CSV格式")
            if output_file.suffix.lower() not in ['.csv']:
                new_path = output_file.with_suffix('.csv')
                return 'csv', str(new_path)
            else:
                return 'csv', str(output_file)

    def _print_merge_summary(self, operation_name: str):
        """打印合并摘要"""
        print(f"\n📊 {operation_name}完成！")
        print(f"=" * 80)
        print(f"📄 总数据源: {self.merge_stats['total_sources']}")
        print(f"✅ 成功合并: {self.merge_stats['merged_sources']}")
        print(f"❌ 跳过数量: {self.merge_stats['skipped_sources']}")
        print(f"🔄 重复数量: {self.merge_stats['duplicate_sources']}")
        print(f"📊 合并行数: {self.merge_stats['merged_rows']}")

        if self.merge_stats['total_sources'] > 0:
            success_rate = (self.merge_stats['merged_sources'] / self.merge_stats['total_sources']) * 100
            duplicate_rate = (self.merge_stats['duplicate_sources'] / self.merge_stats['total_sources']) * 100
            print(f"📈 成功率: {success_rate:.1f}%")
            print(f"🔄 重复率: {duplicate_rate:.1f}%")

        # 显示详细合并信息
        if self.merge_stats['merged_details']:
            print(f"\n✅ 成功合并的数据源详情:")
            print(f"{'序号':<4} {'文件名':<25} {'工作表':<15} {'行数':<8} {'列数':<6} {'角色':<10} {'说明'}")
            print("-" * 80)

            for i, detail in enumerate(self.merge_stats['merged_details'], 1):
                file_name = detail['file_name'][:24] if len(detail['file_name']) > 24 else detail['file_name']
                sheet_name = detail['sheet_name'][:14] if detail['sheet_name'] else '-'
                if sheet_name and len(sheet_name) > 14:
                    sheet_name = sheet_name[:14]

                print(f"{i:<4} {file_name:<25} {sheet_name:<15} {detail['rows']:<8} {detail['columns']:<6} {detail['role']:<10} {detail['reason']}")

        # 按文件汇总统计
        if self.merge_stats['merged_details']:
            file_summary = {}
            for detail in self.merge_stats['merged_details']:
                file_name = detail['file_name']
                if file_name not in file_summary:
                    file_summary[file_name] = {
                        'sheets': 0,
                        'total_rows': 0,
                        'total_columns': set()
                    }
                file_summary[file_name]['sheets'] += 1
                file_summary[file_name]['total_rows'] += detail['rows']
                file_summary[file_name]['total_columns'].add(detail['columns'])

            print(f"\n📁 文件级别汇总:")
            print(f"{'文件名':<30} {'工作表数':<10} {'总行数':<10} {'列数':<8}")
            print("-" * 60)

            for file_name, summary in file_summary.items():
                display_name = file_name[:29] if len(file_name) > 29 else file_name
                columns_info = f"{min(summary['total_columns'])}" if len(summary['total_columns']) == 1 else f"{min(summary['total_columns'])}-{max(summary['total_columns'])}"
                print(f"{display_name:<30} {summary['sheets']:<10} {summary['total_rows']:<10} {columns_info:<8}")

        # 显示重复检测详情
        if self.merge_stats['duplicate_details']:
            print(f"\n🔄 重复数据详情:")
            for detail in self.merge_stats['duplicate_details']:
                print(f"   - {detail['source']} → 重复于 {detail['duplicate_of']}")
                print(f"     原因: {detail['reason']}")

        # 显示跳过详情
        if self.merge_stats['skipped_details']:
            print(f"\n⚠️  跳过详情:")
            for detail in self.merge_stats['skipped_details']:
                print(f"   - {detail['source']}: {detail['reason']}")

        print(f"=" * 80)

    def execute_task(self, task_config: Dict) -> bool:
        """执行单个合并任务"""
        task_name = task_config.get('task_name', '未命名任务')

        print(f"\n🚀 开始执行任务: {task_name}")
        print(f"⏰ 开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

        try:
            # 验证配置
            if not self._validate_task_config(task_config):
                return False

            # 准备路径
            input_dir = task_config['input_directory']
            output_dir = task_config['output_directory']
            output_filename = task_config['output_filename']

            # 处理输出文件名（添加时间戳避免重复）
            output_file = Path(output_filename)
            filename_stem = output_file.stem  # 文件名（不含扩展名）
            filename_suffix = output_file.suffix  # 扩展名

            # 根据设置决定是否添加时间戳
            if self.settings.get('add_timestamp_to_filename', True):
                # 生成带时间戳的文件名
                timestamped_filename = f"{filename_stem}_{self.timestamp}{filename_suffix}"
                output_path = Path(output_dir) / timestamped_filename
            else:
                # 使用原始文件名
                output_path = Path(output_dir) / output_filename

            # 创建输出目录
            output_path.parent.mkdir(parents=True, exist_ok=True)

            # 显示任务配置
            self._print_task_config(task_config, str(output_path))

            # 执行合并
            start_time = datetime.now()

            if task_config.get('merge_strategy', 'smart') == 'smart':
                result = self.smart_merge_directory(
                    input_path=input_dir,
                    output_path=str(output_path),
                    file_pattern=task_config.get('file_pattern', '*'),
                    recursive=task_config.get('recursive', True)
                )
            else:
                # 传统合并方法（可以在这里实现）
                result = self.smart_merge_directory(
                    input_path=input_dir,
                    output_path=str(output_path),
                    file_pattern=task_config.get('file_pattern', '*'),
                    recursive=task_config.get('recursive', True)
                )

            end_time = datetime.now()
            duration = end_time - start_time

            # 记录执行历史
            task_result = {
                'task_name': task_name,
                'start_time': start_time,
                'end_time': end_time,
                'duration': duration,
                'success': not result.empty,
                'result_shape': result.shape if not result.empty else (0, 0),
                'output_file': str(output_path),
                'stats': self.merge_stats.copy()
            }

            self.execution_history.append(task_result)

            # 显示结果
            self._print_task_result(task_result)

            return not result.empty

        except Exception as e:
            print(f"❌ 任务执行失败: {str(e)}")
            import traceback
            traceback.print_exc()
            return False

    def _validate_task_config(self, config: Dict) -> bool:
        """验证任务配置"""
        required_fields = ['input_directory', 'output_directory', 'output_filename']

        for field in required_fields:
            if not config.get(field):
                print(f"❌ 配置错误: {field} 未设置")
                return False

        # 检查输入目录
        input_dir = Path(config['input_directory'])
        if not input_dir.exists():
            print(f"❌ 配置错误: 输入目录不存在 - {input_dir}")
            return False

        if not input_dir.is_dir():
            print(f"❌ 配置错误: 输入路径不是目录 - {input_dir}")
            return False

        return True

    def _print_task_config(self, config: Dict, output_path: str):
        """打印任务配置"""
        print(f"\n📋 任务配置:")
        print(f"   输入目录: {config['input_directory']}")
        print(f"   输出文件: {output_path}")
        print(f"   文件模式: {config.get('file_pattern', '*')}")
        print(f"   递归搜索: {config.get('recursive', True)}")
        print(f"   合并策略: {config.get('merge_strategy', 'smart')}")

    def _print_task_result(self, result: Dict):
        """打印任务结果"""
        print(f"\n🎯 任务执行结果")
        print(f"=" * 60)
        print(f"📋 任务名称: {result['task_name']}")
        print(f"⏱️  执行时间: {result['duration'].total_seconds():.2f} 秒")
        print(f"📊 结果状态: {'✅ 成功' if result['success'] else '❌ 失败'}")

        if result['success']:
            print(f"📄 数据形状: {result['result_shape'][0]} 行 × {result['result_shape'][1]} 列")
            print(f"💾 输出文件: {result['output_file']}")

            stats = result['stats']
            print(f"\n📈 合并统计:")
            print(f"   - 总数据源: {stats['total_sources']}")
            print(f"   - 成功合并: {stats['merged_sources']}")
            print(f"   - 重复跳过: {stats['duplicate_sources']}")
            print(f"   - 其他跳过: {stats['skipped_sources']}")
            print(f"   - 合并行数: {stats['merged_rows']}")

        print(f"=" * 60)

    def print_execution_history(self):
        """打印执行历史"""
        if not self.execution_history:
            print("📝 暂无执行历史记录")
            return

        print(f"\n📚 执行历史记录 (共 {len(self.execution_history)} 个任务)")
        print(f"=" * 80)

        for i, result in enumerate(self.execution_history, 1):
            status = "✅ 成功" if result['success'] else "❌ 失败"
            duration = result['duration'].total_seconds()

            print(f"{i:2d}. {result['task_name']}")
            print(f"    时间: {result['start_time'].strftime('%Y-%m-%d %H:%M:%S')}")
            print(f"    状态: {status} | 耗时: {duration:.2f}s")

            if result['success']:
                shape = result['result_shape']
                print(f"    结果: {shape[0]} 行 × {shape[1]} 列")
                print(f"    文件: {Path(result['output_file']).name}")

            print()

        print(f"=" * 80)


# ========================================
# 🚀 主执行函数
# ========================================

def execute_all_enabled_tasks():
    """执行所有启用的任务"""
    print("🎯 Excel数据合并器 - 集成版")
    print("=" * 60)

    # 创建合并器实例
    merger = IntegratedDataMerger(GLOBAL_SETTINGS)

    # 获取启用的任务
    enabled_tasks = {k: v for k, v in MERGE_TASKS.items() if v.get('enabled', False)}

    if not enabled_tasks:
        print("⚠️  没有启用的任务")
        print("请在配置区域将任务的 'enabled' 设置为 True")
        return

    print(f"📋 找到 {len(enabled_tasks)} 个启用的任务")

    # 执行所有启用的任务
    results = []
    for task_id, task_config in enabled_tasks.items():
        print(f"\n{'='*60}")
        success = merger.execute_task(task_config)
        results.append(success)

    # 显示总结
    successful_tasks = sum(results)
    total_tasks = len(results)

    print(f"\n📊 批量执行总结:")
    print(f"=" * 60)
    print(f"   总任务数: {total_tasks}")
    print(f"   成功任务: {successful_tasks}")
    print(f"   失败任务: {total_tasks - successful_tasks}")
    print(f"   成功率: {successful_tasks/total_tasks*100:.1f}%")

    # 显示执行历史
    merger.print_execution_history()


def execute_single_task(task_id: str):
    """执行单个指定的任务"""
    if task_id not in MERGE_TASKS:
        print(f"❌ 任务不存在: {task_id}")
        print(f"可用任务: {list(MERGE_TASKS.keys())}")
        return False

    task_config = MERGE_TASKS[task_id]

    # 创建合并器实例
    merger = IntegratedDataMerger(GLOBAL_SETTINGS)

    # 执行任务
    success = merger.execute_task(task_config)

    return success


def show_task_configurations():
    """显示所有任务配置"""
    print("📋 任务配置概览")
    print("=" * 80)

    for task_id, config in MERGE_TASKS.items():
        status = "✅ 启用" if config.get('enabled', False) else "❌ 禁用"

        print(f"\n🔸 任务ID: {task_id}")
        print(f"   任务名称: {config.get('task_name', '未命名')}")
        print(f"   状态: {status}")
        print(f"   输入目录: {config.get('input_directory', '未设置')}")
        print(f"   输出目录: {config.get('output_directory', '未设置')}")
        print(f"   文件模式: {config.get('file_pattern', '*')}")
        print(f"   合并策略: {config.get('merge_strategy', 'smart')}")

    print(f"\n" + "=" * 80)


# ========================================
# 🎯 便捷函数（向后兼容）
# ========================================

def merge_excel_sheets(excel_path: str, output_path: str = None) -> pd.DataFrame:
    """合并Excel文件中所有工作表的便捷函数"""
    merger = IntegratedDataMerger()
    return merger.merge_excel_sheets(excel_path, output_path)


def merge_multiple_files(input_path: str, output_path: str = None,
                        file_pattern: str = "*", recursive: bool = False) -> pd.DataFrame:
    """合并多个文件的便捷函数"""
    merger = IntegratedDataMerger()
    return merger.smart_merge_directory(input_path, output_path, file_pattern, recursive)


# ========================================
# 🏃 主程序入口
# ========================================

if __name__ == "__main__":
    print("🎯 Excel数据合并器 - 完整集成版")
    print("=" * 60)

    # 显示配置概览
    show_task_configurations()

    # 默认执行所有启用的任务
    execute_all_enabled_tasks()

    print(f"\n✅ 程序执行完成！")
    print("如需修改配置，请编辑文件顶部的配置区域。")
