#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试Tooltip样式优化
验证鼠标悬停时的属性文字是否清晰可见
"""

import pandas as pd
import tempfile
import os
import shutil

def test_tooltip_optimization():
    """测试Tooltip样式优化"""
    print("🖱️ 测试Tooltip样式优化")
    print("=" * 60)
    
    # 创建临时目录
    temp_dir = tempfile.mkdtemp()
    print(f"测试目录: {temp_dir}")
    
    try:
        # 创建测试数据
        test_data = pd.DataFrame({
            '时间段': [
                '08:00-09:00', '09:00-10:00', '10:00-11:00', '11:00-12:00', 
                '12:00-13:00', '13:00-14:00', '14:00-15:00', '15:00-16:00'
            ],
            '进场数量': [120, 180, 150, 90, 110, 85, 95, 140],
            '出场数量': [80, 120, 160, 130, 100, 110, 120, 160],
            '总流量': [200, 300, 310, 220, 210, 195, 215, 300]
        })
        
        # 创建测试Excel文件
        excel_file = os.path.join(temp_dir, 'test_tooltip.xlsx')
        with pd.ExcelWriter(excel_file, engine='openpyxl') as writer:
            test_data.to_excel(writer, sheet_name='进出量时间分布', index=False)
        
        print(f"✅ 创建测试Excel文件")
        print(f"📊 测试数据概览:")
        print(f"   时间段数量: {len(test_data)}")
        print(f"   数据范围较大，便于测试tooltip显示效果")
        
        # 导入图表生成器
        from parking_chart_generator import ParkingChartGenerator
        
        # 创建图表生成器
        chart_generator = ParkingChartGenerator(excel_file, temp_dir)
        
        print(f"\n🎨 应用Tooltip样式优化...")
        
        # 生成图表
        chart_file = chart_generator.generate_traffic_flow_chart()
        
        if chart_file and os.path.exists(chart_file):
            print(f"✅ 图表生成成功: {os.path.basename(chart_file)}")
            
            # 读取HTML内容检查tooltip配置
            with open(chart_file, 'r', encoding='utf-8') as f:
                html_content = f.read()
            
            # 检查tooltip样式配置
            tooltip_checks = {
                '白色背景': 'rgba(255, 255, 255, 0.95)' in html_content,
                '边框颜色': '#cccccc' in html_content,
                '文字颜色': '#333333' in html_content,
                '字体大小': 'font_size' in html_content,
                '边框宽度': 'border_width' in html_content
            }
            
            print(f"\n🔍 Tooltip样式验证:")
            all_styles_applied = True
            for style_name, is_applied in tooltip_checks.items():
                status = "✅" if is_applied else "❌"
                print(f"   {status} {style_name}: {'已应用' if is_applied else '未应用'}")
                if not is_applied:
                    all_styles_applied = False
            
            # 显示优化详情
            print(f"\n🎯 Tooltip优化详情:")
            print(f"   背景色: rgba(255, 255, 255, 0.95) (白色半透明)")
            print(f"   边框色: #cccccc (浅灰色)")
            print(f"   文字色: #333333 (深灰色)")
            print(f"   字体大小: 12px")
            print(f"   边框宽度: 1px")
            
            # 对比说明
            print(f"\n📋 优化对比:")
            print(f"   修改前: 默认黑色半透明背景 + 浅色文字")
            print(f"   问题: 文字与背景对比度不足，难以阅读")
            print(f"   修改后: 白色半透明背景 + 深色文字")
            print(f"   优势: 高对比度，清晰易读")
            
            # 总体评估
            print(f"\n📊 优化效果评估:")
            if all_styles_applied:
                print("✅ Tooltip样式优化成功！")
                print("   - 白色半透明背景提供良好对比")
                print("   - 深灰色文字确保清晰可读")
                print("   - 浅灰色边框增强视觉层次")
                print("   - 鼠标悬停时信息清晰可见")
            else:
                print("❌ Tooltip样式优化有问题！")
                print("   - 部分样式配置未正确应用")
            
            return all_styles_applied
            
        else:
            print("❌ 图表生成失败")
            return False
        
    except Exception as e:
        print(f"❌ 测试异常: {str(e)}")
        import traceback
        traceback.print_exc()
        return False
        
    finally:
        # 清理
        try:
            shutil.rmtree(temp_dir)
            print(f"\n🧹 清理测试目录")
        except:
            pass

def demo_tooltip_styles():
    """演示Tooltip样式对比"""
    print("\n🖱️ Tooltip样式对比")
    print("=" * 60)
    
    print("🔴 修改前的Tooltip样式:")
    print("   背景色: 默认黑色半透明")
    print("   文字色: 默认浅色")
    print("   边框: 无或默认")
    print("   问题: 文字与背景对比度不足，特别是在深色图表主题下")
    
    print("\n🟢 修改后的Tooltip样式:")
    print("   背景色: rgba(255, 255, 255, 0.95) (白色半透明)")
    print("   文字色: #333333 (深灰色)")
    print("   边框色: #cccccc (浅灰色)")
    print("   边框宽度: 1px")
    print("   字体大小: 12px")
    print("   优势: 高对比度，清晰易读，专业外观")
    
    print("\n🎯 设计原理:")
    print("   ✅ 高对比度 - 深色文字 + 浅色背景")
    print("   ✅ 半透明效果 - 不完全遮挡图表内容")
    print("   ✅ 边框设计 - 增强视觉层次和专业感")
    print("   ✅ 字体优化 - 合适的大小和粗细")
    print("   ✅ 颜色一致性 - 与整体图表风格协调")

def main():
    """主函数"""
    print("🖱️ 测试Tooltip样式优化")
    print("=" * 80)
    
    # 演示样式对比
    demo_tooltip_styles()
    
    # 测试优化效果
    result = test_tooltip_optimization()
    
    print(f"\n📊 测试总结:")
    print("=" * 80)
    print(f"Tooltip样式优化: {'✅ 成功' if result else '❌ 有问题'}")
    
    if result:
        print(f"\n🎉 Tooltip样式优化完成！")
        print(f"   - 鼠标悬停时的文字现在清晰可见")
        print(f"   - 白色半透明背景提供良好对比度")
        print(f"   - 深灰色文字确保最佳可读性")
        print(f"   - 浅灰色边框增强专业外观")
        print(f"\n💡 使用提示:")
        print(f"   - 将鼠标悬停在柱状图或折线图上查看数据")
        print(f"   - 现在的tooltip信息清晰易读")
        print(f"   - 适合在各种背景下使用")
    else:
        print(f"\n⚠️ 需要进一步优化")

if __name__ == "__main__":
    main()
