# 🚪 出入口方向Timeline图表功能说明

## 🎯 功能概览

新增出入口进出量方向Timeline图表，显示每个出入口的进场和出场数据，与现有的总量Timeline图表形成互补，提供更详细的流向分析。

## 📊 功能对比

### 总量Timeline图表 (`出入口进出量_总量.html`)
- **数据内容**：每个出入口的总流量
- **数据系列**：单一系列（总量）
- **适用场景**：整体流量对比、总量趋势分析
- **颜色配置**：橙色单色系

### 方向Timeline图表 (`出入口进出量_方向.html`) ← **新增**
- **数据内容**：每个出入口的进场和出场流量
- **数据系列**：双系列（进场+出场）
- **适用场景**：流向分析、进出平衡评估
- **颜色配置**：深蓝色（进场）+ 深紫红色（出场）

## 🔧 技术实现

### 核心方法
```python
def generate_gate_traffic_direction_timeline(self, sheet_name='进出量时间分布(按道闸)'):
    """生成出入口进出量Timeline动态图表（显示进场和出场）"""
    
    # 1. 数据提取
    for i in range(gate_count):
        entry_col_idx = gate_start_col + i * 3      # 进场列
        exit_col_idx = gate_start_col + i * 3 + 1   # 出场列
        
    # 2. Timeline创建
    for time_period in time_periods:
        bar = Bar().add_xaxis(gate_names)
                  .add_yaxis("进场", entry_values)
                  .add_yaxis("出场", exit_values)
        timeline.add(bar, time_period)
```

### 数据结构处理
- **进场数据**：每个出入口的第1列（进场列）
- **出场数据**：每个出入口的第2列（出场列）
- **全局Y轴**：基于所有进场和出场数据计算固定范围

## 🎨 视觉设计

### 1. 颜色配置
```python
# 进场数据
color=self.chart_config['traffic_flow_colors']['entry']  # #2E86AB (深蓝色)

# 出场数据  
color=self.chart_config['traffic_flow_colors']['exit']   # #A23B72 (深紫红色)
```

### 2. 图表样式
- **柱状图类型**：并列柱状图，进场和出场并排显示
- **标签显示**：柱状图顶部显示数值
- **边框效果**：白色边框增强立体感
- **图例位置**：顶部显示，区分进场和出场

### 3. Timeline配置
```python
timeline.add_schema(
    play_interval=2000,    # 2秒播放间隔
    is_auto_play=False,    # 默认不自动播放
    is_loop_play=True,     # 支持循环播放
    pos_left="10%",        # 与X轴对齐
    pos_right="10%",       # 与X轴对齐
    width="80%"            # 时间轴长度与X轴一致
)
```

## 📈 功能特点

### 1. 流向分析
- **进出对比**：直观比较每个出入口的进场和出场流量
- **平衡评估**：评估各出入口的进出流量是否平衡
- **流向识别**：识别主要的进入口和出去口

### 2. 时间维度
- **动态切换**：通过时间轴查看不同时间段的流向变化
- **趋势观察**：观察进出流向随时间的变化趋势
- **峰值分析**：识别进场和出场的高峰时段

### 3. 固定刻度
- **Y轴固定**：基于全局最大值设置固定Y轴范围
- **准确对比**：避免因刻度变化导致的视觉误导
- **数据一致性**：所有时间段使用相同的数据基准

## 🎯 应用场景

### 1. 流量管理
- **瓶颈识别**：识别进场或出场方向的流量瓶颈
- **资源配置**：根据进出流向配置管理资源
- **通道优化**：优化进出通道的设置和管理

### 2. 运营分析
- **平衡分析**：分析停车场进出流量的平衡性
- **容量规划**：根据进出流向规划停车容量
- **效率评估**：评估各出入口的运营效率

### 3. 决策支持
- **政策制定**：为进出管理政策提供数据支持
- **设施规划**：为出入口设施规划提供依据
- **应急预案**：为高流量时段制定应急方案

## 📊 输出效果

### 文件信息
- **文件名**：`出入口进出量_方向.html`
- **文件类型**：HTML格式，支持浏览器交互
- **生成位置**：与其他图表文件相同目录

### 图表内容
- **主标题**：`出入口进出量分布 - [时间段]`
- **副标题**：显示当前时间段的进出流量说明
- **X轴**：各出入口名称
- **Y轴**：车辆数量（固定刻度）
- **图例**：进场/出场标识

## 🎮 使用方法

### 1. 自动生成
```python
chart_generator = ParkingChartGenerator('your_data.xlsx', 'output_dir')
chart_generator.generate_all_charts()  # 会同时生成总量和方向两个Timeline图表
```

### 2. 单独生成
```python
direction_file = chart_generator.generate_gate_traffic_direction_timeline()
```

### 3. 对比分析
- **总量图表**：查看整体流量趋势
- **方向图表**：分析具体流向特点
- **结合使用**：获得全面的流量分析

## 💡 分析技巧

### 1. 流向平衡分析
- 观察每个出入口的进场和出场柱状图高度
- 识别进出流量不平衡的出入口
- 分析造成不平衡的时间段和原因

### 2. 瓶颈识别
- 查找进场或出场流量异常高的出入口
- 结合时间维度分析瓶颈出现的时段
- 评估瓶颈对整体流量的影响

### 3. 趋势分析
- 使用自动播放功能观察流向变化趋势
- 识别流向变化的规律和模式
- 预测未来的流量流向趋势

## 🔄 版本更新

### v2.9 新功能
- ✅ 出入口方向Timeline图表
- ✅ 进场和出场数据分别显示
- ✅ 专业演讲风格双色配置
- ✅ 固定Y轴刻度
- ✅ 与总量图表形成互补

### 兼容性
- 完全兼容现有的总量Timeline图表
- 使用相同的数据源和配置
- 保持一致的交互体验和视觉风格

---

*功能开发完成时间: 2025-06-20*  
*适用版本: parking_chart_generator.py v2.9+*
