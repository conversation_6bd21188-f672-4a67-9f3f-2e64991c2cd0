#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试不同mode下延停时长概率密度功能的兼容性
"""

import pandas as pd
import numpy as np
import os
from parking_report_generator import ReportGenerator

def create_mode1_simple_data():
    """创建mode1_simple测试数据"""
    np.random.seed(42)
    
    # mode1_simple数据结构：每条记录是独立的进出事件
    n_records = 100
    base_time = pd.Timestamp('2024-01-01 08:00:00')
    
    data = pd.DataFrame({
        'timestamp': [base_time + pd.Timedelta(minutes=i*5) for i in range(n_records)],
        'direction': np.random.choice(['入场', '出场'], n_records),
        'gate': np.random.choice(['A口', 'B口', 'C口'], n_records),
        'vtype': np.random.choice(['小型车', '中型车', '大型车'], n_records)
    })
    
    return data

def create_mode1_data():
    """创建mode1测试数据（配对后的数据）"""
    np.random.seed(42)
    
    # mode1数据结构：配对后的进出记录，有真实的停车时长
    n_records = 50
    base_time = pd.Timestamp('2024-01-01 08:00:00')
    
    # 生成真实的停车时长
    durations = np.random.exponential(2, n_records)  # 平均2小时
    durations = np.clip(durations, 0.1, 24.0)  # 限制在6分钟到24小时
    
    entry_times = [base_time + pd.Timedelta(hours=i*0.5) for i in range(n_records)]
    exit_times = [entry_times[i] + pd.Timedelta(hours=durations[i]) for i in range(n_records)]
    
    data = pd.DataFrame({
        'entry_time': entry_times,
        'exit_time': exit_times,
        'duration': durations,  # 真实停车时长（小时）
        'entry_gate': np.random.choice(['A口', 'B口', 'C口'], n_records),
        'exit_gate': np.random.choice(['A口', 'B口', 'C口'], n_records),
        'vtype': np.random.choice(['小型车', '中型车', '大型车'], n_records),
        'vid': [f'车{i+1:04d}' for i in range(n_records)]
    })
    
    return data

def create_mode2_data():
    """创建mode2测试数据"""
    np.random.seed(42)
    
    # mode2数据结构：直接包含进出时间和停车时长
    n_records = 60
    base_time = pd.Timestamp('2024-01-01 08:00:00')
    
    # 生成真实的停车时长
    durations = np.random.lognormal(1, 1, n_records)  # 对数正态分布
    durations = np.clip(durations, 0.1, 48.0)  # 限制在6分钟到48小时
    
    entry_times = [base_time + pd.Timedelta(hours=i*0.3) for i in range(n_records)]
    exit_times = [entry_times[i] + pd.Timedelta(hours=durations[i]) for i in range(n_records)]
    
    data = pd.DataFrame({
        'entry_time': entry_times,
        'exit_time': exit_times,
        'duration': durations,  # 真实停车时长（小时）
        'entry_gate': np.random.choice(['A口', 'B口', 'C口'], n_records),
        'exit_gate': np.random.choice(['A口', 'B口', 'C口'], n_records),
        'vtype': np.random.choice(['小型车', '中型车', '大型车'], n_records),
        'vehicle_id': [f'车{i+1:04d}' for i in range(n_records)]
    })
    
    return data

def test_mode_compatibility():
    """测试不同mode的兼容性"""
    print("=== 测试不同mode下延停时长概率密度功能的兼容性 ===\n")
    
    modes_data = {
        'mode1_simple': create_mode1_simple_data(),
        'mode1': create_mode1_data(),
        'mode2': create_mode2_data()
    }
    
    for mode, test_data in modes_data.items():
        print(f"{'='*60}")
        print(f"测试 {mode} 模式")
        print(f"{'='*60}")
        
        # 配置参数
        params = {
            'mode': mode,
            '聚焦日期': '2024-01-01'
        }
        
        print(f"数据结构:")
        print(f"  - 记录数: {len(test_data)}")
        print(f"  - 列名: {list(test_data.columns)}")
        if 'duration' in test_data.columns:
            print(f"  - duration列存在: 是")
            print(f"  - duration范围: {test_data['duration'].min():.3f} - {test_data['duration'].max():.3f} 小时")
            print(f"  - duration唯一值数量: {test_data['duration'].nunique()}")
        else:
            print(f"  - duration列存在: 否")
        print()
        
        # 创建报告生成器
        try:
            generator = ReportGenerator(test_data, params)
            generator.processed_data = test_data
            
            # 测试延停时长概率密度计算
            print("测试延停时长概率密度计算:")
            density_stats = generator._calculate_duration_probability_density()
            
            if density_stats is not None and not density_stats.empty:
                print(f"  ✅ 计算成功，生成 {len(density_stats)} 个时段")
                print(f"  ✅ 总记录数: {density_stats['频数'].sum()}")
                
                # 显示前3个时段
                print("  ✅ 前3个时段:")
                for i in range(min(3, len(density_stats))):
                    row = density_stats.iloc[i]
                    print(f"     {row['时长区间']:20} | 频数: {row['频数']:3d} | 百分比: {row['百分比(%)']:6.2f}%")
            else:
                print(f"  ⏭️  计算跳过（符合预期）")
            
            print()
            
            # 测试完整报告生成
            print("测试完整报告生成:")
            try:
                output_path = os.path.join(os.getcwd(), f"测试报告_{mode}.xlsx")
                
                # 删除已存在的文件
                if os.path.exists(output_path):
                    os.remove(output_path)
                
                result_path = generator.export_to_excel(output_path)
                
                if result_path and os.path.exists(result_path):
                    # 检查工作表
                    with pd.ExcelFile(result_path) as xls:
                        sheet_names = xls.sheet_names
                        
                    if '延停时长概率密度' in sheet_names:
                        print(f"  ✅ 包含'延停时长概率密度'工作表")
                        
                        # 验证数据
                        df = pd.read_excel(result_path, sheet_name='延停时长概率密度')
                        if not df.empty:
                            print(f"  ✅ 工作表有数据: {len(df)} 行")
                        else:
                            print(f"  ⚠️  工作表为空")
                    else:
                        print(f"  ⏭️  未包含'延停时长概率密度'工作表（符合预期）")
                    
                    print(f"  ✅ 报告生成成功: {len(sheet_names)} 个工作表")
                else:
                    print(f"  ❌ 报告生成失败")
                    
            except Exception as e:
                print(f"  ❌ 报告生成异常: {str(e)}")
            
        except Exception as e:
            print(f"❌ 测试异常: {str(e)}")
            import traceback
            traceback.print_exc()
        
        print("\n")

def test_virtual_duration_detection():
    """测试虚拟停车时长检测"""
    print("=== 测试虚拟停车时长检测 ===\n")
    
    # 创建包含虚拟duration的数据
    virtual_data = pd.DataFrame({
        'entry_time': pd.to_datetime(['2024-01-01 08:00:00'] * 10),
        'exit_time': pd.to_datetime(['2024-01-01 08:01:00'] * 10),  # 1分钟后
        'duration': [1/60] * 10,  # 1分钟 = 1/60小时，全部相同
        'vtype': ['小型车'] * 10
    })
    
    print("虚拟duration数据:")
    print(f"  - 记录数: {len(virtual_data)}")
    print(f"  - duration唯一值: {virtual_data['duration'].unique()}")
    print(f"  - duration唯一值数量: {virtual_data['duration'].nunique()}")
    
    params = {'mode': 'mode1'}  # 非mode1_simple模式
    generator = ReportGenerator(virtual_data, params)
    generator.processed_data = virtual_data
    
    print("\n测试虚拟duration检测:")
    density_stats = generator._calculate_duration_probability_density()
    
    if density_stats is not None and not density_stats.empty:
        print("  ❌ 未正确检测到虚拟duration")
    else:
        print("  ✅ 正确检测到虚拟duration，跳过计算")

if __name__ == "__main__":
    test_mode_compatibility()
    test_virtual_duration_detection()
    
    print("="*60)
    print("测试总结:")
    print("- mode1_simple: 应该跳过延停时长概率密度分析")
    print("- mode1: 应该支持延停时长概率密度分析（真实停车时长）")
    print("- mode2: 应该支持延停时长概率密度分析（真实停车时长）")
    print("- 虚拟duration检测: 应该能检测并跳过虚拟停车时长数据")
