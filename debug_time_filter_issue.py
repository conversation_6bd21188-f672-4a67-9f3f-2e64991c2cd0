import pandas as pd
from parking_data_base import ParkingDataBase
from parking_analyzer import ParkingAnalyzer
import logging

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(levelname)s:%(name)s:%(message)s')
logger = logging.getLogger(__name__)

# 读取实际数据
csv_file = r'C:\Users\<USER>\Desktop\停车分析\数据\正泰\义乌正泰_北门_合并_20250623_230242.csv'

try:
    data = pd.read_csv(csv_file, encoding='utf-8')
except:
    try:
        data = pd.read_csv(csv_file, encoding='gbk')
    except:
        data = pd.read_csv(csv_file, encoding='gb2312')

print(f"原始数据: {len(data)} 条记录")

# 设置参数
params = {
    'mode': 'mode1',
    'input_file': csv_file,
    'output': r'C:\Users\<USER>\Desktop\停车分析',
    'date': '',
    'month': '',
    'time_interval': 60,
    'time_slip': 30,
}

MODE_CONFIG = {
    'mode1': {
        '车辆唯一标识字段': '车牌',
        '车辆类型字段': '车辆类型',
        '时间记录字段': '时间',
        '进出类型字段': '方向',
        '进出标识值': ('进', '出'),
        '道闸编号字段': '出入口'
    }
}

# 合并配置
params.update(MODE_CONFIG)

# 创建处理器
processor = ParkingDataBase(data, params, logger)

# 处理数据
try:
    processor.process()
    print(f"\n数据处理完成: {len(processor.processed_data)} 条记录")
    
    if len(processor.processed_data) > 0:
        print("处理后的数据时间范围:")
        print(f"最早时间: {processor.processed_data['entry_time'].min()}")
        print(f"最晚时间: {processor.processed_data['exit_time'].max()}")
        
        # 创建分析器
        print("\n创建分析器...")
        analyzer = ParkingAnalyzer(data, params, logger)
        
        # 手动设置处理后的数据
        analyzer.processed_data = processor.processed_data.copy()
        
        print(f"分析器初始数据: {len(analyzer.processed_data)} 条记录")
        
        # 检查时间过滤
        print("\n检查时间过滤...")
        print(f"date参数: '{params.get('date', '')}'")
        print(f"month参数: '{params.get('month', '')}'")
        
        # 手动调用时间过滤
        try:
            analyzer._filter_by_time()
            print(f"时间过滤后: {len(analyzer.processed_data)} 条记录")
            
            if len(analyzer.processed_data) > 0:
                print("时间过滤后的数据时间范围:")
                print(f"最早时间: {analyzer.processed_data['entry_time'].min()}")
                print(f"最晚时间: {analyzer.processed_data['exit_time'].max()}")
                
                print("\n前5条记录:")
                print(analyzer.processed_data[['vid', 'entry_time', 'exit_time', 'duration']].head())
                
                # 检查数据验证
                print("\n检查数据验证...")
                try:
                    analyzer._validate_processed_data()
                    print("✅ 数据验证通过")
                except Exception as e:
                    print(f"❌ 数据验证失败: {e}")
                    
                    # 检查具体的验证条件
                    print("\n详细检查:")
                    print(f"数据长度: {len(analyzer.processed_data)}")
                    print(f"必要列是否存在:")
                    required_cols = ['vid', 'entry_time', 'exit_time', 'duration']
                    for col in required_cols:
                        exists = col in analyzer.processed_data.columns
                        print(f"  {col}: {exists}")
                        if exists:
                            null_count = analyzer.processed_data[col].isna().sum()
                            print(f"    空值数量: {null_count}")
            else:
                print("❌ 时间过滤后数据为空")
                
        except Exception as e:
            print(f"时间过滤失败: {e}")
            import traceback
            traceback.print_exc()
        
    else:
        print("❌ 处理后数据为空")
        
except Exception as e:
    print(f"处理失败: {e}")
    import traceback
    traceback.print_exc()
