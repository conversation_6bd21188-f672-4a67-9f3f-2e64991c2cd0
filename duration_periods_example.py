#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
停车时长分段示例
演示如何使用 create_duration_periods 方法创建自定义时长分段
"""

import pandas as pd
from parking_time_filter import TimeFilter

def example_duration_periods():
    """演示时长分段功能"""
    
    # 创建一个简单的数据集用于演示
    sample_data = pd.DataFrame({
        'entry_time': pd.to_datetime(['2024-01-01 08:00:00', '2024-01-01 09:00:00']),
        'exit_time': pd.to_datetime(['2024-01-01 10:00:00', '2024-01-01 11:00:00']),
        'vehicle_type': ['小型车', '大型车']
    })
    
    # 创建时间过滤器实例
    params = {}
    time_filter = TimeFilter(sample_data, params)
    
    print("=== 停车时长分段示例 ===\n")
    
    # 示例1：使用默认配置
    print("1. 默认配置:")
    print("   控制点: [2, 8, 24, 72] 小时")
    print("   时段长度: [0.5, 1, 16, 48] 小时")
    default_periods = time_filter.create_duration_periods()
    for i, period in enumerate(default_periods, 1):
        print(f"   时段{i}: {period}")

    print("\n" + "="*50 + "\n")

    # 示例2：自定义配置 - 更细粒度的短时停车分析
    print("2. 短时停车分析配置:")
    control_points_short = [1, 3, 6, 12]     # 1小时, 3小时, 6小时, 12小时
    period_lengths_short = [0.25, 0.5, 1, 2] # 15分钟, 30分钟, 1小时, 2小时
    print(f"   控制点: {control_points_short} 小时")
    print(f"   时段长度: {period_lengths_short} 小时")
    short_periods = time_filter.create_duration_periods(control_points_short, period_lengths_short)
    for i, period in enumerate(short_periods, 1):
        print(f"   时段{i}: {period}")

    print("\n" + "="*50 + "\n")

    # 示例3：长期停车分析配置
    print("3. 长期停车分析配置:")
    control_points_long = [24, 168, 720]    # 1天, 7天, 30天
    period_lengths_long = [4, 24, 168]      # 4小时, 1天, 7天
    print(f"   控制点: {control_points_long} 小时")
    print(f"   时段长度: {period_lengths_long} 小时")
    long_periods = time_filter.create_duration_periods(control_points_long, period_lengths_long)
    for i, period in enumerate(long_periods, 1):
        print(f"   时段{i}: {period}")

    print("\n" + "="*50 + "\n")

    # 示例4：商业区停车分析配置
    print("4. 商业区停车分析配置:")
    control_points_business = [0.5, 2, 8, 24]  # 30分钟, 2小时, 8小时, 1天
    period_lengths_business = [1/6, 0.5, 2, 8] # 10分钟, 30分钟, 2小时, 8小时
    print(f"   控制点: {control_points_business} 小时")
    print(f"   时段长度: {period_lengths_business} 小时")
    business_periods = time_filter.create_duration_periods(control_points_business, period_lengths_business)
    for i, period in enumerate(business_periods, 1):
        print(f"   时段{i}: {period}")

def demonstrate_period_usage():
    """演示如何在数据分析中使用时长分段"""
    
    print("\n=== 时长分段在数据分析中的应用 ===\n")
    
    # 创建模拟停车数据
    import numpy as np
    np.random.seed(42)
    
    # 生成不同时长的停车记录
    durations = []
    # 短时停车 (0-2小时)
    durations.extend(np.random.exponential(30, 100))  # 平均30分钟
    # 中时停车 (2-8小时)  
    durations.extend(np.random.normal(300, 60, 50))   # 平均5小时
    # 长时停车 (8小时以上)
    durations.extend(np.random.exponential(720, 30))  # 平均12小时起
    
    # 确保时长为正数
    durations = [max(5, d) for d in durations]
    
    # 创建数据框
    parking_data = pd.DataFrame({
        'duration_minutes': durations,
        'vehicle_type': np.random.choice(['小型车', '中型车', '大型车'], len(durations))
    })
    
    print(f"生成了 {len(parking_data)} 条停车记录")
    print(f"时长范围: {parking_data['duration_minutes'].min():.1f} - {parking_data['duration_minutes'].max():.1f} 分钟")
    
    # 创建时间过滤器并获取时长分段
    sample_data = pd.DataFrame({
        'entry_time': pd.to_datetime(['2024-01-01 08:00:00']),
        'exit_time': pd.to_datetime(['2024-01-01 10:00:00'])
    })
    time_filter = TimeFilter(sample_data, {})
    
    # 使用默认分段
    periods = time_filter.create_duration_periods()
    
    # 将停车时长分配到各个时段
    def assign_period(duration):
        """将停车时长分配到对应的时段"""
        if duration <= 120:  # 0-2小时
            period_index = int(duration // 30)  # 每30分钟一段
            return min(period_index, 3)  # 最多4段 (0-3)
        elif duration <= 480:  # 2-8小时
            return 4 + int((duration - 120) // 60)  # 从第5段开始，每60分钟一段
        elif duration <= 1440:  # 8-24小时
            return 10  # 第11段
        elif duration <= 4320:  # 1-3天
            return 11  # 第12段
        else:  # >3天
            return 12  # 第13段
    
    parking_data['period_index'] = parking_data['duration_minutes'].apply(assign_period)
    parking_data['period_label'] = parking_data['period_index'].apply(lambda x: periods[min(x, len(periods)-1)])
    
    # 统计各时段的停车数量
    period_stats = parking_data.groupby('period_label').agg({
        'duration_minutes': ['count', 'mean'],
        'vehicle_type': lambda x: x.value_counts().to_dict()
    }).round(1)
    
    print("\n各时段停车统计:")
    print("-" * 60)
    for period in periods:
        if period in period_stats.index:
            count = period_stats.loc[period, ('duration_minutes', 'count')]
            avg_duration = period_stats.loc[period, ('duration_minutes', 'mean')]
            print(f"{period:25} | 数量: {count:3d} | 平均时长: {avg_duration:6.1f}分钟")
        else:
            print(f"{period:25} | 数量:   0 | 平均时长:    0.0分钟")

if __name__ == "__main__":
    # 运行示例
    example_duration_periods()
    demonstrate_period_usage()
