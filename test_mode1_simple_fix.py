#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试mode1_simple修复后的行为
"""

import pandas as pd
import numpy as np
import os
from parking_report_generator import ReportGenerator

def test_mode1_simple_fixed():
    """测试修复后的mode1_simple行为"""
    print("=== 测试修复后的mode1_simple行为 ===\n")
    
    # 创建mode1_simple测试数据
    np.random.seed(42)
    n_records = 50
    base_time = pd.Timestamp('2024-01-01 08:00:00')
    
    data = pd.DataFrame({
        'timestamp': [base_time + pd.Timedelta(minutes=i*5) for i in range(n_records)],
        'direction': np.random.choice(['入场', '出场'], n_records),
        'gate': np.random.choice(['A口', 'B口', 'C口'], n_records),
        'vtype': np.random.choice(['小型车', '中型车', '大型车'], n_records)
    })
    
    params = {
        'mode': 'mode1_simple',
        '聚焦日期': '2024-01-01'
    }
    
    print(f"测试数据:")
    print(f"  - 记录数: {len(data)}")
    print(f"  - 列名: {list(data.columns)}")
    print(f"  - 模式: {params['mode']}")
    print()
    
    # 创建报告生成器
    generator = ReportGenerator(data, params)
    generator.processed_data = data
    
    # 测试延停时长概率密度计算
    print("1. 测试延停时长概率密度计算:")
    density_stats = generator._calculate_duration_probability_density()
    
    if density_stats is not None and not density_stats.empty:
        print(f"  ❌ 计算未跳过，返回了 {len(density_stats)} 行数据")
    else:
        print(f"  ✅ 计算正确跳过（符合预期）")
    
    print()
    
    # 测试完整报告生成
    print("2. 测试完整报告生成:")
    try:
        output_path = os.path.join(os.getcwd(), "测试报告_mode1_simple_修复.xlsx")
        
        # 删除已存在的文件
        if os.path.exists(output_path):
            os.remove(output_path)
        
        result_path = generator.export_to_excel(output_path)
        
        if result_path and os.path.exists(result_path):
            # 检查工作表
            with pd.ExcelFile(result_path) as xls:
                sheet_names = xls.sheet_names
            
            print(f"  ✅ 报告生成成功，包含 {len(sheet_names)} 个工作表:")
            for i, sheet in enumerate(sheet_names, 1):
                print(f"     {i:2d}. {sheet}")
            
            if '延停时长概率密度' in sheet_names:
                print(f"\n  ❌ 意外包含了'延停时长概率密度'工作表")
                
                # 检查工作表内容
                df = pd.read_excel(result_path, sheet_name='延停时长概率密度')
                if df.empty:
                    print(f"     但工作表为空，这是可以接受的")
                else:
                    print(f"     工作表有数据: {len(df)} 行")
            else:
                print(f"\n  ✅ 正确跳过了'延停时长概率密度'工作表")
            
            # 检查其他应该跳过的工作表
            should_skip = ['停车时长_分析日', '停车时长_入场_分析日', '车辆类型_分析日', '道闸组合_分析日']
            skipped_sheets = [sheet for sheet in should_skip if sheet not in sheet_names]
            included_sheets = [sheet for sheet in should_skip if sheet in sheet_names]
            
            if skipped_sheets:
                print(f"  ✅ 正确跳过的工作表: {skipped_sheets}")
            if included_sheets:
                print(f"  ⚠️  意外包含的工作表: {included_sheets}")
                
        else:
            print(f"  ❌ 报告生成失败")
            
    except Exception as e:
        print(f"  ❌ 报告生成异常: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_mode1_simple_fixed()
    
    print("\n" + "="*60)
    print("期望结果:")
    print("- 延停时长概率密度计算应该跳过")
    print("- 完整报告不应包含'延停时长概率密度'工作表")
    print("- 其他需要车辆追踪的工作表也应该跳过")
