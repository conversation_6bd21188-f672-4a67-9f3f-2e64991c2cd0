#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
生成真正的停车时长概率密度图
"""

import os
import pandas as pd
import numpy as np
from parking_chart_generator import ParkingChartGenerator

def analyze_duration_data():
    """分析duration数据"""
    print("🔍 分析duration数据\n")
    
    # 查找包含数据_总量的Excel文件
    excel_files = [f for f in os.listdir('.') if f.endswith('.xlsx') and not f.startswith('~')]
    
    best_file = None
    best_data_count = 0
    
    for excel_file in excel_files:
        try:
            with pd.ExcelFile(excel_file) as xls:
                if '数据_总量' in xls.sheet_names:
                    df = pd.read_excel(excel_file, sheet_name='数据_总量')
                    if 'duration' in df.columns:
                        valid_duration = df['duration'].dropna()
                        valid_duration = valid_duration[valid_duration >= 0]
                        
                        if len(valid_duration) > best_data_count:
                            best_data_count = len(valid_duration)
                            best_file = excel_file
                            
        except Exception as e:
            print(f"检查文件 {excel_file} 时出错: {str(e)}")
            continue
    
    if not best_file:
        print("❌ 未找到包含有效duration数据的Excel文件")
        return None, None
    
    print(f"📊 最佳数据文件: {best_file}")
    
    # 读取并分析数据
    df = pd.read_excel(best_file, sheet_name='数据_总量')
    duration_data = df['duration'].dropna()
    duration_data = duration_data[duration_data >= 0]
    
    print(f"📋 数据统计:")
    print(f"   - 总记录数: {len(df)}")
    print(f"   - 有效duration数: {len(duration_data)}")
    print(f"   - 时长范围: {duration_data.min():.3f} - {duration_data.max():.3f} 小时")
    print(f"   - 平均时长: {duration_data.mean():.3f} 小时")
    print(f"   - 中位数时长: {duration_data.median():.3f} 小时")
    print(f"   - 标准差: {duration_data.std():.3f} 小时")
    
    # 检查车辆类型
    if 'vtype' in df.columns:
        vehicle_types = df['vtype'].dropna().unique()
        print(f"🚗 车辆类型: {list(vehicle_types)}")
        
        for vtype in vehicle_types:
            vtype_durations = df[df['vtype'] == vtype]['duration'].dropna()
            vtype_durations = vtype_durations[vtype_durations >= 0]
            print(f"   - {vtype}: {len(vtype_durations)} 个数据点")
    
    # 显示数据分布
    print(f"\n📊 数据分布:")
    percentiles = [10, 25, 50, 75, 90, 95, 99]
    for p in percentiles:
        value = np.percentile(duration_data, p)
        print(f"   - {p}%分位数: {value:.3f} 小时")
    
    return best_file, duration_data

def generate_probability_density_chart():
    """生成概率密度图"""
    print("\n🎯 生成停车时长概率密度图\n")
    
    # 分析数据
    excel_file, duration_data = analyze_duration_data()
    
    if not excel_file:
        return None
    
    try:
        # 创建图表生成器
        chart_generator = ParkingChartGenerator(excel_file)
        
        print(f"📁 输出目录: {chart_generator.output_dir}")
        
        # 生成概率密度图
        print(f"📈 开始生成概率密度图...")
        density_file = chart_generator.generate_duration_probability_density_from_raw_data()
        
        if density_file and os.path.exists(density_file):
            file_size = os.path.getsize(density_file)
            print(f"✅ 概率密度图生成成功!")
            print(f"📄 文件: {os.path.basename(density_file)}")
            print(f"📊 大小: {file_size:,} 字节")
            print(f"📁 位置: {os.path.dirname(density_file)}")
            
            return density_file
        else:
            print(f"❌ 概率密度图生成失败")
            return None
            
    except Exception as e:
        print(f"❌ 生成概率密度图时出错: {str(e)}")
        import traceback
        traceback.print_exc()
        return None

def explain_probability_density():
    """解释概率密度图"""
    print(f"\n📚 概率密度图说明\n")
    
    print(f"🎯 什么是概率密度图:")
    print(f"   概率密度图显示数据在不同值处的密度分布")
    print(f"   - X轴: 停车时长（小时）")
    print(f"   - Y轴: 概率密度值")
    print(f"   - 密度值越高，表示该时长出现的可能性越大")
    print(f"   - 整个曲线下的面积等于1（概率总和为100%）")
    
    print(f"\n📊 如何理解密度值:")
    print(f"   - 密度值本身不是概率，而是概率除以区间宽度")
    print(f"   - 某个区间的概率 = 密度值 × 区间宽度")
    print(f"   - 密度值高的地方表示数据集中的区域")
    print(f"   - 可以用来识别数据的分布模式和异常值")
    
    print(f"\n🔍 图表特点:")
    print(f"   - 基于原始duration数据计算")
    print(f"   - 使用直方图方法估计密度")
    print(f"   - 支持按车辆类型分组显示")
    print(f"   - 提供缩放和交互功能")

def verify_chart_location():
    """验证图表位置"""
    print(f"\n✅ 验证图表位置\n")
    
    target_dir = r"C:\Users\<USER>\Desktop\停车分析"
    density_file = os.path.join(target_dir, "停车时长概率密度图.html")
    
    if os.path.exists(density_file):
        file_size = os.path.getsize(density_file)
        print(f"✅ 概率密度图文件存在")
        print(f"📄 文件: 停车时长概率密度图.html")
        print(f"📊 大小: {file_size:,} 字节")
        print(f"📁 位置: {target_dir}")
        
        # 列出目录中所有HTML文件
        html_files = [f for f in os.listdir(target_dir) if f.endswith('.html')]
        print(f"\n📋 目录中的所有HTML文件 ({len(html_files)}个):")
        for i, file in enumerate(sorted(html_files), 1):
            file_path = os.path.join(target_dir, file)
            file_size = os.path.getsize(file_path)
            print(f"   {i:2d}. {file} ({file_size:,} 字节)")
        
        return True
    else:
        print(f"❌ 概率密度图文件不存在: {density_file}")
        return False

def main():
    """主函数"""
    print("🎯 生成停车时长概率密度图\n")
    
    # 1. 解释概率密度图
    explain_probability_density()
    
    # 2. 生成概率密度图
    density_file = generate_probability_density_chart()
    
    # 3. 验证图表位置
    chart_exists = verify_chart_location()
    
    # 4. 总结
    print(f"\n{'='*60}")
    print(f"🎉 操作完成!")
    
    if density_file and chart_exists:
        print(f"✅ 停车时长概率密度图生成成功")
        print(f"📊 这是一个真正的概率密度图，基于原始duration数据")
        print(f"📁 文件位置: C:\\Users\\<USER>\\Desktop\\停车分析\\停车时长概率密度图.html")
        print(f"💡 可以在浏览器中打开查看效果")
        print(f"\n🔍 图表说明:")
        print(f"   - Y轴显示概率密度值（不是频率）")
        print(f"   - 密度值高的区域表示停车时长集中的地方")
        print(f"   - 整个分布的面积等于1")
        print(f"   - 支持按车辆类型查看不同的密度分布")
    else:
        print(f"❌ 概率密度图生成失败")
    
    print(f"{'='*60}")

if __name__ == "__main__":
    main()
