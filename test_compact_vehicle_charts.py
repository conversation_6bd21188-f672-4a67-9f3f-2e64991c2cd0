#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试紧凑布局的车辆类型图表生成功能
"""

import os

def test_compact_vehicle_charts():
    """测试紧凑布局的车辆类型图表生成"""
    print("🚗 测试紧凑布局的车辆类型图表生成")
    print("=" * 50)
    
    # Excel文件路径
    excel_file = r'C:\Users\<USER>\Desktop\停车分析\义乌火车站道闸_私家车_合并_20250617_083509_analysis_20250618_211554.xlsx'
    
    if not os.path.exists(excel_file):
        print(f"❌ Excel文件不存在: {excel_file}")
        return False
    
    try:
        # 导入图表生成器
        from parking_chart_generator import ParkingChartGenerator
        
        # 创建图表生成器
        print("📊 创建图表生成器...")
        chart_generator = ParkingChartGenerator(excel_file, None)
        
        # 检查是否有进出量时间分布工作表
        if '进出量时间分布' not in chart_generator.excel_data:
            print("❌ 未找到'进出量时间分布'工作表")
            available_sheets = list(chart_generator.excel_data.keys())
            print(f"可用工作表: {available_sheets}")
            return False
        
        # 获取数据并分析结构
        data = chart_generator.excel_data['进出量时间分布']
        columns = list(data.columns)
        total_cols = len(columns)
        
        print(f"📋 数据结构分析:")
        print(f"   总列数: {total_cols}")
        print(f"   前4列: {columns[:4]}")
        if total_cols > 4:
            vehicle_cols = columns[4:]
            if total_cols >= 7:
                vehicle_cols = columns[4:-3]  # 去除后3列
            print(f"   车辆类型相关列: {vehicle_cols}")
            estimated_vehicle_types = len(vehicle_cols) // 2
            print(f"   预估车辆类型数量: {estimated_vehicle_types}")
        
        # 尝试生成紧凑布局的车辆类型图表
        print(f"\n🚗 尝试生成紧凑布局的车辆类型图表...")
        result = chart_generator.generate_vehicle_type_traffic_charts()
        
        if result:
            print(f"✅ 成功生成: {os.path.basename(result)}")
            
            # 检查文件是否存在
            if os.path.exists(result):
                file_size = os.path.getsize(result) / 1024
                print(f"📄 文件大小: {file_size:.1f}KB")
                print(f"📁 文件路径: {result}")
                
                # 读取HTML内容检查布局特征
                with open(result, 'r', encoding='utf-8') as f:
                    html_content = f.read()
                
                # 检查紧凑布局特征
                layout_checks = {
                    '使用Page布局': 'SimplePageLayout' in html_content or 'page' in html_content.lower(),
                    '多个子图': html_content.count('title') > 2,  # 多个标题表示多个子图
                    '紧凑高度': '150px' in html_content or '200px' in html_content,  # 检查是否有较小的高度设置
                    '字体优化': 'font_size:10' in html_content or 'font_size":10' in html_content,  # 检查小字体
                }
                
                print(f"\n📊 紧凑布局验证:")
                all_checks_passed = True
                for check_name, is_passed in layout_checks.items():
                    status = "✅" if is_passed else "❌"
                    print(f"   {status} {check_name}: {'通过' if is_passed else '未通过'}")
                    if not is_passed:
                        all_checks_passed = False
                
                # 估算总高度
                chart_count = html_content.count('Bar(')
                if chart_count > 0:
                    estimated_total_height = chart_count * 200 + 100  # 估算值
                    print(f"\n📏 布局信息:")
                    print(f"   检测到子图数量: {chart_count}")
                    print(f"   估算总高度: ~{estimated_total_height}px")
                    print(f"   目标总高度: 600px")
                    
                    height_optimized = estimated_total_height <= 700  # 允许一些误差
                    print(f"   高度优化: {'✅ 是' if height_optimized else '❌ 否'}")
                    all_checks_passed = all_checks_passed and height_optimized
                
                if all_checks_passed:
                    print("\n✅ 紧凑布局生成成功！")
                    print("   - 多个子图并列显示")
                    print("   - 每个子图高度缩窄")
                    print("   - 总高度控制在目标范围内")
                    print("   - 字体和元素尺寸优化")
                    return True
                else:
                    print("\n⚠️ 布局生成但可能不是预期的紧凑格式")
                    return False
            else:
                print(f"❌ 文件未生成: {result}")
                return False
        else:
            print("❌ 未能生成车辆类型图表")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def demo_compact_layout_features():
    """演示紧凑布局的特点"""
    print("\n📊 紧凑布局特点")
    print("=" * 50)
    
    print("🎯 布局优化:")
    print("   ✅ 多个子图并列显示（保持独立性）")
    print("   ✅ 每个子图高度自动缩窄")
    print("   ✅ 总高度控制在600px左右")
    print("   ✅ 使用SimplePageLayout减少间距")
    
    print("\n📏 尺寸计算:")
    print("   - 总高度: 600px")
    print("   - 标题空间: 60px")
    print("   - 子图间距: 20px × (子图数-1)")
    print("   - 子图高度: (600-60-间距) ÷ 子图数")
    print("   - 最小高度: 150px（保证可读性）")
    
    print("\n🎨 视觉优化:")
    print("   - 标题字体: 14px（原18px）")
    print("   - 轴标签字体: 10px（原12px）")
    print("   - 图例字体: 10px（原12px）")
    print("   - 图例图标: 15x10px（原默认尺寸）")
    print("   - 只在最后一个子图显示X轴标签")
    
    print("\n📋 保持功能:")
    print("   ✅ 每个车辆类型独立显示")
    print("   ✅ 专业演讲风格颜色")
    print("   ✅ 交互功能（tooltip等）")
    print("   ✅ 数据完整性")

def main():
    """主函数"""
    # 演示功能特点
    demo_compact_layout_features()
    
    # 测试功能
    success = test_compact_vehicle_charts()
    
    if success:
        print("\n🎉 测试成功！紧凑布局的车辆类型图表已生成！")
        print("📁 文件名: 进出量时间分布_车型.html")
        print("💡 现在所有子图以紧凑方式显示，总高度控制在600px左右")
        print("🔍 建议在浏览器中查看效果，验证布局是否符合预期")
    else:
        print("\n⚠️ 测试失败")
        print("💡 可能的原因:")
        print("   1. Excel文件中没有足够的车辆类型数据")
        print("   2. 数据格式不正确")
        print("   3. 代码执行过程中出现错误")
    
    print("\n" + "="*50)
    input("按回车键退出...")

if __name__ == "__main__":
    main()
