#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试Timeline图表固定Y轴刻度功能
验证时间轴滑动时纵坐标刻度保持不变
"""

import os

def test_fixed_yaxis():
    """测试Timeline图表固定Y轴刻度"""
    print("📊 测试Timeline图表固定Y轴刻度")
    print("=" * 50)
    
    # Excel文件路径
    excel_file = r'C:\Users\<USER>\Desktop\停车分析\义乌火车站道闸_私家车_合并_20250617_083509_analysis_20250618_211554.xlsx'
    
    if not os.path.exists(excel_file):
        print(f"❌ Excel文件不存在: {excel_file}")
        return False
    
    try:
        # 导入图表生成器
        from parking_chart_generator import ParkingChartGenerator
        
        # 创建图表生成器
        print("📊 创建图表生成器...")
        chart_generator = ParkingChartGenerator(excel_file, None)
        
        # 检查是否有进出量时间分布(按道闸)工作表
        target_sheet = '进出量时间分布(按道闸)'
        if target_sheet not in chart_generator.excel_data:
            print(f"❌ 未找到'{target_sheet}'工作表")
            available_sheets = list(chart_generator.excel_data.keys())
            print(f"可用工作表: {available_sheets}")
            return False
        
        # 分析数据范围
        data = chart_generator.excel_data[target_sheet]
        columns = list(data.columns)
        total_cols = len(columns)
        
        print(f"📋 数据分析:")
        print(f"   总列数: {total_cols}")
        
        # 计算出入口数量和数据范围
        if total_cols > 1:
            gate_cols_count = total_cols - 1
            gate_count = gate_cols_count // 3
            
            print(f"   出入口数量: {gate_count}")
            
            # 分析所有总量数据的范围
            all_total_values = []
            for i in range(gate_count):
                total_col_idx = 1 + i * 3 + 2  # 总量列索引
                if total_col_idx < total_cols:
                    total_data = data.iloc[:, total_col_idx].fillna(0).tolist()
                    all_total_values.extend(total_data)
            
            if all_total_values:
                min_value = min(all_total_values)
                max_value = max(all_total_values)
                expected_y_max = max_value * 1.2
                
                print(f"   数据范围: {min_value:.0f} - {max_value:.0f}")
                print(f"   预期Y轴最大值: {expected_y_max:.0f}")
        
        # 尝试生成Timeline图表
        print(f"\n🎬 生成带固定Y轴的Timeline图表...")
        result = chart_generator.generate_gate_traffic_timeline()
        
        if result:
            print(f"✅ 成功生成: {os.path.basename(result)}")
            
            # 检查文件是否存在
            if os.path.exists(result):
                file_size = os.path.getsize(result) / 1024
                print(f"📄 文件大小: {file_size:.1f}KB")
                
                # 读取HTML内容检查Y轴配置
                with open(result, 'r', encoding='utf-8') as f:
                    html_content = f.read()
                
                # 检查固定Y轴相关配置
                yaxis_checks = {
                    'Y轴最小值设置': 'min_:' in html_content or 'min":' in html_content,
                    'Y轴最大值设置': 'max_:' in html_content or 'max":' in html_content,
                    'Timeline组件': 'Timeline' in html_content,
                    '多个时间段': html_content.count('add(') > 1,
                }
                
                print(f"\n📊 固定Y轴验证:")
                all_checks_passed = True
                for check_name, is_passed in yaxis_checks.items():
                    status = "✅" if is_passed else "❌"
                    print(f"   {status} {check_name}: {'通过' if is_passed else '未通过'}")
                    if not is_passed:
                        all_checks_passed = False
                
                # 检查Y轴范围设置
                if 'max_:' in html_content or 'max":' in html_content:
                    print(f"\n📏 Y轴配置:")
                    print(f"   ✅ Y轴最大值已固定")
                    print(f"   ✅ Y轴最小值设为0")
                    print(f"   ✅ 时间轴滑动时Y轴刻度保持不变")
                
                if all_checks_passed:
                    print("\n✅ 固定Y轴Timeline图表生成成功！")
                    print("   - Y轴刻度基于全局数据范围计算")
                    print("   - 时间轴滑动时纵坐标刻度保持固定")
                    print("   - 便于对比不同时间段的数据")
                    print("   - 避免因数据范围变化导致的视觉误导")
                    return True
                else:
                    print("\n⚠️ Y轴配置可能有问题")
                    return False
            else:
                print(f"❌ 文件未生成: {result}")
                return False
        else:
            print("❌ 未能生成Timeline图表")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def demo_fixed_yaxis_benefits():
    """演示固定Y轴的优势"""
    print("\n📊 固定Y轴刻度的优势")
    print("=" * 50)
    
    print("🔄 对比效果:")
    print("   修改前（动态Y轴）:")
    print("     ❌ 每个时间段的Y轴范围可能不同")
    print("     ❌ 数据对比容易产生视觉误导")
    print("     ❌ 小数值时间段的柱状图被放大显示")
    print("     ❌ 无法直观比较不同时间段的流量大小")
    
    print("\n   修改后（固定Y轴）:")
    print("     ✅ 所有时间段使用相同的Y轴范围")
    print("     ✅ 数据对比直观准确")
    print("     ✅ 柱状图高度真实反映数据大小")
    print("     ✅ 便于识别流量高峰和低谷时段")
    
    print("\n🎯 用户体验提升:")
    print("   ✅ 视觉一致性：所有时间段的图表具有相同的视觉基准")
    print("   ✅ 数据准确性：避免因Y轴变化导致的数据误读")
    print("   ✅ 对比便利性：可直接通过柱状图高度比较数据")
    print("   ✅ 专业性：符合数据可视化的最佳实践")
    
    print("\n📈 技术实现:")
    print("   - 全局扫描：分析所有时间段和出入口的数据范围")
    print("   - 统一计算：Y轴最大值 = 全局最大值 × 1.2")
    print("   - 固定应用：每个时间段的图表都使用相同的Y轴范围")
    print("   - 空间预留：20%的上方空间确保视觉效果")
    
    print("\n💡 应用价值:")
    print("   - 流量对比：准确比较不同时间段的流量大小")
    print("   - 趋势分析：清晰识别流量变化趋势")
    print("   - 决策支持：为管理决策提供准确的数据基础")
    print("   - 演示效果：提升专业演示的可信度")

def main():
    """主函数"""
    # 演示固定Y轴的优势
    demo_fixed_yaxis_benefits()
    
    # 测试功能
    success = test_fixed_yaxis()
    
    if success:
        print("\n🎉 固定Y轴Timeline图表测试成功！")
        print("📁 文件名: 进出量出入口分布.html")
        print("💡 现在时间轴滑动时Y轴刻度保持固定")
        print("🔍 建议在浏览器中验证效果:")
        print("   1. 滑动时间轴到不同时间段")
        print("   2. 观察Y轴刻度是否保持不变")
        print("   3. 通过柱状图高度直接比较数据大小")
    else:
        print("\n⚠️ 测试失败")
        print("💡 可能的原因:")
        print("   1. Excel文件中没有'进出量时间分布(按道闸)'工作表")
        print("   2. 数据格式不正确")
        print("   3. 代码执行过程中出现错误")
    
    print("\n" + "="*50)
    input("按回车键退出...")

if __name__ == "__main__":
    main()
