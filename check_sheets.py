import pandas as pd

excel_file = r'C:\Users\<USER>\Desktop\停车分析\义乌正泰_北门_合并_20250623_230242_analysis_20250625_000829.xlsx'

# 读取两个工作表
try:
    df1 = pd.read_excel(excel_file, sheet_name='道闸组合_分析日')
    print('道闸组合_分析日 时间段格式:')
    print(f'前10行时间段: {list(df1["时间段"].head(10))}')
    print(f'总行数: {len(df1)}')
    print(f'唯一时间段数: {len(df1["时间段"].unique())}')
    print()
except Exception as e:
    print(f'读取道闸组合_分析日失败: {e}')

try:
    df2 = pd.read_excel(excel_file, sheet_name='道闸组合_货车')
    print('道闸组合_货车 时间段格式:')
    print(f'前10行时间段: {list(df2["时间段"].head(10))}')
    print(f'总行数: {len(df2)}')
    print(f'唯一时间段数: {len(df2["时间段"].unique())}')
    print()
except Exception as e:
    print(f'读取道闸组合_货车失败: {e}')

# 比较时间段
try:
    periods1 = set(df1["时间段"].unique())
    periods2 = set(df2["时间段"].unique())

    print('时间段比较:')
    print(f'道闸组合_分析日独有的时间段: {periods1 - periods2}')
    print(f'道闸组合_货车独有的时间段: {periods2 - periods1}')
    print(f'共同时间段数量: {len(periods1 & periods2)}')

    if periods1 == periods2:
        print('✅ 两个工作表的时间段完全一致！')
    else:
        print('❌ 两个工作表的时间段不一致')

except Exception as e:
    print(f'比较时间段失败: {e}')
