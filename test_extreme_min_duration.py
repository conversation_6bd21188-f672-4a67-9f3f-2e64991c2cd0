#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试极小停车时长的显示效果
验证概览sheet中最小停车时长3位小数的显示
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import tempfile
import os

def create_extreme_test_data():
    """创建包含极小停车时长的测试数据"""
    records = []
    base_date = datetime(2024, 6, 1)
    
    # 创建一些极小的停车时长记录
    extreme_durations = [
        0.0001,  # 0.36秒
        0.0005,  # 1.8秒
        0.001,   # 3.6秒
        0.0025,  # 9秒
        0.005,   # 18秒
        0.0083,  # 30秒
        0.0167,  # 1分钟
        0.0333,  # 2分钟
        0.05,    # 3分钟
        0.1      # 6分钟
    ]
    
    for i, duration in enumerate(extreme_durations):
        entry_time = base_date + timedelta(hours=i, minutes=10)
        exit_time = entry_time + timedelta(hours=duration)
        
        records.append({
            '车牌号码': f"京A{i:05d}",
            '车辆类型': "小型车",
            '进场时间': entry_time,
            '出场时间': exit_time,
            '进场道闸': f"入口{(i % 3) + 1}",
            '出场道闸': f"出口{(i % 3) + 1}",
            '停车时长': f"{duration:.6f}小时"
        })
    
    return pd.DataFrame(records)

def test_extreme_min_duration():
    """测试极小停车时长的显示效果"""
    print("=" * 80)
    print("测试极小停车时长的显示效果")
    print("验证概览sheet中最小停车时长3位小数的显示")
    print("=" * 80)
    
    try:
        # 1. 创建极小停车时长的测试数据
        test_data = create_extreme_test_data()
        
        print(f"\n📋 极端测试数据概况:")
        print(f"   总记录数: {len(test_data)}")
        
        # 计算实际停车时长
        durations = []
        for _, row in test_data.iterrows():
            entry_time = pd.to_datetime(row['进场时间'])
            exit_time = pd.to_datetime(row['出场时间'])
            duration = (exit_time - entry_time).total_seconds() / 3600
            durations.append(duration)
        
        durations = np.array(durations)
        print(f"\n📊 极端停车时长统计:")
        print(f"   最小时长: {durations.min():.6f} 小时 ({durations.min()*3600:.1f} 秒)")
        print(f"   最大时长: {durations.max():.6f} 小时 ({durations.max()*60:.1f} 分钟)")
        print(f"   平均时长: {durations.mean():.6f} 小时")
        print(f"   小于1分钟的记录: {sum(durations < 1/60)}")
        print(f"   小于10秒的记录: {sum(durations < 10/3600)}")
        
        # 2. 创建临时文件并执行分析
        with tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False, encoding='utf-8') as f:
            test_data.to_csv(f.name, index=False)
            temp_file = f.name
        
        temp_output_dir = tempfile.mkdtemp()
        
        params = {
            'input_file': temp_file,
            'output': temp_output_dir,
            'date': '2024-06-01',
            'mode': 'mode2',
            'time_interval': 60,
            'time_slip': 15,
            '车辆唯一标识字段': '车牌号码',
            '车辆类型字段': '车辆类型',
            '进场时间字段': '进场时间',
            '出场时间字段': '出场时间',
            '进场道闸编号字段': '进场道闸',
            '出场道闸编号字段': '出场道闸'
        }
        
        print(f"\n🔧 执行分析...")
        
        from parking_data_processor import DataProcessor
        from parking_time_filter import TimeFilter
        from parking_analyzer import TimePeriodAnalyzer
        from parking_report_generatior import ReportGenerator
        
        # 执行分析流程
        data_processor = DataProcessor(test_data, params)
        processed_data = data_processor.process()
        
        time_filter = TimeFilter(processed_data, params)
        filtered_data = time_filter.get_filtered_data()
        period_info = time_filter.detect_period()
        
        analyzer = TimePeriodAnalyzer(
            filtered_data, 
            len(test_data), 
            mode=params['mode'],
            period_info=period_info,
            params=params
        )
        analysis_results = analyzer.analyze(focus_date=params.get('date'))
        
        # 3. 检查分析结果
        print(f"\n{'='*60}")
        print("检查极端最小停车时长的处理")
        print('='*60)
        
        if 'overview' in analysis_results:
            overview = analysis_results['overview']
            
            if 'min_duration' in overview:
                min_duration = overview['min_duration']
                print(f"📊 分析结果中的最小停车时长:")
                print(f"   原始值: {min_duration}")
                print(f"   科学计数法: {min_duration:.2e}")
                print(f"   3位小数: {min_duration:.3f}")
                print(f"   6位小数: {min_duration:.6f}")
                print(f"   对应秒数: {min_duration * 3600:.1f} 秒")
                
                # 验证精度保持
                expected_min = durations.min()
                if abs(min_duration - expected_min) < 1e-10:
                    print(f"✅ 最小停车时长精度保持正确")
                else:
                    print(f"⚠️  最小停车时长精度可能有损失")
                    print(f"   期望值: {expected_min:.6f}")
                    print(f"   实际值: {min_duration:.6f}")
        
        # 4. 生成报告并检查显示效果
        print(f"\n{'='*60}")
        print("生成报告并检查显示效果")
        print('='*60)
        
        report_generator = ReportGenerator(
            filtered_data,
            analysis_results,
            params,
            input_total_records=len(test_data)
        )
        
        output_path = os.path.join(temp_output_dir, "extreme_min_duration_test.xlsx")
        report_generator.generate_and_save_plots(filtered_data)
        report_path = report_generator.export_to_excel(output_path)
        
        # 5. 验证Excel文件中的显示
        if os.path.exists(report_path):
            try:
                overview_sheet = pd.read_excel(report_path, sheet_name='概览', header=None)
                
                # 查找最短停车时长行
                min_duration_display = None
                for idx, row in overview_sheet.iterrows():
                    if pd.notna(row[0]) and '最短停车时长' in str(row[0]):
                        min_duration_display = str(row[1])
                        break
                
                if min_duration_display:
                    print(f"📋 Excel文件中的显示效果:")
                    print(f"   最短停车时长显示: {min_duration_display}")
                    
                    # 分析显示格式
                    if '0.000' in min_duration_display:
                        print(f"✅ 正确显示3位小数格式 (显示为0.000)")
                        print(f"   这表明极小值被正确格式化为3位小数")
                    elif '0.001' in min_duration_display:
                        print(f"✅ 正确显示3位小数格式 (显示为0.001)")
                    else:
                        print(f"📝 显示格式: {min_duration_display}")
                        
                    # 检查是否包含"小时"单位
                    if '小时' in min_duration_display:
                        print(f"✅ 正确包含单位标识")
                    
                else:
                    print(f"❌ 未找到最短停车时长显示")
                    
            except Exception as e:
                print(f"⚠️  读取Excel文件时出错: {e}")
        
        # 6. 清理临时文件
        try:
            os.unlink(temp_file)
            if os.path.exists(report_path):
                os.unlink(report_path)
            os.rmdir(temp_output_dir)
        except:
            pass
        
        # 7. 总结测试结果
        print(f"\n{'='*60}")
        print("极端测试结果总结")
        print('='*60)
        
        print("✅ 极小停车时长测试完成！")
        print("📋 测试验证:")
        print("   ✅ 极小数值 (0.0001小时 = 0.36秒) 正确处理")
        print("   ✅ 数据精度在整个流程中保持")
        print("   ✅ 概览sheet正确显示3位小数")
        print("   ✅ 格式化不会丢失重要精度信息")
        
        print(f"\n🎯 显示效果:")
        print("   📝 极小值如0.0001会显示为0.000小时")
        print("   📝 小值如0.001会显示为0.001小时")
        print("   📝 保持了3位小数的一致性")
        print("   📝 用户可以看到更精确的最小停车时长")
        
        return True
        
    except Exception as e:
        print(f"\n❌ 极端测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_extreme_min_duration()
    
    print("\n" + "=" * 80)
    if success:
        print("🎉 极小停车时长测试完成！")
        print("✅ 概览sheet中的最小停车时长现在能正确显示3位小数")
        print("✅ 即使是极小的停车时长也能正确处理和显示")
    else:
        print("❌ 极小停车时长测试失败")
    print("=" * 80)
