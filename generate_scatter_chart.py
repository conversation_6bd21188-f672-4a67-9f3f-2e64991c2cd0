#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
生成延停时长概率密度散点图
"""

import os
import pandas as pd
from parking_chart_generator import ParkingChartGenerator

def find_excel_files():
    """查找当前目录下的Excel文件"""
    excel_files = []
    for file in os.listdir('.'):
        if file.endswith('.xlsx') and not file.startswith('~'):
            excel_files.append(file)
    return excel_files

def check_excel_for_density_data(excel_file):
    """检查Excel文件是否包含延停时长概率密度数据"""
    try:
        with pd.ExcelFile(excel_file) as xls:
            sheet_names = xls.sheet_names
            if '延停时长概率密度' in sheet_names:
                # 读取数据检查是否有车辆类型列
                df = pd.read_excel(excel_file, sheet_name='延停时长概率密度')
                vtype_cols = [col for col in df.columns if col.endswith('_频数') and col != '频数']
                if vtype_cols:
                    return True, len(df), len(vtype_cols)
        return False, 0, 0
    except Exception as e:
        print(f"检查文件 {excel_file} 时出错: {str(e)}")
        return False, 0, 0

def generate_scatter_chart():
    """生成延停时长概率密度散点图"""
    print("🎯 生成延停时长概率密度散点图\n")
    
    # 查找Excel文件
    excel_files = find_excel_files()
    
    if not excel_files:
        print("❌ 当前目录下没有找到Excel文件")
        return None
    
    print(f"📁 找到 {len(excel_files)} 个Excel文件:")
    for i, file in enumerate(excel_files, 1):
        print(f"   {i}. {file}")
    
    # 检查哪些文件包含延停时长概率密度数据
    valid_files = []
    for excel_file in excel_files:
        has_data, rows, vtypes = check_excel_for_density_data(excel_file)
        if has_data:
            valid_files.append((excel_file, rows, vtypes))
            print(f"✅ {excel_file}: 包含延停时长概率密度数据 ({rows}行, {vtypes}种车型)")
        else:
            print(f"⏭️  {excel_file}: 不包含延停时长概率密度数据")
    
    if not valid_files:
        print("\n❌ 没有找到包含延停时长概率密度数据的Excel文件")
        return None
    
    # 选择最合适的文件（数据最丰富的）
    best_file = max(valid_files, key=lambda x: x[1] * x[2])  # 行数 × 车型数
    excel_file, rows, vtypes = best_file
    
    print(f"\n📊 选择文件: {excel_file}")
    print(f"📋 数据规模: {rows} 行, {vtypes} 种车型")
    
    try:
        # 创建图表生成器
        print(f"\n📈 开始生成散点图...")
        chart_generator = ParkingChartGenerator(excel_file)
        
        # 生成散点图
        scatter_file = chart_generator.generate_duration_probability_density_scatter()
        
        if scatter_file and os.path.exists(scatter_file):
            # 重命名文件为指定名称
            target_file = "延停时长概率密度散点图.html"
            if os.path.exists(target_file):
                os.remove(target_file)
            
            os.rename(scatter_file, target_file)
            
            # 验证文件
            file_size = os.path.getsize(target_file)
            print(f"✅ 散点图生成成功!")
            print(f"📄 文件名: {target_file}")
            print(f"📊 文件大小: {file_size:,} 字节")
            
            # 显示数据统计
            density_data = chart_generator.excel_data['延停时长概率密度']
            vtype_freq_cols = [col for col in density_data.columns if col.endswith('_频数') and col != '频数']
            vehicle_types = [col.replace('_频数', '') for col in vtype_freq_cols]
            
            print(f"📋 车辆类型: {', '.join(vehicle_types)}")
            print(f"📋 时长区间: {len(density_data)} 个")
            
            # 计算数据点数量
            total_points = 0
            for vtype in vehicle_types:
                freq_col = f'{vtype}_频数'
                if freq_col in density_data.columns:
                    points = (density_data[freq_col] > 0).sum()
                    total_points += points
                    print(f"📊 {vtype}: {points} 个有效数据点")
            
            print(f"📊 总数据点: {total_points} 个")
            
            return target_file
        else:
            print(f"❌ 散点图生成失败")
            return None
            
    except Exception as e:
        print(f"❌ 生成散点图时出错: {str(e)}")
        import traceback
        traceback.print_exc()
        return None

def main():
    """主函数"""
    result_file = generate_scatter_chart()
    
    if result_file:
        print(f"\n{'='*60}")
        print(f"🎉 延停时长概率密度散点图生成完成!")
        print(f"📄 文件位置: {os.path.abspath(result_file)}")
        print(f"💡 可以在浏览器中打开查看效果")
        print(f"{'='*60}")
    else:
        print(f"\n{'='*60}")
        print(f"❌ 散点图生成失败")
        print(f"💡 请确保有包含'延停时长概率密度'工作表的Excel文件")
        print(f"{'='*60}")

if __name__ == "__main__":
    main()
