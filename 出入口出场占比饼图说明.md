# 🥧 出入口出场占比Timeline饼图功能说明

## 🎯 功能概览

新增出入口出场占比Timeline饼图，显示各出入口在每个时间段的出场量占比，与进场占比饼图形成完整的进出对比分析体系。

## 📊 完整图表体系

### 现在的完整体系
1. **`出入口进出量_总量.html`** - 总量Timeline柱状图
2. **`出入口进出量_方向.html`** - 方向Timeline柱状图
3. **`出入口占比_进.html`** - 进场占比Timeline饼图
4. **`出入口占比_出.html`** - 出场占比Timeline饼图 ← **新增**

### 分析维度完整性
- **绝对数量分析**：柱状图显示具体数值
- **相对占比分析**：饼图显示占比分布
- **进出对比分析**：进场vs出场占比对比
- **时间变化分析**：Timeline动态展示趋势

## 🔧 技术实现

### 核心代码结构
```python
def generate_gate_exit_proportion_timeline(self, sheet_name='进出量时间分布(按道闸)'):
    """生成出入口出场占比Timeline饼图"""
    
    # 1. 数据提取 - 使用出场列
    for i in range(gate_count):
        exit_col_idx = gate_start_col + i * 3 + 1  # 每组第2列：出场
        
    # 2. 智能阈值处理
    if gate_count_current <= 4:
        threshold_percentage = 3.0
    elif gate_count_current <= 6:
        threshold_percentage = 4.0
    else:
        threshold_percentage = 5.0
        
    # 3. Timeline饼图创建
    for time_period in time_periods:
        period_exit_data = [[gate_name, exit_value], ...]
        pie = Pie().add("出场占比", period_exit_data, rosetype="radius")
        timeline.add(pie, time_period)
```

### 数据处理特点
- **出场数据提取**：每个出入口的第2列（出场列）
- **智能阈值**：根据出入口数量动态调整阈值
- **数据清洗**：处理NaN值和异常数据
- **占比计算**：出场量/总出场量×100%

## 🎨 视觉设计

### 1. 与进场饼图保持一致
- **玫瑰图样式**：`rosetype="radius"`
- **半径设置**：`radius=["30%", "55%"]`
- **颜色配置**：相同的专业演讲风格颜色
- **布局样式**：统一的Timeline配置

### 2. 标题和标签差异化
- **主标题**：`出入口出场占比 - {时间段}`
- **副标题**：显示各出入口在该时间段的出场量占比
- **数据系列**：`出场占比`
- **Tooltip格式**：`出场量: {数量}辆\n占比: {百分比}%`

### 3. 颜色和交互
- **主要出入口**：专业演讲风格彩色
- **其他出入口**：中性灰色 `#95A5A6`
- **图例位置**：左侧垂直排列
- **播放间隔**：3秒，适合饼图观察

## 📈 应用价值

### 1. 进出对比分析
- **主要出去口识别**：识别承担主要出场流量的出入口
- **进出流向对比**：对比同一出入口的进场和出场占比
- **不平衡分析**：发现进出流向的不平衡情况

### 2. 流量管理优化
- **出口瓶颈识别**：识别出场流量集中的出入口
- **资源配置**：根据出场占比配置出口管理资源
- **疏导策略**：制定出场流量疏导策略

### 3. 运营决策支持
- **设施规划**：为出入口设施规划提供数据支持
- **管理策略**：制定差异化的出入口管理策略
- **应急预案**：为高出场流量时段制定应急方案

## 🔍 进出对比分析方法

### 1. 同时段对比
```
时间段 08:00-09:00:
进场占比: 出入口A(40%), 出入口B(35%), 出入口C(25%)
出场占比: 出入口A(30%), 出入口B(45%), 出入口C(25%)

分析结果:
- 出入口A: 主要进入口，次要出去口
- 出入口B: 次要进入口，主要出去口  
- 出入口C: 进出平衡
```

### 2. 流向特征识别
- **单向主导型**：某出入口进场占比高，出场占比低（或相反）
- **双向平衡型**：某出入口进场和出场占比相近
- **流量集中型**：少数出入口承担大部分进出流量
- **流量分散型**：各出入口进出流量相对均衡

### 3. 时间变化分析
- **高峰时段**：观察高峰时段的进出占比变化
- **流向转换**：识别进出主导地位的转换时点
- **趋势预测**：根据历史变化预测未来流向

## 💡 使用建议

### 1. 分析流程
1. **总量分析**：先看总量Timeline了解整体情况
2. **方向分析**：再看方向Timeline分析进出流向
3. **进场占比**：用进场饼图识别主要进入口
4. **出场占比**：用出场饼图识别主要出去口
5. **对比分析**：对比进出占比发现流向特点

### 2. 关键指标
- **占比集中度**：前3个出入口的占比总和
- **进出差异度**：同一出入口进出占比的差值
- **流向稳定性**：占比随时间的变化幅度
- **平衡指数**：各出入口占比的均衡程度

### 3. 决策应用
- **主要出去口**：重点配置出场管理资源
- **流向不平衡**：制定流量引导和疏导措施
- **瓶颈预防**：提前预防高占比出入口成为瓶颈
- **容量规划**：根据占比分布规划出入口容量

## 🔄 智能阈值处理

### 阈值策略（与进场饼图一致）
- **出入口数量 ≤ 4**：使用3%阈值
- **出入口数量 ≤ 6**：使用4%阈值
- **出入口数量 > 6**：使用5%阈值

### 处理效果
- **突出重点**：主要出去口更加明显
- **简化显示**：减少小占比扇区的视觉干扰
- **保持完整**：所有数据通过"其他出入口"保留

## 📊 输出效果

### 文件信息
- **文件名**：`出入口占比_出.html`
- **文件类型**：HTML格式，支持浏览器交互
- **生成位置**：与其他图表文件相同目录

### 图表内容
- **主标题**：`出入口出场占比 - [时间段]`
- **副标题**：显示当前时间段的出场占比说明
- **数据系列**：`出场占比`
- **图例**：各出入口名称和"其他出入口"

## 🎯 分析案例

### 典型分析场景
```
某停车场有4个出入口，分析发现：

进场占比模式:
- 上午高峰：出入口A(50%), 出入口B(30%), 其他(20%)
- 下午高峰：出入口B(45%), 出入口A(35%), 其他(20%)

出场占比模式:
- 上午高峰：出入口C(60%), 出入口D(25%), 其他(15%)
- 下午高峰：出入口D(50%), 出入口C(30%), 其他(20%)

分析结论:
- A、B主要承担进场，C、D主要承担出场
- 存在明显的进出分离模式
- 需要在C、D出入口加强出场管理
```

## 🔄 版本更新

### v3.2 新功能
- ✅ 出入口出场占比Timeline饼图
- ✅ 完整的进出对比分析体系
- ✅ 智能阈值处理（与进场一致）
- ✅ 专业演讲风格视觉设计
- ✅ 详细的调试和分析信息

### 图表体系完整性
- **4个Timeline图表**：总量、方向、进场占比、出场占比
- **2个分析维度**：绝对数量、相对占比
- **完整进出对比**：进场vs出场全面分析

---

*功能开发完成时间: 2025-06-20*  
*适用版本: parking_chart_generator.py v3.2+*
