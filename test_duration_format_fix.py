#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试时长格式修复
"""

import pandas as pd
from parking_time_filter import TimeFilter

def test_duration_format():
    """测试时长格式是否正确"""
    print("=== 测试时长格式修复 ===\n")
    
    # 创建简单测试数据
    test_data = pd.DataFrame({
        'entry_time': pd.to_datetime(['2024-01-01 08:00:00']),
        'exit_time': pd.to_datetime(['2024-01-01 10:00:00'])
    })
    
    # 创建时间过滤器
    time_filter = TimeFilter(test_data, {})
    
    # 测试1：默认配置
    print("1. 默认配置测试:")
    print("   控制点: [2, 8, 24, 72] 小时")
    print("   时段长度: [0.5, 1, 16, 48] 小时")
    
    try:
        default_periods = time_filter.create_duration_periods()
        print(f"   生成 {len(default_periods)} 个时段:")
        for i, period in enumerate(default_periods, 1):
            print(f"     {i:2d}. {period}")
            
            # 检查是否还有"分钟"出现在不应该出现的地方
            if '分钟' in period and any(hour_word in period for hour_word in ['小时', '天']):
                print(f"        ⚠️  混合单位: {period}")
            elif '480分钟' in period or '1440分钟' in period or '4320分钟' in period:
                print(f"        ❌ 应该用小时/天表示: {period}")
    except Exception as e:
        print(f"   ❌ 错误: {str(e)}")
    
    print("\n" + "="*60 + "\n")
    
    # 测试2：自定义配置
    print("2. 自定义配置测试:")
    control_points = [1, 4, 12, 48]  # 1小时, 4小时, 12小时, 2天
    period_lengths = [0.25, 1, 6, 24]  # 15分钟, 1小时, 6小时, 1天
    print(f"   控制点: {control_points} 小时")
    print(f"   时段长度: {period_lengths} 小时")
    
    try:
        custom_periods = time_filter.create_duration_periods(control_points, period_lengths)
        print(f"   生成 {len(custom_periods)} 个时段:")
        for i, period in enumerate(custom_periods, 1):
            print(f"     {i:2d}. {period}")
            
            # 检查格式
            if '60分钟' in period:
                print(f"        ⚠️  应该用'1小时': {period}")
            elif '240分钟' in period:
                print(f"        ⚠️  应该用'4小时': {period}")
            elif '720分钟' in period:
                print(f"        ⚠️  应该用'12小时': {period}")
            elif '2880分钟' in period:
                print(f"        ⚠️  应该用'2天': {period}")
    except Exception as e:
        print(f"   ❌ 错误: {str(e)}")
    
    print("\n" + "="*60 + "\n")
    
    # 测试3：边界情况
    print("3. 边界情况测试:")
    edge_cases = [
        ([0.5, 2, 8], [0.25, 1, 4]),  # 30分钟, 2小时, 8小时
        ([1, 6, 24], [0.5, 3, 12]),   # 1小时, 6小时, 1天
        ([2, 12, 72], [1, 6, 36])     # 2小时, 12小时, 3天
    ]
    
    for i, (cp, pl) in enumerate(edge_cases, 1):
        print(f"   测试{i}: 控制点{cp}, 时段长度{pl}")
        try:
            periods = time_filter.create_duration_periods(cp, pl)
            
            # 检查关键时段
            problematic_periods = []
            for period in periods:
                # 检查是否有应该用小时/天表示的分钟值
                if any(minutes in period for minutes in ['60分钟', '120分钟', '180分钟', '240分钟', '360分钟', '480分钟', '720分钟', '1440分钟']):
                    problematic_periods.append(period)
            
            if problematic_periods:
                print(f"      ❌ 发现格式问题: {problematic_periods}")
            else:
                print(f"      ✅ 格式正确")
                
        except Exception as e:
            print(f"      ❌ 错误: {str(e)}")

def test_format_duration_label():
    """测试_format_duration_label方法"""
    print("\n=== 测试_format_duration_label方法 ===\n")
    
    # 创建时间过滤器实例
    test_data = pd.DataFrame({
        'entry_time': pd.to_datetime(['2024-01-01 08:00:00']),
        'exit_time': pd.to_datetime(['2024-01-01 10:00:00'])
    })
    time_filter = TimeFilter(test_data, {})
    
    # 测试用例
    test_cases = [
        (0, "0分钟"),
        (30, "30分钟"),
        (60, "1小时"),
        (90, "1小时30分钟"),
        (120, "2小时"),
        (480, "8小时"),
        (720, "12小时"),
        (1440, "1天"),
        (1500, "1天1小时"),
        (1530, "1天1小时30分钟"),
        (2880, "2天"),
        (4320, "3天")
    ]
    
    print("分钟值 -> 格式化结果:")
    print("-" * 30)
    
    all_correct = True
    for minutes, expected in test_cases:
        try:
            result = time_filter._format_duration_label(minutes)
            status = "✅" if result == expected else "❌"
            print(f"{minutes:4d}分钟 -> {result:15} {status}")
            if result != expected:
                print(f"         期望: {expected}")
                all_correct = False
        except Exception as e:
            print(f"{minutes:4d}分钟 -> 错误: {str(e)} ❌")
            all_correct = False
    
    print("-" * 30)
    if all_correct:
        print("✅ 所有测试用例通过")
    else:
        print("❌ 部分测试用例失败")

if __name__ == "__main__":
    test_duration_format()
    test_format_duration_label()
