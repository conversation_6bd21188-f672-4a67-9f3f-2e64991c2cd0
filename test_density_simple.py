#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版延停时长概率密度测试
"""

import pandas as pd
import numpy as np
from parking_time_filter import TimeFilter

def create_test_data():
    """创建测试数据"""
    np.random.seed(42)
    
    # 生成不同时长分布的停车记录
    durations = []
    vehicle_types = []
    
    # 短时停车 (0-2小时)
    short_durations = np.random.exponential(0.5, 50)  # 平均30分钟
    short_durations = np.clip(short_durations, 0.1, 2.0)
    durations.extend(short_durations)
    vehicle_types.extend(['小型车'] * len(short_durations))
    
    # 中时停车 (2-8小时)
    medium_durations = np.random.normal(5, 1.5, 30)  # 平均5小时
    medium_durations = np.clip(medium_durations, 2.0, 8.0)
    durations.extend(medium_durations)
    vehicle_types.extend(['中型车'] * len(medium_durations))
    
    # 长时停车 (8小时以上)
    long_durations = np.random.exponential(12, 20)  # 平均12小时起
    long_durations = np.clip(long_durations, 8.0, 72.0)
    durations.extend(long_durations)
    vehicle_types.extend(['大型车'] * len(long_durations))
    
    # 创建DataFrame
    n_records = len(durations)
    base_time = pd.Timestamp('2024-01-01 08:00:00')
    
    test_data = pd.DataFrame({
        'entry_time': [base_time + pd.Timedelta(hours=i*0.5) for i in range(n_records)],
        'exit_time': [base_time + pd.Timedelta(hours=i*0.5 + durations[i]) for i in range(n_records)],
        'duration': durations,  # 停车时长（小时）
        'vtype': vehicle_types
    })
    
    return test_data

def simple_assign_period(duration_hours):
    """简化的时段分配逻辑"""
    # 基于默认时段结构的硬编码分配
    if duration_hours < 0.5:
        return 0  # "0分钟-30分钟"
    elif duration_hours < 1.0:
        return 1  # "30分钟-1小时"
    elif duration_hours < 1.5:
        return 2  # "1小时-1小时30分钟"
    elif duration_hours < 2.0:
        return 3  # "1小时30分钟-2小时"
    elif duration_hours < 3.0:
        return 4  # "2小时-3小时"
    elif duration_hours < 4.0:
        return 5  # "3小时-4小时"
    elif duration_hours < 5.0:
        return 6  # "4小时-5小时"
    elif duration_hours < 6.0:
        return 7  # "5小时-6小时"
    elif duration_hours < 7.0:
        return 8  # "6小时-7小时"
    elif duration_hours < 8.0:
        return 9  # "7小时-8小时"
    elif duration_hours < 24.0:
        return 10  # "480分钟-1天"
    elif duration_hours < 72.0:
        return 11  # "1天-3天"
    else:
        return 12  # ">3天"

def calculate_duration_density(data):
    """计算延停时长概率密度"""
    print("=== 计算延停时长概率密度 ===\n")
    
    # 生成时段标签
    time_filter = TimeFilter(data, {})
    periods = time_filter.create_duration_periods()
    
    print(f"生成的时段 (共{len(periods)}个):")
    for i, period in enumerate(periods):
        print(f"  {i:2d}. {period}")
    
    print(f"\n数据统计:")
    print(f"  总记录数: {len(data)}")
    print(f"  时长范围: {data['duration'].min():.2f} - {data['duration'].max():.2f} 小时")
    print(f"  车辆类型: {data['vtype'].unique()}")
    
    # 分配时段
    data['period_index'] = data['duration'].apply(simple_assign_period)
    data['period_label'] = data['period_index'].apply(lambda x: periods[min(x, len(periods)-1)])
    
    # 统计频数
    period_counts = data['period_label'].value_counts()
    
    # 创建结果DataFrame
    result = pd.DataFrame({
        '时长区间': periods,
        '频数': [period_counts.get(period, 0) for period in periods]
    })
    
    # 计算概率密度
    total_count = result['频数'].sum()
    result['频率'] = (result['频数'] / total_count).round(4)
    result['百分比(%)'] = (result['频率'] * 100).round(2)
    result['累积频率'] = result['频率'].cumsum().round(4)
    result['累积百分比(%)'] = (result['累积频率'] * 100).round(2)
    
    # 按车辆类型统计
    vehicle_types = data['vtype'].unique()
    for vtype in vehicle_types:
        vtype_data = data[data['vtype'] == vtype]
        vtype_counts = vtype_data['period_label'].value_counts()
        result[f'{vtype}_频数'] = [vtype_counts.get(period, 0) for period in periods]
        
        vtype_total = result[f'{vtype}_频数'].sum()
        if vtype_total > 0:
            result[f'{vtype}_频率'] = (result[f'{vtype}_频数'] / vtype_total).round(4)
            result[f'{vtype}_百分比(%)'] = (result[f'{vtype}_频率'] * 100).round(2)
        else:
            result[f'{vtype}_频率'] = 0.0
            result[f'{vtype}_百分比(%)'] = 0.0
    
    return result

def display_results(result):
    """显示结果"""
    print(f"\n=== 延停时长概率密度分布 ===\n")
    
    print("主要统计:")
    print("-" * 80)
    print(f"{'时长区间':25} {'频数':>6} {'频率':>8} {'百分比':>8} {'累积%':>8}")
    print("-" * 80)
    
    for i, row in result.iterrows():
        time_range = row['时长区间']
        frequency = row['频数']
        rate = row['频率']
        percentage = row['百分比(%)']
        cumulative = row['累积百分比(%)']
        print(f"{time_range:25} {frequency:6d} {rate:8.4f} {percentage:7.2f}% {cumulative:7.2f}%")
    
    print("-" * 80)
    print(f"{'总计':25} {result['频数'].sum():6d} {result['频率'].sum():8.4f} {result['百分比(%)'].sum():7.2f}%")
    
    # 显示车辆类型分布
    vtype_columns = [col for col in result.columns if '_频数' in col and col != '频数']
    if vtype_columns:
        print(f"\n按车辆类型分布:")
        for vtype_col in vtype_columns:
            vtype = vtype_col.replace('_频数', '')
            total_vtype = result[vtype_col].sum()
            percentage = (total_vtype / result['频数'].sum() * 100) if result['频数'].sum() > 0 else 0
            print(f"  {vtype}: {total_vtype} 条记录 ({percentage:.1f}%)")

def export_to_excel(result):
    """导出到Excel"""
    try:
        output_path = "延停时长概率密度_测试.xlsx"
        
        with pd.ExcelWriter(output_path, engine='xlsxwriter') as writer:
            # 导出到工作表
            result.to_excel(writer, sheet_name='延停时长概率密度', index=False)
            
            # 获取工作表和工作簿对象
            workbook = writer.book
            worksheet = writer.sheets['延停时长概率密度']
            
            # 设置格式
            header_format = workbook.add_format({
                'bold': True,
                'text_wrap': True,
                'valign': 'top',
                'fg_color': '#D7E4BC',
                'border': 1
            })
            
            # 应用表头格式
            for col_num, column in enumerate(result.columns):
                worksheet.write(0, col_num, column, header_format)
            
            # 自动调整列宽
            for i, col in enumerate(result.columns):
                max_len = max(len(str(col)), result[col].astype(str).str.len().max())
                worksheet.set_column(i, i, min(max_len + 2, 30))
        
        print(f"\n✅ 成功导出到: {output_path}")
        
    except Exception as e:
        print(f"\n❌ 导出失败: {str(e)}")

if __name__ == "__main__":
    # 创建测试数据
    test_data = create_test_data()
    
    # 计算概率密度
    result = calculate_duration_density(test_data)
    
    # 显示结果
    display_results(result)
    
    # 导出到Excel
    export_to_excel(result)
