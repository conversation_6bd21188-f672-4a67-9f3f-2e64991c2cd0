"""
停车数据处理基类，提供统一的字段映射和数据处理逻辑
"""

import pandas as pd
from parking_field_mapping_config import (
    get_field_mapping,
    validate_required_fields,
    STANDARDIZED_FIELDS,
    ANALYSIS_FIELDS,
    FIELD_VALIDATION
)

# 数据清洗配置 - 可以根据需要调整
DATA_CLEANING_CONFIG = {
    'duration_limits': {
        'min_hours': 1/60,      # 最小停车时长（小时）- 1分钟
        'max_hours': 30*24,     # 最大停车时长（小时）- 30天
    },
    'vehicle_id_limits': {
        'min_length': 5,        # 车牌号最小长度
    },
    'time_interval_limits': {
        'min_interval_minutes': 10,  # 同一车辆最小进出间隔（分钟）
    },
    'anomaly_detection': {
        'suspicious_duration_min': 0.05,  # 可疑停车时长下限（小时）- 3分钟
        'suspicious_duration_max': 7*24,  # 可疑停车时长上限（小时）- 7天
    }
}

class ParkingDataBase:
    """停车数据处理基类"""
    
    def __init__(self, data=None, params=None, logger=None):
        """
        初始化基类
        
        Args:
            data: DataFrame, 原始数据
            params: dict, 处理参数
            logger: Logger对象, 用于日志记录
        """
        self.raw_data = data
        self.params = params or {}
        self.logger = logger
        self.field_mapping = None
        self.processed_data = None
        
        # 验证并构建字段映射
        if data is not None and params is not None:
            self._validate_and_build_mapping()
    
    def _validate_and_build_mapping(self):
        """
        构建字段映射（不进行验证）
        """
        # 检查数据是否已经标准化
        if self._is_data_standardized():
            self.field_mapping = {field: field for field in STANDARDIZED_FIELDS}
            return
            
        # 获取字段映射（不验证）
        # 注意：MODE_CONFIG现在在main函数中，通过params传递字段映射信息
        self.field_mapping = get_field_mapping(
            self.params['mode'],
            self.params,
            logger=self.logger
        )
        
        # 添加调试信息
        if self.logger:
            self.logger.debug(f"使用的字段映射: {self.field_mapping}")
    
    def _is_data_standardized(self):
        """检查数据是否已经标准化"""
        # 检查是否所有标准字段都存在
        return all(field in self.raw_data.columns for field in STANDARDIZED_FIELDS)
    
    def _standardize_data(self):
        """将数据标准化为统一格式"""
        if self.raw_data is None or self.field_mapping is None:
            raise ValueError("未初始化原始数据或字段映射")
        
        # 复制原始数据
        data = self.raw_data.copy()
        
        # 重命名列为标准字段名
        # 过滤掉空值，避免重命名问题
        rename_map = {v: k for k, v in self.field_mapping.items() if v and v.strip()}

        # 添加调试信息
        if self.logger:
            self.logger.debug(f"原始数据字段: {data.columns.tolist()}")
            self.logger.debug(f"字段映射: {self.field_mapping}")
            self.logger.debug(f"重命名映射: {rename_map}")

        data = data.rename(columns=rename_map)

        # 添加调试信息
        if self.logger:
            self.logger.debug(f"重命名后字段: {data.columns.tolist()}")
        
        # 根据模式处理数据
        if self.params['mode'] == 'mode1_simple':
            # Mode1_simple: 每条记录都是独立的进出事件，不需要配对
            data = self._standardize_mode1_simple(data)
        elif self.params['mode'] == 'mode1':
            # Mode1: 需要根据进出记录生成配对数据
            data = self._standardize_mode1_regular(data)
        else:  # mode2
            # 转换时间字段
            data['entry_time'] = pd.to_datetime(data['entry_time'], errors='coerce')
            data['exit_time'] = pd.to_datetime(data['exit_time'], errors='coerce')

            # 检查是否有车辆唯一标识字段，没有则自动生成
            if 'vid' not in data.columns:
                if self.logger:
                    self.logger.warning("缺少车辆唯一标识字段，将自动生成唯一ID")
                # 使用进场时间+道闸编号生成唯一ID
                data['vid'] = (
                    data['entry_time'].dt.strftime('%Y%m%d%H%M%S') + '_' +
                    data['entry_gate'].astype(str)
                )

            # 计算停车时长
            data['duration'] = (data['exit_time'] - data['entry_time']).dt.total_seconds() / 3600

            # 添加标准字段
            data['timestamp'] = data['entry_time']  # 使用入场时间作为时间记录
            data['direction'] = self.params.get('进出标识值', ['入场'])[0]  # 使用入场标识
            data['gate'] = data['entry_gate']  # 使用入场道闸

        # 确保数据类型正确
        if 'duration' in data.columns:
            data['duration'] = pd.to_numeric(data['duration'], errors='coerce')

        # 根据模式选择需要的字段
        required_fields = ['vid', 'vtype', 'entry_time', 'exit_time', 'entry_gate', 'exit_gate', 'duration',
                         'timestamp', 'direction', 'gate']
        available_fields = [field for field in required_fields if field in data.columns]

        self.processed_data = data[available_fields]

        return self.processed_data

    def _standardize_mode1_simple(self, data):
        """mode1_simple模式的数据标准化"""
        # 转换时间字段
        data['timestamp'] = pd.to_datetime(data['timestamp'], errors='coerce')

        # 为每条记录生成唯一的虚拟车牌号
        data['vid'] = data.index.astype(str) + '_' + data['direction'].astype(str)

        # 为简化模式创建必要的字段
        # 每条记录都被视为独立的进出事件
        virtual_duration_minutes = 1  # 虚拟停车时长（分钟）
        virtual_duration_hours = virtual_duration_minutes / 60  # 转换为小时

        data['entry_time'] = data['timestamp']
        data['exit_time'] = data['timestamp'] + pd.Timedelta(minutes=virtual_duration_minutes)  # 虚拟出场时间
        data['entry_gate'] = data['gate']
        data['exit_gate'] = data['gate']
        data['duration'] = virtual_duration_hours  # 虚拟停车时长

        return data

    def _pair_records_by_logic_closure(self, vid_data):
        """
        基于进出逻辑闭合的配对策略

        策略说明：
        1. 按时间顺序处理所有记录
        2. 对于连续多次进场，只保留最后一次进场
        3. 对于连续多次出场，只保留最先一次出场
        4. 确保进出逻辑闭合：进→出→进→出...

        Args:
            vid_data: 单个车辆的所有记录，已按时间排序

        Returns:
            list: 配对后的记录列表
        """
        if vid_data.empty:
            return []

        paired_records = []
        entry_exit_values = self.params.get('进出标识值', ('进', '出'))
        if isinstance(entry_exit_values, tuple):
            entry_exit_values = list(entry_exit_values)

        entry_value = entry_exit_values[0]  # 进场标识
        exit_value = entry_exit_values[1]   # 出场标识

        # 状态跟踪
        current_entry = None  # 当前有效的进场记录

        for _, record in vid_data.iterrows():
            direction = record['direction']

            if direction == entry_value:  # 进场记录
                # 如果是进场记录，更新当前进场记录（覆盖之前的进场记录）
                current_entry = record
                if self.logger:
                    self.logger.debug(f"车辆 {record['vid']} 更新进场记录: {record['timestamp']}")

            elif direction == exit_value:  # 出场记录
                if current_entry is not None:
                    # 有有效的进场记录，可以配对
                    duration = (record['timestamp'] - current_entry['timestamp']).total_seconds() / 3600

                    # 检查停车时长是否合理
                    if duration > 0:
                        paired_records.append({
                            'vid': record['vid'],
                            'vtype': current_entry['vtype'],
                            'entry_time': current_entry['timestamp'],
                            'exit_time': record['timestamp'],
                            'entry_gate': current_entry['gate'],
                            'exit_gate': record['gate'],
                            'duration': duration
                        })

                        if self.logger:
                            self.logger.debug(f"车辆 {record['vid']} 成功配对: "
                                            f"{current_entry['timestamp']} → {record['timestamp']} "
                                            f"(时长: {duration:.2f}小时)")
                    else:
                        if self.logger:
                            self.logger.warning(f"车辆 {record['vid']} 停车时长异常: {duration:.2f}小时")

                    # 清除当前进场记录，等待下一次进场
                    current_entry = None
                else:
                    # 没有对应的进场记录，跳过这个出场记录
                    if self.logger:
                        self.logger.warning(f"车辆 {record['vid']} 出场记录无对应进场: {record['timestamp']}")

        # 处理未配对的进场记录
        if current_entry is not None:
            if self.logger:
                self.logger.warning(f"车辆 {current_entry['vid']} 进场记录无对应出场: {current_entry['timestamp']}")

            # 为未配对的进场记录创建默认出场记录（可选）
            # 这里可以根据需要决定是否包含未配对的记录
            # 暂时不包含，保持严格的配对策略

        return paired_records

    def _standardize_mode1_regular(self, data):
        """mode1常规模式的数据标准化 - 基于时间顺序和进出逻辑闭合的策略"""
        # 确保时间字段是datetime类型
        if 'timestamp' in data.columns:
            data['timestamp'] = pd.to_datetime(data['timestamp'], errors='coerce')
        else:
            raise ValueError(f"缺少时间字段 'timestamp'，当前字段: {data.columns.tolist()}")

        # 合并进出场记录
        merged_records = []
        total_vehicles = len(data['vid'].unique())
        processed_vehicles = 0
        successful_pairs = 0

        if self.logger:
            self.logger.info(f"开始处理 {total_vehicles} 辆车的数据")

        for vid in data['vid'].unique():
            vid_data = data[data['vid'] == vid].copy()

            # 按时间排序所有记录（不分进出方向）
            vid_data = vid_data.sort_values('timestamp').reset_index(drop=True)

            if self.logger:
                self.logger.debug(f"车辆 {vid} 有 {len(vid_data)} 条记录")

            # 使用基于进出逻辑闭合的配对策略
            paired_records = self._pair_records_by_logic_closure(vid_data)
            if paired_records:
                successful_pairs += len(paired_records)
            merged_records.extend(paired_records)
            processed_vehicles += 1

        if self.logger:
            self.logger.info(f"处理完成: {processed_vehicles} 辆车, 成功配对 {successful_pairs} 条记录")

        # 将结果转换为DataFrame
        if merged_records:
            merged = pd.DataFrame(merged_records)

            # 确保时间列是datetime类型
            merged['entry_time'] = pd.to_datetime(merged['entry_time'], errors='coerce')
            merged['exit_time'] = pd.to_datetime(merged['exit_time'], errors='coerce')

            # 检查时间转换后是否有NaT值
            entry_nat_count = merged['entry_time'].isna().sum()
            exit_nat_count = merged['exit_time'].isna().sum()

            if entry_nat_count > 0 or exit_nat_count > 0:
                if self.logger:
                    self.logger.warning(f"时间转换后发现NaT值: entry_time={entry_nat_count}, exit_time={exit_nat_count}")
                # 移除有NaT值的记录
                merged = merged.dropna(subset=['entry_time', 'exit_time'])
                if self.logger:
                    self.logger.info(f"移除NaT记录后剩余: {len(merged)} 条记录")
        else:
            if self.logger:
                self.logger.warning("没有找到有效的进出场记录匹配")
            merged = pd.DataFrame(columns=['vid', 'vtype', 'entry_time', 'exit_time',
                                        'entry_gate', 'exit_gate', 'duration'])

        # 计算停车时长(小时)，处理可能的空值
        if len(merged) > 0:
            # 确保时间列是datetime类型
            merged['entry_time'] = pd.to_datetime(merged['entry_time'], errors='coerce')
            merged['exit_time'] = pd.to_datetime(merged['exit_time'], errors='coerce')

            # 计算停车时长
            merged['duration'] = (
                merged['exit_time'] - merged['entry_time']
            ).dt.total_seconds() / 3600

            # 记录无效时间记录
            invalid_times = merged[merged['duration'].isna()]
            if len(invalid_times) > 0 and self.logger:
                self.logger.warning(f"发现 {len(invalid_times)} 条记录的停车时长计算无效")
        else:
            # 如果没有数据，确保duration列存在
            merged['duration'] = pd.Series([], dtype='float64')

        # 保留原始字段
        if len(merged) > 0:
            merged['timestamp'] = merged['entry_time']  # 使用入场时间作为时间记录
            entry_exit_values = self.params.get('进出标识值', ('进', '出'))
            if isinstance(entry_exit_values, tuple):
                entry_exit_values = list(entry_exit_values)
            merged['direction'] = entry_exit_values[0]  # 使用入场标识
            merged['gate'] = merged['entry_gate']  # 使用入场道闸
        else:
            # 如果没有数据，确保必要的列存在
            merged['timestamp'] = pd.Series([], dtype='datetime64[ns]')
            merged['direction'] = pd.Series([], dtype='object')
            merged['gate'] = pd.Series([], dtype='object')

        return merged

    def _clean_data(self):
        """清洗数据，移除或修正异常值"""
        if self.processed_data is None or self.processed_data.empty:
            return

        # 根据模式选择不同的清洗策略
        if self.params.get('mode') == 'mode1_simple':
            self._clean_data_simple()
        else:
            self._clean_data_regular()

    def _clean_data_simple(self):
        """mode1_simple模式的数据清洗方法"""
        if self.processed_data is None or self.processed_data.empty:
            return

        # 记录原始数据行数
        original_count = len(self.processed_data)

        # mode1_simple模式下的清洗逻辑：
        # 1. 只清洗明显的数据质量问题
        # 2. 不进行停车时长相关的过滤
        # 3. 每条记录都是独立的进出事件

        # 动态获取关键字段名（避免硬编码）
        time_field = None
        direction_field = None
        gate_field = None

        # 查找时间字段
        for field in ['timestamp', 'entry_time', 'time', '时间']:
            if field in self.processed_data.columns:
                time_field = field
                break

        # 查找方向字段
        for field in ['direction', 'dir', '方向', '进出类型']:
            if field in self.processed_data.columns:
                direction_field = field
                break

        # 查找道闸字段
        for field in ['gate', 'gate_id', '道闸', '出入口']:
            if field in self.processed_data.columns:
                gate_field = field
                break

        # 移除时间字段为空的记录
        if time_field:
            self.processed_data = self.processed_data.dropna(subset=[time_field])

        # 移除方向字段为空的记录
        if direction_field:
            self.processed_data = self.processed_data.dropna(subset=[direction_field])

        # 移除重复记录（基于可用的关键字段）
        duplicate_subset = []
        if time_field:
            duplicate_subset.append(time_field)
        if direction_field:
            duplicate_subset.append(direction_field)
        if gate_field:
            duplicate_subset.append(gate_field)

        if duplicate_subset:
            self.processed_data = self.processed_data.drop_duplicates(
                subset=duplicate_subset,
                keep='first'
            )

        # 清理字符串字段（动态识别）
        string_fields = []

        # 添加已识别的字段
        if direction_field:
            string_fields.append(direction_field)
        if gate_field:
            string_fields.append(gate_field)

        # 添加其他可能的字符串字段
        for field in ['vtype', '车辆类型', 'vehicle_type']:
            if field in self.processed_data.columns:
                string_fields.append(field)

        # 清理字符串字段
        for field in string_fields:
            if field in self.processed_data.columns:
                self.processed_data[field] = (
                    self.processed_data[field]
                    .astype(str)
                    .str.strip()
                    .replace('', pd.NA)  # 空字符串转为NA
                )
                # 移除该字段为空的记录
                self.processed_data = self.processed_data.dropna(subset=[field])

        # 处理车牌号字段（如果存在）
        if 'vid' in self.processed_data.columns:
            # 清理车牌号
            self.processed_data['vid'] = (
                self.processed_data['vid']
                .astype(str)
                .str.strip()
                .str.upper()
                .str.replace(r'[^A-Z0-9\u4e00-\u9fff]', '', regex=True)
            )

            # 为长度不足的车牌号生成虚拟车牌
            min_vid_length = DATA_CLEANING_CONFIG['vehicle_id_limits']['min_length']
            short_vid_mask = self.processed_data['vid'].str.len() < min_vid_length

            if short_vid_mask.any():
                short_vid_count = short_vid_mask.sum()
                if self.logger:
                    self.logger.warning(f"简化模式：发现{short_vid_count}个长度不足的车牌号，将生成虚拟车牌号")

                # 为长度不足的车牌号生成虚拟车牌
                short_vid_indices = self.processed_data[short_vid_mask].index
                for idx in short_vid_indices:
                    original_vid = self.processed_data.loc[idx, 'vid']
                    # 生成虚拟车牌：原车牌号 + 补充字符 + 时间戳后4位
                    timestamp_suffix = str(abs(hash(str(idx) + str(original_vid))))[-4:]
                    padding_length = max(0, min_vid_length - len(original_vid) - 4)
                    virtual_vid = f"{original_vid}{'X' * padding_length}{timestamp_suffix}"
                    self.processed_data.loc[idx, 'vid'] = virtual_vid

                if self.logger:
                    self.logger.info(f"简化模式：已为{short_vid_count}个车牌号生成虚拟车牌，继续处理流程")

        # 记录清洗后的数据行数
        cleaned_count = len(self.processed_data)
        removed_count = original_count - cleaned_count

        if self.logger and removed_count > 0:
            self.logger.info(f"简化模式数据清洗: 移除了 {removed_count} 条异常记录 "
                           f"({removed_count/original_count*100:.2f}%)")

    def _clean_data_regular(self):
        """常规模式的数据清洗方法"""
        if self.processed_data is None or self.processed_data.empty:
            return

        # 记录原始数据行数
        original_count = len(self.processed_data)

        # 移除空值
        self.processed_data = self.processed_data.dropna(subset=['vid', 'entry_time', 'exit_time'])

        # 移除重复记录
        self.processed_data = self.processed_data.drop_duplicates(
            subset=['vid', 'entry_time', 'exit_time'],
            keep='first'
        )

        # 彻底清理车牌号
        self.processed_data['vid'] = (
            self.processed_data['vid']
            .astype(str)  # 确保是字符串类型
            .str.strip()  # 移除前后空格
            .str.upper()  # 转为大写
            .str.replace(r'[^A-Z0-9\u4e00-\u9fff]', '', regex=True)  # 只保留字母、数字和中文
        )

        # 增强车牌号健壮性：为长度不足的车牌号生成虚拟车牌
        min_vid_length = DATA_CLEANING_CONFIG['vehicle_id_limits']['min_length']
        short_vid_mask = self.processed_data['vid'].str.len() < min_vid_length

        if short_vid_mask.any():
            short_vid_count = short_vid_mask.sum()
            if self.logger:
                self.logger.warning(f"发现{short_vid_count}个长度不足的车牌号，将生成虚拟车牌号")

            # 为长度不足的车牌号生成虚拟车牌
            short_vid_indices = self.processed_data[short_vid_mask].index
            for idx in short_vid_indices:
                original_vid = self.processed_data.loc[idx, 'vid']
                # 生成虚拟车牌：原车牌号 + 补充字符 + 时间戳后4位
                timestamp_suffix = str(abs(hash(str(idx) + str(original_vid))))[-4:]
                padding_length = max(0, min_vid_length - len(original_vid) - 4)
                virtual_vid = f"{original_vid}{'X' * padding_length}{timestamp_suffix}"
                self.processed_data.loc[idx, 'vid'] = virtual_vid

            if self.logger:
                self.logger.info(f"已为{short_vid_count}个车牌号生成虚拟车牌，继续处理流程")

        # 记录无效车牌号示例
        if self.logger:
            invalid_vids = self.processed_data[
                self.processed_data['vid'].str.len() == 0
            ]['vid'].unique()
            if len(invalid_vids) > 0:
                self.logger.warning(f"清理后仍有{len(invalid_vids)}个无效车牌号")

        # 移除无效的时间记录
        self.processed_data = self.processed_data[
            self.processed_data['entry_time'] <= self.processed_data['exit_time']
        ]

        # 移除异常的停车时长记录（使用配置化的限制）
        min_duration = DATA_CLEANING_CONFIG['duration_limits']['min_hours']
        max_duration = DATA_CLEANING_CONFIG['duration_limits']['max_hours']

        duration_mask = (
            (self.processed_data['duration'] >= min_duration) &
            (self.processed_data['duration'] <= max_duration)
        )
        self.processed_data = self.processed_data[duration_mask]

        # 记录清洗后的数据行数
        cleaned_count = len(self.processed_data)
        removed_count = original_count - cleaned_count

        if self.logger and removed_count > 0:
            self.logger.info(f"数据清洗: 移除了 {removed_count} 条异常记录 "
                           f"({removed_count/original_count*100:.2f}%)")
    
    def _validate_processed_data(self):
        """验证处理后的数据"""
        if self.processed_data is None:
            raise ValueError("数据未经过处理")

        if self.processed_data.empty:
            raise ValueError("处理后的数据为空")

        # 根据模式选择不同的验证策略
        if self.params.get('mode') == 'mode1_simple':
            self._validate_processed_data_simple()
        else:
            self._validate_processed_data_regular()

    def _validate_processed_data_simple(self):
        """mode1_simple模式的数据验证"""
        # mode1_simple模式下的验证：
        # 1. 不验证车牌号相关规则（因为使用虚拟车牌号）
        # 2. 不验证停车时长相关规则（因为没有车辆追踪）
        # 3. 只验证基本的数据完整性

        # 验证基本字段存在
        basic_fields = ['timestamp', 'direction']
        missing_fields = [field for field in basic_fields
                        if field in self.processed_data.columns and
                        self.processed_data[field].isnull().all()]
        if missing_fields:
            raise ValueError(f"简化模式缺少基本字段数据: {missing_fields}")

        # 验证方向字段值
        if 'direction' in self.processed_data.columns:
            entry_exit_values = self.params.get('进出标识值', ('入场', '出场'))
            if isinstance(entry_exit_values, tuple):
                entry_exit_values = list(entry_exit_values)

            invalid_directions = ~self.processed_data['direction'].isin(entry_exit_values)
            if invalid_directions.any():
                invalid_count = invalid_directions.sum()
                if self.logger:
                    self.logger.warning(f"发现{invalid_count}条方向字段值异常的记录")
                # 在简化模式下，只警告不抛出异常

    def _validate_processed_data_regular(self):
        """常规模式的数据验证"""
        # 从FIELD_VALIDATION中获取必需字段列表
        required_fields = [field for field, validation in FIELD_VALIDATION.items()
                         if validation.get('required', False)]

        # 验证必需字段存在
        if required_fields:  # 只在有必需字段时进行验证
            missing_fields = [field for field in required_fields
                            if field not in self.processed_data.columns]
            if missing_fields:
                raise ValueError(f"处理后的数据缺少必需字段: {missing_fields}")

        # 验证字段类型
        type_validations = {
            'vid': str,
            'entry_time': 'datetime64[ns]',
            'exit_time': 'datetime64[ns]',
            'duration': 'float64'
        }

        for field, expected_type in type_validations.items():
            if field not in self.processed_data.columns:
                continue

            if expected_type == str:
                if not all(isinstance(x, str) for x in self.processed_data[field].dropna()):
                    raise ValueError(f"字段 '{field}' 包含非字符串值")
            else:
                if not pd.api.types.is_dtype_equal(self.processed_data[field].dtype, expected_type):
                    raise ValueError(f"字段 '{field}' 类型错误，期望 {expected_type}，"
                                  f"实际为 {self.processed_data[field].dtype}")

        # 验证数据逻辑（使用配置化的限制）
        max_duration = DATA_CLEANING_CONFIG['duration_limits']['max_hours']

        validations = [
            (self.processed_data['entry_time'] <= self.processed_data['exit_time'],
             "存在出场时间早于入场时间的记录"),
            (self.processed_data['duration'] >= 0,
             "存在负数停车时长"),
            (self.processed_data['duration'] <= max_duration,
             f"存在超过{max_duration/24:.0f}天的停车记录"),
            # 移除车牌号长度验证，因为在数据清理阶段已经处理了短车牌号
            # (self.processed_data['vid'].str.len() >= min_vid_length,
            #  f"存在长度小于{min_vid_length}的车牌号"),
            (~self.processed_data['vid'].str.contains(r'[^A-Z0-9\u4e00-\u9fff]'),
             "车牌号包含非法字符 (只允许中文、字母和数字)")
        ]

        for condition, error_message in validations:
            invalid_count = (~condition).sum()
            if invalid_count > 0:
                if self.logger:
                    self.logger.error(f"{error_message}: {invalid_count}条记录")
                raise ValueError(error_message)

        # 验证必需字段的数据完整性
        required_fields = [field for field, validation in FIELD_VALIDATION.items()
                         if validation.get('required', False)]
        if required_fields:  # 只在有必需字段时进行验证
            null_counts = self.processed_data[required_fields].isnull().sum()
            if null_counts.any():
                null_fields = null_counts[null_counts > 0].index.tolist()
                raise ValueError(f"以下必需字段存在空值: {null_fields}")
    
    def _handle_data_anomalies(self):
        """处理数据异常情况，标记可疑数据"""
        if self.processed_data is None or self.processed_data.empty:
            return

        # 根据模式选择不同的异常处理策略
        if self.params.get('mode') == 'mode1_simple':
            self._handle_data_anomalies_simple()
        else:
            self._handle_data_anomalies_regular()

    def _handle_data_anomalies_simple(self):
        """mode1_simple模式的数据异常处理"""
        if self.processed_data is None or self.processed_data.empty:
            return

        # 创建数据质量标记列
        self.processed_data['data_quality'] = 'normal'  # 默认质量标记

        # mode1_simple模式下的异常检测：
        # 1. 不检测停车时长相关异常（因为没有车辆追踪）
        # 2. 不检测车牌号相关异常（因为没有车牌号）
        # 3. 只检测基本的数据质量问题

        # 检测时间异常（如果有timestamp字段）
        if 'timestamp' in self.processed_data.columns:
            # 标记时间格式异常的记录
            try:
                pd.to_datetime(self.processed_data['timestamp'])
            except:
                # 如果时间转换失败，标记为异常
                self.processed_data.loc[:, 'data_quality'] = 'suspicious_time'

        # 检测方向字段异常
        if 'direction' in self.processed_data.columns:
            # 获取进出标识值
            entry_exit_values = self.params.get('进出标识值', ('入场', '出场'))
            if isinstance(entry_exit_values, tuple):
                entry_exit_values = list(entry_exit_values)

            # 标记方向值不在预期范围内的记录
            invalid_direction = ~self.processed_data['direction'].isin(entry_exit_values)
            self.processed_data.loc[invalid_direction, 'data_quality'] = 'suspicious_direction'

        # 统计异常数据
        anomaly_count = (self.processed_data['data_quality'] != 'normal').sum()
        if self.logger and anomaly_count > 0:
            self.logger.warning(f"简化模式发现{anomaly_count}条可疑数据记录 "
                              f"({anomaly_count/len(self.processed_data)*100:.2f}%)")

    def _handle_data_anomalies_regular(self):
        """常规模式的数据异常处理"""
        if self.processed_data is None or self.processed_data.empty:
            return

        # 创建数据质量标记列
        self.processed_data['data_quality'] = 'normal'  # 默认质量标记

        # 使用配置化的异常检测参数
        suspicious_min = DATA_CLEANING_CONFIG['anomaly_detection']['suspicious_duration_min']
        suspicious_max = DATA_CLEANING_CONFIG['anomaly_detection']['suspicious_duration_max']
        min_vid_length = DATA_CLEANING_CONFIG['vehicle_id_limits']['min_length']
        min_interval_minutes = DATA_CLEANING_CONFIG['time_interval_limits']['min_interval_minutes']

        # 标记可疑的停车时长
        suspicious_duration = (
            (self.processed_data['duration'] < suspicious_min) |
            (self.processed_data['duration'] > suspicious_max)
        )
        self.processed_data.loc[suspicious_duration, 'data_quality'] = 'suspicious_duration'

        # 标记可疑的车牌号
        suspicious_vid = self.processed_data['vid'].str.len() < min_vid_length
        self.processed_data.loc[suspicious_vid, 'data_quality'] = 'suspicious_vid'

        # 标记同一车辆短时间内多次进出
        self.processed_data = self.processed_data.sort_values(['vid', 'entry_time'])
        time_diffs = self.processed_data.groupby('vid')['entry_time'].diff()
        suspicious_interval = time_diffs < pd.Timedelta(minutes=min_interval_minutes)
        self.processed_data.loc[suspicious_interval.fillna(False), 'data_quality'] = 'suspicious_interval'

        # 统计异常数据
        anomaly_count = (self.processed_data['data_quality'] != 'normal').sum()
        if self.logger and anomaly_count > 0:
            self.logger.warning(f"发现{anomaly_count}条可疑数据记录 "
                              f"({anomaly_count/len(self.processed_data)*100:.2f}%)")
    
    def process(self):
        """处理数据的主方法"""
        self._validate_and_build_mapping()
        self._standardize_data()
        self._clean_data()
        self._handle_data_anomalies()
        self._validate_processed_data()
        
        if self.logger:
            self.logger.info(f"数据处理完成，共{len(self.processed_data)}条有效记录")
            
        return self.processed_data