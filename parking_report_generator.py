import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from matplotlib.ticker import FuncFormatter
import matplotlib
# 配置matplotlib支持中文显示
matplotlib.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'SimSun', 'Arial Unicode MS']  # 优先使用的中文字体
matplotlib.rcParams['axes.unicode_minus'] = False  # 解决负号显示问题
from io import BytesIO
import xlsxwriter
import os
from datetime import datetime
from parking_time_filter import TimeFilter
from parking_data_processor import DataProcessor
from functools import lru_cache
import logging
import traceback
# 移除对外部配置文件的依赖，使用内置配置
from parking_time_filter import TimeFilter


class ReportGenerator:
    """停车报告生成器，负责生成各类统计报表和图表
    
    注意：文件名有拼写错误，应为'parking_report_generator.py'，
    但为了保持向后兼容性，暂时保留原文件名。
    """
    """停车报告生成器，负责生成各类统计报表和图表"""
    
    # Excel格式配置
    EXCEL_FORMATS = {
        'title': {
            'bold': True,
            'font_size': 14,
            'align': 'center',
            'valign': 'vcenter'
        },
        'header': {
            'bold': True,
            'bg_color': '#D9E1F2',
            'border': 1,
            'align': 'center',
            'valign': 'vcenter',
            'text_wrap': True  # 添加自动换行
        },
        'cell': {
            'border': 1
        },
        'highlight': {
            'border': 1,
            'bg_color': '#FFEB9C',
            'bold': True
        }
    }
    
    # 默认分析参数
    DEFAULT_PARAMS = {
        'mode': '',
        'time_interval': 60,  # 分钟
        'time_slip': 60,      # 分钟
        'duration_bins': [
            (0, 1, '0-1h'), (1, 2, '1-2h'), (2, 3, '2-3h'), (3, 4, '3-4h'),
            (4, 5, '4-5h'), (5, 6, '5-6h'), (6, 7, '6-7h'), (7, 8, '7-8h'),
            (8, 9, '8-9h'), (9, 10, '9-10h'), (10, 11, '10-11h'), (11, 12, '11-12h'),
            (12, 13, '12-13h'), (13, 14, '13-14h'), (14, 15, '14-15h'), (15, 16, '15-16h'),
            (16, 17, '16-17h'), (17, 18, '17-18h'), (18, 19, '18-19h'), (19, 20, '19-20h'),
            (20, 21, '20-21h'), (21, 22, '21-22h'), (22, 23, '22-23h'), (23, 24, '23-24h'),
            (24, float('inf'), '>=24h')
        ]
    }
    
    def __init__(self, data, analysis_results, params=None, logger=None, config_manager=None, input_total_records=None, processed_data=None):
        """初始化报告生成器

        参数:
            data: DataFrame - 经过时间过滤的停车数据
            analysis_results: dict - 分析结果
            params: dict - 参数配置(可选)
            logger: Logger - 日志记录器(可选)
            config_manager: ConfigManager - 配置管理器(可选)
            input_total_records: int - 输入文件的记录总数(可选)
            processed_data: DataFrame - 全周期清洗后的数据(可选)
        """
        self.data = data  # 经过时间过滤的数据
        self.processed_data = processed_data if processed_data is not None else data  # 全周期清洗后的数据
        self.analysis_results = analysis_results
        # 合并参数，用户参数优先
        default_params = self.DEFAULT_PARAMS.copy()
        user_params = params or {}
        self.params = {**default_params, **user_params}
        self.logger = logger

        if config_manager:
            self.logger = config_manager.get_logger()
            report_config = config_manager.get_report_config()
            self.params.update({k: v for k, v in report_config.items() if k not in self.params})

        self.plot_figures = {}
        self._filtered_data_cache = None
        self._vehicle_types_cache = None
        self.original_total_records = input_total_records if input_total_records is not None else analysis_results.get('overview', {}).get('original_total_records', 0)

        # 初始化字段映射
        self._init_field_mapping()

    def _init_field_mapping(self):
        """初始化字段映射"""
        try:
            # 使用简化的字段映射逻辑，不依赖外部配置文件
            STANDARDIZED_FIELDS = {
                'entry_time': 'entry_time',
                'exit_time': 'exit_time',
                'entry_gate': 'entry_gate',
                'exit_gate': 'exit_gate',
                'vtype': 'vtype',
                'duration': 'duration'
            }

            # 检查数据是否已经标准化
            if self._is_data_standardized():
                # 数据已标准化，直接使用标准字段名
                self.field_mapping = STANDARDIZED_FIELDS.copy()
                self._log_info("数据已标准化，使用标准字段映射")
            else:
                # 数据未标准化，使用默认字段映射
                self.field_mapping = STANDARDIZED_FIELDS.copy()
                self._log_info("数据未标准化，使用默认字段映射")

            self._log_info(f"字段映射初始化完成: {self.field_mapping}")

        except Exception as e:
            self._log_warning(f"字段映射初始化失败: {e}")
            # 使用默认字段映射
            self.field_mapping = {
                'entry_time': 'entry_time',
                'exit_time': 'exit_time',
                'entry_gate': 'entry_gate',
                'exit_gate': 'exit_gate',
                'vtype': 'vtype',
                'duration': 'duration'
            }

    def _is_data_standardized(self):
        """检查数据是否已经标准化"""
        # 检查是否所有标准字段都存在
        standard_fields = ['entry_time', 'exit_time', 'entry_gate', 'exit_gate', 'vtype']
        return all(field in self.data.columns for field in standard_fields)

    def _get_field_name(self, standard_field):
        """获取标准字段对应的实际字段名"""
        # 如果数据已标准化，直接返回标准字段名
        if self._is_data_standardized():
            return standard_field
        # 否则从字段映射中获取
        return self.field_mapping.get(standard_field, standard_field)

    def _create_excel_formats(self, workbook):
        """创建Excel格式对象"""
        return {k: workbook.add_format(v) for k, v in self.EXCEL_FORMATS.items()}

    def _set_header_row_height(self, worksheet, row_num, min_height=30):
        """设置表头行高度以适应自动换行"""
        try:
            worksheet.set_row(row_num, min_height)
        except Exception as e:
            self._log_warning(f"设置行高失败: {e}")
        
    def _create_overview_sheet(self, writer, workbook):
        """创建概览工作表，包含数据统计和分析结果"""
        try:
            # 设置格式
            formats = self._create_excel_formats(workbook)
            
            # 创建工作表
            worksheet = workbook.add_worksheet('概览')
            
            # 设置列宽
            worksheet.set_column('A:A', 25)
            worksheet.set_column('B:B', 40)
            
            # 初始化行计数器
            row = 0
            
            # 确保analysis_results存在
            if not hasattr(self, 'analysis_results'):
                self.analysis_results = {}
                self._log_warning("analysis_results不存在，已初始化")
            
            # # 确保overview键存在
            if 'overview' not in self.analysis_results:
                self.analysis_results['overview'] = {}

            
            # 获取概览数据
            focus_date = self.params.get('date') or self.params.get('聚焦日期')
            focus_month = self.params.get('month') or self.params.get('聚焦月份')
            overview_data = self._get_overview_data(
                filtered_data=self._get_filtered_data(),
                focus_date=focus_date,
                focus_month=focus_month
            )
            
            # 1. 基本信息区
            worksheet.write(row, 0, '基本信息', formats['header'])
            worksheet.write(row, 1, '', formats['header'])
            row += 1
            
            # 写入基本统计信息
            # 有效记录数应该使用全周期清洗后的数据，而不是时间过滤后的数据
            processed_records_count = len(self.processed_data) if hasattr(self, 'processed_data') and self.processed_data is not None else len(self.data)

            # Mode1模式下，有效记录数应该是数据_总量的2倍，因为一进一出两条记录生成一条数据_总量记录
            current_mode = self.params.get('mode', '')
            if current_mode == 'mode1':
                # Mode1模式：原始记录数应该是处理后记录数的2倍
                effective_records_count = processed_records_count * 2
                data_valid_rate = (effective_records_count / self.original_total_records * 100) if self.original_total_records > 0 else 0
                basic_info = [
                    ('总记录数', self.original_total_records),
                    ('有效记录数', effective_records_count),
                    ('数据有效率', f"{data_valid_rate:.2f}%")
                ]
            else:
                # 其他模式：直接使用处理后的记录数
                data_valid_rate = (processed_records_count / self.original_total_records * 100) if self.original_total_records > 0 else 0
                basic_info = [
                    ('总记录数', self.original_total_records),
                    ('有效记录数', processed_records_count),
                    ('数据有效率', f"{data_valid_rate:.2f}%")
                ]
            
            for label, value in basic_info:
                worksheet.write(row, 0, label, formats['header'])
                worksheet.write(row, 1, value, formats['cell'])
                row += 1
            
            # 如果有聚焦日期，显示时间范围
            if focus_date:
                focus_data = self._get_filtered_data()
                if not focus_data.empty:
                    entry_time_field = self._get_field_name('entry_time')
                    exit_time_field = self._get_field_name('exit_time')

                    time_range_info = [
                        ('分析日记录数', len(focus_data)),
                        ('最早入场时间', focus_data[entry_time_field].min().strftime('%Y-%m-%d %H:%M')),
                        ('最晚出场时间', focus_data[exit_time_field].max().strftime('%Y-%m-%d %H:%M'))
                    ]
                    
                    for label, value in time_range_info:
                        worksheet.write(row, 0, label, formats['header'])
                        worksheet.write(row, 1, value, formats['cell'])
                        row += 1
            
            # 2. 分析结果区
            row += 1
            worksheet.write(row, 0, '分析结果', formats['header'])
            worksheet.write(row, 1, '', formats['header'])
            row += 1
            
            # 从原始数据补充缺失的指标
            duration_field = self._get_field_name('duration')
            vtype_field = self._get_field_name('vtype')

            if duration_field in self.data.columns:
                overview_data = self.analysis_results['overview']
                if 'average_duration' not in overview_data:
                    overview_data['average_duration'] = round(self.data[duration_field].mean(), 2)
                if 'max_duration' not in overview_data:
                    overview_data['max_duration'] = round(self.data[duration_field].max(), 2)
                if 'min_duration' not in overview_data:
                    overview_data['min_duration'] = self.data[duration_field].min()

            if vtype_field in self.data.columns and 'vehicle_distribution' not in overview_data:
                overview_data['vehicle_distribution'] = self.data[vtype_field].value_counts(normalize=True).to_dict()
            
            # 写入分析结果
            metrics = [
                ('平均停车时长', 'average_duration', lambda x: f"{x:.2f}小时"),
                ('最长停车时长', 'max_duration', lambda x: f"{x:.2f}小时"),
                ('最短停车时长', 'min_duration', lambda x: f"{x:.3f}小时"),
                ('车辆类型分布', 'vehicle_distribution',
                 lambda x: ', '.join(f"{k}:{v:.1%}" for k,v in x.items()) if isinstance(x, dict) else str(x))
            ]
            
            for display_name, key, formatter in metrics:
                value = overview_data.get(key)
                if value is not None:
                    worksheet.write(row, 0, display_name, formats['header'])
                    worksheet.write(row, 1, formatter(value), formats['cell'])
                    row += 1
                else:
                    self._log_warning(f"缺少{key}指标数据")
            
            # 3. 参数区
            row += 1
            worksheet.write(row, 0, '分析参数', formats['header'])
            worksheet.write(row, 1, '', formats['header'])
            row += 1
            
            # 写入重要参数
            important_params = [
                ('分析日期', 'date', '聚焦日期', ''),
                ('分析模式', 'mode', '', ''),
                ('时间间隔', 'time_interval', '', '分钟'),
                ('时间滑动', 'time_slip', '', '分钟'),
                ('最小停车时长', 'min_duration_hours', '', '小时')
            ]
            
            for display_name, param_key, alt_key, unit in important_params:
                value = self.params.get(param_key) or self.params.get(alt_key, '')
                if value:
                    # 特殊处理mode1_simple的显示
                    if param_key == 'mode' and value == 'mode1_simple':
                        display_value = f"{value} (简化模式)"
                    else:
                        display_value = f"{value} {unit}" if unit else str(value)
                    worksheet.write(row, 0, display_name, formats['header'])
                    worksheet.write(row, 1, display_value, formats['cell'])
                    row += 1
            
            self._log_info("成功创建概览工作表")

        except Exception as e:
            self._log_warning(f"创建概览工作表失败: {str(e)}")
            # 确保至少创建了工作表
            if 'worksheet' not in locals():
                workbook.add_worksheet('概览')

    def _create_charts_sheet(self, writer, workbook):
        """创建图_分析日工作表，统一放置所有图表"""
        try:
            # 创建工作表
            worksheet = workbook.add_worksheet('图_分析日')

            # 设置格式
            formats = self._create_excel_formats(workbook)

            # 为分析图sheet创建专用格式（不包含自动换行）
            chart_title_format = workbook.add_format({
                'bold': True,
                'bg_color': '#D9E1F2',
                'border': 1,
                'align': 'center',
                'valign': 'vcenter'
                # 注意：这里没有 text_wrap: True
            })

            # 写入标题
            worksheet.write(0, 0, '停车数据分析图表', formats['title'])

            # 获取已创建的工作表名称列表
            existing_sheets = [sheet.name for sheet in workbook.worksheets()]

            # 动态生成图表配置，只包含存在对应工作表的图表
            chart_configs = []

            # 进出量时间分布图表（总是存在）
            if '进出量时间分布_分析日' in existing_sheets:
                chart_configs.append({
                    'title': '各时段进出场数量分布',
                    'type': 'peak_flow',
                    'position': (2, 0),  # 行, 列
                    'size': {'width': 600, 'height': 400}
                })

            # 停车时长分布图表（mode1_simple模式下跳过）
            if '停车时长_分析日' in existing_sheets:
                chart_configs.append({
                    'title': '停车时长分布',
                    'type': 'duration_distribution',
                    'position': (2, 10),  # 行, 列
                    'size': {'width': 600, 'height': 400}
                })

            # 在场车辆分布图表（mode1_simple模式下跳过）
            if '在场车辆分布' in existing_sheets:
                chart_configs.append({
                    'title': '在场车辆分布',
                    'type': 'occupancy',
                    'position': (22, 0),  # 行, 列
                    'size': {'width': 600, 'height': 400}
                })

            # 每日统计趋势图表（总是存在）
            if '日进出量_分析周期' in existing_sheets:
                chart_configs.append({
                    'title': '每日统计趋势',
                    'type': 'daily_stats',
                    'position': (22, 10),  # 行, 列
                    'size': {'width': 600, 'height': 400}
                })

            # 延停时长概率密度图表（mode1_simple模式下跳过）
            if '延停时长概率密度' in existing_sheets:
                chart_configs.append({
                    'title': '延停时长概率密度分布',
                    'type': 'duration_probability_density',
                    'position': (42, 0),  # 行, 列
                    'size': {'width': 600, 'height': 400}
                })

            # 道闸时间分布图表
            if '进出量-道闸时间分布_分析日' in existing_sheets:
                chart_configs.append({
                    'title': '道闸进出量时间分布',
                    'type': 'gate_time_distribution',
                    'position': (42, 0),  # 行, 列
                    'size': {'width': 600, 'height': 400}
                })

            # 车型道闸时间分布图表
            if '进出量时间分布_车型_道闸' in existing_sheets:
                chart_configs.append({
                    'title': '车型道闸进出量分布',
                    'type': 'vehicle_gate_distribution',
                    'position': (42, 10),  # 行, 列
                    'size': {'width': 600, 'height': 400}
                })

            # 创建并插入图表
            for config in chart_configs:
                try:
                    chart = self._create_chart_for_sheet(workbook, config)
                    if chart:
                        # 插入图表
                        row, col = config['position']
                        worksheet.insert_chart(row, col, chart, {
                            'x_scale': config['size']['width'] / 480,  # 默认图表宽度480
                            'y_scale': config['size']['height'] / 288  # 默认图表高度288
                        })

                        # 在图表上方写入标题（使用不包含自动换行的格式）
                        worksheet.write(row - 1, col, config['title'], chart_title_format)

                except Exception as e:
                    self._log_warning(f"创建图表 {config['title']} 失败: {str(e)}")
                    continue

            # 如果是mode1_simple模式，添加说明
            if self.params.get('mode') == 'mode1_simple':
                worksheet.write(1, 0, '简化模式：部分图表因缺少车辆追踪数据而跳过', formats['cell'])

            self._log_info("成功创建分析图工作表")

        except Exception as e:
            self._log_warning(f"创建分析图工作表失败: {str(e)}")
            # 确保至少创建了工作表
            if 'worksheet' not in locals():
                workbook.add_worksheet('图_分析日')

    def _create_chart_for_sheet(self, workbook, config):
        """为分析图sheet创建图表"""
        try:
            chart_type = config['type']

            # 获取已创建的工作表名称列表
            existing_sheets = [sheet.name for sheet in workbook.worksheets()]

            if chart_type == 'peak_flow':
                if '进出量时间分布_分析日' in existing_sheets:
                    return self._create_peak_flow_chart(workbook)
            elif chart_type == 'duration_distribution':
                if '停车时长_分析日' in existing_sheets:
                    return self._create_duration_chart(workbook)
            elif chart_type == 'occupancy':
                if '在场车辆分布' in existing_sheets:
                    return self._create_occupancy_chart(workbook)
            elif chart_type == 'daily_stats':
                if '日进出量_分析周期' in existing_sheets:
                    return self._create_daily_stats_chart(workbook)
            elif chart_type == 'duration_probability_density':
                if '延停时长概率密度' in existing_sheets:
                    return self._create_duration_probability_density_chart(workbook)
            elif chart_type == 'gate_time_distribution':
                if '进出量-道闸时间分布_分析日' in existing_sheets:
                    return self._create_gate_time_distribution_chart(workbook)
            elif chart_type == 'vehicle_gate_distribution':
                if '进出量时间分布_车型_道闸' in existing_sheets:
                    return self._create_vehicle_gate_distribution_chart(workbook)

            return None

        except Exception as e:
            self._log_warning(f"创建图表失败: {str(e)}")
            return None

    def _create_peak_flow_chart(self, workbook):
        """创建进出量时间分布图表"""
        try:
            # 计算高峰流量数据
            peak_flow = self._calculate_peak_flow()
            if peak_flow.empty:
                return None

            # 创建组合图表
            chart = workbook.add_chart({'type': 'column'})

            # 添加进场数据系列
            chart.add_series({
                'name': '进场数量',
                'categories': ['进出量时间分布_分析日', 1, 0, len(peak_flow), 0],
                'values': ['进出量时间分布_分析日', 1, 1, len(peak_flow), 1],
                'fill': {'color': '#4F81BD'},
            })

            # 添加出场数据系列
            chart.add_series({
                'name': '出场数量',
                'categories': ['进出量时间分布_分析日', 1, 0, len(peak_flow), 0],
                'values': ['进出量时间分布_分析日', 1, 2, len(peak_flow), 2],
                'fill': {'color': '#C0504D'},
            })

            # 添加总流量线形图
            line_chart = workbook.add_chart({'type': 'line'})
            line_chart.add_series({
                'name': '总流量',
                'categories': ['进出量时间分布_分析日', 1, 0, len(peak_flow), 0],
                'values': ['进出量时间分布_分析日', 1, 3, len(peak_flow), 3],
                'line': {'color': '#9BBB59', 'width': 2.5},
                'marker': {'type': 'diamond', 'size': 5},
            })

            # 组合图表
            chart.combine(line_chart)

            # 设置图表标题和轴标签
            chart.set_title({'name': '各时段进出场数量及总流量'})
            chart.set_x_axis({
                'name': '时间段',
                'text_axis': True,
                'num_font': {'rotation': -45}
            })
            chart.set_y_axis({'name': '车辆数量'})

            return chart

        except Exception as e:
            self._log_warning(f"创建进出量时间分布图表失败: {str(e)}")
            return None

    def _create_duration_chart(self, workbook):
        """创建停车时长分布图表"""
        try:
            # 计算停车时长分布数据
            parking_stats = self.calculate_parking_stats()
            if parking_stats.empty:
                return None

            # 创建柱状图
            chart = workbook.add_chart({'type': 'column'})

            # 添加总量数据系列
            chart.add_series({
                'name': '总量',
                'categories': ['停车时长_分析日', 1, 0, len(parking_stats), 0],
                'values': ['停车时长_分析日', 1, 1, len(parking_stats), 1],
                'fill': {'color': '#4F81BD'},
            })

            # 如果有车辆类型数据，添加额外的数据系列
            columns = parking_stats.columns.tolist()
            if len(columns) > 2:  # 除了时长和总量列外还有其他列
                colors = ['#C0504D', '#9BBB59', '#8064A2', '#F79646']  # 不同颜色
                for i in range(2, min(len(columns), 6)):  # 最多显示4种车型
                    color = colors[(i-2) % len(colors)]
                    chart.add_series({
                        'name': ['停车时长_分析日', 0, i],
                        'categories': ['停车时长_分析日', 1, 0, len(parking_stats), 0],
                        'values': ['停车时长_分析日', 1, i, len(parking_stats), i],
                        'fill': {'color': color},
                    })

            # 设置图表标题和轴标签
            chart.set_title({'name': '停车时长分布'})
            chart.set_x_axis({'name': '停车时长'})
            chart.set_y_axis({'name': '车辆数量'})

            return chart

        except Exception as e:
            self._log_warning(f"创建停车时长分布图表失败: {str(e)}")
            return None

    def _create_occupancy_chart(self, workbook):
        """创建在场车辆分布图表"""
        try:
            # 计算在场车辆分布数据
            focus_date = self.params.get('date') or self.params.get('聚焦日期')
            focus_month = self.params.get('month') or self.params.get('聚焦月份')
            occupancy = self._calculate_occupancy(self.data, focus_date, focus_month)

            if occupancy.empty:
                return None

            # 创建透视表以便图表使用
            if '车辆类型' in occupancy.columns:
                pivot_occupancy = occupancy.pivot_table(
                    index='时间',
                    columns='车辆类型',
                    values='在场车辆数',
                    fill_value=0
                ).reset_index()
            else:
                pivot_occupancy = occupancy[['时间', '在场车辆数']].copy()

            # 创建线形图
            chart = workbook.add_chart({'type': 'line'})

            # 添加数据系列
            colors = ['#4F81BD', '#C0504D', '#9BBB59', '#8064A2']
            for i, col in enumerate(pivot_occupancy.columns[1:], start=1):
                color = colors[(i-1) % len(colors)]
                chart.add_series({
                    'name': col,
                    'categories': ['在场车辆分布', 1, 0, len(pivot_occupancy), 0],
                    'values': ['在场车辆分布', 1, i, len(pivot_occupancy), i],
                    'line': {'color': color, 'width': 2},
                    'marker': {'type': 'automatic'},
                })

            # 设置图表标题和轴标签
            title_text = f"在场车辆分布统计 ({focus_date if focus_date else '日均'})"
            chart.set_title({'name': title_text})
            chart.set_x_axis({'name': '时间'})
            chart.set_y_axis({'name': '在场车辆数'})

            return chart

        except Exception as e:
            self._log_warning(f"创建在场车辆分布图表失败: {str(e)}")
            return None

    def _create_daily_stats_chart(self, workbook):
        """创建每日统计图表"""
        try:
            # 计算每日统计数据 - 使用全周期清洗后的数据（与数据_总量sheet保持一致）
            daily_stats = self._calculate_daily_stats(data=self.processed_data)
            if daily_stats.empty:
                return None

            # 创建线形图
            chart = workbook.add_chart({'type': 'line'})

            # 添加进场数量系列
            chart.add_series({
                'name': '进场数量',
                'categories': ['日进出量_分析周期', 1, 0, len(daily_stats), 0],
                'values': ['日进出量_分析周期', 1, 1, len(daily_stats), 1],
                'line': {'color': '#4F81BD', 'width': 2},
                'marker': {'type': 'circle', 'size': 5},
            })

            # 添加出场数量系列
            chart.add_series({
                'name': '出场数量',
                'categories': ['日进出量_分析周期', 1, 0, len(daily_stats), 0],
                'values': ['日进出量_分析周期', 1, 2, len(daily_stats), 2],
                'line': {'color': '#C0504D', 'width': 2},
                'marker': {'type': 'square', 'size': 5},
            })

            # 添加总流量系列
            chart.add_series({
                'name': '总流量',
                'categories': ['日进出量_分析周期', 1, 0, len(daily_stats), 0],
                'values': ['日进出量_分析周期', 1, 3, len(daily_stats), 3],
                'line': {'color': '#9BBB59', 'width': 3},
                'marker': {'type': 'diamond', 'size': 6},
            })

            # 设置图表标题和轴标签
            chart.set_title({'name': '每日进出场数量统计'})
            chart.set_x_axis({'name': '日期'})
            chart.set_y_axis({'name': '车辆数量'})

            return chart

        except Exception as e:
            self._log_warning(f"创建每日统计图表失败: {str(e)}")
            return None

    def _create_duration_probability_density_chart(self, workbook):
        """创建延停时长概率密度图表"""
        try:
            # 计算延停时长概率密度数据
            density_stats = self._calculate_duration_probability_density()
            if density_stats.empty:
                return None

            # 创建组合图表（柱状图 + 折线图）
            chart = workbook.add_chart({'type': 'column'})

            # 添加频数柱状图系列
            chart.add_series({
                'name': '频数',
                'categories': ['延停时长概率密度', 1, 0, len(density_stats), 0],
                'values': ['延停时长概率密度', 1, 1, len(density_stats), 1],
                'fill': {'color': '#5470c6'},
                'border': {'color': '#4F81BD'},
                'data_labels': {'value': True, 'position': 'outside_end'}
            })

            # 创建次坐标轴用于百分比
            chart2 = workbook.add_chart({'type': 'line'})

            # 添加累积百分比折线图系列
            if '累积百分比(%)' in density_stats.columns:
                # 获取累积百分比列的索引
                cumulative_col_index = list(density_stats.columns).index('累积百分比(%)')
                chart2.add_series({
                    'name': '累积百分比',
                    'categories': ['延停时长概率密度', 1, 0, len(density_stats), 0],
                    'values': ['延停时长概率密度', 1, cumulative_col_index, len(density_stats), cumulative_col_index],
                    'line': {'color': '#ee6666', 'width': 3},
                    'marker': {'type': 'circle', 'size': 5, 'fill': {'color': '#ee6666'}},
                    'y2_axis': True
                })

            # 组合两个图表
            chart.combine(chart2)

            # 设置图表标题和轴标签
            focus_date = self.params.get('聚焦日期', '')
            title_text = f"延停时长概率密度分布 ({focus_date if focus_date else '全期'})"
            chart.set_title({'name': title_text, 'name_font': {'size': 14, 'bold': True}})

            # 设置主Y轴（频数）
            chart.set_y_axis({
                'name': '频数',
                'name_font': {'size': 12},
                'num_font': {'size': 10},
                'major_gridlines': {'visible': True, 'line': {'color': '#E0E0E0'}}
            })

            # 设置次Y轴（百分比）
            chart.set_y2_axis({
                'name': '累积百分比(%)',
                'name_font': {'size': 12},
                'num_font': {'size': 10},
                'max': 100
            })

            # 设置X轴
            chart.set_x_axis({
                'name': '停车时长区间',
                'name_font': {'size': 12},
                'num_font': {'size': 9},
                'text_axis': True,  # 文本轴，适合时长区间标签
                'label_position': 'low'
            })

            # 设置图例
            chart.set_legend({
                'position': 'top',
                'font': {'size': 10}
            })

            # 设置图表样式
            chart.set_style(10)  # 使用预定义样式
            chart.set_size({'width': 720, 'height': 480})

            return chart

        except Exception as e:
            if hasattr(self, '_log_warning'):
                self._log_warning(f"创建延停时长概率密度图表失败: {str(e)}")
            return None

    def _create_gate_time_distribution_chart(self, workbook):
        """创建道闸时间分布图表"""
        try:
            # 读取道闸时间分布数据
            import pandas as pd

            # 从Excel工作表中读取数据（这里假设数据已经写入）
            # 实际应用中，我们需要从计算结果中获取数据
            gate_data = self._calculate_gate_time_distribution_for_chart()
            if gate_data.empty:
                return None

            # 创建柱状图
            chart = workbook.add_chart({'type': 'column'})

            # 获取道闸列表（除了时间段列）
            gate_columns = [col for col in gate_data.columns if col != '时间段' and '_进' in col]
            gates = list(set([col.split('_')[0] for col in gate_columns]))

            # 为每个道闸添加进场和出场数据系列
            colors = ['#4F81BD', '#C0504D', '#9BBB59', '#8064A2', '#F79646']
            for i, gate in enumerate(gates[:3]):  # 最多显示3个道闸
                entry_col = f'{gate}_进'
                exit_col = f'{gate}_出'

                if entry_col in gate_data.columns:
                    chart.add_series({
                        'name': f'{gate}_进场',
                        'categories': ['进出量-道闸时间分布_分析日', 1, 0, len(gate_data), 0],
                        'values': ['进出量-道闸时间分布_分析日', 1, gate_data.columns.get_loc(entry_col), len(gate_data), gate_data.columns.get_loc(entry_col)],
                        'fill': {'color': colors[i*2 % len(colors)]},
                    })

                if exit_col in gate_data.columns:
                    chart.add_series({
                        'name': f'{gate}_出场',
                        'categories': ['进出量-道闸时间分布_分析日', 1, 0, len(gate_data), 0],
                        'values': ['进出量-道闸时间分布_分析日', 1, gate_data.columns.get_loc(exit_col), len(gate_data), gate_data.columns.get_loc(exit_col)],
                        'fill': {'color': colors[i*2+1 % len(colors)]},
                    })

            # 设置图表标题和轴标签
            chart.set_title({'name': '各道闸进出量时间分布'})
            chart.set_x_axis({
                'name': '时间段',
                'text_axis': True,
                'num_font': {'rotation': -45}
            })
            chart.set_y_axis({'name': '车辆数量'})

            return chart

        except Exception as e:
            self._log_warning(f"创建道闸时间分布图表失败: {str(e)}")
            return None

    def _create_vehicle_gate_distribution_chart(self, workbook):
        """创建车型道闸分布图表"""
        try:
            # 计算车型道闸分布数据
            vehicle_gate_data = self._calculate_vehicle_gate_time_distribution()
            if vehicle_gate_data.empty:
                return None

            # 创建柱状图
            chart = workbook.add_chart({'type': 'column'})

            # 获取前几个数据列（除了时间段列）
            data_columns = [col for col in vehicle_gate_data.columns if col != '时间段'][:6]  # 最多显示6个系列

            # 为每个车型道闸组合添加数据系列
            colors = ['#4F81BD', '#C0504D', '#9BBB59', '#8064A2', '#F79646', '#4BACC6']
            for i, col in enumerate(data_columns):
                chart.add_series({
                    'name': col,
                    'categories': ['进出量时间分布_车型_道闸', 1, 0, len(vehicle_gate_data), 0],
                    'values': ['进出量时间分布_车型_道闸', 1, vehicle_gate_data.columns.get_loc(col), len(vehicle_gate_data), vehicle_gate_data.columns.get_loc(col)],
                    'fill': {'color': colors[i % len(colors)]},
                })

            # 设置图表标题和轴标签
            chart.set_title({'name': '车型道闸进出量分布'})
            chart.set_x_axis({
                'name': '时间段',
                'text_axis': True,
                'num_font': {'rotation': -45}
            })
            chart.set_y_axis({'name': '车辆数量'})

            return chart

        except Exception as e:
            self._log_warning(f"创建车型道闸分布图表失败: {str(e)}")
            return None

    def _calculate_gate_time_distribution_for_chart(self):
        """为图表计算道闸时间分布数据"""
        try:
            # 生成时间段
            time_periods = self._generate_time_periods(self.data)

            # 获取道闸信息
            mode = self.params.get('mode', 'mode1')
            if mode == 'mode1':
                gate_field = self.params.get('道闸编号字段', 'gate_id')
                if gate_field not in self.data.columns:
                    return pd.DataFrame()
                gates = sorted(self.data[gate_field].dropna().unique())
            else:
                entry_gate_field = self.params.get('进场道闸字段', 'entry_gate')
                exit_gate_field = self.params.get('出场道闸字段', 'exit_gate')
                if entry_gate_field not in self.data.columns or exit_gate_field not in self.data.columns:
                    return pd.DataFrame()
                entry_gates = set(self.data[entry_gate_field].dropna().unique())
                exit_gates = set(self.data[exit_gate_field].dropna().unique())
                gates = sorted(entry_gates | exit_gates)

            # 初始化结果数据
            result_data = []

            # 按时间段统计
            for period in time_periods:
                start_time_str, end_time_str = period.split('-')
                start_time = pd.to_datetime(start_time_str).time()
                end_time = pd.to_datetime(end_time_str).time()

                # 筛选当前时间段的数据
                if start_time <= end_time:
                    period_mask = (
                        (self.data['entry_time'].dt.time >= start_time) &
                        (self.data['entry_time'].dt.time < end_time)
                    )
                else:
                    period_mask = (
                        (self.data['entry_time'].dt.time >= start_time) |
                        (self.data['entry_time'].dt.time < end_time)
                    )

                period_data = self.data[period_mask]

                # 初始化当前时间段的统计结果
                row_data = {'时间段': period}

                # 按道闸统计
                for gate in gates:
                    if mode == 'mode1':
                        gate_data = period_data[period_data[gate_field] == gate]
                        entry_count = len(gate_data)

                        exit_mask = (
                            (~gate_data['exit_time'].isna()) &
                            (gate_data['exit_time'].dt.time >= start_time) &
                            (gate_data['exit_time'].dt.time < end_time)
                        ) if start_time <= end_time else (
                            (~gate_data['exit_time'].isna()) &
                            ((gate_data['exit_time'].dt.time >= start_time) |
                             (gate_data['exit_time'].dt.time < end_time))
                        )
                        exit_count = exit_mask.sum()
                    else:
                        # mode2模式
                        entry_gate_data = period_data[period_data[entry_gate_field] == gate]
                        entry_count = len(entry_gate_data)

                        exit_mask = (
                            (~period_data['exit_time'].isna()) &
                            (period_data[exit_gate_field] == gate)
                        )
                        if start_time <= end_time:
                            exit_mask = exit_mask & (
                                (period_data['exit_time'].dt.time >= start_time) &
                                (period_data['exit_time'].dt.time < end_time)
                            )
                        else:
                            exit_mask = exit_mask & (
                                (period_data['exit_time'].dt.time >= start_time) |
                                (period_data['exit_time'].dt.time < end_time)
                            )
                        exit_count = exit_mask.sum()

                    row_data[f'{gate}_进'] = entry_count
                    row_data[f'{gate}_出'] = exit_count
                    row_data[f'{gate}_总量'] = entry_count + exit_count

                result_data.append(row_data)

            return pd.DataFrame(result_data)

        except Exception as e:
            self._log_error(f"计算道闸时间分布数据失败: {str(e)}")
            return pd.DataFrame()

    def _generate_duration_bins(self, max_duration=None, bin_size=1/4, max_bins=97):
        """
        动态生成停车时长分段配置，格式为[(start, end, label), ...]

        参数:
            max_duration: float - 最大时长(小时)，如果为None则使用数据中的最大值或默认值24
            bin_size: float - 每个分段的大小(小时)，如果为None则使用self.params中的值或默认值1
            max_bins: int - 最大分段数量，如果为None则使用self.params中的值或默认值25

        返回:
            list - 时长分段配置列表，格式为[(start, end, label), ...]

        示例输出（使用默认参数 bin_size=1, max_duration=24）:
        [
            (0, 1, '0-1h'), (1, 2, '1-2h'), ..., (23, 24, '23-24h'), (24, inf, '>=24h')
        ]
        """
        try:
            # 获取参数，优先使用传入参数，然后是配置参数，最后是默认值
            bin_size = bin_size if bin_size is not None else self.params.get('duration_bin_size', 1.0)
            max_bins = max_bins if max_bins is not None else self.params.get('duration_max_bins', 25)

            # 确定最大时长
            if max_duration is None:
                # 尝试从数据中获取最大时长
                if hasattr(self, 'data') and self.data is not None and not self.data.empty and 'duration' in self.data.columns:
                    # 只使用有效的duration数据
                    valid_durations = self.data['duration'][self.data['duration'].notna() & (self.data['duration'] >= 0)]
                    if not valid_durations.empty:
                        data_max_duration = valid_durations.max()
                        # 向上取整到最近的bin_size倍数，确保覆盖所有数据
                        max_duration = np.ceil(data_max_duration / bin_size) * bin_size
                        # 设置合理的上限，但允许根据数据调整
                        default_max = self.params.get('duration_max_hours', 24.0)
                        max_duration = max(default_max, max_duration)
                    else:
                        max_duration = self.params.get('duration_max_hours', 24.0)
                else:
                    max_duration = self.params.get('duration_max_hours', 24.0)

            # 生成时长分段
            duration_bins = []
            current_start = 0.0
            bin_count = 0

            while current_start < max_duration and bin_count < max_bins - 1:
                current_end = current_start + bin_size

                # 生成标签
                if current_end <= max_duration:
                    if bin_size == 1.0:
                        # 整数小时的标签格式
                        label = f"{int(current_start)}-{int(current_end)}h"
                    else:
                        # 小数小时的标签格式
                        label = f"{current_start:.2f}-{current_end:.2f}h"
                else:
                    # 最后一个完整分段
                    if bin_size == 1.0:
                        label = f"{int(current_start)}-{int(max_duration)}h"
                    else:
                        label = f"{current_start:.2f}-{max_duration:.2f}h"
                    current_end = max_duration

                duration_bins.append((current_start, current_end, label))
                current_start = current_end
                bin_count += 1

            # 添加最后一个开放式分段（>=max_duration）
            if max_duration < float('inf'):
                if bin_size == 1.0:
                    label = f">={int(max_duration)}h"
                else:
                    label = f">={max_duration:.2f}h"
                duration_bins.append((max_duration, float('inf'), label))

            self._log_info(f"生成了 {len(duration_bins)} 个时长分段，分段大小: {bin_size}h，最大时长: {max_duration}h")
            return duration_bins

        except Exception as e:
            self._log_warning(f"动态生成时长分段失败: {e}，使用默认配置")
            # 返回默认的时长分段配置
            return self.DEFAULT_PARAMS['duration_bins']

    def _generate_time_periods(self, data, slip_time=None, interval_minutes=None):
        """
        生成时间段列表，格式为["HH:MM-HH:MM", ...]
        
        参数:
            data: DataFrame - 停车数据
            slip_time: int - 滑动步长(分钟)，如果为None则使用self.params中的值或默认值60
            interval_minutes: int - 每个时间段的长度(分钟)，如果为None则使用self.params中的值或默认值60
        
        示例输出（使用默认参数 time_interval=60, time_slip=15）:
        [
            "00:00-01:00", "00:15-01:15", "00:30-01:30", "00:45-01:45",
            "01:00-02:00", ..., "22:45-23:45", "23:00-00:00"
        ]
        """
        try:
            # 如果slip_time和interval_minutes都是60（整点小时），直接生成24小时的时间段
            if (slip_time == 60 or (slip_time is None and self.params.get('time_slip', 60) == 60)) and \
               (interval_minutes == 60 or (interval_minutes is None and self.params.get('time_interval', 60) == 60)):
                self._log_info("使用标准24小时时间段")
                # 生成标准的24小时时间段
                periods = []
                for h in range(24):
                    if h == 23:
                        # 特殊处理23点时段，结束时间设为23:59而不是00:00
                        # 这样避免跨天的时间处理问题
                        periods.append(f"{h:02d}:00-23:59")
                    else:
                        periods.append(f"{h:02d}:00-{(h+1):02d}:00")
                return periods
            
            # 其他情况使用TimeFilter的分段时间方法
            time_filter = TimeFilter(data, self.params)
            periods = time_filter.split_time_period(
                start_time="00:00",
                end_time="23:59",
                slip_time=slip_time if slip_time is not None else self.params.get('time_slip', 60),
                interval_minutes=interval_minutes if interval_minutes is not None else self.params.get('time_interval', 60)
            )
            
            if len(periods) <= 1:
                self._log_warning(f"只生成了{len(periods)}个时间段，检查时间间隔参数")
                # 默认生成24小时的时间段
                periods = [f"{h:02d}:00-{(h+1)%24:02d}:00" for h in range(24)]
            
            # 检查是否包含23:00开头的时间段
            has_23_hour = any(period.startswith("23:00") for period in periods)
            if not has_23_hour:
                self._log_warning("生成的时间段中缺少23:00时段，添加23:00-23:59")
                periods.append("23:00-23:59")
            
            return periods
        except ValueError as e:
            self._log_warning(f"时间段分割失败: {e}")
            # 默认生成24小时的时间段
            periods = [f"{h:02d}:00-{(h+1)%24:02d}:00" for h in range(24)]
            return periods

    def _calculate_peak_flow(self, data=None):
        """
        计算高峰流量
        
        参数:
            data: DataFrame - 停车数据(可选)
            
        返回:
            DataFrame - 高峰流量统计，包含:
                - 各时段进出场数量
                - 按车辆类型统计
                - 高峰时段标记
        """
        try:
            data = data if data is not None else self.data
            if data.empty:
                return pd.DataFrame()
            
            # 生成时间段 - 使用TimeFilter的split_time_period方法
            time_periods = self._generate_time_periods(data)

            # 获取字段名称
            entry_time_field = self._get_field_name('entry_time')
            exit_time_field = self._get_field_name('exit_time')
            vtype_field = self._get_field_name('vtype')

            # 获取基准日期
            base_date = data[entry_time_field].min().date() if not data.empty else datetime.now().date()

            # 获取车辆类型
            vehicle_types = self._get_vehicle_types(data) if vtype_field in data.columns else []
            
            # 初始化结果DataFrame
            peak_flow = pd.DataFrame({'时段': time_periods})
            
            # 统计每个时间段的进出场数据
            for period in time_periods:
                start_time_str, end_time_str = period.split('-')
                
                # 转换时间段为datetime
                start_dt = datetime.strptime(start_time_str, '%H:%M').time()
                end_dt = datetime.strptime(end_time_str, '%H:%M').time()
                
                # 筛选该时间段的进场数据
                entry_mask = (
                    (data[entry_time_field].dt.time >= start_dt) &
                    (data[entry_time_field].dt.time < end_dt)
                )
                entry_data = data[entry_mask]

                # 筛选该时间段的出场数据（排除空值）
                exit_mask = (
                    (~data[exit_time_field].isna()) &
                    (data[exit_time_field].dt.time >= start_dt) &
                    (data[exit_time_field].dt.time < end_dt)
                )
                exit_data = data[exit_mask]
                
                # 记录统计信息
                # self._log_info(
                #     f"时间段 {period} 统计: 进场 {len(entry_data)} 条, 出场 {len(exit_data)} 条"
                # )
                
                # 记录总体进出场数量
                peak_flow.loc[peak_flow['时段'] == period, '进场数量'] = len(entry_data)
                peak_flow.loc[peak_flow['时段'] == period, '出场数量'] = len(exit_data)
                
                # 按车辆类型统计进出场数量
                if vtype_field in data.columns:
                    for vtype in vehicle_types:
                        # 进场统计
                        vtype_entry = entry_data[entry_data[vtype_field] == vtype]
                        peak_flow.loc[peak_flow['时段'] == period, f"{vtype}_进"] = len(vtype_entry)

                        # 出场统计
                        vtype_exit = exit_data[exit_data[vtype_field] == vtype]
                        peak_flow.loc[peak_flow['时段'] == period, f"{vtype}_出"] = len(vtype_exit)
            
            # 计算总流量
            peak_flow['总流量'] = peak_flow['进场数量'] + peak_flow['出场数量']
            
            # 找出高峰时段（仅按全日最大量标记）
            # 进场高峰：标记进场数量最大的时段
            max_entry = peak_flow['进场数量'].max()
            peak_flow['进场高峰'] = peak_flow['进场数量'].apply(
                lambda x: 1 if x == max_entry and x > 0 else 0
            )

            # 出场高峰：标记出场数量最大的时段
            max_exit = peak_flow['出场数量'].max()
            peak_flow['出场高峰'] = peak_flow['出场数量'].apply(
                lambda x: 1 if x == max_exit and x > 0 else 0
            )

            # 总流量高峰：标记总流量最大的时段
            max_total = peak_flow['总流量'].max()
            peak_flow['总流量高峰'] = peak_flow['总流量'].apply(
                lambda x: 1 if x == max_total and x > 0 else 0
            )
            
            # 记录高峰时段信息
            peak_entry = peak_flow[peak_flow['进场高峰'] == 1]['时段'].tolist()
            peak_exit = peak_flow[peak_flow['出场高峰'] == 1]['时段'].tolist()
            peak_total = peak_flow[peak_flow['总流量高峰'] == 1]['时段'].tolist()
            

            
            return peak_flow
            
        except Exception as e:
            self._log_error(f"计算进出量时间分布失败: {str(e)}")
            return pd.DataFrame()

    def _calculate_vehicle_gate_time_distribution(self, data=None):
        """
        计算进出量时间分布_车型_道闸数据

        参数:
            data: DataFrame - 停车数据(可选)

        返回:
            DataFrame - 车型道闸时间分布统计，格式为：
                时间段, 车辆类型_道闸名_进, 车辆类型_道闸名_出, ...
        """
        try:
            data = data if data is not None else self.data
            if data.empty:
                self._log_warning("输入数据为空")
                return pd.DataFrame()

            # 获取车辆类型字段
            vtype_field = self._get_field_name('vtype')
            if vtype_field not in data.columns:
                self._log_warning(f"未找到车辆类型字段 '{vtype_field}'")
                return pd.DataFrame()

            # 获取道闸字段
            mode = self.params.get('mode', 'mode1')
            if mode == 'mode1':
                gate_field = self.params.get('道闸编号字段', 'gate_id')
                if gate_field not in data.columns:
                    self._log_warning(f"mode1模式下未找到道闸字段: {gate_field}")
                    return pd.DataFrame()
                gates = sorted(data[gate_field].dropna().unique())
            else:
                # mode2模式
                entry_gate_field = self.params.get('进场道闸字段', 'entry_gate')
                exit_gate_field = self.params.get('出场道闸字段', 'exit_gate')
                if entry_gate_field not in data.columns or exit_gate_field not in data.columns:
                    self._log_warning(f"mode2模式下未找到道闸字段: {entry_gate_field}, {exit_gate_field}")
                    return pd.DataFrame()
                # 获取所有道闸（进场和出场道闸的并集）
                entry_gates = set(data[entry_gate_field].dropna().unique())
                exit_gates = set(data[exit_gate_field].dropna().unique())
                gates = sorted(entry_gates | exit_gates)

            # 获取车辆类型
            vehicle_types = sorted(self._get_vehicle_types(data))
            if not vehicle_types:
                self._log_warning("未找到车辆类型数据")
                return pd.DataFrame()

            # 生成时间段
            time_periods = self._generate_time_periods(data)

            # 初始化结果数据
            result_data = []

            # 按时间段统计
            for period in time_periods:
                start_time_str, end_time_str = period.split('-')
                start_time = pd.to_datetime(start_time_str).time()
                end_time = pd.to_datetime(end_time_str).time()

                # 分别筛选进场和出场数据（修复算法）
                if start_time <= end_time:
                    # 正常时间段（不跨天）
                    entry_mask = (
                        (data['entry_time'].dt.time >= start_time) &
                        (data['entry_time'].dt.time < end_time)
                    )
                    exit_mask = (
                        (~data['exit_time'].isna()) &
                        (data['exit_time'].dt.time >= start_time) &
                        (data['exit_time'].dt.time < end_time)
                    )
                else:
                    # 跨天时间段（如23:30-00:30）
                    entry_mask = (
                        (data['entry_time'].dt.time >= start_time) |
                        (data['entry_time'].dt.time < end_time)
                    )
                    exit_mask = (
                        (~data['exit_time'].isna()) &
                        ((data['exit_time'].dt.time >= start_time) |
                         (data['exit_time'].dt.time < end_time))
                    )

                # 获取进场和出场数据
                entry_data = data[entry_mask]
                exit_data = data[exit_mask]

                # 初始化当前时间段的统计结果
                row_data = {'时间段': period}

                # 按车辆类型和道闸统计
                for vtype in vehicle_types:
                    for gate in gates:
                        if mode == 'mode1':
                            # mode1模式：使用统一道闸字段
                            # 进场数量（该时间段内从该道闸进入的该类车辆）
                            entry_count = len(entry_data[
                                (entry_data[vtype_field] == vtype) &
                                (entry_data[gate_field] == gate)
                            ])

                            # 出场数量（该时间段内从该道闸出去的该类车辆）
                            exit_count = len(exit_data[
                                (exit_data[vtype_field] == vtype) &
                                (exit_data[gate_field] == gate)
                            ])

                        else:
                            # mode2模式：分别处理进场和出场道闸
                            # 进场数量（该时间段内从该道闸进入的该类车辆）
                            entry_count = len(entry_data[
                                (entry_data[vtype_field] == vtype) &
                                (entry_data[entry_gate_field] == gate)
                            ])

                            # 出场数量（该时间段内从该道闸出去的该类车辆）
                            exit_count = len(exit_data[
                                (exit_data[vtype_field] == vtype) &
                                (exit_data[exit_gate_field] == gate)
                            ])

                        # 添加到结果中，使用指定的列名格式
                        row_data[f'{vtype}_{gate}_进'] = entry_count
                        row_data[f'{vtype}_{gate}_出'] = exit_count

                result_data.append(row_data)

            # 创建结果DataFrame
            result_df = pd.DataFrame(result_data)

            # 确保列的顺序：时间段在前，然后按车辆类型和道闸排序
            if not result_df.empty:
                time_col = ['时间段']
                other_cols = [col for col in result_df.columns if col != '时间段']
                # 按车辆类型和道闸名称排序列
                other_cols.sort()
                result_df = result_df[time_col + other_cols]

            return result_df

        except Exception as e:
            self._log_error(f"计算进出量时间分布_车型_道闸失败: {str(e)}")
            return pd.DataFrame()

    def calculate_parking_stats(self, data=None, is_daily_avg=False):
        """
        计算停车时长分布统计

        参数:
            data: DataFrame - 停车数据(可选)
            is_daily_avg: bool - 是否计算日均值(默认False)

        返回:
            DataFrame - 停车时长分布统计
        """
        data = data if data is not None else self.data
        if data.empty:
            return pd.DataFrame()

        # 检查duration字段是否存在
        if 'duration' not in data.columns:
            self._log_warning("数据中缺少duration字段，无法计算停车时长分布")
            return pd.DataFrame()

        # 过滤掉duration为NaN或无效的记录
        valid_data = data[data['duration'].notna() & (data['duration'] >= 0)].copy()
        if valid_data.empty:
            self._log_warning("没有有效的停车时长数据，无法计算停车时长分布")
            return pd.DataFrame()

        self._log_info(f"停车时长分布计算: 原始数据{len(data)}条，有效数据{len(valid_data)}条")

        # 使用动态生成的时长分段，如果配置中有自定义的duration_bins则优先使用
        if 'duration_bins' in self.params and self.params['duration_bins'] != self.DEFAULT_PARAMS['duration_bins']:
            # 用户自定义了duration_bins，使用用户配置
            duration_bins = self.params['duration_bins']
            self._log_info("使用用户自定义的时长分段配置")
        else:
            # 使用动态生成的时长分段
            duration_bins = self._generate_duration_bins()
            self._log_info(f"使用动态生成的时长分段，共 {len(duration_bins)} 个分段")

        bins = [bin[0] for bin in duration_bins] + [float('inf')]
        labels = [bin[2] for bin in duration_bins]

        # 使用cut函数计算时长分布
        data_copy = valid_data.copy()
        data_copy['duration_bin'] = pd.cut(
            data_copy['duration'],
            bins=bins,
            labels=labels,
            right=False,
            include_lowest=True
        )
        
        # 计算总量分布
        counts = data_copy['duration_bin'].value_counts().reindex(labels, fill_value=0)
        result = pd.DataFrame({
            '时长': labels,
            '总量': counts.values
        })

        # 计算总量百分占比
        total_sum = result['总量'].sum()
        if total_sum > 0:
            result['总量占比'] = (result['总量'] / total_sum * 100).round(2)
        else:
            result['总量占比'] = 0.0

        # 按车辆类型统计
        vtype_field = self._get_field_name('vtype')
        if vtype_field in data.columns:
            vehicle_types = self._get_vehicle_types(data)
            for vtype in vehicle_types:
                vtype_counts = data_copy[data_copy[vtype_field] == vtype]['duration_bin'].value_counts()
                result[vtype] = vtype_counts.reindex(labels, fill_value=0).values

                # 计算该车型的百分占比
                vtype_sum = result[vtype].sum()
                if vtype_sum > 0:
                    result[f'{vtype}占比'] = (result[vtype] / vtype_sum * 100).round(2)
                else:
                    result[f'{vtype}占比'] = 0.0

        return result

    def _calculate_duration_probability_density(self, data=None):
        """
        计算延停时长概率密度分布

        参数:
            data: DataFrame - 停车数据(可选，默认使用数据_总量sheet的数据)

        返回:
            DataFrame - 延停时长概率密度分布统计
        """
        try:
            # 检查当前模式是否支持真实的停车时长分析
            current_mode = self.params.get('mode', '')
            if current_mode == 'mode1_simple':
                if hasattr(self, '_log_warning'):
                    self._log_warning("mode1_simple模式不支持延停时长概率密度分析：该模式使用虚拟停车时长")
                return pd.DataFrame()

            # 使用数据_总量sheet的数据（全周期清洗后数据）
            data = data if data is not None else self.processed_data
            if data is None or data.empty:
                if hasattr(self, '_log_warning'):
                    self._log_warning("数据为空，无法计算延停时长概率密度")
                return pd.DataFrame()

            # 检查duration字段是否存在
            if 'duration' not in data.columns:
                if hasattr(self, '_log_warning'):
                    self._log_warning("数据中缺少duration字段，无法计算延停时长概率密度")
                return pd.DataFrame()

            # 对于mode1_simple以外的模式，检查duration是否为虚拟值
            if current_mode == 'mode1_simple' or (data['duration'].nunique() == 1 and data['duration'].iloc[0] == 1/60):
                if hasattr(self, '_log_warning'):
                    self._log_warning("检测到虚拟停车时长数据，跳过延停时长概率密度分析")
                return pd.DataFrame()

            # 过滤掉duration为NaN或无效的记录
            valid_data = data[data['duration'].notna() & (data['duration'] >= 0)].copy()
            if valid_data.empty:
                if hasattr(self, '_log_warning'):
                    self._log_warning("没有有效的停车时长数据，无法计算延停时长概率密度")
                return pd.DataFrame()

            if hasattr(self, '_log_info'):
                self._log_info(f"延停时长概率密度计算: 原始数据{len(data)}条，有效数据{len(valid_data)}条")

            # 导入时间过滤器以使用新的时段函数
            from parking_time_filter import TimeFilter

            # 创建时间过滤器实例
            time_filter = TimeFilter(valid_data, self.params, getattr(self, 'config_manager', None))

            # 使用新的时段函数生成时长分段
            # 可以通过参数配置自定义控制点和时段长度
            control_points = self.params.get('duration_control_points', [2, 8, 24, 72])  # 默认: 2小时, 8小时, 1天, 3天
            period_lengths = self.params.get('duration_period_lengths', [0.5, 1, 16, 48])  # 默认: 30分钟, 1小时, 16小时, 2天

            duration_periods = time_filter.create_duration_periods(control_points, period_lengths)

            # 将停车时长（小时）分配到各个时段
            def assign_duration_period(duration_hours):
                """将停车时长分配到对应的时段（简化版）"""
                try:
                    # 基于默认时段结构的硬编码分配逻辑
                    # 这个逻辑对应默认的控制点 [2, 8, 24, 72] 和时段长度 [0.5, 1, 16, 48]
                    if duration_hours < 0.5:
                        return 0  # "0分钟-30分钟"
                    elif duration_hours < 1.0:
                        return 1  # "30分钟-1小时"
                    elif duration_hours < 1.5:
                        return 2  # "1小时-1小时30分钟"
                    elif duration_hours < 2.0:
                        return 3  # "1小时30分钟-2小时"
                    elif duration_hours < 3.0:
                        return 4  # "2小时-3小时"
                    elif duration_hours < 4.0:
                        return 5  # "3小时-4小时"
                    elif duration_hours < 5.0:
                        return 6  # "4小时-5小时"
                    elif duration_hours < 6.0:
                        return 7  # "5小时-6小时"
                    elif duration_hours < 7.0:
                        return 8  # "6小时-7小时"
                    elif duration_hours < 8.0:
                        return 9  # "7小时-8小时"
                    elif duration_hours < 24.0:
                        return 10  # "480分钟-1天"
                    elif duration_hours < 72.0:
                        return 11  # "1天-3天"
                    else:
                        return 12  # ">3天"

                except Exception as e:
                    self._log_warning(f"时段分配失败: {str(e)}, duration_hours: {duration_hours}")
                    return 0

            # 为每条记录分配时段
            valid_data['period_index'] = valid_data['duration'].apply(assign_duration_period)
            valid_data['period_label'] = valid_data['period_index'].apply(lambda x: duration_periods[min(x, len(duration_periods)-1)])

            # 统计各时段的频数
            period_counts = valid_data['period_label'].value_counts()

            # 创建结果DataFrame，确保所有时段都包含
            result = pd.DataFrame({
                '时长区间': duration_periods,
                '频数': [period_counts.get(period, 0) for period in duration_periods]
            })

            # 计算概率密度
            total_count = result['频数'].sum()
            if total_count > 0:
                result['频率'] = (result['频数'] / total_count).round(4)
                result['百分比(%)'] = (result['频率'] * 100).round(2)
            else:
                result['频率'] = 0.0
                result['百分比(%)'] = 0.0

            # 计算累积频率
            result['累积频率'] = result['频率'].cumsum().round(4)
            result['累积百分比(%)'] = (result['累积频率'] * 100).round(2)

            # 按车辆类型统计（如果有车辆类型字段）
            vtype_field = self._get_field_name('vtype')
            if vtype_field in data.columns:
                vehicle_types = self._get_vehicle_types(data)
                for vtype in vehicle_types:
                    vtype_data = valid_data[valid_data[vtype_field] == vtype]
                    vtype_counts = vtype_data['period_label'].value_counts()
                    result[f'{vtype}_频数'] = [vtype_counts.get(period, 0) for period in duration_periods]

                    # 计算该车型的频率
                    vtype_total = result[f'{vtype}_频数'].sum()
                    if vtype_total > 0:
                        result[f'{vtype}_频率'] = (result[f'{vtype}_频数'] / vtype_total).round(4)
                        result[f'{vtype}_百分比(%)'] = (result[f'{vtype}_频率'] * 100).round(2)
                    else:
                        result[f'{vtype}_频率'] = 0.0
                        result[f'{vtype}_百分比(%)'] = 0.0

            self._log_info(f"成功计算延停时长概率密度，共{len(result)}个时段")
            return result

        except Exception as e:
            self._log_error(f"计算延停时长概率密度失败: {str(e)}")
            return pd.DataFrame()

    def _parse_duration_to_hours(self, duration_str):
        """
        解析时长字符串为小时数

        参数:
            duration_str: str - 时长字符串，如"30分钟", "2小时", "1天"等

        返回:
            float - 小时数
        """
        try:
            duration_str = duration_str.strip()

            if '分钟' in duration_str:
                minutes = float(duration_str.replace('分钟', ''))
                return minutes / 60.0
            elif '小时' in duration_str:
                # 处理复合格式，如"1小时30分钟"
                if '分钟' in duration_str:
                    parts = duration_str.split('小时')
                    hours = float(parts[0])
                    minutes_part = parts[1].replace('分钟', '').strip()
                    if minutes_part:
                        minutes = float(minutes_part)
                        return hours + minutes / 60.0
                    return hours
                else:
                    return float(duration_str.replace('小时', ''))
            elif '天' in duration_str:
                # 处理复合格式，如"1天2小时"
                if '小时' in duration_str:
                    parts = duration_str.split('天')
                    days = float(parts[0])
                    hours_part = parts[1].replace('小时', '').strip()
                    if hours_part:
                        hours = float(hours_part)
                        return days * 24 + hours
                    return days * 24
                else:
                    return float(duration_str.replace('天', '')) * 24
            else:
                # 尝试直接解析为数字（假设是分钟）
                return float(duration_str) / 60.0

        except Exception as e:
            self._log_warning(f"解析时长字符串失败: {duration_str}, 错误: {str(e)}")
            return 0.0

    def _calculate_vehicle_duration_stats(self, data=None):
        """
        计算车辆类型停车时长统计
        
        参数:
            data: DataFrame - 停车数据(可选，默认使用self.data)
            
        返回:
            DataFrame - 车辆类型停车时长统计，总计行在第一行
        """
        # 使用传入的数据或默认数据
        data_to_use = data if data is not None else self.data
        vtype_field = self._get_field_name('vtype')
        duration_field = self._get_field_name('duration')

        if data_to_use.empty or vtype_field not in data_to_use.columns:
            return pd.DataFrame()

        # 获取所有车辆类型并排序
        vehicle_types = sorted(self._get_vehicle_types(data_to_use))

        # 使用分组操作提高效率
        stats = []
        vtype_groups = data_to_use.groupby(vtype_field)
        
        # 按排序后的车辆类型顺序生成统计数据
        for vtype in vehicle_types:
            if vtype in vtype_groups.groups:
                group = vtype_groups.get_group(vtype)
                duration_stats = group[duration_field].agg(['size', 'mean', 'max', 'min'])
                stats.append({
                    '车辆类型': vtype,
                    '总量': duration_stats['size'],
                    '平均停车时长(h)': round(duration_stats['mean'], 3),
                    '最长停车时长(h)': round(duration_stats['max'], 3),
                    '最短停车时长(h)': round(duration_stats['min'], 3)
                })

        # 创建结果DataFrame
        result = pd.DataFrame(stats)

        # 创建总计行
        total_stats = data_to_use[duration_field].agg(['size', 'mean', 'max', 'min'])
        total_row = pd.DataFrame([{
            '车辆类型': '总计',
            '总量': total_stats['size'],
            '平均停车时长(h)': round(total_stats['mean'], 3),
            '最长停车时长(h)': round(total_stats['max'], 3),
            '最短停车时长(h)': round(total_stats['min'], 3)
        }])
        
        # 将总计行添加到结果的开头
        result = pd.concat([total_row, result], ignore_index=True)
        
        return result
        
    def _calculate_occupancy(self, data=None, focus_date=None, focus_month=None):
        """
        计算在场车辆分布
        
        参数:
            data: DataFrame - 停车数据(可选)
            focus_date: str - 聚焦日期(可选)
            focus_month: str - 聚焦月份(可选)
            
        返回:
            DataFrame - 在场车辆分布
        """
        data = data if data is not None else self.data
        if data.empty:
            return pd.DataFrame()
        
        # 生成时间段，使用完整的时间段格式（如"00:00-01:00"）
        time_periods = self._generate_time_periods(data)
        # 提取时间点用于计算在场车辆数（使用时间段的开始时间）
        time_points = [t.split('-')[0] for t in time_periods]

        # 初始化结果DataFrame，使用完整时间段格式
        occupancy = pd.DataFrame({'时间': time_periods})

        # 如果有聚焦日期，计算该日期的在场车辆数
        if focus_date:
            target_date = pd.to_datetime(focus_date).date()

            # 向量化计算每个时间点的在场车辆数
            occupancy_counts = []
            for time_point_str in time_points:
                time_point = pd.Timestamp(f"{target_date} {time_point_str}")
                count = len(data[(data['entry_time'] <= time_point) & 
                              ((data['exit_time'] > time_point) | data['exit_time'].isna())])
                occupancy_counts.append(count)
            
            # 添加到结果DataFrame
            occupancy['在场车辆数'] = occupancy_counts
            occupancy['车辆类型'] = '所有车辆'
            
            # 如果有车辆类型列，按类型计算在场车辆数
            if 'vtype' in data.columns:
                vehicle_types = self._get_vehicle_types(data)
                
                # 预先分组以提高效率
                vtype_groups = {}
                for vtype in vehicle_types:
                    vtype_groups[vtype] = data[data['vtype'] == vtype]
                
                for vtype in vehicle_types:
                    vtype_data = vtype_groups[vtype]
                    vtype_counts = []
                    
                    for time_point_str in time_points:
                        time_point = pd.Timestamp(f"{target_date} {time_point_str}")
                        count = len(vtype_data[(vtype_data['entry_time'] <= time_point) & 
                                          ((vtype_data['exit_time'] > time_point) | vtype_data['exit_time'].isna())])
                        vtype_counts.append(count)
                    
                    # 添加到结果DataFrame，使用完整时间段格式
                    vtype_df = pd.DataFrame({
                        '时间': time_periods,
                        '在场车辆数': vtype_counts,
                        '车辆类型': vtype
                    })
                    occupancy = pd.concat([occupancy, vtype_df], ignore_index=True)
        
        # 如果没有聚焦日期，计算日均在场车辆数
        else:
            # 计算总天数
            min_date = data['entry_time'].min().date()
            max_date = data['exit_time'].max().date()
            date_range = pd.date_range(min_date, max_date)
            total_days = len(date_range)
            if total_days < 1:
                total_days = 1  # 确保至少为1天
            
            # 使用更高效的方法计算日均在场车辆数
            all_occupancy = []
            
            for date in date_range:
                date_occupancy = []
                for i, time_point_str in enumerate(time_points):
                    time_point = pd.Timestamp(f"{date.date()} {time_point_str}")
                    count = len(data[(data['entry_time'] <= time_point) &
                                  ((data['exit_time'] > time_point) | data['exit_time'].isna())])
                    date_occupancy.append({
                        '时间': time_periods[i],  # 使用完整时间段格式
                        '在场车辆数': count,
                        '日期': date.date()
                    })
                all_occupancy.extend(date_occupancy)
            
            # 创建DataFrame并计算日均值
            all_occupancy_df = pd.DataFrame(all_occupancy)
            avg_occupancy = all_occupancy_df.groupby('时间')['在场车辆数'].mean().reset_index()
            avg_occupancy['车辆类型'] = '所有车辆'
            
            occupancy = avg_occupancy
        
        return occupancy

    def _log_warning(self, message):
        """记录警告日志，包含调用位置信息"""
        import inspect
        # 获取调用堆栈信息
        stack = inspect.stack()
        # 跳过当前帧(_log_warning方法本身)，获取上一帧的调用信息
        caller = stack[1]
        # 提取文件名、行号和函数名
        filename = caller.filename.split('\\')[-1]  # 只取文件名
        lineno = caller.lineno
        funcname = caller.function

        detailed_msg = f"[{filename}:{lineno} {funcname}] {message}"

        if self.logger:
            self.logger.warning(detailed_msg)
            
    def _log_info(self, message):
        """记录信息日志"""
        if self.logger:
            self.logger.info(message)
            
    def _log_error(self, message):
        """记录错误日志，包含调用位置信息"""
        import inspect
        # 获取调用堆栈信息
        stack = inspect.stack()
        # 跳过当前帧(_log_error方法本身)，获取上一帧的调用信息
        caller = stack[1]
        # 提取文件名、行号和函数名
        filename = caller.filename.split('\\')[-1]  # 只取文件名
        lineno = caller.lineno
        funcname = caller.function

        detailed_msg = f"[{filename}:{lineno} {funcname}] {message}"

        if self.logger:
            self.logger.error(detailed_msg)
            
    def _get_vehicle_types(self, data=None):
        """获取数据中的所有车辆类型（带缓存功能）

        参数:
            data: DataFrame - 停车数据(可选)

        返回:
            list - 车辆类型列表（去重且排除空值）
        """
        try:
            # 如果有缓存且未指定特定数据，直接返回缓存
            if self._vehicle_types_cache is not None and data is None:
                return self._vehicle_types_cache

            # 使用指定数据或默认数据
            data_to_use = data if data is not None else self.data
            vtype_field = self._get_field_name('vtype')

            # 检查数据有效性
            if data_to_use is None or data_to_use.empty or vtype_field not in data_to_use.columns:
                self._log_warning(f"未找到车辆类型字段 '{vtype_field}' 或数据为空")
                return ['默认车辆类型']  # 返回默认车辆类型

            # 获取所有非空的车辆类型（确保去重）
            vehicle_types = data_to_use[vtype_field].dropna().drop_duplicates().tolist()

            # 如果没有找到任何车辆类型，返回默认值
            if not vehicle_types:
                self._log_warning("未找到任何有效的车辆类型，使用默认值")
                return ['默认车辆类型']

            # 如果使用默认数据，缓存结果
            if data is None:
                self._vehicle_types_cache = vehicle_types

            return vehicle_types

        except Exception as e:
            self._log_error(f"获取车辆类型时出错: {str(e)}")
            return ['默认车辆类型']  # 发生错误时返回默认车辆类型

    def export_to_excel(self, output_path):
        """将分析结果导出到Excel文件
        
        参数:
            output_path: str - 输出文件路径
            
        返回:
            str - 生成的Excel文件路径
        """
        writer = None
        try:
            # 前置检查
            if self.data is None or self.data.empty:
                raise RuntimeError("数据为空，无法导出Excel")
            if self.analysis_results is None:
                raise RuntimeError("分析结果为空，无法导出Excel")
    
            # 确保输出目录存在
            os.makedirs(os.path.dirname(output_path), exist_ok=True)
            
            # 检查文件是否已存在并尝试删除
            if os.path.exists(output_path):
                try:
                    os.remove(output_path)
                except PermissionError:
                    raise PermissionError(f"无法覆盖文件，请关闭已打开的Excel文件: {output_path}")
                except Exception as e:
                    raise RuntimeError(f"删除旧文件失败: {str(e)}")
    
            # 创建Excel工作簿
            writer = pd.ExcelWriter(output_path, engine='xlsxwriter')
            workbook = writer.book
                
            # 获取筛选后的数据
            filtered_data = self._get_filtered_data()
            if filtered_data.empty:
                self._log_warning("筛选后数据为空，仅导出总量数据")
                
            # 定义工作表创建顺序
            sheet_creation_order = [
                ('概览', self._create_overview_sheet),
                ('数据_总量', self._export_cleaned_data),
                ('数据_分析日', self._export_focus_date_data),
                ('日进出量_分析周期', self._export_daily_stats),
                ('车辆类型_分析日', self._export_vehicle_duration_stats),
                ('图_分析日', self._create_charts_sheet),
                ('进出量时间分布_分析日', self._export_peak_flow),
                ('进出量-道闸时间分布_分析日', self._export_gate_time_distribution),
                ('进出量时间分布_车型_道闸_三列式', self._export_gate_flow_distribution),
                ('进出量时间分布_车型_道闸', self._export_vehicle_gate_time_distribution),
                ('停车时长_分析日', self._export_parking_stats),
                ('停车时长_入场_分析日', self._export_parking_duration_by_entry_period),
                ('延停时长概率密度', self._export_duration_probability_density),
                ('道闸组合_分析日', self._export_gate_pairs_analysis)
            ]

            # 开始生成报告提示
            print("📊 生成报告工作表:")
            
            # 按顺序创建工作表
            for sheet_name, export_func in sheet_creation_order:
                try:
                    # 跳过没有车辆类型数据的情况
                    if sheet_name == '车辆类型_分析日' and 'vtype' not in self.data.columns:
                        continue

                    # 跳过没有车辆类型或道闸数据的情况
                    if sheet_name == '进出量时间分布_车型_道闸':
                        vtype_field = self._get_field_name('vtype')
                        if vtype_field not in self.data.columns:
                            self._log_warning("跳过进出量时间分布_车型_道闸sheet创建：车辆类型字段缺失")
                            continue

                        mode = self.params.get('mode', 'mode1')
                        if mode == 'mode1':
                            gate_field = self.params.get('道闸编号字段', 'gate_id')
                            if gate_field not in self.data.columns:
                                self._log_warning("跳过进出量时间分布_车型_道闸sheet创建：道闸字段缺失")
                                continue
                        else:
                            entry_gate_field = self.params.get('进场道闸字段', 'entry_gate')
                            exit_gate_field = self.params.get('出场道闸字段', 'exit_gate')
                            if entry_gate_field not in self.data.columns or exit_gate_field not in self.data.columns:
                                self._log_warning("跳过进出量时间分布_车型_道闸sheet创建：道闸字段缺失")
                                continue

                    # 跳过没有道闸数据的情况
                    if sheet_name == '进出量时间分布_车型_道闸_三列式':
                        fields_ok, _ = self._check_gate_fields(self.data)
                        if not fields_ok:
                            self._log_warning("跳过进出量时间分布_车型_道闸_三列式sheet创建：道闸字段缺失")
                            continue

                    # mode1_simple 模式跳过需要车辆追踪的工作表
                    if self.params.get('mode') == 'mode1_simple':
                        skip_sheets = ['停车时长_分析日', '停车时长_入场_分析日', '车辆类型_分析日', '在场车辆分布', '道闸组合_分析日', '进出量时间分布_车型_道闸', '延停时长概率密度']
                        if sheet_name in skip_sheets:
                            self._log_warning(f"跳过'{sheet_name}'工作表：mode1_simple模式不支持车辆追踪分析")
                            print(f"  ⏭️  {sheet_name} (简化模式跳过)")
                            continue

                    # 调用导出函数
                    if sheet_name == '车辆类型停车时长':
                        export_func(writer, workbook, filtered_data)
                    else:
                        export_func(writer, workbook)

                    # 输出完成提示
                    print(f"  ✅ {sheet_name}")

                except Exception as e:
                    self._log_warning(f"创建'{sheet_name}'工作表失败: {str(e)}")
                    print(f"  ❌ {sheet_name} (失败)")
                    # 创建一个空的工作表，确保工作表名称存在
                    try:
                        workbook.add_worksheet(sheet_name)
                    except:
                        pass
                    continue
            
            # 显式保存并关闭writer
            writer.close()
            return output_path
            
        except Exception as e:
            # 确保writer被正确关闭
            if writer is not None:
                try:
                    writer.close()
                except:
                    pass
            # 删除可能创建的不完整文件
            if os.path.exists(output_path):
                try:
                    os.remove(output_path)
                except:
                    pass
            raise RuntimeError(f"导出Excel失败: {str(e)}")
        except Exception as e:
            self._log_warning(f"导出Excel失败: {str(e)}")
            raise
        
    def _get_filtered_data(self):
        """获取筛选后的数据"""
        # 如果已有缓存，直接返回
        if self._filtered_data_cache is not None:
            return self._filtered_data_cache
            
        # 如果没有聚焦日期，返回所有数据
        if '聚焦日期' not in self.params or not self.params['聚焦日期']:
            self._filtered_data_cache = self.data
            return self.data
        
        # 根据聚焦日期筛选数据
        focus_date = pd.to_datetime(self.params['聚焦日期']).normalize()
        focus_date_end = focus_date + pd.Timedelta(days=1)
        
        # 筛选条件：车辆在分析日期间有停留
        # 即：entry_time <= focus_date_end AND (exit_time >= focus_date OR exit_time is null)
        mask = (
            (self.data['entry_time'] <= focus_date_end) & 
            (
                (self.data['exit_time'] >= focus_date) | 
                (self.data['exit_time'].isna())
            )
        )
        self._filtered_data_cache = self.data[mask].copy()
        return self._filtered_data_cache
        
    def _calculate_focus_date_data(self, filtered_data):
        """仅做数据验证和预处理"""
        if filtered_data is None or filtered_data.empty:
            self._log_warning("筛选数据为空，无法创建分析日数据表")
            return None
        return filtered_data
            
    def _calculate_focus_date_duration(self, row, focus_date_start, focus_date_end):
        """
        计算分析日的停车时长（新规则）
        
        规则：
        1. 入场时间在选择日0:00之前的车辆，视为0:00入场
        2. 出场时间在选择日24:00之后的车辆，视为24:00出场
        3. 计算调整后的出场和入场时间差作为停车时长
        
        参数:
            row: Series - 数据行
            focus_date_start: Timestamp - 分析日开始时间(0:00)
            focus_date_end: Timestamp - 分析日结束时间(24:00)
            
        返回:
            float - 计算后的停车时长(小时)，范围0-24小时
        """
        entry_time = row['entry_time']
        exit_time = row['exit_time']
        
        # 处理缺失的出场时间
        if pd.isna(exit_time):
            # 如果没有出场时间，假设车辆仍在停车场内
            exit_time = pd.Timestamp.now()
        
        # 调整入场时间：如果早于分析日开始时间，设为开始时间
        adjusted_entry = max(entry_time, focus_date_start)
        
        # 调整出场时间：如果晚于分析日结束时间，设为结束时间
        adjusted_exit = min(exit_time, focus_date_end)
        
        # 计算时长（小时）
        duration = (adjusted_exit - adjusted_entry).total_seconds() / 3600
        
        # 确保结果在0-24小时范围内
        duration = max(0.0, min(24.0, duration))
            
        return round(duration, 3)  # 保留三位小数
      
    def _export_vehicle_duration_stats(self, writer, workbook):
        """导出车辆类型停车时长统计到Excel工作表"""
        try:
            # 获取所有车辆类型
            vehicle_types = self._get_vehicle_types(self.data)

            if not vehicle_types:
                self._log_warning("没有找到车辆类型数据，无法生成车辆类型停车时长统计")
                # 创建空工作表
                workbook.add_worksheet('车辆类型_分析日')
                return

            # 计算车辆类型停车时长统计
            vehicle_stats = self._calculate_vehicle_duration_stats(self.data)

            if vehicle_stats.empty:
                self._log_warning("车辆类型停车时长统计数据为空")
                # 创建空工作表
                workbook.add_worksheet('车辆类型_分析日')
                return
            
            # 设置格式
            formats = self._create_excel_formats(workbook)
            
            # 创建工作表
            sheet_name = '车辆类型_分析日'
            worksheet = workbook.add_worksheet(sheet_name)
            
            # 设置列宽和列名
            columns = [
                {'name': '车辆类型', 'width': 15},
                {'name': '总量', 'width': 18},
                {'name': '平均停车时长(h)', 'width': 18},
                {'name': '最长停车时长(h)', 'width': 18},
                {'name': '最短停车时长(h)', 'width': 18}
            ]
            
            # 设置列宽
            for i, col in enumerate(columns):
                worksheet.set_column(i, i, col['width'])
            
            # 设置表头行高度以适应自动换行
            self._set_header_row_height(worksheet, 0, 35)

            # 写入列名
            for col_num, col in enumerate(columns):
                worksheet.write(0, col_num, col['name'], formats['header'])
            
            # 写入所有数据行
            for row_num, row in enumerate(vehicle_stats.itertuples(index=False), start=1):
                for col_num, value in enumerate(row):
                    # 第一行（总计行）高亮显示
                    cell_format = formats['highlight'] if row_num == 2 else formats['cell']
                    worksheet.write(row_num, col_num, value, cell_format)

            # 记录日志
            self._log_info(f"成功导出车辆类型停车时长统计，包含{len(vehicle_types)}种车型")
            
        except Exception as e:
            self._log_warning(f"导出车辆类型停车时长统计失败: {str(e)}")
            
    def _calculate_daily_stats(self, data=None):
        """计算每日分析数据
        
        参数:
            data: DataFrame - 停车数据(可选)
            
        返回:
            DataFrame - 每日分析数据
        """
        data = data if data is not None else self.data
        if data.empty:
            return pd.DataFrame()
        
        # 确保数据中有日期列
        data_copy = data.copy()
        data_copy['entry_date'] = data_copy['entry_time'].dt.date
        data_copy['exit_date'] = data_copy['exit_time'].dt.date
        
        # 获取所有日期
        all_dates = sorted(set(data_copy['entry_date'].dropna().tolist() + data_copy['exit_date'].dropna().tolist()))
        
        # 初始化结果列表
        daily_stats = []
        
        # 计算每日进出场数量
        for date in all_dates:
            # 进场统计
            entry_count = len(data_copy[data_copy['entry_date'] == date])
            
            # 出场统计
            exit_count = len(data_copy[data_copy['exit_date'] == date])
            
            # 计算当日停车时长
            day_data = data_copy[(data_copy['entry_date'] == date) | (data_copy['exit_date'] == date)]
            avg_duration = day_data['duration'].mean() if not day_data.empty else 0
            
            daily_stats.append({
                '日期': date.strftime('%Y-%m-%d'),
                '进场数量': entry_count,
                '出场数量': exit_count,
                '总流量': entry_count + exit_count,
                '平均停车时长(h)': round(avg_duration, 2) if not pd.isna(avg_duration) else 0
            })
        
        # 创建结果DataFrame
        result = pd.DataFrame(daily_stats)

        # 如果有车辆类型列，按类型统计（先添加车辆类型统计）
        if 'vtype' in data.columns:
            vehicle_types = self._get_vehicle_types(data)

            # 初始化所有车辆类型列
            for vtype in vehicle_types:
                result[f'{vtype}_进'] = 0
                result[f'{vtype}_出'] = 0

            # 填充车辆类型数据
            for date in all_dates:
                date_str = date.strftime('%Y-%m-%d')
                for vtype in vehicle_types:
                    vtype_data = data_copy[data_copy['vtype'] == vtype]

                    # 进场统计
                    entry_count = len(vtype_data[vtype_data['entry_date'] == date])
                    result.loc[result['日期'] == date_str, f'{vtype}_进'] = entry_count

                    # 出场统计
                    exit_count = len(vtype_data[vtype_data['exit_date'] == date])
                    result.loc[result['日期'] == date_str, f'{vtype}_出'] = exit_count

        # 添加道闸统计（在车辆类型统计之后）
        self._add_gate_stats_to_daily(result, data_copy, all_dates)

        # 添加总流量列（放在最后）
        result['总流量'] = result['进场数量'] + result['出场数量']
        return result

    def _add_gate_stats_to_daily(self, result, data_copy, all_dates):
        """为每日统计添加道闸进出量统计"""
        try:
            # 获取当前模式
            mode = self.params.get('mode', 'mode1')

            if mode == 'mode1':
                # mode1模式 - 使用统一道闸字段
                gate_field = self.params.get('道闸编号字段', 'gate_id')
                if gate_field not in data_copy.columns:
                    self._log_warning(f"mode1模式下未找到道闸字段: {gate_field}")
                    return

                gates = sorted(data_copy[gate_field].unique())

                # 为每个道闸初始化列
                for gate in gates:
                    # 简化道闸名称作为列名
                    gate_name = str(gate).replace('道闸', '').replace('编号', '').replace('入口', '').replace('出口', '')
                    if not gate_name:
                        gate_name = str(gate)

                    result[f'{gate_name}_进'] = 0
                    result[f'{gate_name}_出'] = 0

                # 填充道闸数据
                for date in all_dates:
                    date_str = date.strftime('%Y-%m-%d')
                    for gate in gates:
                        gate_name = str(gate).replace('道闸', '').replace('编号', '').replace('入口', '').replace('出口', '')
                        if not gate_name:
                            gate_name = str(gate)

                        gate_data = data_copy[data_copy[gate_field] == gate]

                        # 进场统计（基于进场时间）
                        entry_count = len(gate_data[gate_data['entry_date'] == date])
                        result.loc[result['日期'] == date_str, f'{gate_name}_进'] = entry_count

                        # 出场统计（基于出场时间）
                        exit_count = len(gate_data[gate_data['exit_date'] == date])
                        result.loc[result['日期'] == date_str, f'{gate_name}_出'] = exit_count

            else:
                # mode2模式 - 分别处理进场和出场道闸
                entry_gate_field = self.params.get('进场道闸字段', 'entry_gate')
                exit_gate_field = self.params.get('出场道闸字段', 'exit_gate')

                if entry_gate_field not in data_copy.columns or exit_gate_field not in data_copy.columns:
                    self._log_warning(f"mode2模式下未找到道闸字段: {entry_gate_field}, {exit_gate_field}")
                    return

                # 获取所有道闸
                entry_gates = sorted(data_copy[entry_gate_field].unique())
                exit_gates = sorted(data_copy[exit_gate_field].unique())
                all_gates = sorted(set(entry_gates + exit_gates))

                # 为每个道闸初始化列
                for gate in all_gates:
                    # 简化道闸名称作为列名
                    gate_name = str(gate).replace('道闸', '').replace('编号', '').replace('入口', '').replace('出口', '')
                    if not gate_name:
                        gate_name = str(gate)

                    result[f'{gate_name}_进'] = 0
                    result[f'{gate_name}_出'] = 0

                # 填充道闸数据
                for date in all_dates:
                    date_str = date.strftime('%Y-%m-%d')

                    # 统计进场道闸
                    for gate in entry_gates:
                        gate_name = str(gate).replace('道闸', '').replace('编号', '').replace('入口', '').replace('出口', '')
                        if not gate_name:
                            gate_name = str(gate)

                        entry_count = len(data_copy[
                            (data_copy[entry_gate_field] == gate) &
                            (data_copy['entry_date'] == date)
                        ])
                        result.loc[result['日期'] == date_str, f'{gate_name}_进'] = entry_count

                    # 统计出场道闸
                    for gate in exit_gates:
                        gate_name = str(gate).replace('道闸', '').replace('编号', '').replace('入口', '').replace('出口', '')
                        if not gate_name:
                            gate_name = str(gate)

                        exit_count = len(data_copy[
                            (data_copy[exit_gate_field] == gate) &
                            (data_copy['exit_date'] == date)
                        ])
                        result.loc[result['日期'] == date_str, f'{gate_name}_出'] = exit_count

        except Exception as e:
            self._log_warning(f"添加道闸统计失败: {str(e)}")
            # 不影响主要功能，继续执行

    def _get_overview_data(self, filtered_data=None, focus_date=None, focus_month=None, total_data=None):
        """从_create_overview_sheet中提取数据
        
        参数:
            filtered_data: DataFrame - 过滤后的数据(可选)
            focus_date: str - 聚焦日期(可选)
            focus_month: str - 聚焦月份(可选)
            total_data: DataFrame - 原始完整数据(可选)
            
        返回:
            dict - 包含概览统计信息的字典
        """
        # 如果传入了total_data，更新原始记录数
        if total_data is not None:
            self.original_total_records = len(total_data)
        # 使用传入的过滤数据或默认数据
        data_to_use = filtered_data if filtered_data is not None else self.data
        
        overview_data = {
            'original_total_records': self.original_total_records,
            'total_records': len(data_to_use),
            'filtered_percentage': round(len(data_to_use) / self.original_total_records * 100, 2) 
                                  if self.original_total_records > 0 else 0,
            'time_range': {
                'start': data_to_use['entry_time'].min().strftime('%Y-%m-%d %H:%M:%S') if not data_to_use.empty else '',
                'end': data_to_use['exit_time'].max().strftime('%Y-%m-%d %H:%M:%S') if not data_to_use.empty else ''
            },
            'focus_period': focus_date if focus_date else focus_month,
            'analysis_mode': self.params.get('mode', '')
        }
        
        # 添加停车时长统计
        if 'duration' in data_to_use.columns and not data_to_use.empty:
            overview_data.update({
                'average_duration': data_to_use['duration'].mean(),
                'max_duration': data_to_use['duration'].max(),
                'min_duration': data_to_use['duration'].min()
            })
        
        # 添加车辆类型分布
        if 'vtype' in data_to_use.columns and not data_to_use.empty:
            vtype_counts = data_to_use['vtype'].value_counts(normalize=True)
            overview_data['vehicle_distribution'] = vtype_counts.to_dict()
            
        return overview_data

    def _calculate_gate_flow_stats(self, data):
        """
        计算道闸进出量统计
        
        参数:
            data: DataFrame - 停车数据
            
        返回:
            DataFrame - 各时间段各出入口的进出量统计
        """
        if data.empty:
            return pd.DataFrame()
            
        # 获取当前模式
        mode = self.params.get('mode', 'mode1')
        
        # 修改点：使用_generate_time_periods生成时间段，参数设置为60分钟
        time_periods = self._generate_time_periods(data)
        
        # 初始化结果DataFrame
        stats = []
        
        if mode == 'mode1':
            # mode1模式 - 使用统一道闸字段
            gate_field = self.params.get('道闸编号字段', 'gate_id')
            if gate_field not in data.columns:
                return pd.DataFrame()
                
            gates = sorted(data[gate_field].unique())
            vehicle_types = self._get_vehicle_types(data)
            
            # 按时间段统计
            for period in time_periods:
                start_time_str, end_time_str = period.split('-')
                start_time = pd.to_datetime(start_time_str).time()
                end_time = pd.to_datetime(end_time_str).time()
                
                # 筛选当前时间段的数据
                period_data = data[
                    (data['entry_time'].dt.time >= start_time) & 
                    (data['entry_time'].dt.time <= end_time)
                ]
                
                # 按出入口统计进出量
                for gate in gates:
                    gate_data = period_data[period_data[gate_field] == gate]
                    
                    # 进场数量
                    entry_count = len(gate_data)
                    
                    # 出场数量 - 需要根据exit_time判断
                    exit_count = len(gate_data[
                        (gate_data['exit_time'].dt.time >= start_time) &
                        (gate_data['exit_time'].dt.time <= end_time)
                    ])
                    
                    # 按车辆类型统计
                    vtype_stats = {}
                    if vehicle_types:
                        for vtype in vehicle_types:
                            vtype_data = gate_data[gate_data['vtype'] == vtype]
                            vtype_stats[f'{vtype}_进'] = len(vtype_data)
                            vtype_stats[f'{vtype}_出'] = len(vtype_data[
                                (vtype_data['exit_time'].dt.time >= start_time) &
                                (vtype_data['exit_time'].dt.time <= end_time)
                            ])
                    
                    stats.append({
                        '时间段': period,
                        '出入口': gate,
                        '进场总量': entry_count,
                        '出场总量': exit_count,
                        **vtype_stats
                    })
                    
        else:
            # mode2模式 - 分别处理进场和出场道闸
            entry_gate_field = self.params.get('进场道闸字段', 'entry_gate')
            exit_gate_field = self.params.get('出场道闸字段', 'exit_gate')
            
            if entry_gate_field not in data.columns or exit_gate_field not in data.columns:
                return pd.DataFrame()
                
            entry_gates = sorted(data[entry_gate_field].unique())
            exit_gates = sorted(data[exit_gate_field].unique())
            vehicle_types = self._get_vehicle_types(data)
            
            # 按时间段统计
            for period in time_periods:
                start_time_str, end_time_str = period.split('-')
                start_time = pd.to_datetime(start_time_str).time()
                end_time = pd.to_datetime(end_time_str).time()
                
                # 筛选当前时间段的数据
                period_data = data[
                    (data['entry_time'].dt.time >= start_time) & 
                    (data['entry_time'].dt.time <= end_time)
                ]
                
                # 统计进场道闸
                for gate in entry_gates:
                    gate_data = period_data[period_data[entry_gate_field] == gate]
                    entry_count = len(gate_data)
                    
                    # 按车辆类型统计
                    vtype_stats = {}
                    if vehicle_types:
                        for vtype in vehicle_types:
                            vtype_data = gate_data[gate_data['vtype'] == vtype]
                            vtype_stats[f'{vtype}_进'] = len(vtype_data)
                    
                    stats.append({
                        '时间段': period,
                        '出入口': gate,
                        '进出类型': '进',
                        '数量': entry_count,
                        **vtype_stats
                    })
                
                # 统计出场道闸
                for gate in exit_gates:
                    exit_data = period_data[
                        (period_data['exit_time'].dt.time >= start_time) &
                        (period_data['exit_time'].dt.time <= end_time) &
                        (period_data[exit_gate_field] == gate)
                    ]
                    exit_count = len(exit_data)
                    
                    # 按车辆类型统计
                    vtype_stats = {}
                    if vehicle_types:
                        for vtype in vehicle_types:
                            vtype_data = exit_data[exit_data['vtype'] == vtype]
                            vtype_stats[f'{vtype}_出'] = len(vtype_data)
                    
                    stats.append({
                        '时间段': period,
                        '出入口': gate,
                        '进出类型': '出',
                        '数量': exit_count,
                        **vtype_stats
                    })
        
        return pd.DataFrame(stats)

    def generate_and_save_plots(self, data=None):
        """生成并保存图表
        
        参数:
            data: DataFrame - 停车数据(可选)
            
        返回:
            None
        """
        try:
            # 使用传入的数据或默认数据
            data_to_use = data if data is not None else self.data
            if data_to_use.empty:
                self._log_warning("数据为空，无法生成图表")
                return
            
            # 生成进出量时间分布图表
            self._generate_peak_flow_plot(data_to_use)
            
            # 生成停车时长分布图表
            self._generate_duration_distribution_plot(data_to_use)
            
            # 生成在场车辆分布图表
            focus_date = self.params.get('date') or self.params.get('聚焦日期')
            focus_month = self.params.get('month') or self.params.get('聚焦月份')
            self._generate_occupancy_plot(data_to_use, focus_date, focus_month)
            
            # 生成每日统计图表
            self._generate_daily_stats_plot(data_to_use)

        except Exception as e:
            self._log_warning(f"生成图表失败: {str(e)}")
    
    def _generate_peak_flow_plot(self, data):
        """生成进出量时间分布图表"""
        try:
            # 计算高峰流量数据
            peak_flow = self._calculate_peak_flow(data)
            if peak_flow.empty:
                return
            
            # 创建图表
            plt.figure(figsize=(12, 6))
            
            # 绘制进出场数量柱状图
            x = range(len(peak_flow))
            width = 0.35
            plt.bar(x, peak_flow['进场数量'], width, label='进场数量')
            plt.bar([i + width for i in x], peak_flow['出场数量'], width, label='出场数量')
            
            # 设置图表标题和标签
            plt.title('各时段进出场数量分布')
            plt.xlabel('时间段')
            plt.ylabel('车辆数量')
            plt.xticks([i + width/2 for i in x], peak_flow['时段'], rotation=45)
            plt.legend()
            plt.tight_layout()
            
            # 保存图表到内存
            self.plot_figures['peak_flow'] = self._save_figure_to_memory()
        except Exception as e:
            self._log_warning(f"生成进出量时间分布图表失败: {str(e)}")
    
    def _generate_duration_distribution_plot(self, data):
        """生成停车时长分布图表"""
        try:
            # 计算停车时长分布数据
            duration_stats = self.calculate_parking_stats(data)
            if duration_stats.empty:
                return
            
            # 创建图表
            plt.figure(figsize=(12, 6))
            
            # 绘制停车时长分布柱状图
            plt.bar(duration_stats['时长'], duration_stats['总量'])
            
            # 设置图表标题和标签
            plt.title('停车时长分布')
            plt.xlabel('停车时长')
            plt.ylabel('车辆数量')
            plt.xticks(rotation=45)
            plt.tight_layout()
            
            # 保存图表到内存
            self.plot_figures['duration_distribution'] = self._save_figure_to_memory()
        except Exception as e:
            self._log_warning(f"生成停车时长分布图表失败: {str(e)}")
    
    def _generate_occupancy_plot(self, data, focus_date=None, focus_month=None):
        """生成在场车辆分布图表"""
        try:
            # 计算在场车辆分布数据
            occupancy = self._calculate_occupancy(data, focus_date, focus_month)
            if occupancy.empty:
                return
            
            # 创建图表
            plt.figure(figsize=(12, 6))
            
            # 如果有车辆类型列，按类型绘制折线图
            if '车辆类型' in occupancy.columns:
                for vtype in occupancy['车辆类型'].unique():
                    vtype_data = occupancy[occupancy['车辆类型'] == vtype]
                    plt.plot(vtype_data['时间'], vtype_data['在场车辆数'], label=vtype)
            else:
                plt.plot(occupancy['时间'], occupancy['在场车辆数'])
            
            # 设置图表标题和标签
            title_text = f"在场车辆分布 ({focus_date if focus_date else '日均'})"
            plt.title(title_text)
            plt.xlabel('时间')
            plt.ylabel('在场车辆数')
            plt.xticks(rotation=45)
            plt.legend()
            plt.tight_layout()
            
            # 保存图表到内存
            self.plot_figures['occupancy'] = self._save_figure_to_memory()
        except Exception as e:
            self._log_warning(f"生成在场车辆分布图表失败: {str(e)}")
    
    def _generate_daily_stats_plot(self, data):
        """生成每日统计图表"""
        try:
            # 计算每日统计数据
            daily_stats = self._calculate_daily_stats(data)
            if daily_stats.empty:
                return
            
            # 创建图表
            plt.figure(figsize=(12, 6))
            
            # 绘制每日进出场数量折线图
            plt.plot(daily_stats['日期'], daily_stats['进场数量'], label='进场数量')
            plt.plot(daily_stats['日期'], daily_stats['出场数量'], label='出场数量')
            plt.plot(daily_stats['日期'], daily_stats['总流量'], label='总流量')
            
            # 设置图表标题和标签
            plt.title('每日进出场数量统计')
            plt.xlabel('日期')
            plt.ylabel('车辆数量')
            plt.xticks(rotation=45)
            plt.legend()
            plt.tight_layout()
            
            # 保存图表到内存
            self.plot_figures['daily_stats'] = self._save_figure_to_memory()
        except Exception as e:
            self._log_warning(f"生成每日统计图表失败: {str(e)}")
    
    def _export_parking_stats(self, writer, workbook):
        """导出停车时长分布数据到'停车时长_分析日'工作表

        参数:
            writer: ExcelWriter - Excel写入器
            workbook: Workbook - Excel工作簿对象
        """
        try:
            # 计算停车时长分布数据
            parking_stats = self.calculate_parking_stats()

            if parking_stats.empty:
                workbook.add_worksheet('停车时长_分析日')
                self._log_warning("没有停车时长分布数据可导出")
                return

            # 设置格式
            formats = self._create_excel_formats(workbook)

            # 创建工作表
            sheet_name = '停车时长_分析日'
            worksheet = workbook.add_worksheet(sheet_name)
            
            # 写入数据
            # 获取所有列名
            columns = parking_stats.columns.tolist()

            # 设置表头行高度以适应自动换行
            self._set_header_row_height(worksheet, 0, 35)

            # 写入列名
            for col_num, column in enumerate(columns):
                worksheet.write(0, col_num, column, formats['header'])
            
            # 写入数据行
            for row_num, row in enumerate(parking_stats.itertuples(index=False), start=1):
                for col_num, value in enumerate(row):
                    worksheet.write(row_num, col_num, value, formats['cell'])
            
            # 设置列宽
            for i, col in enumerate(columns):
                # 根据列名长度和内容自动调整列宽
                max_len = max(
                    len(str(col)),  # 列名长度
                    parking_stats[col].astype(str).str.len().max()  # 数据最大长度
                )
                worksheet.set_column(i, i, min(max_len + 2, 30))  # 限制最大宽度为30

            self._log_info(f"成功导出停车时长分布数据，共{len(parking_stats)}条记录")
            
        except Exception as e:
            self._log_warning(f"导出停车时长分布数据失败: {str(e)}")
            # 创建一个空的工作表
            workbook.add_worksheet('停车时长_分析日')
            raise

    def _export_duration_probability_density(self, writer, workbook):
        """导出延停时长概率密度数据到'延停时长概率密度'工作表

        参数:
            writer: ExcelWriter - Excel写入器
            workbook: Workbook - Excel工作簿对象
        """
        try:
            # 计算延停时长概率密度数据
            density_stats = self._calculate_duration_probability_density()

            if density_stats.empty:
                workbook.add_worksheet('延停时长概率密度')
                self._log_warning("没有延停时长概率密度数据可导出")
                return

            # 设置格式
            formats = self._create_excel_formats(workbook)

            # 创建工作表
            sheet_name = '延停时长概率密度'
            worksheet = workbook.add_worksheet(sheet_name)

            # 写入数据
            # 获取所有列名
            columns = density_stats.columns.tolist()

            # 设置表头行高度以适应自动换行
            self._set_header_row_height(worksheet, 0, 35)

            # 写入列名
            for col_num, column in enumerate(columns):
                worksheet.write(0, col_num, column, formats['header'])

            # 写入数据行
            for row_num, row in enumerate(density_stats.itertuples(index=False), start=1):
                for col_num, value in enumerate(row):
                    # 处理数值格式
                    if isinstance(value, (int, float)) and not pd.isna(value):
                        # 频数列保持整数
                        if '频数' in columns[col_num]:
                            value = int(value)
                        # 频率和百分比保持小数
                        elif any(keyword in columns[col_num] for keyword in ['频率', '百分比', '累积']):
                            value = round(float(value), 4) if '频率' in columns[col_num] else round(float(value), 2)

                    worksheet.write(row_num, col_num, value, formats['cell'])

            # 设置列宽
            for i, col in enumerate(columns):
                # 根据列名长度和内容自动调整列宽
                max_len = max(
                    len(str(col)),  # 列名长度
                    density_stats[col].astype(str).str.len().max() if len(density_stats) > 0 else 0  # 数据最大长度
                )
                worksheet.set_column(i, i, min(max_len + 2, 30))  # 限制最大宽度为30

            self._log_info(f"成功导出延停时长概率密度数据，共{len(density_stats)}条记录")

        except Exception as e:
            self._log_warning(f"导出延停时长概率密度数据失败: {str(e)}")
            # 创建一个空的工作表
            workbook.add_worksheet('延停时长概率密度')
            raise

    def _export_peak_flow(self, writer, workbook):
        """导出进出量时间分布数据到'进出量时间分布_分析日'工作表

        参数:
            writer: ExcelWriter - Excel写入器
            workbook: Workbook - Excel工作簿对象

        返回:
            None
        """
        try:
            # 计算高峰流量数据
            peak_flow = self._calculate_peak_flow()
            if peak_flow.empty:
                workbook.add_worksheet('进出量时间分布_分析日')
                self._log_warning("没有进出量时间分布数据可导出")
                return

            # 设置格式
            formats = self._create_excel_formats(workbook)

            # 创建工作表
            sheet_name = '进出量时间分布_分析日'
            worksheet = workbook.add_worksheet(sheet_name)
            
            # 获取所有列名并排序
            columns = peak_flow.columns.tolist()
            
            # 重新排列列顺序：时段、进场数量、出场数量、总流量在前
            preferred_order = ['时段', '进场数量', '出场数量', '总流量']
            other_cols = [col for col in columns if col not in preferred_order]
            ordered_cols = preferred_order + other_cols
            
            # 设置表头行高度以适应自动换行
            self._set_header_row_height(worksheet, 0, 35)

            # 写入列名
            for col_num, column in enumerate(ordered_cols):
                worksheet.write(0, col_num, column, formats['header'])
            
            # 写入数据行
            for row_num, row in peak_flow.iterrows():
                for col_num, column in enumerate(ordered_cols):
                    value = row[column]
                    
                    # 高亮显示高峰时段
                    is_peak = (
                        ('高峰' in column and value == 1) or
                        (column in ['进场数量', '出场数量', '总流量'] and 
                         any(row[f"{col}_高峰"] == 1 for col in ['进场', '出场', '总流量'] if f"{col}_高峰" in peak_flow.columns))
                    )
                    
                    cell_format = formats['highlight'] if is_peak else formats['cell']
                    worksheet.write(row_num + 1, col_num, value, cell_format)
            
            # 设置列宽
            for i, col in enumerate(ordered_cols):
                max_len = max(
                    len(str(col)),
                    peak_flow[col].astype(str).str.len().max()
                )
                worksheet.set_column(i, i, min(max_len + 2, 30))

            self._log_info(f"成功导出进出量时间分布数据，共{len(peak_flow)}条记录")

        except Exception as e:
            self._log_error(f"导出进出量时间分布数据失败: {str(e)}")
            # 创建一个空的工作表
            workbook.add_worksheet('进出量时间分布_分析日')

    def _export_vehicle_gate_time_distribution(self, writer, workbook):
        """导出进出量时间分布_车型_道闸数据到Excel工作表

        参数:
            writer: ExcelWriter - Excel写入器
            workbook: Workbook - Excel工作簿对象

        返回:
            None
        """
        try:
            # 计算车型道闸时间分布数据
            vehicle_gate_data = self._calculate_vehicle_gate_time_distribution()
            if vehicle_gate_data.empty:
                workbook.add_worksheet('进出量时间分布_车型_道闸')
                self._log_warning("没有进出量时间分布_车型_道闸数据可导出")
                return

            # 设置格式
            formats = self._create_excel_formats(workbook)

            # 创建工作表
            sheet_name = '进出量时间分布_车型_道闸'
            worksheet = workbook.add_worksheet(sheet_name)

            # 获取所有列名
            columns = vehicle_gate_data.columns.tolist()

            # 设置表头行高度以适应自动换行
            self._set_header_row_height(worksheet, 0, 35)

            # 写入列名
            for col_num, column in enumerate(columns):
                worksheet.write(0, col_num, column, formats['header'])

            # 写入数据行
            for row_num, row in enumerate(vehicle_gate_data.itertuples(index=False), start=1):
                for col_num, value in enumerate(row):
                    worksheet.write(row_num, col_num, value, formats['cell'])

            # 设置列宽
            for i, col in enumerate(columns):
                # 根据列名长度和内容自动调整列宽
                max_len = max(
                    len(str(col)),  # 列名长度
                    vehicle_gate_data[col].astype(str).str.len().max() if len(vehicle_gate_data) > 0 else 0  # 数据最大长度
                )
                worksheet.set_column(i, i, min(max_len + 2, 30))  # 限制最大宽度为30

            self._log_info(f"成功导出进出量时间分布_车型_道闸数据，共{len(vehicle_gate_data)}条记录")

        except Exception as e:
            self._log_error(f"导出进出量时间分布_车型_道闸数据失败: {str(e)}")
            # 创建一个空的工作表
            workbook.add_worksheet('进出量时间分布_车型_道闸')

    def _export_cleaned_data(self, writer, workbook):
        """导出全周期清洗后数据到'数据_总量'工作表"""
        try:
            # 设置格式
            formats = self._create_excel_formats(workbook)

            # 创建工作表
            sheet_name = '数据_总量'
            worksheet = workbook.add_worksheet(sheet_name)

            # 使用全周期清洗后的数据，而不是经过时间过滤的数据
            data_to_export = self.processed_data

            # 写入数据
            if not data_to_export.empty:
                # 新增：先过滤掉不需要的字段
                exclude_columns = ['timestamp', 'direction', 'gate', 'data_quality']
                # 使用drop方法创建新DataFrame，避免修改原始数据
                filtered_data = data_to_export.drop(columns=[col for col in exclude_columns if col in data_to_export.columns])
                
                # 获取剩余列
                columns = list(filtered_data.columns)

                # 设置表头行高度以适应自动换行
                self._set_header_row_height(worksheet, 0, 35)

                # 写入列名
                for col_num, column in enumerate(columns):
                    worksheet.write(0, col_num, column, formats['header'])
                
                # 写入数据行（修改为使用过滤后的数据）
                for row_num, row in enumerate(filtered_data.itertuples(index=False), start=1):
                    for col_num, value in enumerate(row):
                        # 处理日期时间类型
                        if pd.api.types.is_datetime64_any_dtype(filtered_data.dtypes[col_num]):
                            value = value.strftime('%Y-%m-%d %H:%M:%S') if not pd.isna(value) else ''
                        # 处理duration列，保留3位小数
                        elif column == 'duration' and isinstance(value, (int, float)):
                            value = round(value, 3)
                        
                        worksheet.write(row_num, col_num, value, formats['cell'])
                
                # 自动调整列宽
                for i, col in enumerate(columns):
                    max_len = max(
                        len(str(col)),
                        filtered_data[col].astype(str).str.len().max() if len(filtered_data) > 0 else 0
                    )
                    worksheet.set_column(i, i, min(max_len + 2, 30))
            
            
        except Exception as e:
            self._log_warning(f"导出原始数据失败: {str(e)}")
            raise

    def _export_focus_date_data(self, writer, workbook):
        """导出分析日数据到'数据_分析日'工作表"""
        try:
            # 直接使用原始数据
            raw_data = self.data.copy()
            
            # 检查是否有聚焦日期
            focus_date = self.params.get('date') or self.params.get('聚焦日期')
            if not focus_date:
                workbook.add_worksheet('数据_分析日')
                self._log_warning("未指定分析日期")
                return
            
            # 转换日期格式
            target_date = pd.to_datetime(focus_date).normalize()
            focus_date_start = target_date
            focus_date_end = target_date + pd.Timedelta(days=1)
            
            # 移除不需要的字段
            exclude_columns = ['timestamp', 'direction', 'gate', 'data_quality']
            raw_data = raw_data.drop(columns=[col for col in exclude_columns if col in raw_data.columns])
            
            # 添加计算列：分析日停车时长
            if 'entry_time' in raw_data.columns and 'exit_time' in raw_data.columns:
                raw_data['duration_filter'] = raw_data.apply(
                    lambda row: self._calculate_focus_date_duration(
                        row, focus_date_start, focus_date_end
                    ),
                    axis=1
                )
            
            # 筛选条件：车辆在分析日期间有停留
            # 即：entry_time <= focus_date_end AND (exit_time >= focus_date_start OR exit_time is null)
            mask = (
                (raw_data['entry_time'] <= focus_date_end) & 
                (
                    (raw_data['exit_time'] >= focus_date_start) | 
                    (raw_data['exit_time'].isna())
                )
            )
            filtered_data = raw_data[mask].copy()
            
            if filtered_data.empty:
                workbook.add_worksheet('数据_分析日')
                self._log_warning(f"分析日 {focus_date} 筛选后数据为空")
                return
            
            # 设置格式
            formats = self._create_excel_formats(workbook)
            
            # 创建工作表
            sheet_name = '数据_分析日'
            worksheet = workbook.add_worksheet(sheet_name)
            
            # 写入数据
            columns = list(filtered_data.columns)

            # 设置表头行高度以适应自动换行
            self._set_header_row_height(worksheet, 0, 35)

            # 写入列名
            for col_num, column in enumerate(columns):
                worksheet.write(0, col_num, column, formats['header'])
            
            # 写入数据行
            for row_num, row in enumerate(filtered_data.itertuples(index=False), start=1):
                for col_num, value in enumerate(row):
                    # 处理日期时间类型
                    if pd.api.types.is_datetime64_any_dtype(filtered_data.dtypes[col_num]):
                        value = value.strftime('%Y-%m-%d %H:%M:%S') if not pd.isna(value) else ''
                    # 处理duration列，保留3位小数
                    elif column in ['duration', 'duration_filter'] and isinstance(value, (int, float)):
                        value = round(value, 3)
                    
                    worksheet.write(row_num, col_num, value, formats['cell'])
            
            # 自动调整列宽
            for i, col in enumerate(columns):
                max_len = max(
                    len(str(col)),
                    filtered_data[col].astype(str).str.len().max()
                )
                worksheet.set_column(i, i, min(max_len + 2, 30))
            
            self._log_info(f"成功导出分析日数据，共{len(filtered_data)}条记录")
            
        except Exception as e:
            self._log_warning(f"导出分析日数据失败: {str(e)}")
            # 创建一个空的工作表
            workbook.add_worksheet('数据_分析日')
            raise
    
    def _save_figure_to_memory(self):
        """将当前图表保存到内存"""
        buf = BytesIO()
        plt.savefig(buf, format='png', dpi=300)
        buf.seek(0)
        plt.close()
        return buf
    
    def _check_gate_fields(self, data):
        mode = self.params.get('mode', 'mode1')
        # self._log_info(f"数据实际列名: {data.columns.tolist()}")

        # 无论是mode1还是mode2，处理后的数据都应该包含entry_gate和exit_gate列
        if 'entry_gate' not in data.columns or 'exit_gate' not in data.columns:
            self._log_warning(f"缺少标准化道闸字段: entry_gate 或 exit_gate")
            return False, f"缺少标准化道闸字段: entry_gate 或 exit_gate"
        
        return True, ""
        

    def _calculate_gate_flow_distribution(self, data=None):
        """
        计算出入口流量占比统计

        参数:
            data: DataFrame - 停车数据(可选)

        返回:
            DataFrame - 出入口流量占比统计，包含进场和出场数据在同一行
        """
        try:
            data = data if data is not None else self.data
            if data.empty:
                self._log_warning("输入数据为空")
                return pd.DataFrame(columns=['时间段', '车辆类型', '出入口', '进场数量', '出场数量'])

            # 使用标准化的字段名
            entry_gate_field = self._get_field_name('entry_gate')
            exit_gate_field = self._get_field_name('exit_gate')
            vtype_field = self._get_field_name('vtype')

            # 检查必要字段
            missing_fields = [f for f in [entry_gate_field, exit_gate_field] if f not in data.columns]
            if missing_fields:
                self._log_warning(f"缺少道闸字段: {', '.join(missing_fields)}")
                return pd.DataFrame(columns=['时间段', '车辆类型', '出入口', '进场数量', '出场数量'])

            # 预处理数据
            data = self._preprocess_gate_data(data, entry_gate_field, exit_gate_field, vtype_field)
            if data.empty:
                return pd.DataFrame(columns=['时间段', '车辆类型', '出入口', '进场数量', '出场数量'])

            # 使用_generate_time_periods生成时间段，使用配置中的时间间隔和滑动步长参数
            time_periods = self._generate_time_periods(data)

            # 统计各时间段各道闸的进出场数量
            results = []
            for period in time_periods:
                start_time, end_time = period.split('-')
                start_dt = datetime.strptime(start_time, '%H:%M').time()
                end_dt = datetime.strptime(end_time, '%H:%M').time()

                # 获取当前时间段的进出场数据
                entry_data, exit_data = self._get_period_data(data, start_dt, end_dt)

                # 获取所有道闸（进场道闸和出场道闸的并集）
                all_gates = sorted(set(data[entry_gate_field].dropna().unique()) |
                                 set(data[exit_gate_field].dropna().unique()))

                # 统计每个道闸的进出场数量
                for gate in all_gates:
                    # 按车辆类型统计
                    if vtype_field in data.columns:
                        vtypes = sorted(data[vtype_field].dropna().unique())
                        for vtype in vtypes:
                            # 统计进场数量（该时间段内从该道闸进入的同类车辆）
                            entry_count = len(entry_data[
                                (entry_data[entry_gate_field] == gate) &
                                (entry_data[vtype_field] == vtype)
                            ])

                            # 统计出场数量（该时间段内从该道闸出去的同类车辆）
                            exit_count = len(exit_data[
                                (exit_data[exit_gate_field] == gate) &
                                (exit_data[vtype_field] == vtype)
                            ])

                            results.append({
                                '时间段': period,
                                '车辆类型': vtype,
                                '出入口': gate,
                                '进场数量': entry_count,
                                '出场数量': exit_count
                            })
                    else:
                        # 没有车辆类型字段时只统计总量
                        entry_count = len(entry_data[entry_data[entry_gate_field] == gate])
                        exit_count = len(exit_data[exit_data[exit_gate_field] == gate])

                        results.append({
                            '时间段': period,
                            '车辆类型': '所有车辆',
                            '出入口': gate,
                            '进场数量': entry_count,
                            '出场数量': exit_count
                        })

            if not results:
                self._log_warning("未生成任何统计结果")
                return pd.DataFrame(columns=['时间段', '车辆类型', '出入口', '进场数量', '出场数量'])

            return pd.DataFrame(results)

        except Exception as e:
            self._log_error(f"计算出入口流量分布失败: {str(e)}")
            return pd.DataFrame(columns=['时间段', '车辆类型', '出入口', '进场数量', '出场数量'])

    def _preprocess_gate_data(self, data, entry_gate_field, exit_gate_field, vtype_field):
        """预处理道闸数据"""
        # 确保时间字段是datetime类型
        data = data.copy()
        data['entry_time'] = pd.to_datetime(data['entry_time'], errors='coerce')
        data['exit_time'] = pd.to_datetime(data['exit_time'], errors='coerce')

        # 只移除入场时间无效的记录，出场时间可以为空（车辆可能还在停车场内）
        data = data[~data['entry_time'].isna()]
        if data.empty:
            self._log_warning("移除无效入场时间记录后数据为空")

        return data

    def _get_period_data(self, data, start_dt, end_dt):
        """获取指定时间段的进出场数据"""
        entry_mask = (
            (data['entry_time'].dt.time >= start_dt) &
            (data['entry_time'].dt.time < end_dt)
        )
        # 出场数据需要排除空值
        exit_mask = (
            (~data['exit_time'].isna()) &
            (data['exit_time'].dt.time >= start_dt) &
            (data['exit_time'].dt.time < end_dt)
        )
        return data[entry_mask], data[exit_mask]



    def _export_gate_flow_distribution(self, writer, workbook):
        """
        导出进出量时间分布_车型_道闸_三列式到Excel工作表

        参数:
            writer: ExcelWriter - Excel写入器
            workbook: Workbook - Excel工作簿对象
        """
        try:
            # 计算出入口流量占比数据
            flow_distribution = self._calculate_gate_flow_distribution()

            # 确保flow_distribution是DataFrame
            if isinstance(flow_distribution, pd.Series):
                flow_distribution = flow_distribution.to_frame().T

            if flow_distribution.empty or not isinstance(flow_distribution, pd.DataFrame):
                # 创建空工作表
                self._create_empty_sheet(workbook, '进出量时间分布_车型_道闸_三列式')
                self._log_warning("没有有效的进出量时间分布_车型_道闸_三列式数据可导出")
                return

            # 设置格式
            formats = self._create_excel_formats(workbook)

            # 创建工作表
            sheet_name = '进出量时间分布_车型_道闸_三列式'
            worksheet = self._get_or_create_worksheet(workbook, sheet_name)
            
            # 写入数据
            if isinstance(flow_distribution, pd.DataFrame):
                try:
                    # 获取所有列名
                    columns = flow_distribution.columns.tolist()

                    # 设置表头行高度以适应自动换行
                    self._set_header_row_height(worksheet, 0, 35)

                    # 写入列名（第一列为时间段）
                    worksheet.write(0, 0, columns[0], formats['header'])
                    
                    # 使用标准化的字段名
                    entry_gate_field = 'entry_gate'
                    exit_gate_field = 'exit_gate'
                    
                   
                    # 处理其他列名格式
                    for col_num, column in enumerate(columns[1:], start=1):
                        try:
                            # 根据列的类型进行不同的处理
                            if isinstance(column, str):
                                formatted_name = column
                            elif isinstance(column, tuple):
                                # 过滤掉第一个元素（如果它等于columns[0]）并转换所有部分为字符串
                                parts = [str(part) for part in column if str(part) != str(columns[0])]
                                formatted_name = "-".join(parts) if parts else str(column)
                            elif isinstance(column, list):
                                parts = [str(part) for part in column if str(part) != str(columns[0])]
                                formatted_name = "-".join(parts) if parts else str(column)
                            else:
                                formatted_name = str(column)

                            # 确保格式化后的名称不为空
                            if not formatted_name.strip():
                                formatted_name = f"column_{col_num}"

                            
                        except Exception as e:
                            self._log_error(f"处理列名时出错: {e}")
                            formatted_name = f"column_{col_num}"
                        
                        worksheet.write(0, col_num, formatted_name, formats['header'])

                        
                except Exception as e:
                    self._log_error(f"处理列名时出错: {str(e)}")
                    raise
                
                # 写入数据行
                for row_num, row in enumerate(flow_distribution.itertuples(index=False), start=1):
                    for col_num, value in enumerate(row):
                        try:
                            # 根据值的类型进行不同的处理
                            if pd.isna(value):
                                safe_value = 0  # 将NA值转换为0
                            elif isinstance(value, (int, float)):
                                safe_value = value  # 数值类型保持不变
                            elif isinstance(value, (tuple, list, dict)):
                                safe_value = str(value)  # 复杂类型转换为字符串
                            else:
                                safe_value = str(value)  # 其他类型转换为字符串
                            
                            worksheet.write(row_num, col_num, safe_value, formats['cell'])
                        except Exception as e:
                            self._log_error(f"处理单元格数据时出错 [行:{row_num}, 列:{col_num}]: {e}")
                            worksheet.write(row_num, col_num, "ERROR", formats['cell'])
            
            # 设置列宽
            for i, col in enumerate(columns):
                # 根据列名长度和内容自动调整列宽
                max_len = max(
                    len(str(col)),  # 列名长度
                    flow_distribution[col].astype(str).str.len().max() if len(flow_distribution) > 0 else 0  # 数据最大长度
                )
                worksheet.set_column(i, i, min(max_len + 2, 30))  # 限制最大宽度为30
            
            self._log_info(f"成功导出进出量时间分布_车型_道闸_三列式数据，共{len(flow_distribution)}条记录")

        except Exception as e:
            self._log_warning(f"导出进出量时间分布_车型_道闸_三列式数据失败: {str(e)}")
            # 创建一个空的工作表
            self._create_empty_sheet(workbook, '进出量时间分布_车型_道闸_三列式')
    
    def _get_or_create_worksheet(self, workbook, sheet_name):
        """获取或创建工作表，处理名称冲突"""
        try:
            # 检查工作表是否已存在
            if sheet_name in [sheet.name for sheet in workbook.worksheets()]:
                worksheet = workbook.get_worksheet_by_name(sheet_name)
                worksheet.clear()
                return worksheet
            
            # 尝试创建新工作表
            return workbook.add_worksheet(sheet_name)
        except:
            # 如果名称冲突，添加后缀
            for i in range(1, 10):
                new_name = f"{sheet_name}_{i}"
                if new_name not in [sheet.name for sheet in workbook.worksheets()]:
                    return workbook.add_worksheet(new_name)
            # 如果所有尝试都失败，返回第一个工作表
            return workbook.get_worksheet_by_name(workbook.worksheets()[0].name)
    
    def _create_empty_sheet(self, workbook, sheet_name):
        """创建空工作表，处理名称冲突"""
        try:
            if sheet_name not in [sheet.name for sheet in workbook.worksheets()]:
                return workbook.add_worksheet(sheet_name)
            return workbook.get_worksheet_by_name(sheet_name)
        except:
            # 如果名称冲突，添加后缀
            for i in range(1, 10):
                new_name = f"{sheet_name}_{i}"
                if new_name not in [sheet.name for sheet in workbook.worksheets()]:
                    return workbook.add_worksheet(new_name)
            # 如果所有尝试都失败，返回第一个工作表
            return workbook.get_worksheet_by_name(workbook.worksheets()[0].name)

    def _export_gate_pairs_analysis(self, writer, workbook):
        """导出道闸进出组合统计到Excel工作表"""
        try:
            # 检查分析结果中是否有道闸进出组合数据
            if 'gate_pairs_analysis' not in self.analysis_results:
                self._log_warning("分析结果中缺少道闸进出组合数据")
                return
                
            # 生成完整的时间段列表，确保所有时间段都被包含
            all_time_periods = self._generate_time_periods(self.data)

            # 准备总体数据
            report_data = []
            for period in all_time_periods:
                # 获取该时间段的分析数据，如果没有则使用空字典
                period_data = self.analysis_results['gate_pairs_analysis'].get(period, {})

                # 获取进场和出场统计数据（总体）
                entry_pairs = period_data.get('entry_based', {}).get('total', {})
                exit_pairs = period_data.get('exit_based', {}).get('total', {})

                # 计算进场和出场总量
                entry_total = sum(entry_pairs.values()) if entry_pairs else 0
                exit_total = sum(exit_pairs.values()) if exit_pairs else 0

                # 合并所有唯一的道闸组合
                all_pairs = set()
                for pairs in [entry_pairs.keys(), exit_pairs.keys()]:
                    for pair in pairs:
                        if isinstance(pair, tuple):
                            all_pairs.add(pair)
                        else:
                            try:
                                all_pairs.add(eval(pair))
                            except:
                                all_pairs.add((str(pair), str(pair)))

                # 如果该时间段没有数据，添加一个空行以保持时间段的完整性
                if not all_pairs:
                    report_data.append({
                        '时间段': period,
                        '入口道闸': '-',
                        '出口道闸': '-',
                        '总量(按进场时间)': 0,
                        '进场占比(%)': 0,
                        '总量(按出场时间)': 0,
                        '出场占比(%)': 0
                    })
                else:
                    # 为每个道闸组合生成统计数据
                    for entry_gate, exit_gate in sorted(all_pairs):
                        # 获取进场统计
                        entry_count = entry_pairs.get((entry_gate, exit_gate), 0)
                        entry_percentage = round((entry_count / entry_total * 100), 2) if entry_total > 0 else 0

                        # 获取出场统计
                        exit_count = exit_pairs.get((entry_gate, exit_gate), 0)
                        exit_percentage = round((exit_count / exit_total * 100), 2) if exit_total > 0 else 0

                        report_data.append({
                            '时间段': period,
                            '入口道闸': entry_gate,
                            '出口道闸': exit_gate,
                            '总量(按进场时间)': entry_count,
                            '进场占比(%)': entry_percentage,
                            '总量(按出场时间)': exit_count,
                            '出场占比(%)': exit_percentage
                        })
            
            # 如果没有数据，添加一个空行
            if not report_data:
                report_data.append({
                    '时间段': '-',
                    '入口道闸': '-',
                    '出口道闸': '-',
                    '总量(按进场时间)': 0,
                    '进场占比(%)': 0,
                    '总量(按出场时间)': 0,
                    '出场占比(%)': 0
                })
            
            # 创建总体统计工作表
            df_total = pd.DataFrame(report_data)

            # 设置格式
            formats = self._create_excel_formats(workbook)

            # 手动创建工作表并写入数据
            worksheet = workbook.add_worksheet('道闸组合_分析日')

            # 设置表头行高度以适应自动换行
            self._set_header_row_height(worksheet, 0, 35)

            # 写入表头
            for col_num, column in enumerate(df_total.columns):
                worksheet.write(0, col_num, column, formats['header'])

            # 写入数据行
            for row_num, row in enumerate(df_total.itertuples(index=False), start=1):
                for col_num, value in enumerate(row):
                    worksheet.write(row_num, col_num, value, formats['cell'])

            # 设置列宽自适应
            for idx, col in enumerate(df_total.columns):
                max_len = max(df_total[col].astype(str).map(len).max(), len(col)) + 2
                worksheet.set_column(idx, idx, max_len)
            
            # 按车辆类型导出数据
            # 首先收集所有车辆类型
            all_vtypes = set()
            for period_data in self.analysis_results['gate_pairs_analysis'].values():
                entry_vtypes = period_data.get('entry_based', {}).get('by_vtype', {})
                exit_vtypes = period_data.get('exit_based', {}).get('by_vtype', {})
                all_vtypes.update(entry_vtypes.keys())
                all_vtypes.update(exit_vtypes.keys())

            # 为每个车辆类型生成完整的时间段数据
            for vtype in all_vtypes:
                vtype_data = []

                for period in all_time_periods:
                    # 获取该时间段的分析数据
                    period_data = self.analysis_results['gate_pairs_analysis'].get(period, {})

                    # 获取该车型的进出场数据
                    entry_vtypes = period_data.get('entry_based', {}).get('by_vtype', {})
                    exit_vtypes = period_data.get('exit_based', {}).get('by_vtype', {})
                    entry_pairs = entry_vtypes.get(vtype, {})
                    exit_pairs = exit_vtypes.get(vtype, {})

                    # 计算该车型在该时间段的进出场总量
                    entry_total = sum(entry_pairs.values()) if entry_pairs else 0
                    exit_total = sum(exit_pairs.values()) if exit_pairs else 0

                    # 合并所有唯一的道闸组合
                    all_pairs = set()
                    for pairs in [entry_pairs.keys(), exit_pairs.keys()]:
                        for pair in pairs:
                            if isinstance(pair, tuple):
                                all_pairs.add(pair)
                            else:
                                try:
                                    all_pairs.add(eval(pair))
                                except:
                                    all_pairs.add((str(pair), str(pair)))

                    # 如果该时间段没有数据，添加一个空行以保持时间段的完整性
                    if not all_pairs:
                        vtype_data.append({
                            '时间段': period,
                            '入口道闸': '-',
                            '出口道闸': '-',
                            '总量(按进场时间)': 0,
                            '进场占比(%)': 0,
                            '总量(按出场时间)': 0,
                            '出场占比(%)': 0
                        })
                    else:
                        # 为每个道闸组合生成统计数据
                        for entry_gate, exit_gate in sorted(all_pairs):
                            # 获取进场统计
                            entry_count = entry_pairs.get((entry_gate, exit_gate), 0)
                            entry_percentage = round((entry_count / entry_total * 100), 2) if entry_total > 0 else 0

                            # 获取出场统计
                            exit_count = exit_pairs.get((entry_gate, exit_gate), 0)
                            exit_percentage = round((exit_count / exit_total * 100), 2) if exit_total > 0 else 0

                            vtype_data.append({
                                '时间段': period,
                                '入口道闸': entry_gate,
                                '出口道闸': exit_gate,
                                '总量(按进场时间)': entry_count,
                                '进场占比(%)': entry_percentage,
                                '总量(按出场时间)': exit_count,
                                '出场占比(%)': exit_percentage
                            })

                # 为该车辆类型创建sheet
                if vtype_data:
                    # 安全处理工作表名称
                    safe_vtype = str(vtype).replace('/', '_').replace('\\', '_').replace('?', '_')
                    safe_vtype = safe_vtype.replace('[', '(').replace(']', ')').replace(':', '-')
                    if len(safe_vtype) > 20:  # Excel工作表名称最长31个字符
                        safe_vtype = safe_vtype[:17] + '...'

                    sheet_name = f"道闸组合_{safe_vtype}"

                    # 创建DataFrame并手动写入Excel
                    df_vtype = pd.DataFrame(vtype_data)

                    # 手动创建工作表并写入数据
                    worksheet_vtype = workbook.add_worksheet(sheet_name)

                    # 设置表头行高度以适应自动换行
                    self._set_header_row_height(worksheet_vtype, 0, 35)

                    # 写入表头
                    for col_num, column in enumerate(df_vtype.columns):
                        worksheet_vtype.write(0, col_num, column, formats['header'])

                    # 写入数据行
                    for row_num, row in enumerate(df_vtype.itertuples(index=False), start=1):
                        for col_num, value in enumerate(row):
                            worksheet_vtype.write(row_num, col_num, value, formats['cell'])

                    # 设置列宽自适应
                    for idx, col in enumerate(df_vtype.columns):
                        max_len = max(df_vtype[col].astype(str).map(len).max(), len(col)) + 2
                        worksheet_vtype.set_column(idx, idx, max_len)
            
            self._log_info("成功导出道闸进出组合统计")
            
        except Exception as e:
            self._log_warning(f"导出道闸进出组合统计失败: {str(e)}")
            self._log_warning(traceback.format_exc())

    def _export_gate_time_distribution(self, writer, workbook):
        """导出按道闸统计的进出量时间分布数据
        
        参数:
            writer: ExcelWriter - Excel写入器
            workbook: Workbook - Excel工作簿对象
        """
        try:
            # 获取时间分布分析结果
            time_distribution = self.analysis_results.get('time_distribution', {})
            
            # 检查是否有按道闸统计的数据
            if not time_distribution.get('entry_by_gate') or not time_distribution.get('exit_by_gate'):
                self._log_warning("没有按道闸统计的进出量数据")
                return
            
            # 设置格式
            formats = self._create_excel_formats(workbook)
            
            # 获取所有道闸
            entry_gates = time_distribution['entry_by_gate'].keys()
            exit_gates = time_distribution['exit_by_gate'].keys()
            all_gates = sorted(set(entry_gates) | set(exit_gates))
            
            # 创建道闸进出量工作表
            gate_sheet_name = '进出量-道闸时间分布_分析日'
            gate_worksheet = workbook.add_worksheet(gate_sheet_name)
            
            # 使用与"进出量时间分布"sheet相同的时间段生成算法
            # 不传递自定义参数，使用默认的params配置，保持与主sheet一致
            time_periods = self._generate_time_periods(self.data)

            # 准备新的数据结构：时间段为行，道闸为列
            # 格式：时间段, 道闸A_进, 道闸A_出, 道闸A_总量, 道闸B_进, 道闸B_出, 道闸B_总量, ...
            pivot_data = []

            for time_period in time_periods:
                row_data = {'时间段': time_period}

                # 为每个道闸添加进、出、总量列
                for gate in all_gates:
                    # 直接从原始数据重新计算统计，而不是依赖预计算的分析结果
                    entry_count = self._calculate_gate_period_count(gate, time_period, 'entry')
                    exit_count = self._calculate_gate_period_count(gate, time_period, 'exit')

                    # 添加该道闸的数据到行中
                    row_data[f'{gate}_进'] = entry_count
                    row_data[f'{gate}_出'] = exit_count
                    row_data[f'{gate}_总量'] = entry_count + exit_count

                pivot_data.append(row_data)
            
            # 创建DataFrame
            pivot_df = pd.DataFrame(pivot_data)

            # 构建列名列表
            columns = ['时间段']
            for gate in all_gates:
                columns.extend([f'{gate}_进', f'{gate}_出', f'{gate}_总量'])

            # 设置表头行高度以适应自动换行
            self._set_header_row_height(gate_worksheet, 0, 35)

            # 写入列名
            for col_num, column in enumerate(columns):
                gate_worksheet.write(0, col_num, column, formats['header'])

            # 写入数据行
            for row_num, row in enumerate(pivot_df.itertuples(index=False), start=1):
                for col_num, value in enumerate(row):
                    gate_worksheet.write(row_num, col_num, value, formats['cell'])

            # 设置列宽
            for i, col in enumerate(columns):
                if i == 0:  # 时间段列
                    gate_worksheet.set_column(i, i, 15)
                else:  # 数据列
                    gate_worksheet.set_column(i, i, 12)
            
            # 计算数据结束行，用于确定图表位置
            data_end_row = len(pivot_df) + 1  # +1因为有标题行

            # 为每个道闸创建图表，放置在数据下方
            chart_start_row = data_end_row + 3  # 在数据下方留3行空隙
            charts_per_row = 2  # 每行放置2个图表
            chart_width = 450
            chart_height = 300

            for gate_index, gate in enumerate(all_gates):
                # 创建组合图表
                chart = workbook.add_chart({'type': 'column'})

                # 计算该道闸对应的列索引
                gate_entry_col = 1 + gate_index * 3  # 进场列
                gate_exit_col = 2 + gate_index * 3   # 出场列
                gate_total_col = 3 + gate_index * 3  # 总量列

                # 数据范围：从第1行到最后一行（第0行是标题）
                start_row = 1
                end_row = len(pivot_df)

                # 添加进场数据系列
                chart.add_series({
                    'name': f"{gate}_进",
                    'categories': [gate_sheet_name, start_row, 0, end_row, 0],  # 时间段列
                    'values': [gate_sheet_name, start_row, gate_entry_col, end_row, gate_entry_col],
                    'fill': {'color': '#4F81BD'}
                })

                # 添加出场数据系列
                chart.add_series({
                    'name': f"{gate}_出",
                    'categories': [gate_sheet_name, start_row, 0, end_row, 0],  # 时间段列
                    'values': [gate_sheet_name, start_row, gate_exit_col, end_row, gate_exit_col],
                    'fill': {'color': '#C0504D'}
                })

                # 添加总流量线形图
                line_chart = workbook.add_chart({'type': 'line'})
                line_chart.add_series({
                    'name': f"{gate}_总流量",
                    'categories': [gate_sheet_name, start_row, 0, end_row, 0],  # 时间段列
                    'values': [gate_sheet_name, start_row, gate_total_col, end_row, gate_total_col],
                    'line': {'color': '#9BBB59', 'width': 2.5},
                    'marker': {'type': 'diamond', 'size': 5}
                })

                # 组合图表
                chart.combine(line_chart)

                # 设置图表标题和轴标签
                chart.set_title({'name': f'道闸 {gate} 进出量分布'})
                chart.set_x_axis({
                    'name': '时段',
                    'text_axis': True,
                    'num_font': {'rotation': -45}
                })
                chart.set_y_axis({'name': '车辆数量'})

                # 图表已移至分析图sheet，此处不再插入图表
            
            self._log_info(f"成功导出按道闸统计的进出量分布数据，共处理{len(all_gates)}个道闸")

        except Exception as e:
            self._log_error(f"导出按道闸统计的进出量分布数据失败: {str(e)}")

    def _export_parking_duration_by_entry_period(self, writer, workbook):
        """导出按入场时段的停车时长统计数据

        参数:
            writer: ExcelWriter - Excel写入器
            workbook: Workbook - Excel工作簿对象
        """
        try:
            # 计算按入场时段的停车时长统计
            duration_stats = self._calculate_parking_duration_by_entry_period()

            if duration_stats.empty:
                self._log_warning("没有按入场时段的停车时长数据可导出")
                # 创建一个空的工作表
                worksheet = workbook.add_worksheet('停车时长_入场_分析日')
                return

            # 设置格式
            formats = self._create_excel_formats(workbook)

            # 创建工作表
            sheet_name = '停车时长_入场_分析日'
            worksheet = workbook.add_worksheet(sheet_name)

            # 设置表头行高度以适应自动换行
            self._set_header_row_height(worksheet, 0, 35)

            # 写入列名
            columns = duration_stats.columns.tolist()
            for col_num, column in enumerate(columns):
                worksheet.write(0, col_num, column, formats['header'])

            # 写入数据行
            for row_num, row in enumerate(duration_stats.itertuples(index=False), start=1):
                for col_num, value in enumerate(row):
                    # 对于时长数据，保留2位小数
                    if col_num > 0 and isinstance(value, (int, float)) and not pd.isna(value):
                        formatted_value = round(float(value), 2)
                        worksheet.write(row_num, col_num, formatted_value, formats['cell'])
                    else:
                        worksheet.write(row_num, col_num, value if not pd.isna(value) else 0, formats['cell'])

            # 设置列宽
            for i, col in enumerate(columns):
                if i == 0:  # 时间段列
                    worksheet.set_column(i, i, 15)
                else:  # 数据列
                    worksheet.set_column(i, i, 12)

            self._log_info(f"成功导出按入场时段的停车时长统计，共{len(duration_stats)}条记录")

        except Exception as e:
            self._log_error(f"导出按入场时段的停车时长统计失败: {str(e)}")
            # 创建一个空的工作表
            try:
                workbook.add_worksheet('停车时长_入场_分析日')
            except:
                pass  # 工作表可能已存在

    def _calculate_parking_duration_by_entry_period(self, data=None):
        """
        计算按入场时段的停车时长统计

        参数:
            data: DataFrame - 停车数据(可选)

        返回:
            DataFrame - 按入场时段的停车时长统计
        """
        try:
            data = data if data is not None else self.data
            if data.empty:
                return pd.DataFrame()

            # 检查必要字段是否存在
            entry_time_field = self._get_field_name('entry_time')
            exit_time_field = self._get_field_name('exit_time')
            vtype_field = self._get_field_name('vtype')
            duration_field = self._get_field_name('duration')

            required_fields = [entry_time_field, exit_time_field, duration_field]
            missing_fields = [f for f in required_fields if f not in data.columns]
            if missing_fields:
                self._log_warning(f"缺少必要字段: {', '.join(missing_fields)}")
                return pd.DataFrame()

            # 过滤有效数据：必须有入场时间、出场时间和停车时长
            valid_data = data[
                data[entry_time_field].notna() &
                data[exit_time_field].notna() &
                data[duration_field].notna() &
                (data[duration_field] >= 0)
            ].copy()

            if valid_data.empty:
                self._log_warning("没有有效的停车时长数据")
                return pd.DataFrame()

            # 确保时间字段为datetime类型
            valid_data[entry_time_field] = pd.to_datetime(valid_data[entry_time_field], errors='coerce')
            valid_data = valid_data[valid_data[entry_time_field].notna()]

            if valid_data.empty:
                self._log_warning("入场时间数据无效")
                return pd.DataFrame()

            # 生成时间段
            time_periods = self._generate_time_periods(data)

            # 获取车辆类型
            vehicle_types = []
            if vtype_field in data.columns:
                vehicle_types = sorted(valid_data[vtype_field].dropna().unique())

            # 准备结果数据
            results = []

            for period in time_periods:
                # 解析时间段
                start_time_str, end_time_str = period.split('-')
                start_time = pd.to_datetime(start_time_str, format='%H:%M').time()
                end_time = pd.to_datetime(end_time_str, format='%H:%M').time()

                # 筛选在该时间段内入场的车辆
                if start_time < end_time:
                    # 正常时间段（不跨天）
                    period_mask = (
                        (valid_data[entry_time_field].dt.time >= start_time) &
                        (valid_data[entry_time_field].dt.time < end_time)
                    )
                else:
                    # 跨天时间段（如23:30-00:30）
                    period_mask = (
                        (valid_data[entry_time_field].dt.time >= start_time) |
                        (valid_data[entry_time_field].dt.time < end_time)
                    )

                period_data = valid_data[period_mask]

                # 初始化该时间段的结果
                row_data = {'时间段': period}

                if vehicle_types:
                    # 按车辆类型统计
                    for vtype in vehicle_types:
                        vtype_data = period_data[period_data[vtype_field] == vtype]
                        if not vtype_data.empty:
                            avg_duration = vtype_data[duration_field].mean()
                            row_data[vtype] = avg_duration
                        else:
                            row_data[vtype] = 0
                else:
                    # 没有车辆类型字段时统计总体
                    if not period_data.empty:
                        avg_duration = period_data[duration_field].mean()
                        row_data['平均停车时长'] = avg_duration
                    else:
                        row_data['平均停车时长'] = 0

                results.append(row_data)

            # 创建DataFrame
            result_df = pd.DataFrame(results)

            self._log_info(f"按入场时段停车时长统计完成: {len(result_df)}个时间段")
            return result_df

        except Exception as e:
            self._log_error(f"计算按入场时段停车时长统计失败: {str(e)}")
            import traceback
            self._log_error(f"错误详情: {traceback.format_exc()}")
            return pd.DataFrame()

    def _calculate_gate_period_count(self, gate, time_period, direction):
        """
        计算指定道闸在指定时间段的车辆数量

        参数:
            gate: str - 道闸编号
            time_period: str - 时间段，格式如"00:00-00:30"
            direction: str - 方向，'entry'或'exit'

        返回:
            int - 车辆数量
        """
        try:
            # 解析时间段
            start_time_str, end_time_str = time_period.split('-')
            start_time = pd.to_datetime(start_time_str, format='%H:%M').time()
            end_time = pd.to_datetime(end_time_str, format='%H:%M').time()

            # 根据方向选择相应的字段，使用标准化字段名称
            if direction == 'entry':
                time_field = self._get_field_name('entry_time')
                gate_field = self._get_field_name('entry_gate')
            elif direction == 'exit':
                time_field = self._get_field_name('exit_time')
                gate_field = self._get_field_name('exit_gate')
            else:
                self._log_warning(f"无效的方向参数: {direction}")
                return 0

            # 检查必要字段是否存在
            if time_field not in self.data.columns or gate_field not in self.data.columns:
                self._log_warning(f"缺少必要字段: {time_field} 或 {gate_field}")
                self._log_warning(f"可用字段: {list(self.data.columns)}")
                return 0

            # 筛选指定道闸的数据
            gate_data = self.data[self.data[gate_field] == gate].copy()

            if gate_data.empty:
                return 0

            # 确保时间字段为datetime类型
            gate_data[time_field] = pd.to_datetime(gate_data[time_field], errors='coerce')
            gate_data = gate_data.dropna(subset=[time_field])

            if gate_data.empty:
                return 0

            # 筛选该时间段的数据
            if start_time < end_time:
                # 正常时间段（不跨天）
                mask = (gate_data[time_field].dt.time >= start_time) & \
                       (gate_data[time_field].dt.time < end_time)
            else:
                # 跨天时间段（如23:30-00:30）
                mask = (gate_data[time_field].dt.time >= start_time) | \
                       (gate_data[time_field].dt.time < end_time)

            count = mask.sum()
            return int(count)

        except Exception as e:
            self._log_warning(f"计算道闸{gate}在时间段{time_period}的{direction}数量失败: {str(e)}")
            import traceback
            self._log_warning(f"错误详情: {traceback.format_exc()}")
            return 0

    def _export_occupancy(self, writer, workbook):
        """导出在场车辆分布到Excel工作表"""
        try:
            # 获取聚焦日期
            focus_date = self.params.get('聚焦日期')
            focus_month = self.params.get('聚焦月份')

            # 计算在场车辆分布
            occupancy = self._calculate_occupancy(self.data, focus_date, focus_month)

            if occupancy.empty:
                workbook.add_worksheet('在场车辆分布')
                self._log_warning("无法计算在场车辆分布")
                return

            # 设置格式
            formats = self._create_excel_formats(workbook)

            # 创建工作表
            sheet_name = '在场车辆分布'
            worksheet = workbook.add_worksheet(sheet_name)

            # 透视表转换：将车辆类型作为列
            if '车辆类型' in occupancy.columns:
                pivot_occupancy = occupancy.pivot(index='时间', columns='车辆类型', values='在场车辆数').reset_index()
            else:
                pivot_occupancy = occupancy.copy()

            # 手动写入数据以控制格式
            columns = pivot_occupancy.columns.tolist()

            # 设置表头行高度以适应自动换行
            self._set_header_row_height(worksheet, 0, 35)

            # 写入表头
            for col_num, column in enumerate(columns):
                # 修改列名显示
                if col_num == 0:
                    worksheet.write(0, col_num, "时间段", formats['header'])
                elif column == '在场车辆数' or (col_num == 1 and len(columns) == 2):
                    worksheet.write(0, col_num, "日均在场车辆", formats['header'])
                else:
                    worksheet.write(0, col_num, column, formats['header'])

            # 写入数据行
            for row_num, row in enumerate(pivot_occupancy.itertuples(index=False), start=1):
                for col_num, value in enumerate(row):
                    # 第一列是时间段，直接写入
                    if col_num == 0:
                        worksheet.write(row_num, col_num, value, formats['cell'])
                    else:
                        # 其他列是在场车辆数，格式化为3位小数
                        if isinstance(value, (int, float)) and not pd.isna(value):
                            formatted_value = round(float(value), 3)
                            worksheet.write(row_num, col_num, formatted_value, formats['cell'])
                        else:
                            worksheet.write(row_num, col_num, value if not pd.isna(value) else 0, formats['cell'])

            # 设置固定列宽（不自动调整）
            worksheet.set_column('A:A', 15)  # 时间列
            for i in range(1, len(columns)):
                worksheet.set_column(i, i, 15)  # 其他列固定宽度

            # 记录日志
            self._log_info(f"成功导出在场车辆分布统计，共{len(pivot_occupancy)}条记录")

        except Exception as e:
            self._log_warning(f"导出在场车辆分布统计失败: {str(e)}")
            # 创建一个空的工作表
            workbook.add_worksheet('在场车辆分布')

    def _export_daily_stats(self, writer, workbook):
        """导出每日统计信息到'日进出量_分析周期'工作表"""
        try:
            # 获取每日统计数据 - 使用全周期清洗后的数据（与数据_总量sheet保持一致）
            daily_stats = self._calculate_daily_stats(data=self.processed_data)
            if daily_stats.empty:
                workbook.add_worksheet('日进出量_分析周期')
                self._log_warning("没有每日统计信息可导出")
                return

            # 设置格式
            formats = self._create_excel_formats(workbook)

            # 创建工作表
            sheet_name = '日进出量_分析周期'
            worksheet = workbook.add_worksheet(sheet_name)
            
            # 写入数据列名（动态）
            if not daily_stats.empty:
                columns = list(daily_stats.columns)

                # 设置表头行高度以适应自动换行
                self._set_header_row_height(worksheet, 0, 35)

                for col_num, column in enumerate(columns):
                    worksheet.write(0, col_num, column, formats['header'])

                # 写入数据行
                for row_num, row in enumerate(daily_stats.itertuples(index=False), start=1):
                    for col_num, value in enumerate(row):
                        worksheet.write(row_num, col_num, value, formats['cell'])
                
                # 自动调整列宽
                for i, col in enumerate(columns):
                    max_len = max(
                        len(str(col)),
                        daily_stats[col].astype(str).str.len().max()
                    )
                    worksheet.set_column(i, i, min(max_len + 2, 30))  # 最大列宽为30
            
            self._log_info(f"成功导出每日统计信息，共{len(daily_stats)}条记录")
            
        except Exception as e:
            self._log_warning(f"导出每日统计信息失败: {str(e)}")
            workbook.add_worksheet('分析周期_每日')           # 