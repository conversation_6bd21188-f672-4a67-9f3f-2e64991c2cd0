<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Awesome-pyecharts</title>
                <script type="text/javascript" src="https://assets.pyecharts.org/assets/v5/echarts.min.js"></script>
            <script type="text/javascript" src="https://assets.pyecharts.org/assets/v5/themes/macarons.js"></script>

    
</head>
<body >
    <div id="f866375dcc20429e9a66c98bdd73dc6a" class="chart-container" style="width:1200px; height:600px; "></div>
    <script>
        var chart_f866375dcc20429e9a66c98bdd73dc6a = echarts.init(
            document.getElementById('f866375dcc20429e9a66c98bdd73dc6a'), 'macarons', {renderer: 'canvas'});
        var option_f866375dcc20429e9a66c98bdd73dc6a = {
    "animation": true,
    "animationThreshold": 2000,
    "animationDuration": 1000,
    "animationEasing": "cubicOut",
    "animationDelay": 0,
    "animationDurationUpdate": 300,
    "animationEasingUpdate": "cubicOut",
    "animationDelayUpdate": 0,
    "aria": {
        "enabled": false
    },
    "series": [
        {
            "type": "bar",
            "name": "\u603b\u91cf",
            "legendHoverLink": true,
            "data": [
                17,
                10,
                9,
                4,
                2,
                2,
                1,
                1,
                4,
                1,
                1,
                0,
                2,
                1,
                3,
                2,
                3,
                3,
                1,
                4,
                1,
                2,
                4,
                1,
                1,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                6,
                0,
                0,
                0,
                0,
                0,
                1,
                0,
                1,
                0,
                0,
                0,
                0,
                0,
                0,
                1,
                0,
                0,
                0,
                0,
                2,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                1,
                1,
                1,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                1,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                1,
                14,
                0
            ],
            "realtimeSort": false,
            "showBackground": false,
            "stackStrategy": "samesign",
            "cursor": "pointer",
            "barMinHeight": 0,
            "barCategoryGap": "20%",
            "barGap": "30%",
            "large": false,
            "largeThreshold": 400,
            "seriesLayoutBy": "column",
            "datasetIndex": 0,
            "clip": true,
            "zlevel": 0,
            "z": 2,
            "label": {
                "show": false,
                "margin": 8,
                "valueAnimation": false
            },
            "itemStyle": {
                "color": "#F18F01",
                "borderColor": "#ffffff",
                "borderWidth": 1
            }
        },
        {
            "type": "bar",
            "name": "\u5c0f\u578b\u8f66",
            "legendHoverLink": true,
            "data": [
                17,
                10,
                9,
                4,
                2,
                2,
                1,
                1,
                4,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0
            ],
            "realtimeSort": false,
            "showBackground": false,
            "stackStrategy": "samesign",
            "cursor": "pointer",
            "barMinHeight": 0,
            "barCategoryGap": "20%",
            "barGap": "30%",
            "large": false,
            "largeThreshold": 400,
            "seriesLayoutBy": "column",
            "datasetIndex": 0,
            "clip": true,
            "zlevel": 0,
            "z": 2,
            "label": {
                "show": false,
                "margin": 8,
                "valueAnimation": false
            },
            "itemStyle": {
                "color": "#2E86AB",
                "borderColor": "#ffffff",
                "borderWidth": 1
            }
        },
        {
            "type": "bar",
            "name": "\u4e2d\u578b\u8f66",
            "legendHoverLink": true,
            "data": [
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                1,
                1,
                0,
                2,
                1,
                3,
                2,
                3,
                3,
                1,
                4,
                1,
                2,
                4,
                1,
                1,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0
            ],
            "realtimeSort": false,
            "showBackground": false,
            "stackStrategy": "samesign",
            "cursor": "pointer",
            "barMinHeight": 0,
            "barCategoryGap": "20%",
            "barGap": "30%",
            "large": false,
            "largeThreshold": 400,
            "seriesLayoutBy": "column",
            "datasetIndex": 0,
            "clip": true,
            "zlevel": 0,
            "z": 2,
            "label": {
                "show": false,
                "margin": 8,
                "valueAnimation": false
            },
            "itemStyle": {
                "color": "#A23B72",
                "borderColor": "#ffffff",
                "borderWidth": 1
            }
        },
        {
            "type": "bar",
            "name": "\u5927\u578b\u8f66",
            "legendHoverLink": true,
            "data": [
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                6,
                0,
                0,
                0,
                0,
                0,
                1,
                0,
                1,
                0,
                0,
                0,
                0,
                0,
                0,
                1,
                0,
                0,
                0,
                0,
                2,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                1,
                1,
                1,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                1,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                1,
                4,
                0
            ],
            "realtimeSort": false,
            "showBackground": false,
            "stackStrategy": "samesign",
            "cursor": "pointer",
            "barMinHeight": 0,
            "barCategoryGap": "20%",
            "barGap": "30%",
            "large": false,
            "largeThreshold": 400,
            "seriesLayoutBy": "column",
            "datasetIndex": 0,
            "clip": true,
            "zlevel": 0,
            "z": 2,
            "label": {
                "show": false,
                "margin": 8,
                "valueAnimation": false
            },
            "itemStyle": {
                "color": "#148F77",
                "borderColor": "#ffffff",
                "borderWidth": 1
            }
        },
        {
            "type": "bar",
            "name": "\u7279\u79cd\u8f66",
            "legendHoverLink": true,
            "data": [
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                10,
                0
            ],
            "realtimeSort": false,
            "showBackground": false,
            "stackStrategy": "samesign",
            "cursor": "pointer",
            "barMinHeight": 0,
            "barCategoryGap": "20%",
            "barGap": "30%",
            "large": false,
            "largeThreshold": 400,
            "seriesLayoutBy": "column",
            "datasetIndex": 0,
            "clip": true,
            "zlevel": 0,
            "z": 2,
            "label": {
                "show": false,
                "margin": 8,
                "valueAnimation": false
            },
            "itemStyle": {
                "color": "#7D3C98",
                "borderColor": "#ffffff",
                "borderWidth": 1
            }
        }
    ],
    "legend": [
        {
            "data": [
                "\u603b\u91cf",
                "\u5c0f\u578b\u8f66",
                "\u4e2d\u578b\u8f66",
                "\u5927\u578b\u8f66",
                "\u7279\u79cd\u8f66"
            ],
            "selected": {},
            "show": true,
            "top": "8%",
            "padding": 5,
            "itemGap": 20,
            "itemWidth": 25,
            "itemHeight": 14,
            "textStyle": {
                "fontWeight": "bold",
                "fontSize": 12
            },
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderRadius": 0,
            "pageButtonItemGap": 5,
            "pageButtonPosition": "end",
            "pageFormatter": "{current}/{total}",
            "pageIconColor": "#2f4554",
            "pageIconInactiveColor": "#aaa",
            "pageIconSize": 15,
            "animationDurationUpdate": 800,
            "selector": false,
            "selectorPosition": "auto",
            "selectorItemGap": 7,
            "selectorButtonGap": 10
        }
    ],
    "tooltip": {
        "show": true,
        "trigger": "axis",
        "triggerOn": "mousemove|click",
        "axisPointer": {
            "type": "cross"
        },
        "showContent": true,
        "alwaysShowContent": false,
        "showDelay": 0,
        "hideDelay": 100,
        "enterable": false,
        "confine": false,
        "appendToBody": false,
        "transitionDuration": 0.4,
        "textStyle": {
            "color": "#333333",
            "fontWeight": "normal",
            "fontSize": 12
        },
        "backgroundColor": "rgba(255, 255, 255, 0.95)",
        "borderColor": "#cccccc",
        "borderWidth": 1,
        "padding": 5,
        "order": "seriesAsc"
    },
    "xAxis": [
        {
            "type": "category",
            "name": "\u505c\u8f66\u65f6\u957f",
            "show": true,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "axisLabel": {
                "show": true,
                "rotate": 45,
                "margin": 8,
                "valueAnimation": false
            },
            "axisPointer": {
                "show": true,
                "type": "shadow",
                "triggerTooltip": true,
                "triggerOn": "mousemove|click"
            },
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0,
            "data": [
                "0.00-0.25h",
                "0.25-0.50h",
                "0.50-0.75h",
                "0.75-1.00h",
                "1.00-1.25h",
                "1.25-1.50h",
                "1.50-1.75h",
                "1.75-2.00h",
                "2.00-2.25h",
                "2.25-2.50h",
                "2.50-2.75h",
                "2.75-3.00h",
                "3.00-3.25h",
                "3.25-3.50h",
                "3.50-3.75h",
                "3.75-4.00h",
                "4.00-4.25h",
                "4.25-4.50h",
                "4.50-4.75h",
                "4.75-5.00h",
                "5.00-5.25h",
                "5.25-5.50h",
                "5.50-5.75h",
                "5.75-6.00h",
                "6.00-6.25h",
                "6.25-6.50h",
                "6.50-6.75h",
                "6.75-7.00h",
                "7.00-7.25h",
                "7.25-7.50h",
                "7.50-7.75h",
                "7.75-8.00h",
                "8.00-8.25h",
                "8.25-8.50h",
                "8.50-8.75h",
                "8.75-9.00h",
                "9.00-9.25h",
                "9.25-9.50h",
                "9.50-9.75h",
                "9.75-10.00h",
                "10.00-10.25h",
                "10.25-10.50h",
                "10.50-10.75h",
                "10.75-11.00h",
                "11.00-11.25h",
                "11.25-11.50h",
                "11.50-11.75h",
                "11.75-12.00h",
                "12.00-12.25h",
                "12.25-12.50h",
                "12.50-12.75h",
                "12.75-13.00h",
                "13.00-13.25h",
                "13.25-13.50h",
                "13.50-13.75h",
                "13.75-14.00h",
                "14.00-14.25h",
                "14.25-14.50h",
                "14.50-14.75h",
                "14.75-15.00h",
                "15.00-15.25h",
                "15.25-15.50h",
                "15.50-15.75h",
                "15.75-16.00h",
                "16.00-16.25h",
                "16.25-16.50h",
                "16.50-16.75h",
                "16.75-17.00h",
                "17.00-17.25h",
                "17.25-17.50h",
                "17.50-17.75h",
                "17.75-18.00h",
                "18.00-18.25h",
                "18.25-18.50h",
                "18.50-18.75h",
                "18.75-19.00h",
                "19.00-19.25h",
                "19.25-19.50h",
                "19.50-19.75h",
                "19.75-20.00h",
                "20.00-20.25h",
                "20.25-20.50h",
                "20.50-20.75h",
                "20.75-21.00h",
                "21.00-21.25h",
                "21.25-21.50h",
                "21.50-21.75h",
                "21.75-22.00h",
                "22.00-22.25h",
                "22.25-22.50h",
                "22.50-22.75h",
                "22.75-23.00h",
                "23.00-23.25h",
                "23.25-23.50h",
                "23.50-23.75h",
                "23.75-24.00h",
                ">=158.25h"
            ]
        }
    ],
    "yAxis": [
        {
            "type": "value",
            "name": "\u8f66\u8f86\u6570\u91cf",
            "show": true,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "axisTick": {
                "show": true,
                "alignWithLabel": false,
                "inside": false
            },
            "axisLabel": {
                "show": true,
                "margin": 8,
                "formatter": "{value}",
                "valueAnimation": false
            },
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0
        }
    ],
    "title": [
        {
            "show": true,
            "text": "\u505c\u8f66\u65f6\u957f\u5206\u5e03",
            "target": "blank",
            "subtext": "\u663e\u793a\u4e0d\u540c\u505c\u8f66\u65f6\u957f\u7684\u8f66\u8f86\u6570\u91cf\u5206\u5e03\uff08\u603b\u91cf + 4\u79cd\u8f66\u8f86\u7c7b\u578b\uff09",
            "subtarget": "blank",
            "left": "center",
            "padding": 5,
            "itemGap": 10,
            "textAlign": "auto",
            "textVerticalAlign": "auto",
            "triggerEvent": false,
            "textStyle": {
                "color": "#2c3e50",
                "fontWeight": "bold",
                "fontSize": 20
            },
            "subtextStyle": {
                "color": "#7f8c8d",
                "fontSize": 14
            }
        }
    ],
    "dataZoom": [
        {
            "show": true,
            "type": "slider",
            "showDetail": true,
            "showDataShadow": true,
            "realtime": true,
            "start": 0,
            "end": 100,
            "orient": "horizontal",
            "zoomLock": false,
            "filterMode": "filter"
        },
        {
            "show": true,
            "type": "inside",
            "showDetail": true,
            "showDataShadow": true,
            "realtime": true,
            "start": 20,
            "end": 80,
            "orient": "horizontal",
            "zoomLock": false,
            "filterMode": "filter",
            "disabled": false,
            "zoomOnMouseWheel": true,
            "moveOnMouseMove": true,
            "moveOnMouseWheel": true,
            "preventDefaultMouseMove": true
        }
    ]
};
        chart_f866375dcc20429e9a66c98bdd73dc6a.setOption(option_f866375dcc20429e9a66c98bdd73dc6a);
    </script>
</body>
</html>
