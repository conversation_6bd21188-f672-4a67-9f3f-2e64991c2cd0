<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Awesome-pyecharts</title>
                <script type="text/javascript" src="https://assets.pyecharts.org/assets/v5/echarts.min.js"></script>
            <script type="text/javascript" src="https://assets.pyecharts.org/assets/v5/themes/macarons.js"></script>

    
</head>
<body >
    <div id="6814bd1d2c8640fc9bebbf5eba5b63d5" class="chart-container" style="width:1200px; height:600px; "></div>
    <script>
        var chart_6814bd1d2c8640fc9bebbf5eba5b63d5 = echarts.init(
            document.getElementById('6814bd1d2c8640fc9bebbf5eba5b63d5'), 'macarons', {renderer: 'canvas'});
        var option_6814bd1d2c8640fc9bebbf5eba5b63d5 = {
    "animation": true,
    "animationThreshold": 2000,
    "animationDuration": 1000,
    "animationEasing": "cubicOut",
    "animationDelay": 0,
    "animationDurationUpdate": 300,
    "animationEasingUpdate": "cubicOut",
    "animationDelayUpdate": 0,
    "aria": {
        "enabled": false
    },
    "series": [
        {
            "type": "scatter",
            "name": "\u505c\u8f66\u65f6\u957f\u9891\u5ea6\u5206\u5e03",
            "symbolSize": 20,
            "data": [
                [
                    "0\u5206\u949f-30\u5206\u949f",
                    0,
                    31,
                    0,
                    "0\u5206\u949f-30\u5206\u949f",
                    "\u5c0f\u578b\u8f66"
                ],
                [
                    "30\u5206\u949f-1\u5c0f\u65f6",
                    1,
                    14,
                    0,
                    "30\u5206\u949f-1\u5c0f\u65f6",
                    "\u5c0f\u578b\u8f66"
                ],
                [
                    "1\u5c0f\u65f6-1\u5c0f\u65f630\u5206\u949f",
                    2,
                    5,
                    0,
                    "1\u5c0f\u65f6-1\u5c0f\u65f630\u5206\u949f",
                    "\u5c0f\u578b\u8f66"
                ],
                [
                    "1\u5c0f\u65f630\u5206\u949f-2\u5c0f\u65f6",
                    3,
                    3,
                    0,
                    "1\u5c0f\u65f630\u5206\u949f-2\u5c0f\u65f6",
                    "\u5c0f\u578b\u8f66"
                ],
                [
                    "2\u5c0f\u65f6-3\u5c0f\u65f6",
                    4,
                    7,
                    0,
                    "2\u5c0f\u65f6-3\u5c0f\u65f6",
                    "\u5c0f\u578b\u8f66"
                ],
                [
                    "3\u5c0f\u65f6-4\u5c0f\u65f6",
                    4,
                    4,
                    1,
                    "2\u5c0f\u65f6-3\u5c0f\u65f6",
                    "\u4e2d\u578b\u8f66"
                ],
                [
                    "4\u5c0f\u65f6-5\u5c0f\u65f6",
                    5,
                    9,
                    1,
                    "3\u5c0f\u65f6-4\u5c0f\u65f6",
                    "\u4e2d\u578b\u8f66"
                ],
                [
                    "5\u5c0f\u65f6-6\u5c0f\u65f6",
                    6,
                    15,
                    1,
                    "4\u5c0f\u65f6-5\u5c0f\u65f6",
                    "\u4e2d\u578b\u8f66"
                ],
                [
                    "6\u5c0f\u65f6-7\u5c0f\u65f6",
                    7,
                    8,
                    1,
                    "5\u5c0f\u65f6-6\u5c0f\u65f6",
                    "\u4e2d\u578b\u8f66"
                ],
                [
                    "7\u5c0f\u65f6-8\u5c0f\u65f6",
                    8,
                    4,
                    1,
                    "6\u5c0f\u65f6-7\u5c0f\u65f6",
                    "\u4e2d\u578b\u8f66"
                ],
                [
                    "480\u5206\u949f-1\u5929",
                    10,
                    26,
                    2,
                    "480\u5206\u949f-1\u5929",
                    "\u5927\u578b\u8f66"
                ],
                [
                    "1\u5929-3\u5929",
                    11,
                    3,
                    2,
                    "1\u5929-3\u5929",
                    "\u5927\u578b\u8f66"
                ],
                [
                    ">3\u5929",
                    12,
                    1,
                    2,
                    ">3\u5929",
                    "\u5927\u578b\u8f66"
                ]
            ],
            "label": {
                "show": false,
                "margin": 8,
                "valueAnimation": false
            }
        }
    ],
    "legend": [
        {
            "data": [
                "\u505c\u8f66\u65f6\u957f\u9891\u5ea6\u5206\u5e03"
            ],
            "selected": {},
            "show": false,
            "padding": 5,
            "itemGap": 10,
            "itemWidth": 25,
            "itemHeight": 14,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderRadius": 0,
            "pageButtonItemGap": 5,
            "pageButtonPosition": "end",
            "pageFormatter": "{current}/{total}",
            "pageIconColor": "#2f4554",
            "pageIconInactiveColor": "#aaa",
            "pageIconSize": 15,
            "animationDurationUpdate": 800,
            "selector": false,
            "selectorPosition": "auto",
            "selectorItemGap": 7,
            "selectorButtonGap": 10
        }
    ],
    "tooltip": {
        "show": true,
        "trigger": "item",
        "triggerOn": "mousemove|click",
        "axisPointer": {
            "type": "line"
        },
        "showContent": true,
        "alwaysShowContent": false,
        "showDelay": 0,
        "hideDelay": 100,
        "enterable": false,
        "confine": false,
        "appendToBody": false,
        "transitionDuration": 0.4,
        "formatter":                             function (params) {                                var data = params.value;                                return '\u65f6\u957f\u533a\u95f4: ' + data[3] + '<br/>' +                                       '\u8f66\u8f86\u7c7b\u578b: ' + data[4] + '<br/>' +                                       '\u9891\u6570: ' + data[1];                            }                            ,
        "textStyle": {
            "fontSize": 14
        },
        "borderWidth": 0,
        "padding": 5,
        "order": "seriesAsc"
    },
    "xAxis": [
        {
            "name": "\u505c\u8f66\u65f6\u957f\u533a\u95f4",
            "show": true,
            "scale": false,
            "nameLocation": "middle",
            "nameGap": 30,
            "gridIndex": 0,
            "axisLabel": {
                "show": true,
                "rotate": 45,
                "margin": 8,
                "fontSize": 10,
                "valueAnimation": false
            },
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0,
            "data": [
                "0\u5206\u949f-30\u5206\u949f",
                "30\u5206\u949f-1\u5c0f\u65f6",
                "1\u5c0f\u65f6-1\u5c0f\u65f630\u5206\u949f",
                "1\u5c0f\u65f630\u5206\u949f-2\u5c0f\u65f6",
                "2\u5c0f\u65f6-3\u5c0f\u65f6",
                "3\u5c0f\u65f6-4\u5c0f\u65f6",
                "4\u5c0f\u65f6-5\u5c0f\u65f6",
                "5\u5c0f\u65f6-6\u5c0f\u65f6",
                "6\u5c0f\u65f6-7\u5c0f\u65f6",
                "7\u5c0f\u65f6-8\u5c0f\u65f6",
                "480\u5206\u949f-1\u5929",
                "1\u5929-3\u5929",
                ">3\u5929"
            ]
        }
    ],
    "yAxis": [
        {
            "name": "\u9891\u6570",
            "show": true,
            "scale": false,
            "nameLocation": "middle",
            "nameGap": 50,
            "gridIndex": 0,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0
        }
    ],
    "title": [
        {
            "show": true,
            "text": "\u5ef6\u505c\u65f6\u957f\u6982\u7387\u5bc6\u5ea6\u6563\u70b9\u56fe",
            "target": "blank",
            "subtext": "\u6309\u8f66\u8f86\u7c7b\u578b\u5206\u7c7b\u7684\u505c\u8f66\u65f6\u957f\u9891\u5ea6\u5206\u5e03",
            "subtarget": "blank",
            "left": "center",
            "padding": 5,
            "itemGap": 10,
            "textAlign": "auto",
            "textVerticalAlign": "auto",
            "triggerEvent": false
        }
    ],
    "visualMap": {
        "show": true,
        "type": "continuous",
        "min": 0,
        "max": 3,
        "inRange": {
            "color": [
                "#313695",
                "#4575b4",
                "#74add1",
                "#abd9e9",
                "#e0f3f8",
                "#ffffcc",
                "#fee090",
                "#fdae61",
                "#f46d43",
                "#d73027",
                "#a50026"
            ]
        },
        "calculable": true,
        "inverse": false,
        "splitNumber": 5,
        "dimension": 2,
        "hoverLink": true,
        "orient": "vertical",
        "left": "left",
        "top": "middle",
        "padding": 5,
        "showLabel": true,
        "itemWidth": 20,
        "itemHeight": 140,
        "borderWidth": 0
    },
    "dataZoom": [
        {
            "show": true,
            "type": "slider",
            "showDetail": true,
            "showDataShadow": true,
            "realtime": true,
            "start": 0,
            "end": 100,
            "orient": "horizontal",
            "zoomLock": false,
            "filterMode": "filter"
        },
        {
            "show": true,
            "type": "slider",
            "showDetail": true,
            "showDataShadow": true,
            "realtime": true,
            "start": 0,
            "end": 100,
            "orient": "vertical",
            "zoomLock": false,
            "filterMode": "filter"
        }
    ]
};
        chart_6814bd1d2c8640fc9bebbf5eba5b63d5.setOption(option_6814bd1d2c8640fc9bebbf5eba5b63d5);
    </script>
</body>
</html>
