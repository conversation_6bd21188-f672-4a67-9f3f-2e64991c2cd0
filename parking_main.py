import os
import sys
import argparse
import logging
from datetime import datetime
import traceback
from parking_data_reader import read_data_file, process_output_path
from parking_data_processor import DataProcessor
from parking_time_filter import TimeFilter
from parking_analyzer import TimePeriodAnalyzer
from parking_report_generator import ReportGenerator
from parking_params_validator import validate_and_convert_params
import json
from copy import deepcopy

def setup_logging(log_level='ERROR', log_file=None):
    """设置日志系统"""
    log_format = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    date_format = '%Y-%m-%d %H:%M:%S'

    # 清除现有的处理器
    root_logger = logging.getLogger()
    for handler in root_logger.handlers[:]:
        root_logger.removeHandler(handler)

    # 设置日志级别
    root_level = getattr(logging, log_level)
    root_logger.setLevel(root_level)

    if log_file:
        # 确保日志目录存在
        os.makedirs(os.path.dirname(log_file), exist_ok=True)
        # 添加文件处理器
        file_handler = logging.FileHandler(log_file, encoding='utf-8')
        file_handler.setLevel(logging.DEBUG)
        file_handler.setFormatter(logging.Formatter(log_format, date_format))
        root_logger.addHandler(file_handler)
    else:
        # 只在有错误时输出到控制台
        console_handler = logging.StreamHandler()
        console_handler.setLevel(logging.ERROR)
        console_handler.setFormatter(logging.Formatter(log_format, date_format))
        root_logger.addHandler(console_handler)

    return logging.getLogger('ParkingAnalyzer')

def main(params, mode_config):
    # 设置日志系统
    logger = setup_logging(log_level='ERROR', log_file=params.get('log_file'))

    try:
        # 1. 读取数据文件
        print(f"📊 开始停车数据分析")
        print(f"📁 输入文件: {params['input_file']}")
        print(f"🔧 处理模式: {params['mode']}")

        raw_data = read_data_file(params['input_file'])
        print(f"✅ 成功读取数据: {len(raw_data)} 条记录")

        # 2. 处理输出路径
        output_path = process_output_path(params['input_file'], params['output'])
        print(f"📄 输出文件: {output_path}")
        
        # 3. 准备参数
        params.update({
            'min_duration_hours': 5 / 60  # 默认5分钟转换为小时
        })
        
        # 添加字段映射配置（优化后的版本）
        # MODE_CONFIG = {
        #     'mode1': {
        #         '车辆唯一标识字段': '车牌号码',
        #         '车辆类型字段': '车辆类型',
        #         '时间记录字段': '通行时间',
        #         '进出类型字段': '方向',
        #         '进出标识值': ('入场', '出场'),
        #         '道闸编号字段': '出入口'
        #     },
        #     'mode2': {
        #         '车辆唯一标识字段': '车牌号码',
        #         '车辆类型字段': '车辆类型',
        #         '进场时间字段': '入场时间',
        #         '出场时间字段': '出场时间',
        #         '进场道闸编号字段': '出入口',
        #         '出场道闸编号字段': '出入口'
        #     }
        # }

        # MODE_CONFIG 已移至 main 函数中，便于用户统一管理参数


        # 彻底清理所有参数值
        def thorough_clean_params(p):
            if isinstance(p, dict):
                cleaned_dict = {}
                for k, v in p.items():
                    # 对键进行彻底清理
                    cleaned_key = str(k).strip().replace('\n', '').replace('\r', '').replace('\t', '')
                    cleaned_key = ' '.join(cleaned_key.split())
                    # 对值进行递归清理
                    cleaned_dict[cleaned_key] = thorough_clean_params(v)
                return cleaned_dict
            elif isinstance(p, (list, tuple)):
                return type(p)(thorough_clean_params(v) for v in p)
            elif isinstance(p, str):
                # 彻底清理字符串：去除所有空白字符，合并连续空格
                cleaned = str(p).strip().replace('\n', '').replace('\r', '').replace('\t', '')
                return ' '.join(cleaned.split())
            return p

        # 终极参数清理函数
        def ultimate_clean_params(p):
            def clean_string(s):
                if not isinstance(s, str):
                    return s
                # 去除所有空白字符（包括换行、制表符等）
                s = ' '.join(s.split())
                # 去除不可见控制字符
                s = ''.join(c for c in s if c.isprintable())
                return s

            if isinstance(p, dict):
                cleaned_dict = {}
                for k, v in p.items():
                    # 清理键
                    cleaned_key = clean_string(str(k))
                    # 清理值
                    if isinstance(v, str):
                        cleaned_value = clean_string(v)
                    elif isinstance(v, (list, tuple)):
                        cleaned_value = type(v)(ultimate_clean_params(i) for i in v)
                    elif isinstance(v, dict):
                        cleaned_value = ultimate_clean_params(v)
                    else:
                        cleaned_value = v
                
                    cleaned_dict[cleaned_key] = cleaned_value
                return cleaned_dict
            elif isinstance(p, (list, tuple)):
                return type(p)(ultimate_clean_params(v) for v in p)
            elif isinstance(p, str):
                return clean_string(p)
            return p

        # 应用参数清理
        params = ultimate_clean_params(params)
        mode_config = ultimate_clean_params(mode_config)

        # 验证和转换参数（增强健壮性，基于数据源智能推导）
        print("🔍 验证和转换参数...")
        params = validate_and_convert_params(params, raw_data, logger)
        print("✅ 参数验证完成")

        # 合并模式配置
        if params['mode'] in mode_config:
            mode_params = deepcopy(mode_config[params['mode']])
            params.update(mode_params)

        # 4. 数据处理
        data_processor = DataProcessor(raw_data, params)
        processed_data = data_processor.process()
        print(f"✅ 数据处理完成: {len(processed_data)} 条有效记录")

        # 5. 时间过滤
        time_filter = TimeFilter(processed_data, params)
        filtered_data = time_filter.get_filtered_data()
        period_info = time_filter.detect_period()
        print(f"✅ 时间过滤完成: {len(filtered_data)} 条记录")

        # 6. 数据分析
        analyzer = TimePeriodAnalyzer(
            filtered_data,
            len(raw_data),
            mode=params['mode'],
            period_info=period_info,
            params=params
        )

        analysis_results = analyzer.analyze(
            focus_date=params.get('聚焦日期'),
            focus_month=params.get('聚焦月份')
        )
        print("✅ 数据分析完成")

        # 7. 生成报告
        report_generator = ReportGenerator(
            filtered_data,
            analysis_results,
            params,
            input_total_records=len(raw_data),  # 传入原始数据的记录数
            processed_data=processed_data  # 传入全周期清洗后的数据
        )
        report_generator.generate_and_save_plots(filtered_data)
        report_path = report_generator.export_to_excel(output_path)
        print(f"🎉 分析完成！报告已保存至: {report_path}")

        return 0  # 成功执行

    except Exception as e:
        # 获取当前时间
        error_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        # 获取异常信息
        exc_type, exc_value, _ = sys.exc_info()

        # 简洁的错误信息输出
        print(f"\n❌ 分析失败 [{error_time}]")
        print(f"错误类型: {exc_type.__name__}")
        print(f"错误信息: {str(exc_value)}")
        print(f"详细错误信息已记录到日志文件: {params.get('log_file', '无')}")

        # 记录详细错误信息到日志系统
        logger.error(f"分析失败: {exc_type.__name__} - {str(exc_value)}", exc_info=True)

        return 1

if __name__ == "__main__":
    # # 设置基础参数
    # params = {
    #     'input_file': r'C:\Users\<USER>\Desktop\停车分析\数据\物流园区\过车信息_2025-03-31.xls',
    #     'output': r'C:\Users\<USER>\Desktop\停车分析',
    #     'date': '',
    #     'month': '',
    #     'mode': 'mode1',
    #     'log_file': r'C:\Users\<USER>\Desktop\停车分析\logs\parking_analysis_m1.log',
    #     'time_interval': 60,  # 时间段间隔60分钟
    #     'time_slip': 30,      # 滑动步长15分钟
    # }

    # # 设置模式配置（字段映射关系）
    # MODE_CONFIG = {
    #     'mode1': {
    #         '车辆唯一标识字段': '车牌号码',
    #         '车辆类型字段': '车辆类型',
    #         '时间记录字段': '通行时间',
    #         '进出类型字段': '方向',
    #         '进出标识值': ('入场', '出场'),
    #         '道闸编号字段': '出入口'
    #     }   }
    
    
    # 设置基础参数
    params = {
        'input_file': r'C:\Users\<USER>\Desktop\停车分析\数据\正泰\义乌正泰_北门_合并_20250623_230242.csv',
        'output': r'C:\Users\<USER>\Desktop\停车分析',
        'date': '',
        'month': '',
        'mode': 'mode1',
        'log_file': r'C:\Users\<USER>\Desktop\停车分析\logs\parking_analysis_m3.log',
        'time_interval': 60,  # 时间段间隔60分钟
        'time_slip': 30,      # 滑动步长15分钟
    }

    # 设置模式配置（字段映射关系）
    MODE_CONFIG = {
        'mode1': {
            '车辆唯一标识字段': '车牌',
            '车辆类型字段': '车辆类型',
            '时间记录字段': '时间',
            '进出类型字段': '方向',
            '进出标识值': ('进', '出'),
            '道闸编号字段': '出入口'
        }   
    }

    # # 设置基础参数
    # params = {
    #     'input_file': r'C:\Users\<USER>\Desktop\停车分析\数据\火车站\义乌火车站道闸_网约车_合并_20250626_005006.csv',
    #     'output': r'C:\Users\<USER>\Desktop\停车分析',
    #     'date': '2025-05-05',
    #     'month': '2025-05',
    #     'mode': 'mode1_simple',
    #     'log_file': r'C:\Users\<USER>\Desktop\停车分析\logs\parking_analysis2.log',
    #     'time_interval': 60,  # 时间段间隔60分钟
    #     'time_slip': 15,      # 滑动步长15分钟
    # }

    # # 设置模式配置（字段映射关系）
    # MODE_CONFIG = {
    #     'mode1_simple': {
    #         '车辆唯一标识字段': '',  # 简化模式不需要车牌号
    #         '车辆类型字段': '车牌颜色',
    #         '时间记录字段': '通过时间',
    #         '进出类型字段': '过车方向',
    #         '进出标识值': ('入场', '出场'),
    #         '道闸编号字段': '出入口'
    #     }
    # }

    sys.exit(main(params, MODE_CONFIG))