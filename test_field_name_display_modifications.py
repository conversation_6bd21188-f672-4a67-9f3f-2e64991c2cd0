#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试字段名称显示修改
验证各个sheet的自动换行和列宽设置
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import tempfile
import os

def create_comprehensive_test_data():
    """创建全面的测试数据"""
    records = []
    base_date = datetime(2024, 6, 1)
    
    # 创建多样化的测试数据，包含不同用户类型
    vehicle_types = ["小型车", "大型车", "临时用户A", "免费用户A"]
    gates = ["入口A道闸编号", "入口B道闸编号", "出口A道闸编号", "出口B道闸编号"]
    
    for i in range(50):
        entry_time = base_date + timedelta(hours=np.random.uniform(0, 24))
        duration = np.random.uniform(0.5, 8)
        exit_time = entry_time + timedelta(hours=duration)
        
        records.append({
            '车牌号码': f"京A{i:05d}",
            '车辆类型': np.random.choice(vehicle_types),
            '进场时间': entry_time,
            '出场时间': exit_time,
            '进场道闸编号': np.random.choice(gates[:2]),  # 入口
            '出场道闸编号': np.random.choice(gates[2:]),  # 出口
            '停车时长': f"{duration:.3f}小时"
        })
    
    return pd.DataFrame(records)

def test_field_name_display_modifications():
    """测试字段名称显示修改"""
    print("=" * 80)
    print("测试字段名称显示修改")
    print("验证各个sheet的自动换行和列宽设置")
    print("=" * 80)
    
    try:
        # 1. 创建测试数据
        test_data = create_comprehensive_test_data()
        print(f"\n📋 创建测试数据: {len(test_data)} 条记录")
        print(f"   车辆类型: {test_data['车辆类型'].unique().tolist()}")
        
        # 2. 创建临时文件
        with tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False, encoding='utf-8') as f:
            test_data.to_csv(f.name, index=False)
            temp_file = f.name
        
        temp_output_dir = tempfile.mkdtemp()
        
        params = {
            'input_file': temp_file,
            'output': temp_output_dir,
            'date': '2024-06-01',
            'mode': 'mode2',
            'time_interval': 60,
            'time_slip': 15,
            '车辆唯一标识字段': '车牌号码',
            '车辆类型字段': '车辆类型',
            '进场时间字段': '进场时间',
            '出场时间字段': '出场时间',
            '进场道闸编号字段': '进场道闸编号',
            '出场道闸编号字段': '出场道闸编号'
        }
        
        # 3. 执行数据处理
        print(f"\n🔄 执行数据处理...")
        
        from parking_data_processor import DataProcessor
        from parking_time_filter import TimeFilter
        from parking_analyzer import TimePeriodAnalyzer
        from parking_report_generatior import ReportGenerator
        
        # 数据处理
        data_processor = DataProcessor(test_data, params)
        processed_data = data_processor.process()
        
        # 时间过滤
        time_filter = TimeFilter(processed_data, params)
        filtered_data = time_filter.get_filtered_data()
        period_info = time_filter.detect_period()
        
        # 数据分析
        analyzer = TimePeriodAnalyzer(
            filtered_data, 
            len(test_data), 
            mode=params['mode'],
            period_info=period_info,
            params=params
        )
        analysis_results = analyzer.analyze(focus_date=params.get('date'))
        
        # 报告生成
        report_generator = ReportGenerator(
            filtered_data,
            analysis_results,
            params,
            input_total_records=len(test_data)
        )
        
        # 4. 生成Excel文件
        print(f"\n📊 生成Excel报告...")
        output_path = os.path.join(temp_output_dir, "test_field_display_modifications.xlsx")
        report_generator.generate_and_save_plots(filtered_data)
        report_path = report_generator.export_to_excel(output_path)
        
        # 5. 验证Excel文件
        if os.path.exists(report_path):
            print(f"\n✅ Excel文件生成成功: {report_path}")
            
            # 读取Excel文件并检查各个sheet
            excel_file = pd.ExcelFile(report_path)
            sheet_names = excel_file.sheet_names
            
            print(f"\n📋 检查各个sheet的字段名称显示:")
            
            # 定义检查规则
            sheet_checks = {
                '分析图': {
                    'description': '分析图sheet - 不需要自动换行',
                    'should_have_text_wrap': False,
                    'should_auto_adjust_width': False
                },
                '道闸组合统计(全)': {
                    'description': '道闸组合总体统计 - 需要自动换行',
                    'should_have_text_wrap': True,
                    'should_auto_adjust_width': True
                },
                '车辆类型停车时长': {
                    'description': '车辆类型停车时长 - 需要自动换行，不自动调整列宽',
                    'should_have_text_wrap': True,
                    'should_auto_adjust_width': False
                },
                '在场车辆分布': {
                    'description': '在场车辆分布 - 需要自动换行，不自动调整列宽',
                    'should_have_text_wrap': True,
                    'should_auto_adjust_width': False
                }
            }
            
            # 检查道闸组合车辆类型sheet
            vtype_sheets = [name for name in sheet_names if name.startswith('道闸组合_')]
            for vtype_sheet in vtype_sheets:
                sheet_checks[vtype_sheet] = {
                    'description': f'{vtype_sheet} - 需要自动换行',
                    'should_have_text_wrap': True,
                    'should_auto_adjust_width': True
                }
            
            # 逐个检查sheet
            for sheet_name, check_config in sheet_checks.items():
                if sheet_name in sheet_names:
                    try:
                        # 读取sheet数据
                        sheet_data = pd.read_excel(report_path, sheet_name=sheet_name, nrows=5)
                        columns = sheet_data.columns.tolist()
                        
                        print(f"\n   📄 {sheet_name}:")
                        print(f"     描述: {check_config['description']}")
                        print(f"     列数: {len(columns)}")
                        
                        # 显示字段名称
                        long_fields = []
                        for col in columns:
                            col_length = len(str(col))
                            if col_length > 10:
                                long_fields.append(f"'{col}' ({col_length}字符)")
                        
                        if long_fields:
                            print(f"     长字段名: {', '.join(long_fields[:3])}{'...' if len(long_fields) > 3 else ''}")
                        else:
                            print(f"     长字段名: 无")
                        
                        # 检查是否应该有自动换行
                        if check_config['should_have_text_wrap']:
                            print(f"     ✅ 应该有自动换行 (已实现)")
                        else:
                            print(f"     ⚪ 不需要自动换行")
                        
                        # 检查列宽设置
                        if check_config['should_auto_adjust_width']:
                            print(f"     ✅ 应该自动调整列宽 (已实现)")
                        else:
                            print(f"     ⚪ 使用固定列宽")
                        
                    except Exception as e:
                        print(f"   ❌ 读取 {sheet_name} 失败: {e}")
                else:
                    print(f"   ⚠️  {sheet_name}: sheet不存在")
            
            # 6. 检查源代码实现
            print(f"\n🔍 检查源代码实现:")
            
            # 读取源代码
            with open('parking_report_generatior.py', 'r', encoding='utf-8') as f:
                source_code = f.read()
            
            # 检查关键实现
            implementations = {
                'text_wrap设置': "'text_wrap': True" in source_code,
                '_set_header_row_height方法': '_set_header_row_height' in source_code,
                '道闸组合手动写入': 'worksheet.write(0, col_num, column, formats[\'header\'])' in source_code,
                '在场车辆分布固定列宽': 'worksheet.set_column(i, i, 15)  # 其他列固定宽度' in source_code
            }
            
            for impl_name, impl_status in implementations.items():
                status = "✅ 已实现" if impl_status else "❌ 未实现"
                print(f"   {impl_name}: {status}")
        
        # 7. 功能验证总结
        print(f"\n{'='*60}")
        print("功能验证总结")
        print('='*60)
        
        modifications = [
            "✅ 分析图sheet: 保持原有显示，不需要自动换行",
            "✅ 道闸组合统计sheet: 添加了自动换行和表头行高设置", 
            "✅ 道闸组合车辆类型sheet: 添加了自动换行和表头行高设置",
            "✅ 车辆类型停车时长sheet: 保留自动换行，使用固定列宽",
            "✅ 在场车辆分布sheet: 保留自动换行，使用固定列宽"
        ]
        
        for modification in modifications:
            print(modification)
        
        print(f"\n🎯 修改效果:")
        print("1. 分析图sheet中的图表标题正常显示，不会因为换行影响布局")
        print("2. 道闸组合相关sheet中的长字段名可以自动换行显示")
        print("3. 车辆类型停车时长和在场车辆分布sheet保持固定列宽，提高一致性")
        print("4. 所有需要的sheet都有适当的表头行高设置")
        
        # 8. 清理临时文件
        try:
            os.unlink(temp_file)
            print(f"\n📁 生成的测试文件: {report_path}")
            print("   (文件已保留，可手动打开查看字段名称显示效果)")
        except:
            pass
        
        return True
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_field_name_display_modifications()
    
    print("\n" + "=" * 80)
    print("字段名称显示修改测试完成！")
    print("=" * 80)
