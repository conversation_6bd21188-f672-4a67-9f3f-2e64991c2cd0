#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试新的合并逻辑演示
创建测试文件并验证任意两个兼容文件都能合并的功能
"""

import pandas as pd
import os
from pathlib import Path
import tempfile
import shutil
from excel_data_merger import IntegratedDataMerger

def create_test_files():
    """创建测试文件"""
    # 创建临时测试目录
    test_dir = Path("test_merge_demo")
    if test_dir.exists():
        shutil.rmtree(test_dir)
    test_dir.mkdir()
    
    print(f"📁 创建测试目录: {test_dir}")
    
    # 创建兼容的文件组1：学生信息（相同结构）
    students_data1 = pd.DataFrame({
        '姓名': ['张三', '李四', '王五'],
        '年龄': [20, 21, 19],
        '专业': ['计算机', '数学', '物理']
    })
    
    students_data2 = pd.DataFrame({
        '姓名': ['赵六', '钱七', '孙八'],
        '年龄': [22, 20, 21],
        '专业': ['化学', '生物', '英语']
    })
    
    students_data3 = pd.DataFrame({
        '姓名': ['周九', '吴十'],
        '年龄': [19, 20],
        '专业': ['历史', '地理']
    })
    
    # 创建兼容的文件组2：商品信息（相同结构）
    products_data1 = pd.DataFrame({
        '商品名称': ['苹果', '香蕉'],
        '价格': [5.0, 3.0],
        '库存': [100, 200],
        '分类': ['水果', '水果']
    })
    
    products_data2 = pd.DataFrame({
        '商品名称': ['橙子', '葡萄'],
        '价格': [4.0, 8.0],
        '库存': [150, 80],
        '分类': ['水果', '水果']
    })
    
    # 创建不兼容的文件：订单信息（不同结构）
    orders_data = pd.DataFrame({
        '订单号': ['ORD001', 'ORD002'],
        '客户': ['客户A', '客户B'],
        '金额': [100.0, 200.0],
        '日期': ['2025-01-01', '2025-01-02'],
        '状态': ['已完成', '处理中']
    })
    
    # 保存文件
    students_data1.to_csv(test_dir / "学生信息1.csv", index=False, encoding='utf-8-sig')
    students_data2.to_csv(test_dir / "学生信息2.csv", index=False, encoding='utf-8-sig')
    students_data3.to_csv(test_dir / "学生信息3.csv", index=False, encoding='utf-8-sig')
    
    products_data1.to_csv(test_dir / "商品信息1.csv", index=False, encoding='utf-8-sig')
    products_data2.to_csv(test_dir / "商品信息2.csv", index=False, encoding='utf-8-sig')
    
    orders_data.to_csv(test_dir / "订单信息.csv", index=False, encoding='utf-8-sig')
    
    print("✅ 测试文件创建完成:")
    print("   兼容组1 - 学生信息: 学生信息1.csv, 学生信息2.csv, 学生信息3.csv")
    print("   兼容组2 - 商品信息: 商品信息1.csv, 商品信息2.csv")
    print("   独立文件 - 订单信息: 订单信息.csv")
    
    return test_dir

def test_new_merge_logic():
    """测试新的合并逻辑"""
    print("\n🧪 开始测试新的合并逻辑")
    print("="*60)
    
    # 创建测试文件
    test_dir = create_test_files()
    
    try:
        # 创建合并器
        merger = IntegratedDataMerger({
            "show_detailed_progress": True,  # 显示详细进度
            "add_timestamp_to_filename": True,
            "encoding": "utf-8-sig",
            "supported_extensions": ['.xlsx', '.xls', '.csv', '.txt'],
            "save_log": False,
            "continue_on_error": True,
            "output_format": "auto"
        })
        
        # 执行合并
        output_path = test_dir / "合并结果.csv"
        
        print(f"\n🚀 开始合并测试目录: {test_dir}")
        result = merger.smart_merge_directory(
            input_path=str(test_dir),
            output_path=str(output_path),
            file_pattern="*",
            recursive=False
        )
        
        print(f"\n📊 合并结果:")
        print(f"   合并后数据形状: {result.shape}")
        print(f"   输出文件: {output_path}")
        
        if not result.empty:
            print(f"\n📋 合并后的数据预览:")
            print(result.head(10))
            
            print(f"\n📈 数据统计:")
            print(f"   总行数: {len(result)}")
            print(f"   总列数: {len(result.columns)}")
            print(f"   列名: {list(result.columns)}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        return False
    
    finally:
        # 清理测试文件（可选）
        # shutil.rmtree(test_dir)
        print(f"\n🗂️  测试文件保留在: {test_dir}")

def main():
    """主函数"""
    print("🎯 新合并逻辑测试演示")
    print("="*60)
    print("本测试将验证以下功能:")
    print("1. 自动检测兼容的文件组")
    print("2. 任意两个兼容文件都能合并")
    print("3. 选择最大兼容组进行合并")
    print("4. 显示详细的兼容性分析结果")
    
    success = test_new_merge_logic()
    
    if success:
        print("\n✅ 测试完成！新的合并逻辑工作正常。")
        print("\n📝 主要改进:")
        print("   - 不再以第一个文件为基准")
        print("   - 自动分析所有文件的兼容性")
        print("   - 找到所有可能的兼容组合")
        print("   - 选择最大的兼容组进行合并")
        print("   - 提供详细的分析报告")
    else:
        print("\n❌ 测试失败，请检查代码。")

if __name__ == "__main__":
    main()
