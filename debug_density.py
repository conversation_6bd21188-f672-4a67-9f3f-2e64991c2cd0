#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试延停时长概率密度计算问题
"""

import pandas as pd
import numpy as np
from parking_report_generator import ReportGenerator

def debug_calculation():
    """调试计算过程"""
    print("=== 调试延停时长概率密度计算 ===\n")
    
    # 创建简单测试数据
    np.random.seed(42)
    durations = [0.5, 1.5, 3.0, 6.0, 12.0, 25.0, 50.0, 100.0]
    
    test_data = pd.DataFrame({
        'entry_time': pd.to_datetime(['2024-01-01 08:00:00'] * len(durations)),
        'exit_time': pd.to_datetime(['2024-01-01 10:00:00'] * len(durations)),
        'duration': durations,
        'vtype': ['小型车'] * len(durations)
    })
    
    print(f"测试数据:")
    print(test_data[['duration', 'vtype']])
    print()
    
    # 创建报告生成器
    params = {}
    generator = ReportGenerator(test_data, params)
    generator.processed_data = test_data
    
    print("开始调试计算过程...")
    
    # 检查数据
    print(f"1. 原始数据检查:")
    print(f"   - 数据行数: {len(test_data)}")
    print(f"   - duration列存在: {'duration' in test_data.columns}")
    print(f"   - duration列类型: {test_data['duration'].dtype}")
    print(f"   - duration列值: {test_data['duration'].tolist()}")
    print()
    
    # 检查processed_data
    print(f"2. processed_data检查:")
    if generator.processed_data is not None:
        print(f"   - processed_data行数: {len(generator.processed_data)}")
        print(f"   - duration列存在: {'duration' in generator.processed_data.columns}")
        if 'duration' in generator.processed_data.columns:
            print(f"   - duration列值: {generator.processed_data['duration'].tolist()}")
    else:
        print(f"   - processed_data为None")
    print()
    
    # 尝试调用计算方法
    try:
        print("3. 调用_calculate_duration_probability_density方法:")
        result = generator._calculate_duration_probability_density()
        
        if result is not None and not result.empty:
            print(f"   ✅ 计算成功，返回 {len(result)} 行数据")
            print(f"   ✅ 列名: {list(result.columns)}")
            print(f"   ✅ 前几行数据:")
            print(result.head())
        else:
            print(f"   ❌ 计算失败，返回空DataFrame或None")
            
    except Exception as e:
        print(f"   ❌ 计算异常: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_calculation()
