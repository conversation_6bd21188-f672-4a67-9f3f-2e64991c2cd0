#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试延停时长概率密度功能
"""

import pandas as pd
import numpy as np
from parking_report_generator import ParkingReportGenerator
from parking_time_filter import TimeFilter

def create_test_data():
    """创建测试数据"""
    np.random.seed(42)
    
    # 生成不同时长分布的停车记录
    durations = []
    vehicle_types = []
    
    # 短时停车 (0-2小时) - 30分钟到2小时
    short_durations = np.random.exponential(0.5, 100)  # 平均30分钟
    short_durations = np.clip(short_durations, 0.1, 2.0)  # 限制在6分钟到2小时
    durations.extend(short_durations)
    vehicle_types.extend(['小型车'] * len(short_durations))
    
    # 中时停车 (2-8小时) - 2到8小时
    medium_durations = np.random.normal(5, 1.5, 80)  # 平均5小时
    medium_durations = np.clip(medium_durations, 2.0, 8.0)
    durations.extend(medium_durations)
    vehicle_types.extend(['中型车'] * len(medium_durations))
    
    # 长时停车 (8小时以上) - 8小时到3天
    long_durations = np.random.exponential(12, 50)  # 平均12小时起
    long_durations = np.clip(long_durations, 8.0, 72.0)  # 最长3天
    durations.extend(long_durations)
    vehicle_types.extend(['大型车'] * len(long_durations))
    
    # 超长时停车 (>3天) - 3天到7天
    very_long_durations = np.random.uniform(72, 168, 20)  # 3天到7天
    durations.extend(very_long_durations)
    vehicle_types.extend(['特种车'] * len(very_long_durations))
    
    # 创建完整的停车数据
    n_records = len(durations)
    base_time = pd.Timestamp('2024-01-01 08:00:00')
    
    # 生成入场时间
    entry_times = [base_time + pd.Timedelta(hours=i*0.5) for i in range(n_records)]
    
    # 根据停车时长计算出场时间
    exit_times = [entry_times[i] + pd.Timedelta(hours=durations[i]) for i in range(n_records)]
    
    # 创建DataFrame
    test_data = pd.DataFrame({
        'entry_time': entry_times,
        'exit_time': exit_times,
        'duration': durations,  # 停车时长（小时）
        'vtype': vehicle_types,
        'vehicle_id': [f'车{i+1:04d}' for i in range(n_records)],
        'entry_gate': np.random.choice(['A口', 'B口', 'C口'], n_records),
        'exit_gate': np.random.choice(['A口', 'B口', 'C口'], n_records)
    })
    
    return test_data

def test_duration_probability_density():
    """测试延停时长概率密度功能"""
    print("=== 测试延停时长概率密度功能 ===\n")
    
    # 创建测试数据
    test_data = create_test_data()
    print(f"生成测试数据: {len(test_data)} 条记录")
    print(f"停车时长范围: {test_data['duration'].min():.2f} - {test_data['duration'].max():.2f} 小时")
    print(f"车辆类型: {test_data['vtype'].unique()}")
    print()
    
    # 创建报告生成器
    params = {
        'mode': 'mode2',
        '聚焦日期': '2024-01-01',
        # 自定义时长分段控制点和时段长度
        'duration_control_points': [2, 8, 24, 72],  # 2小时, 8小时, 1天, 3天
        'duration_period_lengths': [0.5, 1, 16, 48]  # 30分钟, 1小时, 16小时, 2天
    }
    
    generator = ParkingReportGenerator(test_data, params)
    generator.processed_data = test_data  # 设置处理后的数据
    
    # 测试延停时长概率密度计算
    print("1. 计算延停时长概率密度分布:")
    density_stats = generator._calculate_duration_probability_density()
    
    if not density_stats.empty:
        print(f"   成功计算，共 {len(density_stats)} 个时段")
        print("\n   时长分布统计:")
        print("   " + "="*80)
        
        # 显示主要统计信息
        for i, row in density_stats.iterrows():
            time_range = row['时长区间']
            frequency = row['频数']
            percentage = row['百分比(%)']
            cumulative = row['累积百分比(%)']
            print(f"   {time_range:25} | 频数: {frequency:3d} | 占比: {percentage:6.2f}% | 累积: {cumulative:6.2f}%")
        
        print("\n   " + "="*80)
        print(f"   总记录数: {density_stats['频数'].sum()}")
        print(f"   总百分比: {density_stats['百分比(%)'].sum():.2f}%")
        
        # 显示车辆类型分布（如果有）
        vtype_columns = [col for col in density_stats.columns if '_频数' in col and col != '频数']
        if vtype_columns:
            print(f"\n2. 按车辆类型分布:")
            for vtype_col in vtype_columns:
                vtype = vtype_col.replace('_频数', '')
                total_vtype = density_stats[vtype_col].sum()
                print(f"   {vtype}: {total_vtype} 条记录")
    else:
        print("   计算失败或无数据")
    
    print("\n" + "="*80)
    
    # 测试时段函数
    print("\n3. 测试时段函数:")
    time_filter = TimeFilter(test_data, params)
    
    # 使用默认配置
    default_periods = time_filter.create_duration_periods()
    print(f"   默认配置生成 {len(default_periods)} 个时段:")
    for i, period in enumerate(default_periods[:10], 1):  # 只显示前10个
        print(f"     {i:2d}. {period}")
    if len(default_periods) > 10:
        print(f"     ... 还有 {len(default_periods) - 10} 个时段")
    
    # 使用自定义配置
    print(f"\n   自定义配置 (控制点: {params['duration_control_points']}, 时段长度: {params['duration_period_lengths']}):")
    custom_periods = time_filter.create_duration_periods(
        params['duration_control_points'], 
        params['duration_period_lengths']
    )
    print(f"   生成 {len(custom_periods)} 个时段:")
    for i, period in enumerate(custom_periods, 1):
        print(f"     {i:2d}. {period}")

def test_export_to_excel():
    """测试导出到Excel"""
    print("\n=== 测试导出Excel功能 ===\n")
    
    # 创建测试数据
    test_data = create_test_data()
    
    # 创建报告生成器
    params = {
        'mode': 'mode2',
        '聚焦日期': '2024-01-01',
        'duration_control_points': [1, 4, 12, 48],  # 1小时, 4小时, 12小时, 2天
        'duration_period_lengths': [0.25, 1, 6, 24]  # 15分钟, 1小时, 6小时, 1天
    }
    
    generator = ParkingReportGenerator(test_data, params)
    generator.processed_data = test_data
    
    try:
        # 测试单独导出延停时长概率密度sheet
        import xlsxwriter
        output_path = "test_duration_density.xlsx"
        
        with pd.ExcelWriter(output_path, engine='xlsxwriter') as writer:
            workbook = writer.book
            
            # 导出延停时长概率密度sheet
            generator._export_duration_probability_density(writer, workbook)
            
        print(f"✅ 成功导出测试文件: {output_path}")
        print("   包含 '延停时长概率密度' 工作表")
        
    except Exception as e:
        print(f"❌ 导出失败: {str(e)}")

if __name__ == "__main__":
    # 运行测试
    test_duration_probability_density()
    test_export_to_excel()
