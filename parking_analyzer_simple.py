"""
简化模式停车数据分析器模块
专门处理Mode1缺少车牌号的数据源，只提供流量统计分析
"""

import sys
import os
import pandas as pd
import numpy as np
import traceback
from datetime import datetime, timedelta
from parking_data_base import ParkingDataBase

class ParkingAnalyzerSimple(ParkingDataBase):
    """简化模式停车数据分析器，继承自ParkingDataBase"""
    
    def __init__(self, data, params, logger=None):
        """
        初始化简化分析器

        Args:
            data: DataFrame, 原始数据
            params: dict, 分析参数
            logger: Logger对象, 用于日志记录
        """
        super().__init__(data, params, logger)
        self.analysis_results = {}

    def process(self):
        """
        重写父类的process方法，使用简化的数据处理逻辑

        Returns:
            DataFrame: 处理后的数据
        """
        try:
            # 使用简化的数据处理方法，不调用父类的复杂处理逻辑
            processed_data = self._process_simple()
            self.processed_data = processed_data

            if self.logger:
                self.logger.info(f"简化数据处理完成，共{len(processed_data)}条记录")

            return processed_data

        except Exception as e:
            self._log_error(f"简化数据处理失败: {str(e)}")
            raise
    
    def analyze(self):
        """
        执行简化数据分析（不包含车辆追踪相关分析）

        Returns:
            dict: 分析结果
        """
        try:
            # 使用简化的数据处理方法
            processed_data = self._process_simple()

            # 执行简化分析
            self.analysis_results = {
                'basic_stats': self._analyze_basic_stats_simple(processed_data),
                'time_distribution': self._analyze_time_distribution_simple(processed_data),
                'vehicle_types': self._analyze_vehicle_types_simple(processed_data),
                'gate_usage': self._analyze_gate_usage_simple(processed_data)
            }

            return self.analysis_results

        except Exception as e:
            self._log_error(f"简化数据分析失败: {str(e)}")
            raise

    def _process_simple(self):
        """简化的数据处理方法，不需要车辆追踪"""
        try:
            # 直接使用原始数据，只进行基本的字段标准化
            data = self.raw_data.copy() if self.raw_data is not None else pd.DataFrame()

            if data.empty:
                return data

            # 标准化字段名
            field_mapping = {
                self.params.get('时间记录字段', '通行时间'): 'timestamp',
                self.params.get('进出类型字段', '方向'): 'direction',
                self.params.get('道闸编号字段', '出入口'): 'gate',
                self.params.get('车辆类型字段', '车辆类型'): 'vtype'
            }

            # 重命名字段
            for original, standard in field_mapping.items():
                if original in data.columns:
                    data = data.rename(columns={original: standard})

            # 转换时间字段
            if 'timestamp' in data.columns:
                data['timestamp'] = pd.to_datetime(data['timestamp'], errors='coerce')
                # 移除无效时间记录
                data = data[~data['timestamp'].isna()]

            # 清理字符串字段
            string_fields = ['direction', 'gate', 'vtype']
            for field in string_fields:
                if field in data.columns:
                    data[field] = data[field].astype(str).str.strip()
                    # 移除空值
                    data = data[data[field] != '']

            # 为了兼容TimeFilter，添加必要的字段
            if 'timestamp' in data.columns:
                # 添加entry_time和exit_time字段，使用timestamp作为基础
                data['entry_time'] = data['timestamp']
                data['exit_time'] = data['timestamp']

                # 添加基本的车辆ID（使用行号）
                data['vid'] = data.index.astype(str)

                # 添加基本的道闸字段
                if 'gate' in data.columns:
                    data['entry_gate'] = data['gate']
                    data['exit_gate'] = data['gate']

            return data

        except Exception as e:
            self._log_error(f"简化数据处理失败: {str(e)}")
            return pd.DataFrame()
    
    def _analyze_basic_stats_simple(self, data):
        """
        分析基础统计信息（简化版，不包含车辆相关统计）
        
        Args:
            data: DataFrame, 待分析的数据
            
        Returns:
            dict: 基础统计信息
        """
        if data is None or data.empty:
            return {
                'total_records': 0,
                'entry_records': 0,
                'exit_records': 0,
                'date_range': None
            }
        
        try:
            # 统计进出场记录数
            entry_records = 0
            exit_records = 0
            
            if 'direction' in data.columns:
                entry_value = self.params.get('进出标识值', ['入场', '出场'])[0]
                exit_value = self.params.get('进出标识值', ['入场', '出场'])[1]
                
                entry_records = len(data[data['direction'] == entry_value])
                exit_records = len(data[data['direction'] == exit_value])
            
            # 分析时间范围
            date_range = None
            if 'timestamp' in data.columns:
                min_time = data['timestamp'].min()
                max_time = data['timestamp'].max()
                if pd.notna(min_time) and pd.notna(max_time):
                    date_range = {
                        'start': min_time,
                        'end': max_time,
                        'days': (max_time - min_time).days + 1
                    }
            
            return {
                'total_records': len(data),
                'entry_records': entry_records,
                'exit_records': exit_records,
                'date_range': date_range
            }
            
        except Exception as e:
            self._log_error(f"基础统计分析失败: {str(e)}")
            return {
                'total_records': len(data) if data is not None else 0,
                'error': str(e)
            }
    
    def _analyze_time_distribution_simple(self, data):
        """分析时间分布（简化版，只统计记录数）"""
        try:
            if 'timestamp' not in data.columns:
                self._log_warning("缺少时间字段")
                return {
                    'entry_by_hour': {},
                    'exit_by_hour': {},
                    'daily_flow': {}
                }
            
            # 转换时间字段
            data['timestamp'] = pd.to_datetime(data['timestamp'], errors='coerce')
            valid_data = data[~data['timestamp'].isna()]
            
            if valid_data.empty:
                self._log_warning("没有有效的时间记录数据")
                return {
                    'entry_by_hour': {},
                    'exit_by_hour': {},
                    'daily_flow': {}
                }
            
            # 分离进出场记录
            entry_value = self.params.get('进出标识值', ['入场', '出场'])[0]
            exit_value = self.params.get('进出标识值', ['入场', '出场'])[1]
            
            entry_data = valid_data[valid_data['direction'] == entry_value] if 'direction' in valid_data.columns else pd.DataFrame()
            exit_data = valid_data[valid_data['direction'] == exit_value] if 'direction' in valid_data.columns else pd.DataFrame()
            
            # 按小时统计
            entry_by_hour = entry_data['timestamp'].dt.hour.value_counts().sort_index() if not entry_data.empty else pd.Series()
            exit_by_hour = exit_data['timestamp'].dt.hour.value_counts().sort_index() if not exit_data.empty else pd.Series()
            
            # 按日期统计
            daily_flow = valid_data.groupby(valid_data['timestamp'].dt.date).size()
            
            return {
                'entry_by_hour': entry_by_hour.to_dict(),
                'exit_by_hour': exit_by_hour.to_dict(),
                'daily_flow': daily_flow.to_dict()
            }
            
        except Exception as e:
            self._log_error(f"时间分布分析失败: {str(e)}")
            return {
                'entry_by_hour': {},
                'exit_by_hour': {},
                'daily_flow': {}
            }
    
    def _analyze_vehicle_types_simple(self, data):
        """分析车辆类型分布（简化版，只统计记录数）"""
        try:
            if 'vtype' not in data.columns:
                return {'type_distribution': {}}
            
            type_counts = data['vtype'].value_counts()
            
            return {
                'type_distribution': type_counts.to_dict()
            }
            
        except Exception as e:
            self._log_error(f"车辆类型分析失败: {str(e)}")
            return {'type_distribution': {}}
    
    def _analyze_gate_usage_simple(self, data):
        """分析道闸使用情况（简化版）"""
        try:
            if 'gate' not in data.columns:
                self._log_warning("缺少道闸字段")
                return {'gate_usage': {}}
            
            gate_usage = data['gate'].value_counts()
            
            return {
                'gate_usage': gate_usage.to_dict()
            }
            
        except Exception as e:
            self._log_error(f"道闸使用分析失败: {str(e)}")
            return {'gate_usage': {}}
    
    def _log_error(self, message):
        """记录错误信息"""
        if self.logger:
            self.logger.error(message)
        print(f"错误: {message}")
    
    def _log_warning(self, message):
        """记录警告信息"""
        if self.logger:
            self.logger.warning(message)
        print(f"警告: {message}")


class TimePeriodAnalyzerSimple(ParkingAnalyzerSimple):
    """简化模式时间周期分析器，继承自ParkingAnalyzerSimple"""
    
    def __init__(self, data, total_records, mode='mode1_simple', period_info=None, params=None):
        """
        初始化简化时间周期分析器
        
        Args:
            data: DataFrame, 处理后的数据
            total_records: int, 原始记录总数
            mode: str, 处理模式
            period_info: dict, 时间周期信息
            params: dict, 分析参数
        """
        # 构建完整的参数字典
        full_params = {
            'mode': mode,
            '车辆唯一标识字段': '',  # 简化模式不需要车牌号
            '车辆类型字段': '车辆类型',
            '时间记录字段': '通行时间',
            '进出类型字段': '方向',
            '道闸编号字段': '出入口',
            '进出标识值': ['入场', '出场']
        }
        
        # 如果提供了自定义参数，更新默认参数
        if params:
            full_params.update(params)
            # 确保进出标识值是列表格式
            if '进出标识值' in params and isinstance(params['进出标识值'], tuple):
                full_params['进出标识值'] = list(params['进出标识值'])
        
        # 调用父类初始化
        super().__init__(data, full_params)

        # 设置其他属性
        self.total_records = total_records
        self.mode = mode
        self.period_info = period_info or {}

        # 确保data属性被正确设置
        self.data = data

        # 确保processed_data被正确设置
        if self.processed_data is None and data is not None:
            self.processed_data = data.copy()

        # 记录初始化信息到日志
        if self.logger:
            self.logger.info(f"初始化简化时间周期分析器: 总记录数={total_records}, 处理模式={mode}")
    
    def analyze(self, focus_date=None, focus_month=None):
        """
        执行简化分析，包含概览信息
        
        Args:
            focus_date: str, 聚焦日期
            focus_month: str, 聚焦月份
            
        Returns:
            dict: 分析结果
        """
        try:
            # 执行基础分析
            analysis_results = super().analyze()
            
            # 添加概览信息
            analysis_results['overview'] = self._generate_overview_simple()
            
            return analysis_results
            
        except Exception as e:
            self._log_error(f"简化时间周期分析失败: {str(e)}")
            raise
    
    def _generate_overview_simple(self):
        """生成简化概览信息"""
        try:
            basic_stats = self.analysis_results.get('basic_stats', {})
            time_dist = self.analysis_results.get('time_distribution', {})
            vehicle_types = self.analysis_results.get('vehicle_types', {})
            
            overview = {
                'total_records': self.total_records,
                'processed_records': basic_stats.get('total_records', 0),
                'entry_records': basic_stats.get('entry_records', 0),
                'exit_records': basic_stats.get('exit_records', 0),
                'vehicle_distribution': vehicle_types.get('type_distribution', {}),
                'date_range': basic_stats.get('date_range'),
                'mode': self.mode,
                'analysis_type': 'simplified'  # 标记为简化分析
            }
            
            return overview
            
        except Exception as e:
            self._log_error(f"生成概览信息失败: {str(e)}")
            return {'error': str(e)}
