#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试灵活的车辆类型图表生成功能
"""

import os

def test_flexible_vehicle_charts():
    """测试灵活的车辆类型图表生成"""
    print("🚗 测试灵活的车辆类型图表生成")
    print("=" * 50)
    
    # Excel文件路径
    excel_file = r'C:\Users\<USER>\Desktop\停车分析\义乌火车站道闸_私家车_合并_20250617_083509_analysis_20250618_211554.xlsx'
    
    if not os.path.exists(excel_file):
        print(f"❌ Excel文件不存在: {excel_file}")
        return False
    
    try:
        # 导入图表生成器
        from parking_chart_generator import ParkingChartGenerator
        
        # 创建图表生成器
        print("📊 创建图表生成器...")
        chart_generator = ParkingChartGenerator(excel_file, None)
        
        # 检查是否有进出量时间分布工作表
        if '进出量时间分布' not in chart_generator.excel_data:
            print("❌ 未找到'进出量时间分布'工作表")
            available_sheets = list(chart_generator.excel_data.keys())
            print(f"可用工作表: {available_sheets}")
            return False
        
        # 获取数据并分析结构
        data = chart_generator.excel_data['进出量时间分布']
        columns = list(data.columns)
        total_cols = len(columns)
        
        print(f"📋 数据结构分析:")
        print(f"   总列数: {total_cols}")
        print(f"   所有列名:")
        for i, col in enumerate(columns):
            print(f"     {i+1}: {col}")
        
        # 尝试生成车辆类型图表
        print(f"\n🚗 尝试生成车辆类型图表...")
        result = chart_generator.generate_vehicle_type_traffic_charts()
        
        if result:
            print(f"✅ 成功生成: {os.path.basename(result)}")
            
            # 检查文件是否存在
            if os.path.exists(result):
                file_size = os.path.getsize(result) / 1024
                print(f"📄 文件大小: {file_size:.1f}KB")
                print(f"📁 文件路径: {result}")
                return True
            else:
                print(f"❌ 文件未生成: {result}")
                return False
        else:
            print("❌ 未能生成车辆类型图表")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    success = test_flexible_vehicle_charts()
    
    if success:
        print("\n🎉 测试成功！车辆类型图表已生成！")
        print("📁 请检查生成的文件：进出量时间分布_车型.html")
    else:
        print("\n⚠️ 测试失败")
        print("💡 可能的原因:")
        print("   1. Excel文件中没有'进出量时间分布'工作表")
        print("   2. 数据列数不足（少于6列）")
        print("   3. 数据格式不正确")
    
    print("\n" + "="*50)
    input("按回车键退出...")

if __name__ == "__main__":
    main()
