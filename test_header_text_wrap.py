#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试字段名称自动换行功能
验证各个sheet中的表头是否正确显示自动换行
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import tempfile
import os

def create_test_data_with_long_headers():
    """创建包含长字段名的测试数据"""
    records = []
    base_date = datetime(2024, 6, 1)
    
    # 创建一些测试记录
    for i in range(20):
        entry_time = base_date + timedelta(hours=np.random.uniform(0, 24))
        duration = np.random.uniform(0.5, 8)
        exit_time = entry_time + timedelta(hours=duration)
        
        records.append({
            '车牌号码': f"京A{i:05d}",
            '车辆类型': np.random.choice(["小型车", "大型车", "货车"]),
            '进场时间': entry_time,
            '出场时间': exit_time,
            '进场道闸编号': np.random.choice(["入口A道闸", "入口B道闸"]),
            '出场道闸编号': np.random.choice(["出口A道闸", "出口B道闸"]),
            '停车时长': f"{duration:.3f}小时"
        })
    
    return pd.DataFrame(records)

def test_header_text_wrap():
    """测试字段名称自动换行功能"""
    print("=" * 80)
    print("测试字段名称自动换行功能")
    print("验证各个sheet中的表头是否正确显示自动换行")
    print("=" * 80)
    
    try:
        # 1. 创建测试数据
        test_data = create_test_data_with_long_headers()
        print(f"\n📋 创建测试数据: {len(test_data)} 条记录")
        print(f"   字段名称: {test_data.columns.tolist()}")
        
        # 显示字段名称长度
        print(f"\n📏 字段名称长度:")
        for col in test_data.columns:
            print(f"   '{col}': {len(col)} 字符")
        
        # 2. 创建临时文件
        with tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False, encoding='utf-8') as f:
            test_data.to_csv(f.name, index=False)
            temp_file = f.name
        
        temp_output_dir = tempfile.mkdtemp()
        
        params = {
            'input_file': temp_file,
            'output': temp_output_dir,
            'date': '2024-06-01',
            'mode': 'mode2',
            'time_interval': 60,
            'time_slip': 15,
            '车辆唯一标识字段': '车牌号码',
            '车辆类型字段': '车辆类型',
            '进场时间字段': '进场时间',
            '出场时间字段': '出场时间',
            '进场道闸编号字段': '进场道闸编号',
            '出场道闸编号字段': '出场道闸编号'
        }
        
        # 3. 执行数据处理
        print(f"\n🔄 执行数据处理...")
        
        from parking_data_processor import DataProcessor
        from parking_time_filter import TimeFilter
        from parking_analyzer import TimePeriodAnalyzer
        from parking_report_generatior import ReportGenerator
        
        # 数据处理
        data_processor = DataProcessor(test_data, params)
        processed_data = data_processor.process()
        
        # 时间过滤
        time_filter = TimeFilter(processed_data, params)
        filtered_data = time_filter.get_filtered_data()
        period_info = time_filter.detect_period()
        
        # 数据分析
        analyzer = TimePeriodAnalyzer(
            filtered_data, 
            len(test_data), 
            mode=params['mode'],
            period_info=period_info,
            params=params
        )
        analysis_results = analyzer.analyze(focus_date=params.get('date'))
        
        # 报告生成
        report_generator = ReportGenerator(
            filtered_data,
            analysis_results,
            params,
            input_total_records=len(test_data)
        )
        
        # 4. 测试Excel格式配置
        print(f"\n🔍 测试Excel格式配置...")
        
        # 检查EXCEL_FORMATS配置
        excel_formats = report_generator.EXCEL_FORMATS
        print(f"   Excel格式配置:")
        for format_name, format_config in excel_formats.items():
            print(f"     {format_name}: {format_config}")
            
            # 检查header格式是否包含text_wrap
            if format_name == 'header':
                if 'text_wrap' in format_config:
                    print(f"       ✅ 包含自动换行设置: text_wrap = {format_config['text_wrap']}")
                else:
                    print(f"       ❌ 缺少自动换行设置")
        
        # 5. 生成Excel文件
        print(f"\n📊 生成Excel报告...")
        output_path = os.path.join(temp_output_dir, "test_header_text_wrap.xlsx")
        report_generator.generate_and_save_plots(filtered_data)
        report_path = report_generator.export_to_excel(output_path)
        
        # 6. 验证Excel文件
        if os.path.exists(report_path):
            print(f"\n✅ Excel文件生成成功: {report_path}")
            
            # 读取Excel文件并检查各个sheet
            excel_file = pd.ExcelFile(report_path)
            sheet_names = excel_file.sheet_names
            
            print(f"\n📋 检查各个sheet的表头:")
            
            # 重点检查的sheet
            key_sheets = ['数据_总量', '数据_分析日', '在场车辆分布', '出入口流量占比']
            
            for sheet_name in key_sheets:
                if sheet_name in sheet_names:
                    try:
                        # 读取sheet数据
                        sheet_data = pd.read_excel(report_path, sheet_name=sheet_name, nrows=0)  # 只读取表头
                        columns = sheet_data.columns.tolist()
                        
                        print(f"\n   📄 {sheet_name}:")
                        print(f"     列数: {len(columns)}")
                        print(f"     列名:")
                        
                        for i, col in enumerate(columns):
                            col_length = len(str(col))
                            wrap_needed = "需要换行" if col_length > 10 else "无需换行"
                            print(f"       {i+1}. '{col}' ({col_length}字符) - {wrap_needed}")
                        
                    except Exception as e:
                        print(f"   ❌ 读取 {sheet_name} 失败: {e}")
                else:
                    print(f"   ⚠️  {sheet_name}: sheet不存在")
            
            # 7. 检查表头行高设置
            print(f"\n🔍 检查表头行高设置:")
            
            # 检查是否有_set_header_row_height方法
            if hasattr(report_generator, '_set_header_row_height'):
                print(f"   ✅ _set_header_row_height方法存在")
                
                # 测试方法调用
                try:
                    # 创建一个临时workbook来测试
                    import xlsxwriter
                    test_workbook = xlsxwriter.Workbook(os.path.join(temp_output_dir, 'test_row_height.xlsx'))
                    test_worksheet = test_workbook.add_worksheet('test')
                    
                    # 测试设置行高
                    report_generator._set_header_row_height(test_worksheet, 0, 35)
                    print(f"   ✅ 表头行高设置方法调用成功")
                    
                    test_workbook.close()
                    
                except Exception as e:
                    print(f"   ⚠️  表头行高设置测试失败: {e}")
            else:
                print(f"   ❌ _set_header_row_height方法不存在")
        
        # 8. 功能验证总结
        print(f"\n{'='*60}")
        print("功能验证总结")
        print('='*60)
        
        success_count = 0
        total_checks = 5
        
        # 检查1: Excel文件生成成功
        if os.path.exists(report_path):
            print("✅ Excel文件生成成功")
            success_count += 1
        else:
            print("❌ Excel文件生成失败")
        
        # 检查2: header格式包含text_wrap
        header_format = excel_formats.get('header', {})
        if header_format.get('text_wrap'):
            print("✅ header格式包含自动换行设置")
            success_count += 1
        else:
            print("❌ header格式缺少自动换行设置")
        
        # 检查3: _set_header_row_height方法存在
        if hasattr(report_generator, '_set_header_row_height'):
            print("✅ 表头行高设置方法存在")
            success_count += 1
        else:
            print("❌ 表头行高设置方法不存在")
        
        # 检查4: 关键sheet存在
        key_sheets_exist = all(sheet in sheet_names for sheet in ['数据_总量', '数据_分析日'])
        if key_sheets_exist:
            print("✅ 关键数据sheet存在")
            success_count += 1
        else:
            print("❌ 部分关键数据sheet缺失")
        
        # 检查5: 包含长字段名的数据
        has_long_headers = any(len(col) > 10 for col in test_data.columns)
        if has_long_headers:
            print("✅ 测试数据包含长字段名")
            success_count += 1
        else:
            print("⚠️  测试数据字段名较短")
        
        print(f"\n🎯 验证结果: {success_count}/{total_checks} 项检查通过")
        
        if success_count >= 4:
            print("🎉 字段名称自动换行功能测试成功！")
            print("📝 主要改进:")
            print("   - header格式添加了text_wrap: True设置")
            print("   - 各个导出方法中设置了表头行高")
            print("   - 长字段名现在可以在单元格内自动换行")
        else:
            print("⚠️  部分功能需要进一步检查")
        
        # 9. 使用说明
        print(f"\n📖 使用说明:")
        print("1. 在Excel中打开生成的文件")
        print("2. 查看各个sheet的表头行")
        print("3. 长字段名应该在单元格内自动换行显示")
        print("4. 表头行高度应该自动调整以适应换行内容")
        
        # 10. 清理临时文件
        try:
            os.unlink(temp_file)
            print(f"\n📁 生成的测试文件: {report_path}")
            print("   (文件已保留，可手动打开查看自动换行效果)")
        except:
            pass
        
        return True
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_header_text_wrap()
    
    print("\n" + "=" * 80)
    print("字段名称自动换行功能测试完成！")
    print("=" * 80)
