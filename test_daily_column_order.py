#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试分析周期_每日sheet中列顺序的调整
验证车辆类型统计列在道闸统计列之前
"""

import pandas as pd
import numpy as np
from datetime import datetime, timed<PERSON>ta

def test_daily_column_order():
    """测试分析周期_每日sheet中列顺序的调整"""
    print("=" * 70)
    print("测试分析周期_每日sheet中列顺序的调整")
    print("验证车辆类型统计列在道闸统计列之前")
    print("=" * 70)
    
    try:
        # 1. 创建测试数据
        print("\n📋 创建测试数据...")
        
        # 模拟数据
        test_data = pd.DataFrame({
            'entry_time': [
                datetime(2024, 6, 1, 8, 0),
                datetime(2024, 6, 1, 9, 0),
                datetime(2024, 6, 1, 10, 0),
                datetime(2024, 6, 2, 8, 0),
                datetime(2024, 6, 2, 9, 0)
            ],
            'exit_time': [
                datetime(2024, 6, 1, 12, 0),
                datetime(2024, 6, 1, 13, 0),
                datetime(2024, 6, 1, 14, 0),
                datetime(2024, 6, 2, 12, 0),
                datetime(2024, 6, 2, 13, 0)
            ],
            'vtype': ['小型车', '大型车', '小型车', '大型车', '小型车'],
            'entry_gate': ['入口A', '入口B', '入口A', '入口B', '入口A'],
            'exit_gate': ['出口A', '出口B', '出口A', '出口B', '出口A'],
            'duration': [4.0, 4.0, 4.0, 4.0, 4.0]
        })
        
        # 添加日期列
        test_data['entry_date'] = test_data['entry_time'].dt.date
        test_data['exit_date'] = test_data['exit_time'].dt.date
        
        print(f"   测试数据: {len(test_data)} 条记录")
        print(f"   车辆类型: {test_data['vtype'].unique().tolist()}")
        print(f"   进场道闸: {test_data['entry_gate'].unique().tolist()}")
        print(f"   出场道闸: {test_data['exit_gate'].unique().tolist()}")
        
        # 2. 模拟ReportGenerator的参数
        params = {
            'mode': 'mode2',
            '进场道闸字段': 'entry_gate',
            '出场道闸字段': 'exit_gate'
        }
        
        # 3. 直接测试_calculate_daily_stats方法
        print(f"\n🔍 测试_calculate_daily_stats方法...")
        
        # 模拟ReportGenerator类的必要方法
        class MockReportGenerator:
            def __init__(self, data, params):
                self.data = data
                self.params = params
            
            def _get_vehicle_types(self, data):
                """获取车辆类型"""
                if 'vtype' in data.columns:
                    return sorted(data['vtype'].unique())
                return []
            
            def _log_warning(self, message):
                """日志警告"""
                print(f"   警告: {message}")
            
            def _add_gate_stats_to_daily(self, result, data_copy, all_dates):
                """为每日统计添加道闸进出量统计"""
                try:
                    # 获取当前模式
                    mode = self.params.get('mode', 'mode1')
                    
                    if mode == 'mode2':
                        # mode2模式 - 分别处理进场和出场道闸
                        entry_gate_field = self.params.get('进场道闸字段', 'entry_gate')
                        exit_gate_field = self.params.get('出场道闸字段', 'exit_gate')
                        
                        if entry_gate_field not in data_copy.columns or exit_gate_field not in data_copy.columns:
                            self._log_warning(f"mode2模式下未找到道闸字段: {entry_gate_field}, {exit_gate_field}")
                            return
                        
                        # 获取所有道闸
                        entry_gates = sorted(data_copy[entry_gate_field].unique())
                        exit_gates = sorted(data_copy[exit_gate_field].unique())
                        all_gates = sorted(set(entry_gates + exit_gates))
                        
                        # 为每个道闸初始化列
                        for gate in all_gates:
                            # 简化道闸名称作为列名
                            gate_name = str(gate).replace('道闸', '').replace('编号', '').replace('入口', '').replace('出口', '')
                            if not gate_name:
                                gate_name = str(gate)
                            
                            result[f'{gate_name}_进'] = 0
                            result[f'{gate_name}_出'] = 0
                        
                        # 填充道闸数据
                        for date in all_dates:
                            date_str = date.strftime('%Y-%m-%d')
                            
                            # 统计进场道闸
                            for gate in entry_gates:
                                gate_name = str(gate).replace('道闸', '').replace('编号', '').replace('入口', '').replace('出口', '')
                                if not gate_name:
                                    gate_name = str(gate)
                                
                                entry_count = len(data_copy[
                                    (data_copy[entry_gate_field] == gate) & 
                                    (data_copy['entry_date'] == date)
                                ])
                                result.loc[result['日期'] == date_str, f'{gate_name}_进'] = entry_count
                            
                            # 统计出场道闸
                            for gate in exit_gates:
                                gate_name = str(gate).replace('道闸', '').replace('编号', '').replace('入口', '').replace('出口', '')
                                if not gate_name:
                                    gate_name = str(gate)
                                
                                exit_count = len(data_copy[
                                    (data_copy[exit_gate_field] == gate) & 
                                    (data_copy['exit_date'] == date)
                                ])
                                result.loc[result['日期'] == date_str, f'{gate_name}_出'] = exit_count
                                
                except Exception as e:
                    self._log_warning(f"添加道闸统计失败: {str(e)}")
            
            def _calculate_daily_stats(self, data=None):
                """计算每日分析数据（修改后的版本）"""
                data = data if data is not None else self.data
                if data.empty:
                    return pd.DataFrame()
                
                # 确保数据中有日期列
                data_copy = data.copy()
                data_copy['entry_date'] = data_copy['entry_time'].dt.date
                data_copy['exit_date'] = data_copy['exit_time'].dt.date
                
                # 获取所有日期
                all_dates = sorted(set(data_copy['entry_date'].dropna().tolist() + data_copy['exit_date'].dropna().tolist()))
                
                # 初始化结果列表
                daily_stats = []
                
                # 计算每日进出场数量
                for date in all_dates:
                    # 进场统计
                    entry_count = len(data_copy[data_copy['entry_date'] == date])
                    
                    # 出场统计
                    exit_count = len(data_copy[data_copy['exit_date'] == date])
                    
                    # 计算当日停车时长
                    day_data = data_copy[(data_copy['entry_date'] == date) | (data_copy['exit_date'] == date)]
                    avg_duration = day_data['duration'].mean() if not day_data.empty else 0
                    
                    daily_stats.append({
                        '日期': date.strftime('%Y-%m-%d'),
                        '进场数量': entry_count,
                        '出场数量': exit_count,
                        '总流量': entry_count + exit_count,
                        '平均停车时长(h)': round(avg_duration, 2) if not pd.isna(avg_duration) else 0
                    })
                
                # 创建结果DataFrame
                result = pd.DataFrame(daily_stats)
                
                # 如果有车辆类型列，按类型统计（先添加车辆类型统计）
                if 'vtype' in data.columns:
                    vehicle_types = self._get_vehicle_types(data)
                    
                    # 初始化所有车辆类型列
                    for vtype in vehicle_types:
                        result[f'{vtype}_进'] = 0
                        result[f'{vtype}_出'] = 0
                    
                    # 填充车辆类型数据
                    for date in all_dates:
                        date_str = date.strftime('%Y-%m-%d')
                        for vtype in vehicle_types:
                            vtype_data = data_copy[data_copy['vtype'] == vtype]
                            
                            # 进场统计
                            entry_count = len(vtype_data[vtype_data['entry_date'] == date])
                            result.loc[result['日期'] == date_str, f'{vtype}_进'] = entry_count
                            
                            # 出场统计
                            exit_count = len(vtype_data[vtype_data['exit_date'] == date])
                            result.loc[result['日期'] == date_str, f'{vtype}_出'] = exit_count
                
                # 添加道闸统计（在车辆类型统计之后）
                self._add_gate_stats_to_daily(result, data_copy, all_dates)
                
                # 添加总流量列（放在最后）
                result['总流量'] = result['进场数量'] + result['出场数量']
                return result
        
        # 4. 执行测试
        mock_generator = MockReportGenerator(test_data, params)
        daily_stats = mock_generator._calculate_daily_stats()
        
        if not daily_stats.empty:
            print(f"   ✅ 成功生成每日统计数据")
            print(f"   - 数据行数: {len(daily_stats)}")
            print(f"   - 数据列数: {len(daily_stats.columns)}")
            
            # 5. 检查列顺序
            print(f"\n📋 检查列顺序...")
            columns = daily_stats.columns.tolist()
            print(f"   所有列: {columns}")
            
            # 分类列
            basic_columns = []
            vtype_columns = []
            gate_columns = []
            other_columns = []
            
            for col in columns:
                if col in ['日期', '进场数量', '出场数量', '总流量', '平均停车时长(h)']:
                    basic_columns.append(col)
                elif '_进' in col or '_出' in col:
                    if any(vtype in col for vtype in ['小型车', '大型车']):
                        vtype_columns.append(col)
                    else:
                        gate_columns.append(col)
                else:
                    other_columns.append(col)
            
            print(f"\n   📊 列分类:")
            print(f"     基础列: {basic_columns}")
            print(f"     车辆类型列: {vtype_columns}")
            print(f"     道闸列: {gate_columns}")
            print(f"     其他列: {other_columns}")
            
            # 6. 验证顺序
            print(f"\n🔍 验证列顺序...")
            
            # 找到第一个车辆类型列和第一个道闸列的位置
            first_vtype_pos = -1
            first_gate_pos = -1
            
            for i, col in enumerate(columns):
                if first_vtype_pos == -1 and col in vtype_columns:
                    first_vtype_pos = i
                if first_gate_pos == -1 and col in gate_columns:
                    first_gate_pos = i
            
            print(f"   第一个车辆类型列位置: {first_vtype_pos} ({columns[first_vtype_pos] if first_vtype_pos != -1 else 'N/A'})")
            print(f"   第一个道闸列位置: {first_gate_pos} ({columns[first_gate_pos] if first_gate_pos != -1 else 'N/A'})")
            
            # 验证顺序
            if first_vtype_pos != -1 and first_gate_pos != -1:
                if first_vtype_pos < first_gate_pos:
                    print(f"   ✅ 列顺序正确: 车辆类型列在道闸列之前")
                    order_correct = True
                else:
                    print(f"   ❌ 列顺序错误: 道闸列在车辆类型列之前")
                    order_correct = False
            elif first_vtype_pos != -1:
                print(f"   ⚠️  只有车辆类型列，没有道闸列")
                order_correct = True
            elif first_gate_pos != -1:
                print(f"   ⚠️  只有道闸列，没有车辆类型列")
                order_correct = True
            else:
                print(f"   ⚠️  既没有车辆类型列，也没有道闸列")
                order_correct = True
            
            # 7. 显示数据样例
            print(f"\n📋 数据样例:")
            for i, row in daily_stats.head(2).iterrows():
                print(f"   日期: {row['日期']}")
                print(f"     基础统计: 进场{row['进场数量']}, 出场{row['出场数量']}, 总流量{row['总流量']}")
                
                # 显示车辆类型统计
                if vtype_columns:
                    vtype_stats = []
                    for col in vtype_columns[:4]:  # 只显示前4个
                        vtype_stats.append(f"{col}:{row[col]}")
                    print(f"     车辆类型: {', '.join(vtype_stats)}")
                
                # 显示道闸统计
                if gate_columns:
                    gate_stats = []
                    for col in gate_columns[:4]:  # 只显示前4个
                        gate_stats.append(f"{col}:{row[col]}")
                    print(f"     道闸统计: {', '.join(gate_stats)}")
            
            # 8. 总结
            print(f"\n{'='*50}")
            print("列顺序调整验证总结")
            print('='*50)
            
            if order_correct:
                print("🎉 列顺序调整成功！")
                print("✅ 车辆类型统计列在道闸统计列之前")
            else:
                print("❌ 列顺序调整失败")
                print("⚠️  需要进一步检查代码实现")
            
            print(f"\n📝 预期的列顺序:")
            print("1. 基础列: 日期、进场数量、出场数量、总流量、平均停车时长")
            print("2. 车辆类型列: 小型车_进、小型车_出、大型车_进、大型车_出...")
            print("3. 道闸列: A_进、A_出、B_进、B_出...")
            
            return order_correct
        else:
            print(f"   ❌ 每日统计数据为空")
            return False
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_daily_column_order()
    
    print("\n" + "=" * 70)
    print("分析周期_每日列顺序调整测试完成！")
    print("=" * 70)
