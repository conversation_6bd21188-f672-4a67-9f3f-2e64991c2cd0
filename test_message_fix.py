#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试提示信息修复
验证重复文件的提示信息是否正确
"""

import pandas as pd
import tempfile
import os
import shutil

def test_duplicate_message_fix():
    """测试重复文件的提示信息修复"""
    print("🧪 测试重复文件的提示信息修复")
    print("=" * 60)
    
    # 创建临时目录
    temp_dir = tempfile.mkdtemp()
    print(f"测试目录: {temp_dir}")
    
    try:
        # 创建两个内容完全相同的文件（模拟您的场景）
        same_data = pd.DataFrame({
            '车牌号': ['浙A12345', '浙B67890', '浙C11111'],
            '进出方向': ['进', '出', '进'],
            '时间': ['2024-01-01 08:00:00', '2024-01-01 09:00:00', '2024-01-01 10:00:00']
        })
        
        # 文件1和文件2内容完全相同
        file1_data = same_data.copy()
        file2_data = same_data.copy()
        
        # 文件3：不同内容，兼容结构
        file3_data = pd.DataFrame({
            '车牌号': ['浙D22222', '浙E33333'],
            '进出方向': ['出', '进'],
            '时间': ['2024-01-01 11:00:00', '2024-01-01 12:00:00']
        })
        
        # 创建文件（使用您提到的文件名模式）
        file1 = os.path.join(temp_dir, '义乌火车站道闸_网约车_合并_20250618_224630.csv')
        file2 = os.path.join(temp_dir, '义乌火车站道闸_网约车_合并_20250618_230820.csv')
        file3 = os.path.join(temp_dir, '新数据.csv')
        
        file1_data.to_csv(file1, index=False)
        file2_data.to_csv(file2, index=False)
        file3_data.to_csv(file3, index=False)
        
        print(f"\n📁 创建的测试文件:")
        print(f"  {os.path.basename(file1)}: {len(file1_data)} 行")
        print(f"  {os.path.basename(file2)}: {len(file2_data)} 行 (与第一个文件内容相同)")
        print(f"  新数据.csv: {len(file3_data)} 行 (不同内容)")
        
        # 导入合并器
        from excel_data_merger import IntegratedDataMerger
        
        # 创建合并器
        merger = IntegratedDataMerger()
        
        # 执行合并
        output_file = os.path.join(temp_dir, 'result.csv')
        result = merger.smart_merge_directory(
            input_path=temp_dir,
            output_path=output_file,
            file_pattern="*",
            recursive=False
        )
        
        # 分析结果
        stats = merger.merge_stats
        
        print(f"\n📊 合并结果:")
        print(f"总数据源: {stats['total_sources']}")
        print(f"成功合并: {stats['merged_sources']}")
        print(f"重复数量: {stats['duplicate_sources']}")
        print(f"跳过数量: {stats['skipped_sources']}")
        print(f"合并行数: {stats['merged_rows']}")
        print(f"实际行数: {len(result)}")
        
        # 显示详细信息
        if stats['merged_details']:
            print(f"\n✅ 成功合并的文件:")
            for detail in stats['merged_details']:
                print(f"   - {detail['file_name']}: {detail['rows']} 行 ({detail['role']})")
        
        if stats['duplicate_details']:
            print(f"\n🔄 重复检测详情:")
            for detail in stats['duplicate_details']:
                print(f"   - {detail['source']} → 重复于 {detail['duplicate_of']}")
        
        if stats['skipped_details']:
            print(f"\n⚠️ 跳过详情:")
            for detail in stats['skipped_details']:
                print(f"   - {detail['source']}: {detail['reason']}")
        
        # 验证结果
        print(f"\n🔍 预期结果:")
        print(f"  - 第一个合并文件 + 新数据文件 应该被合并")
        print(f"  - 第二个合并文件 应该被标记为重复")
        print(f"  - 不应该有'结构不兼容'的提示")
        
        expected_merged = 2  # 第一个合并文件 + 新数据文件
        expected_duplicates = 1  # 第二个合并文件
        expected_skipped = 0  # 不应该有跳过
        
        # 检查是否有错误的"结构不兼容"提示
        has_incompatible_message = any(
            '结构不兼容' in detail.get('reason', '') 
            for detail in stats['skipped_details']
        )
        
        is_correct = (
            stats['merged_sources'] == expected_merged and
            stats['duplicate_sources'] == expected_duplicates and
            stats['skipped_sources'] == expected_skipped and
            not has_incompatible_message
        )
        
        print(f"\n📋 结果评估:")
        if is_correct:
            print("✅ 提示信息修复成功！")
            print("   - 重复文件被正确识别为重复")
            print("   - 没有错误的'结构不兼容'提示")
            print("   - 日志信息完全正确")
        else:
            print("❌ 提示信息仍有问题！")
            print(f"   实际成功合并: {stats['merged_sources']} (预期: {expected_merged})")
            print(f"   实际重复数量: {stats['duplicate_sources']} (预期: {expected_duplicates})")
            print(f"   实际跳过数量: {stats['skipped_sources']} (预期: {expected_skipped})")
            
            if has_incompatible_message:
                print("   ⚠️ 仍然有错误的'结构不兼容'提示！")
                for detail in stats['skipped_details']:
                    if '结构不兼容' in detail.get('reason', ''):
                        print(f"      - {detail['source']}: {detail['reason']}")
        
        return is_correct
        
    except Exception as e:
        print(f"❌ 测试异常: {str(e)}")
        import traceback
        traceback.print_exc()
        return False
        
    finally:
        # 清理
        try:
            shutil.rmtree(temp_dir)
            print(f"\n🧹 清理测试目录")
        except:
            pass

def test_edge_case():
    """测试边缘情况：三个相同内容的文件"""
    print("\n🧪 测试边缘情况：三个相同内容的文件")
    print("=" * 60)
    
    # 创建临时目录
    temp_dir = tempfile.mkdtemp()
    print(f"测试目录: {temp_dir}")
    
    try:
        # 创建三个内容完全相同的文件
        same_data = pd.DataFrame({
            'field1': [1, 2, 3],
            'field2': ['A', 'B', 'C']
        })
        
        # 三个文件内容完全相同
        file1_data = same_data.copy()
        file2_data = same_data.copy()
        file3_data = same_data.copy()
        
        # 创建文件
        file1 = os.path.join(temp_dir, 'file1.csv')
        file2 = os.path.join(temp_dir, 'file2.csv')
        file3 = os.path.join(temp_dir, 'file3.csv')
        
        file1_data.to_csv(file1, index=False)
        file2_data.to_csv(file2, index=False)
        file3_data.to_csv(file3, index=False)
        
        print(f"\n📁 创建的测试文件:")
        print(f"  file1.csv: {len(file1_data)} 行")
        print(f"  file2.csv: {len(file2_data)} 行 (与file1相同)")
        print(f"  file3.csv: {len(file3_data)} 行 (与file1相同)")
        
        # 导入合并器
        from excel_data_merger import IntegratedDataMerger
        
        # 创建合并器
        merger = IntegratedDataMerger()
        
        # 执行合并
        output_file = os.path.join(temp_dir, 'result.csv')
        result = merger.smart_merge_directory(
            input_path=temp_dir,
            output_path=output_file,
            file_pattern="*",
            recursive=False
        )
        
        # 分析结果
        stats = merger.merge_stats
        
        print(f"\n📊 合并结果:")
        print(f"总数据源: {stats['total_sources']}")
        print(f"成功合并: {stats['merged_sources']}")
        print(f"重复数量: {stats['duplicate_sources']}")
        print(f"跳过数量: {stats['skipped_sources']}")
        
        # 显示详细信息
        if stats['duplicate_details']:
            print(f"\n🔄 重复检测详情:")
            for detail in stats['duplicate_details']:
                print(f"   - {detail['source']} → 重复于 {detail['duplicate_of']}")
        
        if stats['skipped_details']:
            print(f"\n⚠️ 跳过详情:")
            for detail in stats['skipped_details']:
                print(f"   - {detail['source']}: {detail['reason']}")
        
        # 验证结果
        expected_merged = 1  # 只有第一个文件被合并
        expected_duplicates = 2  # 另外两个文件被标记为重复
        expected_skipped = 0  # 不应该有跳过
        
        is_correct = (
            stats['merged_sources'] == expected_merged and
            stats['duplicate_sources'] == expected_duplicates and
            stats['skipped_sources'] == expected_skipped
        )
        
        print(f"\n📋 结果评估:")
        if is_correct:
            print("✅ 边缘情况处理正确！")
            print("   - 第一个文件被保留")
            print("   - 后续重复文件被正确识别")
        else:
            print("❌ 边缘情况处理有问题！")
            print(f"   实际成功合并: {stats['merged_sources']} (预期: {expected_merged})")
            print(f"   实际重复数量: {stats['duplicate_sources']} (预期: {expected_duplicates})")
        
        return is_correct
        
    except Exception as e:
        print(f"❌ 测试异常: {str(e)}")
        import traceback
        traceback.print_exc()
        return False
        
    finally:
        # 清理
        try:
            shutil.rmtree(temp_dir)
            print(f"\n🧹 清理测试目录")
        except:
            pass

def main():
    """主函数"""
    print("🔧 测试提示信息修复")
    print("=" * 80)
    
    # 测试1: 重复文件的提示信息
    result1 = test_duplicate_message_fix()
    
    # 测试2: 边缘情况
    result2 = test_edge_case()
    
    print(f"\n📊 测试总结:")
    print("=" * 80)
    print(f"重复文件提示信息: {'✅ 正确' if result1 else '❌ 有问题'}")
    print(f"边缘情况处理: {'✅ 正确' if result2 else '❌ 有问题'}")
    
    if result1 and result2:
        print(f"\n🎉 所有测试通过！提示信息已修复！")
        print(f"   - 重复文件会被正确识别为重复")
        print(f"   - 不会再出现错误的'结构不兼容'提示")
        print(f"   - 日志信息完全正确")
    else:
        print(f"\n⚠️ 仍有问题需要进一步修复")

if __name__ == "__main__":
    main()
