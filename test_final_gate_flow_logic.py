#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试最终修正的出入口流量占比逻辑
验证是否正确统计同时段内相同道闸进出的同类车辆
"""

import pandas as pd
import numpy as np
from datetime import datetime, timed<PERSON><PERSON>

def test_final_gate_flow_logic():
    """测试最终修正的出入口流量占比逻辑"""
    print("=" * 80)
    print("测试最终修正的出入口流量占比逻辑")
    print("验证是否正确统计同时段内相同道闸进出的同类车辆")
    print("=" * 80)
    
    try:
        # 1. 创建明确的测试数据
        print("\n📋 创建明确的测试数据...")
        
        # 设计特定的测试场景
        test_data = pd.DataFrame({
            'entry_time': [
                # 8:00-9:00时间段进场
                datetime(2024, 6, 1, 8, 30),   # 小型车从入口A进场
                datetime(2024, 6, 1, 8, 45),   # 大型车从入口A进场
                datetime(2024, 6, 1, 8, 20),   # 小型车从入口B进场
                # 9:00-10:00时间段进场
                datetime(2024, 6, 1, 9, 15),   # 大型车从入口A进场
                datetime(2024, 6, 1, 9, 30),   # 小型车从入口B进场
            ],
            'exit_time': [
                # 10:00-11:00时间段出场
                datetime(2024, 6, 1, 10, 30),  # 从出口A出场
                datetime(2024, 6, 1, 10, 45),  # 从出口A出场
                datetime(2024, 6, 1, 10, 20),  # 从出口B出场
                # 11:00-12:00时间段出场
                datetime(2024, 6, 1, 11, 15),  # 从出口A出场
                datetime(2024, 6, 1, 11, 30),  # 从出口B出场
            ],
            'vtype': ['小型车', '大型车', '小型车', '大型车', '小型车'],
            'entry_gate': ['入口A', '入口A', '入口B', '入口A', '入口B'],
            'exit_gate': ['出口A', '出口A', '出口B', '出口A', '出口B'],
            'duration': [2.0, 2.0, 2.0, 2.0, 2.0]
        })
        
        print(f"   测试数据: {len(test_data)} 条记录")
        
        # 显示详细数据
        print(f"\n   详细数据分析:")
        print(f"   8:00-9:00进场:")
        entry_8_9 = test_data[
            (test_data['entry_time'].dt.hour >= 8) & 
            (test_data['entry_time'].dt.hour < 9)
        ]
        for i, row in entry_8_9.iterrows():
            print(f"     {row['vtype']} 从 {row['entry_gate']} 进场")
        
        print(f"\n   10:00-11:00出场:")
        exit_10_11 = test_data[
            (test_data['exit_time'].dt.hour >= 10) & 
            (test_data['exit_time'].dt.hour < 11)
        ]
        for i, row in exit_10_11.iterrows():
            print(f"     {row['vtype']} 从 {row['exit_gate']} 出场")
        
        # 2. 期望的统计结果
        print(f"\n📊 期望的统计结果:")
        print(f"   8:00-9:00时间段:")
        print(f"     入口A: 小型车进场1辆, 大型车进场1辆")
        print(f"     入口B: 小型车进场1辆, 大型车进场0辆")
        print(f"     出口A: 小型车出场0辆, 大型车出场0辆")
        print(f"     出口B: 小型车出场0辆, 大型车出场0辆")
        
        print(f"\n   10:00-11:00时间段:")
        print(f"     入口A: 小型车进场0辆, 大型车进场0辆")
        print(f"     入口B: 小型车进场0辆, 大型车进场0辆")
        print(f"     出口A: 小型车出场1辆, 大型车出场1辆")
        print(f"     出口B: 小型车出场1辆, 大型车出场0辆")
        
        # 3. 模拟修正后的算法
        print(f"\n🔍 测试修正后的算法...")
        
        class MockReportGenerator:
            def __init__(self, data, params):
                self.data = data
                self.params = params
            
            def _log_warning(self, message):
                print(f"   警告: {message}")
            
            def _log_error(self, message):
                print(f"   错误: {message}")
            
            def _generate_time_periods(self, data):
                """生成时间段"""
                return ['08:00-09:00', '09:00-10:00', '10:00-11:00', '11:00-12:00']
            
            def _preprocess_gate_data(self, data, entry_gate_field, exit_gate_field, vtype_field):
                """预处理道闸数据"""
                data = data.copy()
                data['entry_time'] = pd.to_datetime(data['entry_time'], errors='coerce')
                data['exit_time'] = pd.to_datetime(data['exit_time'], errors='coerce')
                data = data[~data['entry_time'].isna() & ~data['exit_time'].isna()]
                return data
            
            def _get_period_data(self, data, start_dt, end_dt):
                """获取指定时间段的进出场数据"""
                entry_mask = (
                    (data['entry_time'].dt.time >= start_dt) & 
                    (data['entry_time'].dt.time < end_dt)
                )
                exit_mask = (
                    (data['exit_time'].dt.time >= start_dt) & 
                    (data['exit_time'].dt.time < end_dt)
                )
                return data[entry_mask], data[exit_mask]
            
            def _calculate_gate_flow_distribution(self, data=None):
                """计算出入口流量占比统计（最终修正版本）"""
                try:
                    data = data if data is not None else self.data
                    if data.empty:
                        self._log_warning("输入数据为空")
                        return pd.DataFrame(columns=['时间段', '车辆类型', '出入口', '进场数量', '出场数量'])
                    
                    # 使用标准化的字段名
                    entry_gate_field = 'entry_gate'
                    exit_gate_field = 'exit_gate'
                    vtype_field = 'vtype'
                    
                    # 预处理数据
                    data = self._preprocess_gate_data(data, entry_gate_field, exit_gate_field, vtype_field)
                    if data.empty:
                        return pd.DataFrame(columns=['时间段', '车辆类型', '出入口', '进场数量', '出场数量'])
                    
                    # 生成时间段
                    time_periods = self._generate_time_periods(data)
                    
                    # 统计各时间段各道闸的进出场数量
                    results = []
                    for period in time_periods:
                        start_time, end_time = period.split('-')
                        start_dt = datetime.strptime(start_time, '%H:%M').time()
                        end_dt = datetime.strptime(end_time, '%H:%M').time()
                        
                        # 获取当前时间段的进出场数据
                        entry_data, exit_data = self._get_period_data(data, start_dt, end_dt)
                        
                        # 获取所有道闸（进场道闸和出场道闸的并集）
                        all_gates = sorted(set(data[entry_gate_field].dropna().unique()) | 
                                         set(data[exit_gate_field].dropna().unique()))
                        
                        # 统计每个道闸的进出场数量
                        for gate in all_gates:
                            # 按车辆类型统计
                            if vtype_field in data.columns:
                                vtypes = sorted(data[vtype_field].dropna().unique())
                                for vtype in vtypes:
                                    # 统计进场数量（该时间段内从该道闸进入的同类车辆）
                                    entry_count = len(entry_data[
                                        (entry_data[entry_gate_field] == gate) & 
                                        (entry_data[vtype_field] == vtype)
                                    ])
                                    
                                    # 统计出场数量（该时间段内从该道闸出去的同类车辆）
                                    exit_count = len(exit_data[
                                        (exit_data[exit_gate_field] == gate) & 
                                        (exit_data[vtype_field] == vtype)
                                    ])
                                    
                                    results.append({
                                        '时间段': period,
                                        '车辆类型': vtype,
                                        '出入口': gate,
                                        '进场数量': entry_count,
                                        '出场数量': exit_count
                                    })
                            else:
                                # 没有车辆类型字段时只统计总量
                                entry_count = len(entry_data[entry_data[entry_gate_field] == gate])
                                exit_count = len(exit_data[exit_data[exit_gate_field] == gate])
                                
                                results.append({
                                    '时间段': period,
                                    '车辆类型': '所有车辆',
                                    '出入口': gate,
                                    '进场数量': entry_count,
                                    '出场数量': exit_count
                                })
                    
                    if not results:
                        self._log_warning("未生成任何统计结果")
                        return pd.DataFrame(columns=['时间段', '车辆类型', '出入口', '进场数量', '出场数量'])
                        
                    return pd.DataFrame(results)
                    
                except Exception as e:
                    self._log_error(f"计算出入口流量分布失败: {str(e)}")
                    return pd.DataFrame(columns=['时间段', '车辆类型', '出入口', '进场数量', '出场数量'])
        
        # 4. 执行测试
        params = {'mode': 'mode2'}
        mock_generator = MockReportGenerator(test_data, params)
        flow_distribution = mock_generator._calculate_gate_flow_distribution()
        
        if not flow_distribution.empty:
            print(f"   ✅ 成功生成出入口流量占比数据")
            print(f"   - 数据行数: {len(flow_distribution)}")
            print(f"   - 数据列数: {len(flow_distribution.columns)}")
            
            # 5. 验证结果
            print(f"\n📊 实际统计结果:")
            
            # 按时间段分组显示
            for period in ['08:00-09:00', '10:00-11:00']:
                print(f"\n   {period}时间段:")
                period_data = flow_distribution[flow_distribution['时间段'] == period]
                
                for i, row in period_data.iterrows():
                    print(f"     {row['出入口']} | {row['车辆类型']} | 进场:{row['进场数量']} | 出场:{row['出场数量']}")
            
            # 6. 验证逻辑正确性
            print(f"\n🔍 验证逻辑正确性...")
            
            # 验证8:00-9:00入口A小型车进场
            result_8_9_A_small = flow_distribution[
                (flow_distribution['时间段'] == '08:00-09:00') &
                (flow_distribution['出入口'] == '入口A') &
                (flow_distribution['车辆类型'] == '小型车')
            ]
            
            if not result_8_9_A_small.empty:
                actual_entry = result_8_9_A_small.iloc[0]['进场数量']
                expected_entry = 1  # 期望1辆小型车从入口A进场
                if actual_entry == expected_entry:
                    print(f"   ✅ 入口A小型车8:00-9:00进场统计正确: {actual_entry}")
                else:
                    print(f"   ❌ 入口A小型车8:00-9:00进场统计错误: 期望{expected_entry}, 实际{actual_entry}")
            
            # 验证10:00-11:00出口A小型车出场
            result_10_11_A_small = flow_distribution[
                (flow_distribution['时间段'] == '10:00-11:00') &
                (flow_distribution['出入口'] == '出口A') &
                (flow_distribution['车辆类型'] == '小型车')
            ]
            
            if not result_10_11_A_small.empty:
                actual_exit = result_10_11_A_small.iloc[0]['出场数量']
                expected_exit = 1  # 期望1辆小型车从出口A出场
                if actual_exit == expected_exit:
                    print(f"   ✅ 出口A小型车10:00-11:00出场统计正确: {actual_exit}")
                else:
                    print(f"   ❌ 出口A小型车10:00-11:00出场统计错误: 期望{expected_exit}, 实际{actual_exit}")
            
            # 验证10:00-11:00入口A小型车进场（应该为0）
            result_10_11_A_small_entry = flow_distribution[
                (flow_distribution['时间段'] == '10:00-11:00') &
                (flow_distribution['出入口'] == '入口A') &
                (flow_distribution['车辆类型'] == '小型车')
            ]
            
            if not result_10_11_A_small_entry.empty:
                actual_entry_10_11 = result_10_11_A_small_entry.iloc[0]['进场数量']
                expected_entry_10_11 = 0  # 期望0辆小型车在10:00-11:00从入口A进场
                if actual_entry_10_11 == expected_entry_10_11:
                    print(f"   ✅ 入口A小型车10:00-11:00进场统计正确: {actual_entry_10_11}")
                else:
                    print(f"   ❌ 入口A小型车10:00-11:00进场统计错误: 期望{expected_entry_10_11}, 实际{actual_entry_10_11}")
            
            # 7. 总结
            print(f"\n{'='*60}")
            print("最终逻辑验证总结")
            print('='*60)
            
            print(f"✅ 修正后的逻辑:")
            print(f"   - 进场数量: 该时间段内从该道闸进入的同类车辆数量")
            print(f"   - 出场数量: 该时间段内从该道闸出去的同类车辆数量")
            print(f"   - 统计维度: 时间段 × 车辆类型 × 道闸")
            print(f"   - 数据结构: 同一行包含进场和出场数量")
            
            print(f"\n🎯 应用场景:")
            print(f"   - 可以看到每个时段每个道闸的进出流量")
            print(f"   - 可以对比不同道闸的使用情况")
            print(f"   - 可以分析不同车辆类型的流量分布")
            print(f"   - 便于发现流量高峰和瓶颈")
            
            return True
        else:
            print(f"   ❌ 出入口流量占比数据为空")
            return False
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_final_gate_flow_logic()
    
    print("\n" + "=" * 80)
    print("最终修正的出入口流量占比逻辑测试完成！")
    print("=" * 80)
