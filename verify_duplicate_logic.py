#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证重复检测逻辑
确认当前的重复检测行为是否符合预期
"""

import pandas as pd
import tempfile
import os
import shutil

def test_duplicate_logic():
    """测试重复检测逻辑"""
    print("🧪 验证重复检测逻辑")
    print("=" * 60)
    
    # 创建临时目录
    temp_dir = tempfile.mkdtemp()
    print(f"测试目录: {temp_dir}")
    
    try:
        # 创建测试数据
        data_a = pd.DataFrame({
            'col1': [1, 2, 3],
            'col2': ['A', 'B', 'C']
        })
        
        data_b = pd.DataFrame({
            'col1': [4, 5, 6],
            'col2': ['D', 'E', 'F']
        })
        
        # data_c 与 data_a 完全相同（重复）
        data_c = data_a.copy()
        
        # 创建文件（注意文件名的字母顺序）
        file_a = os.path.join(temp_dir, 'a_first.csv')
        file_b = os.path.join(temp_dir, 'b_unique.csv')
        file_c = os.path.join(temp_dir, 'c_duplicate.csv')  # 与a_first.csv重复
        
        data_a.to_csv(file_a, index=False)
        data_b.to_csv(file_b, index=False)
        data_c.to_csv(file_c, index=False)
        
        print(f"\n📁 创建的测试文件:")
        print(f"  a_first.csv: {len(data_a)} 行数据 (原始数据)")
        print(f"  b_unique.csv: {len(data_b)} 行数据 (唯一数据)")
        print(f"  c_duplicate.csv: {len(data_c)} 行数据 (与a_first.csv重复)")
        
        # 导入合并器
        from excel_data_merger import IntegratedDataMerger
        
        # 创建合并器
        merger = IntegratedDataMerger()
        
        # 执行合并
        output_file = os.path.join(temp_dir, 'result.csv')
        result = merger.smart_merge_directory(
            input_path=temp_dir,
            output_path=output_file,
            file_pattern="*.csv",
            recursive=False
        )
        
        # 分析结果
        stats = merger.merge_stats
        
        print(f"\n📊 合并结果:")
        print(f"总数据源: {stats['total_sources']}")
        print(f"成功合并: {stats['merged_sources']}")
        print(f"重复数量: {stats['duplicate_sources']}")
        print(f"合并行数: {stats['merged_rows']}")
        print(f"实际行数: {len(result)}")
        
        # 显示重复详情
        if stats['duplicate_details']:
            print(f"\n🔄 重复检测详情:")
            for detail in stats['duplicate_details']:
                print(f"   - {detail['source']} → 重复于 {detail['duplicate_of']}")
        
        # 显示成功合并的文件
        if stats['merged_details']:
            print(f"\n✅ 成功合并的文件:")
            for detail in stats['merged_details']:
                print(f"   - {detail['file_name']}: {detail['rows']} 行")
        
        # 验证逻辑
        print(f"\n🔍 逻辑验证:")
        
        # 预期结果：a_first.csv + b_unique.csv，c_duplicate.csv被跳过
        expected_files = ['a_first.csv', 'b_unique.csv']
        expected_rows = len(data_a) + 1 + len(data_b) + 1  # +1 for header
        expected_duplicates = 1
        
        print(f"预期保留文件: {expected_files}")
        print(f"预期重复数量: {expected_duplicates}")
        print(f"预期总行数: {expected_rows}")
        
        # 检查实际结果
        merged_files = [detail['file_name'] for detail in stats['merged_details']]
        print(f"实际保留文件: {merged_files}")
        
        # 验证结果
        logic_correct = (
            stats['duplicate_sources'] == expected_duplicates and
            len(result) == expected_rows and
            'a_first.csv' in merged_files and
            'b_unique.csv' in merged_files and
            'c_duplicate.csv' not in merged_files
        )
        
        print(f"\n📋 结果评估:")
        if logic_correct:
            print("✅ 重复检测逻辑正确！")
            print("   - 第一个文件被保留作为参考")
            print("   - 重复文件被正确跳过")
            print("   - 唯一文件被正常合并")
        else:
            print("❌ 重复检测逻辑有问题！")
            
        return logic_correct
        
    except Exception as e:
        print(f"❌ 测试异常: {str(e)}")
        import traceback
        traceback.print_exc()
        return False
        
    finally:
        # 清理
        try:
            shutil.rmtree(temp_dir)
            print(f"\n🧹 清理测试目录")
        except:
            pass

def test_file_processing_order():
    """测试文件处理顺序"""
    print("\n🧪 测试文件处理顺序")
    print("=" * 60)
    
    # 创建临时目录
    temp_dir = tempfile.mkdtemp()
    print(f"测试目录: {temp_dir}")
    
    try:
        # 创建相同内容的文件，但文件名不同
        same_data = pd.DataFrame({
            'x': [1, 2, 3],
            'y': ['p', 'q', 'r']
        })
        
        # 创建文件（按字母顺序命名）
        files = ['z_last.csv', 'a_first.csv', 'm_middle.csv']
        
        for filename in files:
            file_path = os.path.join(temp_dir, filename)
            same_data.to_csv(file_path, index=False)
            print(f"创建文件: {filename}")
        
        # 导入合并器
        from excel_data_merger import IntegratedDataMerger
        
        # 创建合并器
        merger = IntegratedDataMerger()
        
        # 执行合并
        output_file = os.path.join(temp_dir, 'result.csv')
        result = merger.smart_merge_directory(
            input_path=temp_dir,
            output_path=output_file,
            file_pattern="*.csv",
            recursive=False
        )
        
        # 分析结果
        stats = merger.merge_stats
        
        print(f"\n📊 处理结果:")
        print(f"总文件数: {len(files)}")
        print(f"成功合并: {stats['merged_sources']}")
        print(f"重复数量: {stats['duplicate_sources']}")
        
        # 显示处理顺序
        if stats['merged_details']:
            print(f"\n✅ 被保留的文件:")
            for detail in stats['merged_details']:
                print(f"   - {detail['file_name']} ({detail['role']})")
        
        if stats['duplicate_details']:
            print(f"\n🔄 被跳过的重复文件:")
            for detail in stats['duplicate_details']:
                print(f"   - {detail['source']} → 重复于 {detail['duplicate_of']}")
        
        print(f"\n💡 结论:")
        print(f"   - 文件处理顺序可能影响哪个文件被保留")
        print(f"   - 第一个被处理的文件成为参考，后续重复文件被跳过")
        
    except Exception as e:
        print(f"❌ 测试异常: {str(e)}")
        
    finally:
        # 清理
        try:
            shutil.rmtree(temp_dir)
            print(f"\n🧹 清理测试目录")
        except:
            pass

def main():
    """主函数"""
    print("🔧 重复检测逻辑验证")
    print("=" * 80)
    
    # 测试1：基本重复检测逻辑
    result1 = test_duplicate_logic()
    
    # 测试2：文件处理顺序
    test_file_processing_order()
    
    print(f"\n📊 总结:")
    print("=" * 80)
    print(f"基本重复检测: {'✅ 正确' if result1 else '❌ 有问题'}")
    print(f"\n💡 当前逻辑:")
    print(f"   1. 按文件系统顺序处理文件")
    print(f"   2. 第一个文件成为参考结构")
    print(f"   3. 后续重复文件被跳过")
    print(f"   4. 这是'先到先得'的正确实现")

if __name__ == "__main__":
    main()
