#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试最终修复
验证重复检测和结构检测的正确逻辑
"""

import pandas as pd
import tempfile
import os
import shutil

def test_duplicate_vs_incompatible():
    """测试重复检测 vs 结构不兼容的正确逻辑"""
    print("🧪 测试重复检测 vs 结构不兼容的正确逻辑")
    print("=" * 60)
    
    # 创建临时目录
    temp_dir = tempfile.mkdtemp()
    print(f"测试目录: {temp_dir}")
    
    try:
        # 场景1：两个内容相同但列名不同的文件（应该被识别为重复，而不是结构不兼容）
        same_data = pd.DataFrame({
            'col1': [1, 2, 3, 4, 5],
            'col2': ['A', 'B', 'C', 'D', 'E'],
            'col3': [10, 20, 30, 40, 50]
        })
        
        # 文件1：原始列名
        file1_data = same_data.copy()
        file1_data.columns = ['字段1', '字段2', '字段3']
        
        # 文件2：相同数据，不同列名
        file2_data = same_data.copy()
        file2_data.columns = ['列A', '列B', '列C']
        
        # 文件3：不同数据，兼容列名
        file3_data = pd.DataFrame({
            '字段1': [6, 7, 8],
            '字段2': ['F', 'G', 'H'],
            '字段3': [60, 70, 80]
        })
        
        # 创建文件
        file1 = os.path.join(temp_dir, 'data1.csv')
        file2 = os.path.join(temp_dir, 'data2.csv')
        file3 = os.path.join(temp_dir, 'data3.csv')
        
        file1_data.to_csv(file1, index=False)
        file2_data.to_csv(file2, index=False)
        file3_data.to_csv(file3, index=False)
        
        print(f"\n📁 创建的测试文件:")
        print(f"  data1.csv: {len(file1_data)} 行 (列名: {list(file1_data.columns)})")
        print(f"  data2.csv: {len(file2_data)} 行 (列名: {list(file2_data.columns)}, 与data1内容相同)")
        print(f"  data3.csv: {len(file3_data)} 行 (列名: {list(file3_data.columns)}, 与data1兼容)")
        
        # 导入合并器
        from excel_data_merger import IntegratedDataMerger
        
        # 创建合并器
        merger = IntegratedDataMerger()
        
        # 执行合并
        output_file = os.path.join(temp_dir, 'result.csv')
        result = merger.smart_merge_directory(
            input_path=temp_dir,
            output_path=output_file,
            file_pattern="*",
            recursive=False
        )
        
        # 分析结果
        stats = merger.merge_stats
        
        print(f"\n📊 合并结果:")
        print(f"总数据源: {stats['total_sources']}")
        print(f"成功合并: {stats['merged_sources']}")
        print(f"重复数量: {stats['duplicate_sources']}")
        print(f"跳过数量: {stats['skipped_sources']}")
        print(f"合并行数: {stats['merged_rows']}")
        print(f"实际行数: {len(result)}")
        
        # 显示详细信息
        if stats['merged_details']:
            print(f"\n✅ 成功合并的文件:")
            for detail in stats['merged_details']:
                print(f"   - {detail['file_name']}: {detail['rows']} 行 ({detail['role']})")
        
        if stats['duplicate_details']:
            print(f"\n🔄 重复检测详情:")
            for detail in stats['duplicate_details']:
                print(f"   - {detail['source']} → 重复于 {detail['duplicate_of']}")
        
        if stats['skipped_details']:
            print(f"\n⚠️ 跳过详情:")
            for detail in stats['skipped_details']:
                print(f"   - {detail['source']}: {detail['reason']}")
        
        # 验证结果
        print(f"\n🔍 预期结果:")
        print(f"  - data1.csv 和 data3.csv 应该被合并（兼容结构）")
        print(f"  - data2.csv 应该被标记为与 data1.csv 重复")
        print(f"  - 不应该有结构不兼容的提示")
        
        expected_merged = 2  # data1.csv + data3.csv
        expected_duplicates = 1  # data2.csv 重复
        expected_skipped = 0  # 不应该有跳过
        
        is_correct = (
            stats['merged_sources'] == expected_merged and
            stats['duplicate_sources'] == expected_duplicates and
            stats['skipped_sources'] == expected_skipped
        )
        
        print(f"\n📋 结果评估:")
        if is_correct:
            print("✅ 逻辑修复成功！")
            print("   - 重复文件被正确识别为重复（而不是结构不兼容）")
            print("   - 兼容文件被正确合并")
            print("   - 没有错误的结构不兼容提示")
        else:
            print("❌ 逻辑仍有问题！")
            print(f"   实际成功合并: {stats['merged_sources']} (预期: {expected_merged})")
            print(f"   实际重复数量: {stats['duplicate_sources']} (预期: {expected_duplicates})")
            print(f"   实际跳过数量: {stats['skipped_sources']} (预期: {expected_skipped})")
            
            if stats['skipped_sources'] > 0:
                print("   ⚠️ 仍然有文件被错误地标记为结构不兼容！")
        
        return is_correct
        
    except Exception as e:
        print(f"❌ 测试异常: {str(e)}")
        import traceback
        traceback.print_exc()
        return False
        
    finally:
        # 清理
        try:
            shutil.rmtree(temp_dir)
            print(f"\n🧹 清理测试目录")
        except:
            pass

def test_real_scenario():
    """测试真实场景：合并结果文件被放在待合并目录中"""
    print("\n🧪 测试真实场景：合并结果文件在待合并目录中")
    print("=" * 60)
    
    # 创建临时目录
    temp_dir = tempfile.mkdtemp()
    print(f"测试目录: {temp_dir}")
    
    try:
        # 模拟您的真实场景
        # 原始数据
        original_data = pd.DataFrame({
            '车牌号': ['浙A12345', '浙B67890', '浙C11111'],
            '进出方向': ['进', '出', '进'],
            '时间': ['2024-01-01 08:00:00', '2024-01-01 09:00:00', '2024-01-01 10:00:00']
        })
        
        # 合并结果文件（内容相同，但文件名不同）
        merged_file1_data = original_data.copy()
        merged_file2_data = original_data.copy()
        
        # 新的原始数据
        new_data = pd.DataFrame({
            '车牌号': ['浙D22222', '浙E33333'],
            '进出方向': ['出', '进'],
            '时间': ['2024-01-01 11:00:00', '2024-01-01 12:00:00']
        })
        
        # 创建文件
        merged1 = os.path.join(temp_dir, '义乌火车站道闸_网约车_合并_20250618_224630.csv')
        merged2 = os.path.join(temp_dir, '义乌火车站道闸_网约车_合并_20250618_230820.csv')
        original = os.path.join(temp_dir, '新数据.csv')
        
        merged_file1_data.to_csv(merged1, index=False)
        merged_file2_data.to_csv(merged2, index=False)
        new_data.to_csv(original, index=False)
        
        print(f"\n📁 创建的测试文件:")
        print(f"  {os.path.basename(merged1)}: {len(merged_file1_data)} 行 (合并结果文件)")
        print(f"  {os.path.basename(merged2)}: {len(merged_file2_data)} 行 (合并结果文件，内容相同)")
        print(f"  新数据.csv: {len(new_data)} 行 (新的原始数据)")
        
        # 导入合并器
        from excel_data_merger import IntegratedDataMerger
        
        # 创建合并器
        merger = IntegratedDataMerger()
        
        # 执行合并
        output_file = os.path.join(temp_dir, 'final_result.csv')
        result = merger.smart_merge_directory(
            input_path=temp_dir,
            output_path=output_file,
            file_pattern="*",
            recursive=False
        )
        
        # 分析结果
        stats = merger.merge_stats
        
        print(f"\n📊 合并结果:")
        print(f"总数据源: {stats['total_sources']}")
        print(f"成功合并: {stats['merged_sources']}")
        print(f"重复数量: {stats['duplicate_sources']}")
        print(f"跳过数量: {stats['skipped_sources']}")
        print(f"合并行数: {stats['merged_rows']}")
        print(f"实际行数: {len(result)}")
        
        # 显示详细信息
        if stats['merged_details']:
            print(f"\n✅ 成功合并的文件:")
            for detail in stats['merged_details']:
                print(f"   - {detail['file_name']}: {detail['rows']} 行")
        
        if stats['duplicate_details']:
            print(f"\n🔄 重复检测详情:")
            for detail in stats['duplicate_details']:
                print(f"   - {detail['source']} → 重复于 {detail['duplicate_of']}")
        
        if stats['skipped_details']:
            print(f"\n⚠️ 跳过详情:")
            for detail in stats['skipped_details']:
                print(f"   - {detail['source']}: {detail['reason']}")
        
        # 验证结果
        print(f"\n🔍 预期结果:")
        print(f"  - 应该保留一个合并结果文件 + 新数据文件")
        print(f"  - 另一个合并结果文件应该被标记为重复")
        print(f"  - 不应该有结构不兼容的错误提示")
        
        expected_merged = 2  # 一个合并结果文件 + 新数据文件
        expected_duplicates = 1  # 另一个合并结果文件
        expected_skipped = 0  # 不应该有跳过
        
        is_correct = (
            stats['merged_sources'] == expected_merged and
            stats['duplicate_sources'] == expected_duplicates and
            stats['skipped_sources'] == expected_skipped
        )
        
        print(f"\n📋 结果评估:")
        if is_correct:
            print("✅ 真实场景测试通过！")
            print("   - 合并结果文件被正确识别为重复")
            print("   - 没有错误的结构不兼容提示")
            print("   - 逻辑完全正确")
        else:
            print("❌ 真实场景测试失败！")
            print(f"   实际成功合并: {stats['merged_sources']} (预期: {expected_merged})")
            print(f"   实际重复数量: {stats['duplicate_sources']} (预期: {expected_duplicates})")
            print(f"   实际跳过数量: {stats['skipped_sources']} (预期: {expected_skipped})")
        
        return is_correct
        
    except Exception as e:
        print(f"❌ 测试异常: {str(e)}")
        import traceback
        traceback.print_exc()
        return False
        
    finally:
        # 清理
        try:
            shutil.rmtree(temp_dir)
            print(f"\n🧹 清理测试目录")
        except:
            pass

def main():
    """主函数"""
    print("🔧 测试最终修复")
    print("=" * 80)
    
    # 测试1: 重复检测 vs 结构不兼容
    result1 = test_duplicate_vs_incompatible()
    
    # 测试2: 真实场景
    result2 = test_real_scenario()
    
    print(f"\n📊 测试总结:")
    print("=" * 80)
    print(f"重复检测逻辑: {'✅ 正确' if result1 else '❌ 有问题'}")
    print(f"真实场景测试: {'✅ 通过' if result2 else '❌ 失败'}")
    
    if result1 and result2:
        print(f"\n🎉 所有测试通过！问题已完全修复！")
        print(f"   - 重复文件会被正确识别为重复（而不是结构不兼容）")
        print(f"   - 不会再出现矛盾的日志信息")
        print(f"   - 合并结果文件在待合并目录中会被正确处理")
    else:
        print(f"\n⚠️ 仍有问题需要进一步修复")

if __name__ == "__main__":
    main()
