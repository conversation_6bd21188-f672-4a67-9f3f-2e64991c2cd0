#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证出入口流量占比图表相关代码是否已完全删除
"""

import os

def verify_deletion():
    """验证删除是否成功"""
    print("🔍 验证出入口流量占比图表代码删除情况")
    print("=" * 50)
    
    # 检查代码文件
    code_file = "parking_chart_generator.py"
    
    if not os.path.exists(code_file):
        print(f"❌ 代码文件不存在: {code_file}")
        return False
    
    try:
        # 读取代码文件
        with open(code_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查是否还有相关代码
        search_terms = [
            "出入口流量占比图表",
            "generate_gate_flow_chart",
            "出入口流量占比",
        ]
        
        print("📋 检查相关代码残留:")
        found_any = False
        
        for term in search_terms:
            if term in content:
                print(f"   ❌ 发现残留: '{term}'")
                found_any = True
                
                # 显示找到的行
                lines = content.split('\n')
                for i, line in enumerate(lines, 1):
                    if term in line:
                        print(f"      第{i}行: {line.strip()}")
            else:
                print(f"   ✅ 已清理: '{term}'")
        
        if not found_any:
            print("\n✅ 所有相关代码已成功删除！")
            
            # 检查现有的方法
            print("\n📊 当前保留的图表生成方法:")
            methods = []
            lines = content.split('\n')
            for line in lines:
                if 'def generate_' in line and 'chart' in line:
                    method_name = line.strip()
                    methods.append(method_name)
                    print(f"   ✅ {method_name}")
            
            print(f"\n📈 总计保留方法数量: {len(methods)}")
            return True
        else:
            print(f"\n⚠️ 发现代码残留，需要进一步清理")
            return False
            
    except Exception as e:
        print(f"❌ 验证失败: {str(e)}")
        return False

def check_current_chart_system():
    """检查当前的图表体系"""
    print("\n📊 当前图表体系")
    print("=" * 50)
    
    print("🎯 Timeline图表系列:")
    print("   1. 出入口进出量_总量.html - 总量Timeline柱状图")
    print("   2. 出入口进出量_方向.html - 方向Timeline柱状图")
    print("   3. 出入口占比_进.html - 进场占比Timeline饼图")
    print("   4. 出入口占比_出.html - 出场占比Timeline饼图")
    
    print("\n📈 其他图表:")
    print("   - 进出量时间分布图表.html")
    print("   - 停车时长分布图表.html")
    print("   - 车辆类型停车时长图表.html")
    print("   - 在场车辆分布图表.html")
    print("   - 进出量时间分布_车型.html")
    
    print("\n❌ 已删除:")
    print("   - 出入口流量占比图表.html (已删除)")
    
    print("\n💡 说明:")
    print("   出入口流量占比图表.html 的功能已被更先进的Timeline饼图替代:")
    print("   - 出入口占比_进.html: 进场占比分析")
    print("   - 出入口占比_出.html: 出场占比分析")
    print("   这两个图表提供了更详细的时间维度分析和智能阈值处理")

def main():
    """主函数"""
    success = verify_deletion()
    
    if success:
        check_current_chart_system()
        print("\n🎉 删除验证成功！")
        print("💡 出入口流量占比图表相关代码已完全清理")
        print("🔄 现有的Timeline饼图提供了更强大的功能")
    else:
        print("\n⚠️ 删除验证失败")
        print("💡 可能需要手动检查和清理残留代码")
    
    print("\n" + "="*50)
    input("按回车键退出...")

if __name__ == "__main__":
    main()
