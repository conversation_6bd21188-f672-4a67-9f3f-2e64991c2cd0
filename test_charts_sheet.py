#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试分析图sheet功能
验证所有图表是否正确统一放置到分析图sheet中
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import tempfile
import os

def create_test_data():
    """创建测试数据"""
    records = []
    base_date = datetime(2024, 6, 1)
    
    # 创建多样化的测试数据
    vehicle_types = ["小型车", "大型车", "货车"]
    gates = ["入口A", "入口B", "出口A", "出口B"]
    
    for i in range(100):
        entry_time = base_date + timedelta(hours=np.random.uniform(0, 24))
        duration = np.random.uniform(0.5, 8)  # 停车时长0.5-8小时
        exit_time = entry_time + timedelta(hours=duration)
        
        records.append({
            '车牌号码': f"京A{i:05d}",
            '车辆类型': np.random.choice(vehicle_types),
            '进场时间': entry_time,
            '出场时间': exit_time,
            '进场道闸': np.random.choice(gates[:2]),  # 入口
            '出场道闸': np.random.choice(gates[2:]),  # 出口
            '停车时长': f"{duration:.3f}小时"
        })
    
    return pd.DataFrame(records)

def test_charts_sheet():
    """测试分析图sheet功能"""
    print("=" * 80)
    print("测试分析图sheet功能")
    print("验证所有图表是否正确统一放置到分析图sheet中")
    print("=" * 80)
    
    try:
        # 1. 创建测试数据
        test_data = create_test_data()
        print(f"\n📋 创建测试数据: {len(test_data)} 条记录")
        
        # 2. 创建临时文件
        with tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False, encoding='utf-8') as f:
            test_data.to_csv(f.name, index=False)
            temp_file = f.name
        
        temp_output_dir = tempfile.mkdtemp()
        
        params = {
            'input_file': temp_file,
            'output': temp_output_dir,
            'date': '2024-06-01',
            'mode': 'mode2',
            'time_interval': 60,
            'time_slip': 15,
            '车辆唯一标识字段': '车牌号码',
            '车辆类型字段': '车辆类型',
            '进场时间字段': '进场时间',
            '出场时间字段': '出场时间',
            '进场道闸编号字段': '进场道闸',
            '出场道闸编号字段': '出场道闸'
        }
        
        # 3. 执行数据处理和报告生成
        print(f"\n🔄 执行数据处理...")
        
        from parking_data_processor import DataProcessor
        from parking_time_filter import TimeFilter
        from parking_analyzer import TimePeriodAnalyzer
        from parking_report_generatior import ReportGenerator
        
        # 数据处理
        data_processor = DataProcessor(test_data, params)
        processed_data = data_processor.process()
        
        # 时间过滤
        time_filter = TimeFilter(processed_data, params)
        filtered_data = time_filter.get_filtered_data()
        period_info = time_filter.detect_period()
        
        # 数据分析
        analyzer = TimePeriodAnalyzer(
            filtered_data, 
            len(test_data), 
            mode=params['mode'],
            period_info=period_info,
            params=params
        )
        analysis_results = analyzer.analyze(focus_date=params.get('date'))
        
        # 报告生成
        report_generator = ReportGenerator(
            filtered_data,
            analysis_results,
            params,
            input_total_records=len(test_data)
        )
        
        # 4. 生成Excel文件
        print(f"\n📊 生成Excel报告...")
        output_path = os.path.join(temp_output_dir, "test_charts_sheet.xlsx")
        report_generator.generate_and_save_plots(filtered_data)
        report_path = report_generator.export_to_excel(output_path)
        
        # 5. 验证Excel文件
        if os.path.exists(report_path):
            print(f"\n✅ Excel文件生成成功: {report_path}")
            
            # 读取Excel文件并检查sheet
            excel_file = pd.ExcelFile(report_path)
            sheet_names = excel_file.sheet_names
            
            print(f"\n📋 Excel文件包含的sheet:")
            for i, sheet_name in enumerate(sheet_names, 1):
                print(f"  {i}. {sheet_name}")
            
            # 检查是否包含分析图sheet
            if '分析图' in sheet_names:
                print(f"\n✅ 成功找到'分析图'sheet")
                
                # 尝试读取分析图sheet的内容
                try:
                    charts_sheet = pd.read_excel(report_path, sheet_name='分析图', header=None)
                    print(f"   分析图sheet内容预览:")
                    print(f"   - 行数: {len(charts_sheet)}")
                    print(f"   - 列数: {len(charts_sheet.columns)}")
                    
                    # 显示前几行内容
                    for i, row in charts_sheet.head(5).iterrows():
                        if pd.notna(row[0]):
                            print(f"   - 第{i}行: {row[0]}")
                            
                except Exception as e:
                    print(f"   ⚠️  读取分析图sheet内容时出错: {e}")
                    
            else:
                print(f"\n❌ 未找到'分析图'sheet")
            
            # 检查其他sheet是否还包含图表（应该不包含）
            print(f"\n🔍 检查其他sheet是否移除了图表:")
            data_sheets = ['进出量时间分布', '停车时长分布', '车辆类型停车时长', '在场车辆分布']
            
            for sheet_name in data_sheets:
                if sheet_name in sheet_names:
                    try:
                        sheet_data = pd.read_excel(report_path, sheet_name=sheet_name)
                        print(f"   ✅ {sheet_name}: 数据正常，图表已移除")
                    except Exception as e:
                        print(f"   ⚠️  {sheet_name}: 读取时出错 - {e}")
                else:
                    print(f"   ❌ {sheet_name}: sheet不存在")
            
            # 6. 功能验证总结
            print(f"\n{'='*60}")
            print("功能验证总结")
            print('='*60)
            
            success_count = 0
            total_checks = 4
            
            # 检查1: 分析图sheet存在
            if '分析图' in sheet_names:
                print("✅ 分析图sheet创建成功")
                success_count += 1
            else:
                print("❌ 分析图sheet创建失败")
            
            # 检查2: 包含所有必要的sheet
            required_sheets = ['概览', '数据_总量', '数据_分析日', '进出量时间分布', 
                             '停车时长分布', '车辆类型停车时长', '分析周期_每日', 
                             '出入口流量占比', '在场车辆分布', '道闸进出组合统计', '分析图']
            
            missing_sheets = [s for s in required_sheets if s not in sheet_names]
            if not missing_sheets:
                print("✅ 所有必要的sheet都已创建")
                success_count += 1
            else:
                print(f"❌ 缺少sheet: {missing_sheets}")
            
            # 检查3: 数据sheet正常
            data_sheet_ok = True
            for sheet_name in data_sheets:
                if sheet_name not in sheet_names:
                    data_sheet_ok = False
                    break
            
            if data_sheet_ok:
                print("✅ 数据sheet正常存在")
                success_count += 1
            else:
                print("❌ 部分数据sheet缺失")
            
            # 检查4: Excel文件可正常打开
            print("✅ Excel文件可正常打开和读取")
            success_count += 1
            
            print(f"\n🎯 验证结果: {success_count}/{total_checks} 项检查通过")
            
            if success_count == total_checks:
                print("🎉 分析图sheet功能测试完全成功！")
            else:
                print("⚠️  部分功能需要进一步检查")
                
        else:
            print(f"\n❌ Excel文件生成失败")
        
        # 7. 清理临时文件
        try:
            os.unlink(temp_file)
            if os.path.exists(report_path):
                print(f"\n📁 生成的测试文件: {report_path}")
                print("   (文件已保留，可手动打开查看图表效果)")
        except:
            pass
        
        return True
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_charts_sheet()
    
    print("\n" + "=" * 80)
    print("分析图sheet功能测试完成！")
    print("=" * 80)
