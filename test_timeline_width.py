#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试Timeline时间轴长度优化
验证时间轴长度是否与X轴对齐
"""

import os

def test_timeline_width():
    """测试Timeline时间轴长度优化"""
    print("📏 测试Timeline时间轴长度优化")
    print("=" * 50)
    
    # Excel文件路径
    excel_file = r'C:\Users\<USER>\Desktop\停车分析\义乌火车站道闸_私家车_合并_20250617_083509_analysis_20250618_211554.xlsx'
    
    if not os.path.exists(excel_file):
        print(f"❌ Excel文件不存在: {excel_file}")
        return False
    
    try:
        # 导入图表生成器
        from parking_chart_generator import ParkingChartGenerator
        
        # 创建图表生成器
        print("📊 创建图表生成器...")
        chart_generator = ParkingChartGenerator(excel_file, None)
        
        # 检查是否有进出量时间分布(按道闸)工作表
        target_sheet = '进出量时间分布(按道闸)'
        if target_sheet not in chart_generator.excel_data:
            print(f"❌ 未找到'{target_sheet}'工作表")
            available_sheets = list(chart_generator.excel_data.keys())
            print(f"可用工作表: {available_sheets}")
            return False
        
        # 尝试生成Timeline图表
        print(f"\n🎬 生成带优化时间轴长度的Timeline图表...")
        result = chart_generator.generate_gate_traffic_timeline()
        
        if result:
            print(f"✅ 成功生成: {os.path.basename(result)}")
            
            # 检查文件是否存在
            if os.path.exists(result):
                file_size = os.path.getsize(result) / 1024
                print(f"📄 文件大小: {file_size:.1f}KB")
                
                # 读取HTML内容检查时间轴配置
                with open(result, 'r', encoding='utf-8') as f:
                    html_content = f.read()
                
                # 检查时间轴长度相关配置
                timeline_width_checks = {
                    '左边距设置': 'pos_left:"10%"' in html_content or 'pos_left":"10%"' in html_content,
                    '右边距设置': 'pos_right:"10%"' in html_content or 'pos_right":"10%"' in html_content,
                    '宽度设置': 'width:"80%"' in html_content or 'width":"80%"' in html_content,
                    'Timeline组件': 'Timeline' in html_content,
                    '底部位置': 'pos_bottom:"5%"' in html_content or 'pos_bottom":"5%"' in html_content,
                }
                
                print(f"\n📏 时间轴长度配置验证:")
                all_checks_passed = True
                for check_name, is_passed in timeline_width_checks.items():
                    status = "✅" if is_passed else "❌"
                    print(f"   {status} {check_name}: {'通过' if is_passed else '未通过'}")
                    if not is_passed:
                        all_checks_passed = False
                
                # 显示配置详情
                print(f"\n📊 时间轴布局配置:")
                print(f"   左边距: 10% (与图表左边距对齐)")
                print(f"   右边距: 10% (与图表右边距对齐)")
                print(f"   宽度: 80% (与X轴长度一致)")
                print(f"   底部位置: 5% (距离底部边距)")
                
                if all_checks_passed:
                    print("\n✅ 时间轴长度优化成功！")
                    print("   - 时间轴长度现在与X轴对齐")
                    print("   - 左右边距与图表区域一致")
                    print("   - 提供更好的视觉对齐效果")
                    print("   - 时间段选择更加精确")
                    return True
                else:
                    print("\n⚠️ 时间轴配置可能有问题")
                    return False
            else:
                print(f"❌ 文件未生成: {result}")
                return False
        else:
            print("❌ 未能生成Timeline图表")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def demo_timeline_width_improvements():
    """演示时间轴长度改进"""
    print("\n📏 时间轴长度改进说明")
    print("=" * 50)
    
    print("🔄 改进对比:")
    print("   修改前:")
    print("     ❌ 时间轴长度较短，居中显示")
    print("     ❌ 与X轴长度不匹配，视觉不协调")
    print("     ❌ 时间段选择精度受限")
    print("     ❌ 整体布局不够美观")
    
    print("\n   修改后:")
    print("     ✅ 时间轴长度与X轴对齐")
    print("     ✅ 左右边距与图表区域一致")
    print("     ✅ 时间段选择更加精确")
    print("     ✅ 整体视觉效果更加协调")
    
    print("\n📊 技术配置:")
    print("   pos_left: '10%'    # 左边距与图表对齐")
    print("   pos_right: '10%'   # 右边距与图表对齐")
    print("   width: '80%'       # 时间轴宽度与X轴一致")
    print("   pos_bottom: '5%'   # 底部位置保持不变")
    
    print("\n🎯 用户体验提升:")
    print("   ✅ 视觉协调性：时间轴与图表区域完美对齐")
    print("   ✅ 操作精确性：更长的时间轴提供更精确的选择")
    print("   ✅ 专业外观：整体布局更加专业美观")
    print("   ✅ 直观性：时间轴长度与数据展示区域匹配")
    
    print("\n💡 设计理念:")
    print("   - 对齐原则：时间轴与数据展示区域保持一致")
    print("   - 比例协调：80%的宽度提供充足的操作空间")
    print("   - 边距统一：10%的左右边距与图表区域匹配")
    print("   - 位置合理：5%的底部边距确保不遮挡内容")

def main():
    """主函数"""
    # 演示改进内容
    demo_timeline_width_improvements()
    
    # 测试功能
    success = test_timeline_width()
    
    if success:
        print("\n🎉 时间轴长度优化测试成功！")
        print("📁 文件名: 进出量出入口分布.html")
        print("💡 现在时间轴长度与X轴对齐")
        print("🔍 建议在浏览器中验证效果:")
        print("   1. 观察时间轴是否与图表X轴对齐")
        print("   2. 测试时间段选择的精确性")
        print("   3. 检查整体视觉效果是否协调")
        
        print("\n📋 预期改进:")
        print("   ✅ 时间轴长度显著增加")
        print("   ✅ 与X轴完美对齐")
        print("   ✅ 时间段选择更加精确")
        print("   ✅ 整体布局更加美观")
    else:
        print("\n⚠️ 测试失败")
        print("💡 可能的原因:")
        print("   1. Excel文件中没有'进出量时间分布(按道闸)'工作表")
        print("   2. 数据格式不正确")
        print("   3. 代码执行过程中出现错误")
    
    print("\n" + "="*50)
    input("按回车键退出...")

if __name__ == "__main__":
    main()
