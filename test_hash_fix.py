#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试哈希计算修复
验证相同数据但不同列名的文件是否能被正确识别为重复
"""

import pandas as pd
import tempfile
import os
import shutil

def test_hash_calculation_fix():
    """测试哈希计算修复"""
    print("🧪 测试哈希计算修复")
    print("=" * 60)
    
    # 创建临时目录
    temp_dir = tempfile.mkdtemp()
    print(f"测试目录: {temp_dir}")
    
    try:
        # 创建相同数据但不同列名的文件
        same_data_values = [
            ['浙A12345', '进', '2024-01-01 08:00:00'],
            ['浙B67890', '出', '2024-01-01 09:00:00'],
            ['浙C11111', '进', '2024-01-01 10:00:00']
        ]
        
        # 文件1：使用中文列名
        file1_data = pd.DataFrame(same_data_values, columns=['车牌号', '进出方向', '时间'])
        
        # 文件2：使用英文列名（相同数据）
        file2_data = pd.DataFrame(same_data_values, columns=['VehicleID', 'Direction', 'Time'])
        
        # 文件3：使用其他列名（相同数据）
        file3_data = pd.DataFrame(same_data_values, columns=['PlateNumber', 'InOut', 'DateTime'])
        
        # 文件4：不同数据
        different_data = pd.DataFrame([
            ['浙D22222', '出', '2024-01-01 11:00:00'],
            ['浙E33333', '进', '2024-01-01 12:00:00']
        ], columns=['车牌号', '进出方向', '时间'])
        
        # 创建文件
        file1 = os.path.join(temp_dir, 'data_chinese.csv')
        file2 = os.path.join(temp_dir, 'data_english.csv')
        file3 = os.path.join(temp_dir, 'data_other.csv')
        file4 = os.path.join(temp_dir, 'data_different.csv')
        
        file1_data.to_csv(file1, index=False)
        file2_data.to_csv(file2, index=False)
        file3_data.to_csv(file3, index=False)
        different_data.to_csv(file4, index=False)
        
        print(f"\n📁 创建的测试文件:")
        print(f"  data_chinese.csv: {len(file1_data)} 行, 列名: {list(file1_data.columns)}")
        print(f"  data_english.csv: {len(file2_data)} 行, 列名: {list(file2_data.columns)}")
        print(f"  data_other.csv: {len(file3_data)} 行, 列名: {list(file3_data.columns)}")
        print(f"  data_different.csv: {len(different_data)} 行, 列名: {list(different_data.columns)}")
        print(f"\n注意: 前三个文件数据内容相同，只是列名不同")
        
        # 导入合并器
        from excel_data_merger import IntegratedDataMerger
        
        # 创建合并器
        merger = IntegratedDataMerger()
        
        # 执行合并
        output_file = os.path.join(temp_dir, 'result.csv')
        result = merger.smart_merge_directory(
            input_path=temp_dir,
            output_path=output_file,
            file_pattern="*",
            recursive=False
        )
        
        # 分析结果
        stats = merger.merge_stats
        
        print(f"\n📊 合并结果:")
        print(f"总数据源: {stats['total_sources']}")
        print(f"成功合并: {stats['merged_sources']}")
        print(f"重复数量: {stats['duplicate_sources']}")
        print(f"跳过数量: {stats['skipped_sources']}")
        print(f"合并行数: {stats['merged_rows']}")
        print(f"实际行数: {len(result)}")
        
        # 显示详细信息
        if stats['merged_details']:
            print(f"\n✅ 成功合并的文件:")
            for detail in stats['merged_details']:
                print(f"   - {detail['file_name']}: {detail['rows']} 行 ({detail['reason']})")
        
        if stats['duplicate_details']:
            print(f"\n🔄 重复检测详情:")
            for detail in stats['duplicate_details']:
                print(f"   - {detail['source']} → 重复于 {detail['duplicate_of']}")
        
        if stats['skipped_details']:
            print(f"\n⚠️ 跳过详情:")
            for detail in stats['skipped_details']:
                print(f"   - {detail['source']}: {detail['reason']}")
        
        # 验证结果
        print(f"\n🔍 预期结果:")
        print(f"  - 应该保留一个文件（第一个被处理的）+ 不同数据的文件")
        print(f"  - 其他两个相同数据的文件应该被标记为重复")
        print(f"  - 总共应该合并2个文件，重复2个文件")
        
        expected_merged = 2  # 第一个文件 + 不同数据的文件
        expected_duplicates = 2  # 另外两个相同数据的文件
        expected_skipped = 0  # 不应该有跳过
        
        is_correct = (
            stats['merged_sources'] == expected_merged and
            stats['duplicate_sources'] == expected_duplicates and
            stats['skipped_sources'] == expected_skipped
        )
        
        print(f"\n📋 结果评估:")
        if is_correct:
            print("✅ 哈希计算修复成功！")
            print("   - 相同数据但不同列名的文件被正确识别为重复")
            print("   - 不同数据的文件被正确合并")
            print("   - 重复检测基于数据内容而非列名")
        else:
            print("❌ 哈希计算仍有问题！")
            print(f"   实际成功合并: {stats['merged_sources']} (预期: {expected_merged})")
            print(f"   实际重复数量: {stats['duplicate_sources']} (预期: {expected_duplicates})")
            print(f"   实际跳过数量: {stats['skipped_sources']} (预期: {expected_skipped})")
            
            if stats['duplicate_sources'] != expected_duplicates:
                print("   ⚠️ 重复检测仍然有问题！")
                print("   可能原因：哈希计算仍然依赖列名")
        
        return is_correct
        
    except Exception as e:
        print(f"❌ 测试异常: {str(e)}")
        import traceback
        traceback.print_exc()
        return False
        
    finally:
        # 清理
        try:
            shutil.rmtree(temp_dir)
            print(f"\n🧹 清理测试目录")
        except:
            pass

def test_hash_calculation_directly():
    """直接测试哈希计算函数"""
    print("\n🧪 直接测试哈希计算函数")
    print("=" * 60)
    
    from excel_data_merger import IntegratedDataMerger
    merger = IntegratedDataMerger()
    
    # 创建相同数据但不同列名的DataFrame
    same_data_values = [
        ['浙A12345', '进', '2024-01-01 08:00:00'],
        ['浙B67890', '出', '2024-01-01 09:00:00'],
        ['浙C11111', '进', '2024-01-01 10:00:00']
    ]
    
    df1 = pd.DataFrame(same_data_values, columns=['车牌号', '进出方向', '时间'])
    df2 = pd.DataFrame(same_data_values, columns=['VehicleID', 'Direction', 'Time'])
    df3 = pd.DataFrame(same_data_values, columns=['PlateNumber', 'InOut', 'DateTime'])
    
    # 创建不同数据的DataFrame
    different_data = [
        ['浙D22222', '出', '2024-01-01 11:00:00'],
        ['浙E33333', '进', '2024-01-01 12:00:00']
    ]
    df4 = pd.DataFrame(different_data, columns=['车牌号', '进出方向', '时间'])
    
    # 计算哈希
    hash1 = merger.calculate_data_hash(df1)
    hash2 = merger.calculate_data_hash(df2)
    hash3 = merger.calculate_data_hash(df3)
    hash4 = merger.calculate_data_hash(df4)
    
    print(f"DataFrame 1 (中文列名): {hash1}")
    print(f"DataFrame 2 (英文列名): {hash2}")
    print(f"DataFrame 3 (其他列名): {hash3}")
    print(f"DataFrame 4 (不同数据): {hash4}")
    
    # 验证结果
    same_data_hashes_match = (hash1 == hash2 == hash3)
    different_data_hash_different = (hash4 != hash1)
    
    print(f"\n📋 哈希计算结果:")
    if same_data_hashes_match and different_data_hash_different:
        print("✅ 哈希计算正确！")
        print("   - 相同数据但不同列名的DataFrame产生相同哈希")
        print("   - 不同数据的DataFrame产生不同哈希")
    else:
        print("❌ 哈希计算有问题！")
        if not same_data_hashes_match:
            print("   - 相同数据但不同列名的DataFrame产生了不同哈希")
        if not different_data_hash_different:
            print("   - 不同数据的DataFrame产生了相同哈希")
    
    return same_data_hashes_match and different_data_hash_different

def main():
    """主函数"""
    print("🔧 测试哈希计算修复")
    print("=" * 80)
    
    # 测试1: 直接测试哈希计算函数
    result1 = test_hash_calculation_directly()
    
    # 测试2: 完整的合并流程测试
    result2 = test_hash_calculation_fix()
    
    print(f"\n📊 测试总结:")
    print("=" * 80)
    print(f"哈希计算函数: {'✅ 正确' if result1 else '❌ 有问题'}")
    print(f"完整合并流程: {'✅ 正确' if result2 else '❌ 有问题'}")
    
    if result1 and result2:
        print(f"\n🎉 所有测试通过！哈希计算已修复！")
        print(f"   - 重复检测现在基于数据内容而非列名")
        print(f"   - 相同数据但不同列名的文件会被正确识别为重复")
        print(f"   - 不会再有重复数据被错误合并的问题")
    else:
        print(f"\n⚠️ 仍有问题需要进一步修复")
        if not result1:
            print(f"   - 哈希计算函数需要进一步修复")
        if not result2:
            print(f"   - 完整合并流程需要进一步调试")

if __name__ == "__main__":
    main()
