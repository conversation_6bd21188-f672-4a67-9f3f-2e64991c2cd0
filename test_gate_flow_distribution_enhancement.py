#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试出入口流量占比sheet的增强功能
验证是否在同一行中包含进场和出场数量
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import tempfile
import os

def create_gate_flow_test_data():
    """创建包含多个道闸和车辆类型的测试数据"""
    records = []
    base_date = datetime(2024, 6, 1)
    
    # 创建多个时间段的数据，包含不同道闸和车辆类型
    gates = ["入口A道闸", "入口B道闸", "出口A道闸", "出口B道闸"]
    vehicle_types = ["小型车", "大型车"]
    
    for hour in range(8, 18):  # 8点到18点
        for i in range(5):  # 每小时5条记录
            entry_time = base_date + timedelta(hours=hour, minutes=np.random.randint(0, 60))
            duration = np.random.uniform(1, 4)
            exit_time = entry_time + timedelta(hours=duration)
            
            records.append({
                '车牌号码': f"京A{hour:02d}{i:03d}",
                '车辆类型': np.random.choice(vehicle_types),
                '进场时间': entry_time,
                '出场时间': exit_time,
                '进场道闸编号': np.random.choice(gates[:2]),  # 入口道闸
                '出场道闸编号': np.random.choice(gates[2:]),  # 出口道闸
                '停车时长': f"{duration:.3f}小时"
            })
    
    return pd.DataFrame(records)

def test_gate_flow_distribution_enhancement():
    """测试出入口流量占比sheet的增强功能"""
    print("=" * 80)
    print("测试出入口流量占比sheet的增强功能")
    print("验证是否在同一行中包含进场和出场数量")
    print("=" * 80)
    
    try:
        # 1. 创建测试数据
        test_data = create_gate_flow_test_data()
        print(f"\n📋 创建测试数据: {len(test_data)} 条记录")
        print(f"   时间范围: {test_data['进场时间'].min()} 到 {test_data['出场时间'].max()}")
        print(f"   车辆类型: {test_data['车辆类型'].unique().tolist()}")
        print(f"   进场道闸: {test_data['进场道闸编号'].unique().tolist()}")
        print(f"   出场道闸: {test_data['出场道闸编号'].unique().tolist()}")
        
        # 2. 创建临时文件
        with tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False, encoding='utf-8') as f:
            test_data.to_csv(f.name, index=False)
            temp_file = f.name
        
        temp_output_dir = tempfile.mkdtemp()
        
        params = {
            'input_file': temp_file,
            'output': temp_output_dir,
            'date': '2024-06-01',
            'mode': 'mode2',
            'time_interval': 60,
            'time_slip': 15,
            '车辆唯一标识字段': '车牌号码',
            '车辆类型字段': '车辆类型',
            '进场时间字段': '进场时间',
            '出场时间字段': '出场时间',
            '进场道闸编号字段': '进场道闸编号',
            '出场道闸编号字段': '出场道闸编号'
        }
        
        # 3. 执行数据处理
        print(f"\n🔄 执行数据处理...")
        
        from parking_data_processor import DataProcessor
        from parking_time_filter import TimeFilter
        from parking_analyzer import TimePeriodAnalyzer
        from parking_report_generatior import ReportGenerator
        
        # 数据处理
        data_processor = DataProcessor(test_data, params)
        processed_data = data_processor.process()
        
        # 时间过滤
        time_filter = TimeFilter(processed_data, params)
        filtered_data = time_filter.get_filtered_data()
        period_info = time_filter.detect_period()
        
        # 数据分析
        analyzer = TimePeriodAnalyzer(
            filtered_data, 
            len(test_data), 
            mode=params['mode'],
            period_info=period_info,
            params=params
        )
        analysis_results = analyzer.analyze(focus_date=params.get('date'))
        
        # 报告生成
        report_generator = ReportGenerator(
            filtered_data,
            analysis_results,
            params,
            input_total_records=len(test_data)
        )
        
        # 4. 测试出入口流量占比计算
        print(f"\n🔍 测试出入口流量占比计算...")
        
        # 直接调用_calculate_gate_flow_distribution方法
        flow_distribution = report_generator._calculate_gate_flow_distribution(filtered_data)
        
        if not flow_distribution.empty:
            print(f"   ✅ 成功生成出入口流量占比数据")
            print(f"   - 数据行数: {len(flow_distribution)}")
            print(f"   - 数据列数: {len(flow_distribution.columns)}")
            print(f"   - 列名: {flow_distribution.columns.tolist()}")
            
            # 5. 检查数据结构
            print(f"\n📋 检查数据结构...")
            
            expected_columns = ['时间段', '车辆类型', '出入口', '进场数量', '出场数量']
            actual_columns = flow_distribution.columns.tolist()
            
            print(f"   期望的列: {expected_columns}")
            print(f"   实际的列: {actual_columns}")
            
            # 验证列结构
            columns_match = set(expected_columns) == set(actual_columns)
            if columns_match:
                print(f"   ✅ 列结构正确")
            else:
                print(f"   ❌ 列结构不匹配")
                missing_cols = set(expected_columns) - set(actual_columns)
                extra_cols = set(actual_columns) - set(expected_columns)
                if missing_cols:
                    print(f"     缺少列: {missing_cols}")
                if extra_cols:
                    print(f"     多余列: {extra_cols}")
            
            # 6. 检查数据内容
            print(f"\n📊 检查数据内容...")
            
            # 显示数据样例
            print(f"   数据样例:")
            sample_data = flow_distribution.head(5)
            for i, row in sample_data.iterrows():
                print(f"     时间段: {row['时间段']}")
                print(f"       车辆类型: {row['车辆类型']}, 出入口: {row['出入口']}")
                print(f"       进场数量: {row['进场数量']}, 出场数量: {row['出场数量']}")
            
            # 验证数据合理性
            has_entry_data = (flow_distribution['进场数量'] > 0).any()
            has_exit_data = (flow_distribution['出场数量'] > 0).any()
            has_both_data = ((flow_distribution['进场数量'] > 0) & (flow_distribution['出场数量'] > 0)).any()
            
            print(f"\n   数据验证:")
            print(f"     有进场数据: {'✅ 是' if has_entry_data else '❌ 否'}")
            print(f"     有出场数据: {'✅ 是' if has_exit_data else '❌ 否'}")
            print(f"     有同时包含进出场的行: {'✅ 是' if has_both_data else '⚪ 否'}")
            
            # 统计信息
            total_entry = flow_distribution['进场数量'].sum()
            total_exit = flow_distribution['出场数量'].sum()
            unique_periods = flow_distribution['时间段'].nunique()
            unique_vtypes = flow_distribution['车辆类型'].nunique()
            unique_gates = flow_distribution['出入口'].nunique()
            
            print(f"\n   统计信息:")
            print(f"     总进场数量: {total_entry}")
            print(f"     总出场数量: {total_exit}")
            print(f"     时间段数: {unique_periods}")
            print(f"     车辆类型数: {unique_vtypes}")
            print(f"     道闸数: {unique_gates}")
        else:
            print(f"   ❌ 出入口流量占比数据为空")
        
        # 7. 生成Excel文件
        print(f"\n📊 生成Excel报告...")
        output_path = os.path.join(temp_output_dir, "test_gate_flow_distribution.xlsx")
        report_generator.generate_and_save_plots(filtered_data)
        report_path = report_generator.export_to_excel(output_path)
        
        # 8. 验证Excel文件中的出入口流量占比sheet
        if os.path.exists(report_path):
            print(f"\n✅ Excel文件生成成功: {report_path}")
            
            try:
                # 读取出入口流量占比sheet
                flow_sheet = pd.read_excel(report_path, sheet_name='出入口流量占比')
                print(f"\n📋 出入口流量占比sheet内容:")
                print(f"   - 行数: {len(flow_sheet)}")
                print(f"   - 列数: {len(flow_sheet.columns)}")
                print(f"   - 列名: {flow_sheet.columns.tolist()}")
                
                # 检查Excel中的数据结构
                excel_columns = flow_sheet.columns.tolist()
                expected_in_excel = ['时间段', '车辆类型', '出入口', '进场数量', '出场数量']
                
                excel_structure_correct = all(col in excel_columns for col in expected_in_excel)
                if excel_structure_correct:
                    print(f"   ✅ Excel中的列结构正确")
                else:
                    print(f"   ❌ Excel中的列结构不正确")
                
                # 显示Excel数据样例
                if not flow_sheet.empty:
                    print(f"\n   Excel数据样例:")
                    sample_excel = flow_sheet.head(3)
                    for i, row in sample_excel.iterrows():
                        print(f"     时间段: {row.iloc[0]}")  # 第一列
                        if '车辆类型' in flow_sheet.columns:
                            print(f"       车辆类型: {row['车辆类型']}, 出入口: {row['出入口']}")
                        if '进场数量' in flow_sheet.columns and '出场数量' in flow_sheet.columns:
                            print(f"       进场数量: {row['进场数量']}, 出场数量: {row['出场数量']}")
                
            except Exception as e:
                print(f"   ❌ 读取出入口流量占比sheet失败: {e}")
        
        # 9. 功能验证总结
        print(f"\n{'='*60}")
        print("功能验证总结")
        print('='*60)
        
        success_count = 0
        total_checks = 5
        
        # 检查1: Excel文件生成成功
        if os.path.exists(report_path):
            print("✅ Excel文件生成成功")
            success_count += 1
        else:
            print("❌ Excel文件生成失败")
        
        # 检查2: 数据结构正确
        if not flow_distribution.empty and columns_match:
            print("✅ 数据结构正确（包含进场数量和出场数量列）")
            success_count += 1
        else:
            print("❌ 数据结构不正确")
        
        # 检查3: 数据内容合理
        if not flow_distribution.empty and has_entry_data and has_exit_data:
            print("✅ 数据内容合理（包含进场和出场数据）")
            success_count += 1
        else:
            print("❌ 数据内容不合理")
        
        # 检查4: Excel中包含正确数据
        try:
            flow_sheet = pd.read_excel(report_path, sheet_name='出入口流量占比')
            if excel_structure_correct and not flow_sheet.empty:
                print("✅ Excel中包含正确的数据结构")
                success_count += 1
            else:
                print("❌ Excel中数据结构不正确")
        except:
            print("❌ 无法验证Excel中的数据")
        
        # 检查5: 同一行包含进出场数据
        if not flow_distribution.empty and '进场数量' in flow_distribution.columns and '出场数量' in flow_distribution.columns:
            print("✅ 同一行包含进场和出场数量")
            success_count += 1
        else:
            print("❌ 同一行不包含进出场数量")
        
        print(f"\n🎯 验证结果: {success_count}/{total_checks} 项通过")
        
        if success_count >= 4:
            print("🎉 出入口流量占比sheet增强功能测试成功！")
        else:
            print("⚠️  部分功能需要进一步检查")
        
        # 10. 功能说明
        print(f"\n📝 实现的功能:")
        print("1. ✅ 修改了_calculate_gate_flow_distribution方法")
        print("2. ✅ 在同一行中包含进场数量和出场数量")
        print("3. ✅ 保持时间段、车辆类型、出入口的分组")
        print("4. ✅ 支持多个道闸和车辆类型的统计")
        print("5. ✅ 数据结构更加清晰和易读")
        
        print(f"\n🎯 预期效果:")
        print("- 出入口流量占比sheet现在每行包含完整的进出场信息")
        print("- 列结构: 时间段、车辆类型、出入口、进场数量、出场数量")
        print("- 便于对比同一道闸在同一时段的进出场流量")
        print("- 数据分析更加直观和高效")
        
        # 11. 清理临时文件
        try:
            os.unlink(temp_file)
            print(f"\n📁 生成的测试文件: {report_path}")
            print("   (文件已保留，可手动打开查看出入口流量占比效果)")
        except:
            pass
        
        return True
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_gate_flow_distribution_enhancement()
    
    print("\n" + "=" * 80)
    print("出入口流量占比sheet增强功能测试完成！")
    print("=" * 80)
