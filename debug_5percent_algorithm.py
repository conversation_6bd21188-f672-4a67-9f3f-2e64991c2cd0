#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试5%阈值算法
检查为什么有些时段没有"其他出入口"
"""

import os
import pandas as pd

def debug_5percent_algorithm():
    """调试5%阈值算法"""
    print("🔍 调试5%阈值算法")
    print("=" * 50)
    
    # Excel文件路径
    excel_file = r'C:\Users\<USER>\Desktop\停车分析\义乌火车站道闸_私家车_合并_20250617_083509_analysis_20250618_211554.xlsx'
    
    if not os.path.exists(excel_file):
        print(f"❌ Excel文件不存在: {excel_file}")
        return False
    
    try:
        # 读取Excel数据
        print("📊 读取Excel数据...")
        target_sheet = '进出量时间分布(按道闸)'
        data = pd.read_excel(excel_file, sheet_name=target_sheet)
        
        print(f"📋 数据基本信息:")
        print(f"   数据形状: {data.shape}")
        print(f"   列名: {list(data.columns)}")
        
        # 分析数据结构
        columns = list(data.columns)
        total_cols = len(columns)
        
        if total_cols < 4:
            print("❌ 数据列数不足")
            return False
        
        # 计算出入口数量
        gate_cols_count = total_cols - 1
        gate_count = gate_cols_count // 3
        
        print(f"📊 结构分析:")
        print(f"   总列数: {total_cols}")
        print(f"   出入口数量: {gate_count}")
        
        # 提取进场数据
        gate_entry_data = {}
        gate_names = []
        
        for i in range(gate_count):
            entry_col_idx = 1 + i * 3  # 进场列
            if entry_col_idx < total_cols:
                gate_name = f"出入口{i + 1}"
                gate_names.append(gate_name)
                
                # 获取进场数据，处理NaN值
                entry_data = data.iloc[:, entry_col_idx].fillna(0).tolist()
                gate_entry_data[gate_name] = entry_data
                
                print(f"   {gate_name}: {columns[entry_col_idx]}")
        
        # 获取时间段数据
        time_periods = data.iloc[:, 0].astype(str).tolist()
        print(f"⏰ 时间段数量: {len(time_periods)}")
        
        # 分析每个时间段的5%阈值处理
        print(f"\n🔍 逐时间段分析:")
        
        for i, time_period in enumerate(time_periods[:10]):  # 只分析前10个时间段
            print(f"\n--- 时间段 {i+1}: {time_period} ---")
            
            # 获取该时间段各出入口的进场数据
            raw_entry_data = []
            total_entry = 0
            
            for gate_name in gate_names:
                raw_value = gate_entry_data[gate_name][i]
                
                # 处理数据类型
                try:
                    value = float(raw_value) if raw_value is not None and str(raw_value).strip() != '' else 0.0
                    if value != value:  # 检查NaN
                        value = 0.0
                except (ValueError, TypeError):
                    value = 0.0
                
                raw_entry_data.append([gate_name, value])
                total_entry += value
                print(f"  {gate_name}: {raw_value} → {value}")
            
            print(f"  总进场量: {total_entry}")
            
            if total_entry == 0:
                print(f"  ⚠️ 总进场量为0，跳过阈值处理")
                continue
            
            # 5%阈值处理
            threshold_percentage = 5.0
            threshold_value = total_entry * (threshold_percentage / 100.0)
            print(f"  5%阈值: {threshold_value:.2f}")
            
            main_gates = []
            other_gates_total = 0
            other_gates_names = []
            
            for gate_name, value in raw_entry_data:
                percentage = (value / total_entry * 100) if total_entry > 0 else 0
                
                if value >= threshold_value:
                    main_gates.append([gate_name, value])
                    print(f"    {gate_name}: {value} ({percentage:.2f}%) → 保留")
                else:
                    other_gates_total += value
                    other_gates_names.append(gate_name)
                    print(f"    {gate_name}: {value} ({percentage:.2f}%) → 合并")
            
            print(f"  结果:")
            print(f"    主要出入口: {len(main_gates)}个")
            print(f"    合并出入口: {len(other_gates_names)}个")
            print(f"    其他出入口总量: {other_gates_total}")
            
            if other_gates_total > 0:
                other_percentage = (other_gates_total / total_entry * 100)
                print(f"    其他出入口占比: {other_percentage:.2f}%")
                print(f"    ✅ 会显示'其他出入口'")
            else:
                print(f"    ❌ 不会显示'其他出入口'")
        
        # 统计分析
        print(f"\n📊 统计分析:")
        
        has_others_count = 0
        no_others_count = 0
        
        for i, time_period in enumerate(time_periods):
            raw_entry_data = []
            total_entry = 0
            
            for gate_name in gate_names:
                raw_value = gate_entry_data[gate_name][i]
                try:
                    value = float(raw_value) if raw_value is not None and str(raw_value).strip() != '' else 0.0
                    if value != value:
                        value = 0.0
                except (ValueError, TypeError):
                    value = 0.0
                raw_entry_data.append([gate_name, value])
                total_entry += value
            
            if total_entry > 0:
                threshold_value = total_entry * 0.05
                other_gates_total = sum(value for gate_name, value in raw_entry_data if value < threshold_value)
                
                if other_gates_total > 0:
                    has_others_count += 1
                else:
                    no_others_count += 1
        
        print(f"   有'其他出入口'的时间段: {has_others_count}个")
        print(f"   无'其他出入口'的时间段: {no_others_count}个")
        print(f"   总时间段: {len(time_periods)}个")
        
        if no_others_count > 0:
            print(f"\n💡 发现问题:")
            print(f"   有{no_others_count}个时间段没有'其他出入口'")
            print(f"   这可能是因为:")
            print(f"   1. 某些时间段所有出入口占比都>=5%")
            print(f"   2. 出入口数量较少")
            print(f"   3. 数据分布比较均匀")
        else:
            print(f"\n✅ 算法正常:")
            print(f"   所有时间段都有'其他出入口'")
        
        return True
        
    except Exception as e:
        print(f"❌ 调试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def suggest_solutions():
    """建议解决方案"""
    print("\n💡 可能的解决方案:")
    print("=" * 50)
    
    print("1. 降低阈值:")
    print("   - 将5%阈值降低到3%或2%")
    print("   - 这样会有更多出入口被合并")
    
    print("\n2. 动态阈值:")
    print("   - 根据出入口数量动态调整阈值")
    print("   - 出入口多时用5%，出入口少时用更低阈值")
    
    print("\n3. 最小合并数量:")
    print("   - 确保至少有1-2个出入口被合并")
    print("   - 即使占比>=5%，也合并最小的几个")
    
    print("\n4. 保持现状:")
    print("   - 当前算法是正确的")
    print("   - 某些时间段确实所有出入口占比都>=5%")
    print("   - 这是数据的真实反映")

def main():
    """主函数"""
    success = debug_5percent_algorithm()
    
    if success:
        suggest_solutions()
        
        print("\n🔧 下一步:")
        print("1. 检查调试输出，确认算法逻辑")
        print("2. 如需要，可以调整阈值或算法")
        print("3. 重新生成饼图验证效果")
    
    print("\n" + "="*50)
    input("按回车键退出...")

if __name__ == "__main__":
    main()
