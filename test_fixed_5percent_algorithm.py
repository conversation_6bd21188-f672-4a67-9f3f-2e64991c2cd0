#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修复后的5%阈值算法
验证智能阈值调整是否解决了问题
"""

import os

def test_fixed_5percent_algorithm():
    """测试修复后的5%阈值算法"""
    print("🔧 测试修复后的5%阈值算法")
    print("=" * 50)
    
    # Excel文件路径
    excel_file = r'C:\Users\<USER>\Desktop\停车分析\义乌火车站道闸_私家车_合并_20250617_083509_analysis_20250618_211554.xlsx'
    
    if not os.path.exists(excel_file):
        print(f"❌ Excel文件不存在: {excel_file}")
        return False
    
    try:
        # 导入图表生成器
        from parking_chart_generator import ParkingChartGenerator
        
        # 创建图表生成器
        print("📊 创建图表生成器...")
        chart_generator = ParkingChartGenerator(excel_file, None)
        
        # 检查是否有进出量时间分布(按道闸)工作表
        target_sheet = '进出量时间分布(按道闸)'
        if target_sheet not in chart_generator.excel_data:
            print(f"❌ 未找到'{target_sheet}'工作表")
            return False
        
        # 尝试生成修复后的饼图
        print(f"\n🥧 生成修复后的进场占比饼图...")
        print("📋 注意观察控制台输出的详细分析信息...")
        
        result = chart_generator.generate_gate_entry_proportion_timeline()
        
        if result:
            print(f"\n✅ 成功生成: {os.path.basename(result)}")
            
            # 检查文件是否存在
            if os.path.exists(result):
                file_size = os.path.getsize(result) / 1024
                print(f"📄 文件大小: {file_size:.1f}KB")
                
                # 读取HTML内容分析
                with open(result, 'r', encoding='utf-8') as f:
                    html_content = f.read()
                
                # 统计"其他出入口"的出现次数
                other_count = html_content.count('其他出入口')
                pie_count = html_content.count('Pie(')
                
                print(f"\n📊 修复效果分析:")
                print(f"   饼图数量: {pie_count}")
                print(f"   '其他出入口'出现次数: {other_count}")
                
                if other_count > 0:
                    print(f"   ✅ 修复成功！现在有{other_count}个时间段显示'其他出入口'")
                    
                    # 估算比例
                    if pie_count > 0:
                        ratio = other_count / pie_count
                        print(f"   📈 '其他出入口'出现比例: {ratio:.1%}")
                        
                        if ratio > 0.8:
                            print(f"   🎉 优秀！大部分时间段都有'其他出入口'")
                        elif ratio > 0.5:
                            print(f"   👍 良好！超过一半时间段有'其他出入口'")
                        else:
                            print(f"   ⚠️ 仍有改进空间，可考虑进一步降低阈值")
                else:
                    print(f"   ❌ 仍然没有'其他出入口'，需要进一步调试")
                
                # 检查智能阈值特征
                smart_threshold_checks = {
                    '智能阈值调整': '使用阈值:' in html_content or '出入口数量:' in html_content,
                    '3%阈值': '3%' in html_content,
                    '4%阈值': '4%' in html_content,
                    '5%阈值': '5%' in html_content,
                    '其他出入口项': '其他出入口' in html_content,
                }
                
                print(f"\n🔍 智能阈值特征检查:")
                for check_name, is_passed in smart_threshold_checks.items():
                    status = "✅" if is_passed else "❌"
                    print(f"   {status} {check_name}: {'检测到' if is_passed else '未检测到'}")
                
                return True
            else:
                print(f"❌ 文件未生成: {result}")
                return False
        else:
            print("❌ 未能生成饼图")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def explain_smart_threshold():
    """解释智能阈值机制"""
    print("\n🧠 智能阈值机制说明")
    print("=" * 50)
    
    print("🎯 问题分析:")
    print("   - 原始5%阈值对于出入口数量少的情况可能过高")
    print("   - 如果只有3-4个出入口，很可能都超过5%")
    print("   - 导致某些时间段没有'其他出入口'")
    
    print("\n🔧 智能阈值解决方案:")
    print("   出入口数量 <= 4: 使用3%阈值")
    print("   出入口数量 <= 6: 使用4%阈值")
    print("   出入口数量 > 6:  使用5%阈值")
    
    print("\n📊 预期效果:")
    print("   - 更多时间段会有'其他出入口'")
    print("   - 饼图重点突出效果更好")
    print("   - 适应不同的出入口数量情况")
    
    print("\n💡 其他改进:")
    print("   - 增强数据类型处理，避免NaN值问题")
    print("   - 详细的调试输出，便于问题诊断")
    print("   - 动态颜色分配，'其他出入口'使用灰色")

def main():
    """主函数"""
    # 解释智能阈值机制
    explain_smart_threshold()
    
    # 测试修复后的算法
    success = test_fixed_5percent_algorithm()
    
    if success:
        print("\n🎉 测试完成！")
        print("📁 文件名: 出入口占比_进.html")
        print("💡 建议检查:")
        print("   1. 查看控制台输出的详细分析信息")
        print("   2. 在浏览器中打开HTML文件验证效果")
        print("   3. 观察不同时间段的'其他出入口'显示")
        print("   4. 验证智能阈值是否按预期工作")
        
        print("\n🔍 如果仍有问题:")
        print("   1. 运行 debug_5percent_algorithm.py 进行详细调试")
        print("   2. 检查原始数据的分布情况")
        print("   3. 考虑进一步调整阈值策略")
    else:
        print("\n⚠️ 测试失败，建议:")
        print("   1. 检查Excel文件是否存在")
        print("   2. 验证数据格式是否正确")
        print("   3. 查看错误信息进行调试")
    
    print("\n" + "="*50)
    input("按回车键退出...")

if __name__ == "__main__":
    main()
