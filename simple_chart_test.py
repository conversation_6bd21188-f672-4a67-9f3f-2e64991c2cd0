#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的图表测试
"""

import pandas as pd
import numpy as np
import os
from parking_report_generator import ReportGenerator

def simple_chart_test():
    """简单的图表测试"""
    print("=== 简单图表测试 ===\n")
    
    # 创建最简单的测试数据
    test_data = pd.DataFrame({
        'duration': [0.5, 1.0, 2.0, 4.0, 8.0],
        'vtype': ['小型车'] * 5,
        'entry_time': pd.to_datetime(['2024-01-01 08:00:00'] * 5),
        'exit_time': pd.to_datetime(['2024-01-01 10:00:00'] * 5)
    })
    
    params = {'mode': 'mode2'}
    analysis_results = {}  # 空的分析结果
    generator = ReportGenerator(test_data, analysis_results, params)
    generator.processed_data = test_data
    
    print(f"测试数据: {len(test_data)} 条记录")
    
    # 测试数据计算
    print("1. 测试数据计算:")
    try:
        density_stats = generator._calculate_duration_probability_density()
        if not density_stats.empty:
            print(f"   ✅ 成功: {len(density_stats)} 行")
            print(f"   列名: {list(density_stats.columns)}")
        else:
            print("   ❌ 失败: 空数据")
            return
    except Exception as e:
        print(f"   ❌ 异常: {str(e)}")
        return
    
    # 测试图表创建
    print("\n2. 测试图表创建:")
    try:
        import xlsxwriter
        
        # 创建工作簿
        workbook = xlsxwriter.Workbook('simple_chart_test.xlsx')
        
        # 创建数据工作表
        worksheet = workbook.add_worksheet('延停时长概率密度')
        
        # 写入数据
        for col_num, column in enumerate(density_stats.columns):
            worksheet.write(0, col_num, column)
        
        for row_num, row in enumerate(density_stats.itertuples(index=False), start=1):
            for col_num, value in enumerate(row):
                worksheet.write(row_num, col_num, value)
        
        print(f"   ✅ 数据工作表创建成功")
        
        # 创建图表
        chart = generator._create_duration_probability_density_chart(workbook)
        
        if chart:
            print(f"   ✅ 图表创建成功")
            
            # 创建图表工作表
            chart_worksheet = workbook.add_worksheet('图表')
            chart_worksheet.insert_chart(1, 0, chart)
            
            print(f"   ✅ 图表插入成功")
        else:
            print(f"   ❌ 图表创建失败")
        
        workbook.close()
        print(f"   ✅ 文件保存: simple_chart_test.xlsx")
        
    except Exception as e:
        print(f"   ❌ 图表测试异常: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    simple_chart_test()
