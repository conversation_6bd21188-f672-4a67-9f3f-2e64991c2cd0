#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试重复检测问题
特别是当目录中同时有xlsx和csv文件时的处理逻辑
"""

import pandas as pd
import tempfile
import os
import shutil

def test_mixed_file_types():
    """测试混合文件类型的重复检测"""
    print("🧪 测试混合文件类型的重复检测")
    print("=" * 60)
    
    # 创建临时目录
    temp_dir = tempfile.mkdtemp()
    print(f"测试目录: {temp_dir}")
    
    try:
        # 创建测试数据
        data_a = pd.DataFrame({
            'col1': [1, 2, 3, 4, 5],
            'col2': ['A', 'B', 'C', 'D', 'E']
        })
        
        data_b = pd.DataFrame({
            'col1': [6, 7, 8, 9, 10],
            'col2': ['F', 'G', 'H', 'I', 'J']
        })
        
        # data_c 与 data_a 相同（重复）
        data_c = data_a.copy()
        
        # 创建不同格式的文件
        file1_csv = os.path.join(temp_dir, 'data1.csv')
        file2_xlsx = os.path.join(temp_dir, 'data2.xlsx')
        file3_csv = os.path.join(temp_dir, 'data3.csv')  # 与data1.csv重复
        
        # 保存文件
        data_a.to_csv(file1_csv, index=False)
        data_b.to_excel(file2_xlsx, index=False)
        data_c.to_csv(file3_csv, index=False)  # 重复文件
        
        print(f"\n📁 创建的文件:")
        print(f"  data1.csv: {len(data_a)} 行 (原始数据)")
        print(f"  data2.xlsx: {len(data_b)} 行 (Excel文件)")
        print(f"  data3.csv: {len(data_c)} 行 (与data1.csv重复)")
        
        # 导入合并器
        from excel_data_merger import IntegratedDataMerger
        
        # 创建合并器
        merger = IntegratedDataMerger()
        
        # 执行合并
        output_file = os.path.join(temp_dir, 'result.xlsx')
        result = merger.smart_merge_directory(
            input_path=temp_dir,
            output_path=output_file,
            file_pattern="*",
            recursive=False
        )
        
        # 分析结果
        stats = merger.merge_stats
        
        print(f"\n📊 合并结果:")
        print(f"总数据源: {stats['total_sources']}")
        print(f"成功合并: {stats['merged_sources']}")
        print(f"重复数量: {stats['duplicate_sources']}")
        print(f"跳过数量: {stats['skipped_sources']}")
        print(f"合并行数: {stats['merged_rows']}")
        print(f"实际行数: {len(result)}")
        
        # 显示详细信息
        if stats['merged_details']:
            print(f"\n✅ 成功合并的文件:")
            for detail in stats['merged_details']:
                print(f"   - {detail['file_name']}: {detail['rows']} 行 ({detail['role']})")
        
        if stats['duplicate_details']:
            print(f"\n🔄 重复检测详情:")
            for detail in stats['duplicate_details']:
                print(f"   - {detail['source']} → 重复于 {detail['duplicate_of']}")
        
        if stats['skipped_details']:
            print(f"\n⚠️ 跳过详情:")
            for detail in stats['skipped_details']:
                print(f"   - {detail['source']}: {detail['reason']}")
        
        # 验证预期结果
        print(f"\n🔍 预期结果分析:")
        print(f"  - 应该保留: data1.csv 或 data3.csv (其中一个)")
        print(f"  - 应该保留: data2.xlsx")
        print(f"  - 应该跳过: data1.csv 或 data3.csv (其中一个，重复)")
        print(f"  - 预期成功合并: 2")
        print(f"  - 预期重复数量: 1")
        
        # 检查是否符合预期
        expected_merged = 2
        expected_duplicates = 1
        
        is_correct = (
            stats['merged_sources'] == expected_merged and
            stats['duplicate_sources'] == expected_duplicates
        )
        
        print(f"\n📋 结果评估:")
        if is_correct:
            print("✅ 混合文件类型重复检测正确！")
        else:
            print("❌ 混合文件类型重复检测有问题！")
            print(f"   实际成功合并: {stats['merged_sources']} (预期: {expected_merged})")
            print(f"   实际重复数量: {stats['duplicate_sources']} (预期: {expected_duplicates})")
        
        return is_correct
        
    except Exception as e:
        print(f"❌ 测试异常: {str(e)}")
        import traceback
        traceback.print_exc()
        return False
        
    finally:
        # 清理
        try:
            shutil.rmtree(temp_dir)
            print(f"\n🧹 清理测试目录")
        except:
            pass

def test_csv_skipped_with_xlsx():
    """测试CSV文件在有xlsx文件时被跳过的问题"""
    print("\n🧪 测试CSV文件在有xlsx文件时是否被跳过")
    print("=" * 60)
    
    # 创建临时目录
    temp_dir = tempfile.mkdtemp()
    print(f"测试目录: {temp_dir}")
    
    try:
        # 创建不同的数据（确保不重复）
        data_csv = pd.DataFrame({
            'field1': [1, 2, 3],
            'field2': ['X', 'Y', 'Z']
        })
        
        data_xlsx = pd.DataFrame({
            'field1': [4, 5, 6],
            'field2': ['A', 'B', 'C']
        })
        
        # 创建文件
        csv_file = os.path.join(temp_dir, 'unique.csv')
        xlsx_file = os.path.join(temp_dir, 'unique.xlsx')
        
        data_csv.to_csv(csv_file, index=False)
        data_xlsx.to_excel(xlsx_file, index=False)
        
        print(f"\n📁 创建的文件:")
        print(f"  unique.csv: {len(data_csv)} 行 (CSV文件)")
        print(f"  unique.xlsx: {len(data_xlsx)} 行 (Excel文件)")
        print(f"  注意: 两个文件内容完全不同，不应该有重复")
        
        # 导入合并器
        from excel_data_merger import IntegratedDataMerger
        
        # 创建合并器
        merger = IntegratedDataMerger()
        
        # 执行合并
        output_file = os.path.join(temp_dir, 'result.xlsx')
        result = merger.smart_merge_directory(
            input_path=temp_dir,
            output_path=output_file,
            file_pattern="*",
            recursive=False
        )
        
        # 分析结果
        stats = merger.merge_stats
        
        print(f"\n📊 合并结果:")
        print(f"总数据源: {stats['total_sources']}")
        print(f"成功合并: {stats['merged_sources']}")
        print(f"重复数量: {stats['duplicate_sources']}")
        print(f"跳过数量: {stats['skipped_sources']}")
        print(f"合并行数: {stats['merged_rows']}")
        print(f"实际行数: {len(result)}")
        
        # 显示详细信息
        if stats['merged_details']:
            print(f"\n✅ 成功合并的文件:")
            for detail in stats['merged_details']:
                print(f"   - {detail['file_name']}: {detail['rows']} 行")
        
        if stats['duplicate_details']:
            print(f"\n🔄 重复检测详情:")
            for detail in stats['duplicate_details']:
                print(f"   - {detail['source']} → 重复于 {detail['duplicate_of']}")
        
        if stats['skipped_details']:
            print(f"\n⚠️ 跳过详情:")
            for detail in stats['skipped_details']:
                print(f"   - {detail['source']}: {detail['reason']}")
        
        # 验证结果
        print(f"\n🔍 预期结果:")
        print(f"  - 两个文件内容不同，都应该被合并")
        print(f"  - 预期成功合并: 2")
        print(f"  - 预期重复数量: 0")
        print(f"  - 预期跳过数量: 0")
        
        expected_merged = 2
        expected_duplicates = 0
        expected_skipped = 0
        
        is_correct = (
            stats['merged_sources'] == expected_merged and
            stats['duplicate_sources'] == expected_duplicates and
            stats['skipped_sources'] == expected_skipped
        )
        
        print(f"\n📋 结果评估:")
        if is_correct:
            print("✅ CSV和Excel文件都被正确处理！")
        else:
            print("❌ CSV文件可能被错误跳过！")
            print(f"   实际成功合并: {stats['merged_sources']} (预期: {expected_merged})")
            print(f"   实际重复数量: {stats['duplicate_sources']} (预期: {expected_duplicates})")
            print(f"   实际跳过数量: {stats['skipped_sources']} (预期: {expected_skipped})")
            
            if stats['duplicate_sources'] > 0:
                print("   ⚠️ 发现了不应该存在的重复！")
            if stats['skipped_sources'] > 0:
                print("   ⚠️ 有文件被跳过！")
        
        return is_correct
        
    except Exception as e:
        print(f"❌ 测试异常: {str(e)}")
        import traceback
        traceback.print_exc()
        return False
        
    finally:
        # 清理
        try:
            shutil.rmtree(temp_dir)
            print(f"\n🧹 清理测试目录")
        except:
            pass

def main():
    """主函数"""
    print("🔧 调试重复检测问题")
    print("=" * 80)
    
    # 测试1: 混合文件类型的重复检测
    result1 = test_mixed_file_types()
    
    # 测试2: CSV文件在有xlsx文件时是否被跳过
    result2 = test_csv_skipped_with_xlsx()
    
    print(f"\n📊 测试总结:")
    print("=" * 80)
    print(f"混合文件类型重复检测: {'✅ 正常' if result1 else '❌ 有问题'}")
    print(f"CSV文件处理: {'✅ 正常' if result2 else '❌ 被错误跳过'}")
    
    if not result1 or not result2:
        print(f"\n💡 发现的问题:")
        if not result1:
            print(f"   - 混合文件类型的重复检测逻辑有问题")
        if not result2:
            print(f"   - CSV文件在有Excel文件时被错误跳过")
            print(f"   - 这可能是您遇到的主要问题！")

if __name__ == "__main__":
    main()
