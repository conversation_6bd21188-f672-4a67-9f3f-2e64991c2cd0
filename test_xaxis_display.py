#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试横坐标显示优化
验证每个子图都显示横坐标，且字体大小适中
"""

import os

def test_xaxis_display():
    """测试横坐标显示优化"""
    print("📊 测试横坐标显示优化")
    print("=" * 50)
    
    # Excel文件路径
    excel_file = r'C:\Users\<USER>\Desktop\停车分析\义乌火车站道闸_私家车_合并_20250617_083509_analysis_20250618_211554.xlsx'
    
    if not os.path.exists(excel_file):
        print(f"❌ Excel文件不存在: {excel_file}")
        return False
    
    try:
        # 导入图表生成器
        from parking_chart_generator import ParkingChartGenerator
        
        # 创建图表生成器
        print("📊 创建图表生成器...")
        chart_generator = ParkingChartGenerator(excel_file, None)
        
        # 检查是否有进出量时间分布工作表
        if '进出量时间分布' not in chart_generator.excel_data:
            print("❌ 未找到'进出量时间分布'工作表")
            return False
        
        # 尝试生成图表
        print(f"\n🚗 生成带横坐标的紧凑车辆类型图表...")
        result = chart_generator.generate_vehicle_type_traffic_charts()
        
        if result:
            print(f"✅ 成功生成: {os.path.basename(result)}")
            
            # 检查文件是否存在
            if os.path.exists(result):
                file_size = os.path.getsize(result) / 1024
                print(f"📄 文件大小: {file_size:.1f}KB")
                
                # 读取HTML内容检查横坐标配置
                with open(result, 'r', encoding='utf-8') as f:
                    html_content = f.read()
                
                # 检查横坐标相关配置
                xaxis_checks = {
                    '横坐标标签显示': 'is_show:true' in html_content or 'is_show":true' in html_content,
                    '小字体设置': 'font_size:8' in html_content or 'font_size":8' in html_content,
                    '标签旋转': 'rotate:45' in html_content or 'rotate":45' in html_content,
                    '多个X轴配置': html_content.count('xaxis_opts') > 1,  # 多个子图应该有多个X轴配置
                }
                
                print(f"\n📋 横坐标配置验证:")
                all_checks_passed = True
                for check_name, is_passed in xaxis_checks.items():
                    status = "✅" if is_passed else "❌"
                    print(f"   {status} {check_name}: {'通过' if is_passed else '未通过'}")
                    if not is_passed:
                        all_checks_passed = False
                
                # 检查子图数量
                chart_count = html_content.count('Bar(')
                print(f"\n📊 子图信息:")
                print(f"   检测到子图数量: {chart_count}")
                
                if chart_count > 1:
                    print(f"   每个子图都应显示横坐标标签")
                    print(f"   横坐标字体大小: 8px（较小但清晰）")
                    print(f"   标签旋转角度: 45度（避免重叠）")
                
                if all_checks_passed:
                    print("\n✅ 横坐标显示优化成功！")
                    print("   - 每个子图都显示横坐标标签")
                    print("   - 横坐标字体缩小到8px")
                    print("   - 保持45度旋转角度")
                    print("   - 便于每个子图独立查看时间信息")
                    return True
                else:
                    print("\n⚠️ 横坐标配置可能有问题")
                    return False
            else:
                print(f"❌ 文件未生成: {result}")
                return False
        else:
            print("❌ 未能生成车辆类型图表")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def demo_xaxis_improvements():
    """演示横坐标改进"""
    print("\n📊 横坐标显示改进")
    print("=" * 50)
    
    print("🔄 改进对比:")
    print("   修改前:")
    print("     ❌ 只有最后一个子图显示横坐标标签")
    print("     ❌ 其他子图无法独立查看时间信息")
    print("     ❌ 需要滚动到底部才能看到时间轴")
    
    print("\n   修改后:")
    print("     ✅ 每个子图都显示横坐标标签")
    print("     ✅ 每个子图可独立查看时间信息")
    print("     ✅ 横坐标字体缩小到8px，节省空间")
    print("     ✅ 保持45度旋转，避免标签重叠")
    
    print("\n🎯 用户体验提升:")
    print("   ✅ 独立性：每个子图都是完整的图表")
    print("   ✅ 便利性：无需滚动即可查看时间信息")
    print("   ✅ 清晰性：小字体但仍保持可读性")
    print("   ✅ 一致性：所有子图使用统一的显示规则")
    
    print("\n📏 字体大小对比:")
    print("   - 原标准字体: 12px")
    print("   - 紧凑布局字体: 10px")
    print("   - 横坐标字体: 8px（进一步优化）")
    print("   - 仍保持良好的可读性")

def main():
    """主函数"""
    # 演示改进内容
    demo_xaxis_improvements()
    
    # 测试功能
    success = test_xaxis_display()
    
    if success:
        print("\n🎉 横坐标显示优化成功！")
        print("📁 文件名: 进出量时间分布_车型.html")
        print("💡 现在每个子图都显示横坐标，便于独立查看")
        print("🔍 建议在浏览器中查看，验证横坐标显示效果")
        
        print("\n📋 查看要点:")
        print("   1. 每个子图底部都有时间段标签")
        print("   2. 横坐标字体较小但清晰可读")
        print("   3. 标签45度旋转避免重叠")
        print("   4. 总体布局仍保持紧凑")
    else:
        print("\n⚠️ 测试失败")
        print("💡 请检查:")
        print("   1. Excel文件是否存在且格式正确")
        print("   2. 是否有足够的车辆类型数据")
        print("   3. 代码是否正确执行")
    
    print("\n" + "="*50)
    input("按回车键退出...")

if __name__ == "__main__":
    main()
