#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单测试车辆类型图表生成
"""

import os
import sys

def test_vehicle_charts():
    """测试车辆类型图表生成"""
    print("🚗 测试车辆类型图表生成")
    print("=" * 50)
    
    # Excel文件路径
    excel_file = r'C:\Users\<USER>\Desktop\停车分析\义乌火车站道闸_私家车_合并_20250617_083509_analysis_20250618_211554.xlsx'
    
    if not os.path.exists(excel_file):
        print(f"❌ Excel文件不存在: {excel_file}")
        return False
    
    try:
        # 导入图表生成器
        from parking_chart_generator import ParkingChartGenerator
        
        # 创建图表生成器
        print("📊 创建图表生成器...")
        chart_generator = ParkingChartGenerator(excel_file, None)
        
        # 列出可用的工作表
        print("\n📋 可用的工作表:")
        chart_generator.list_available_sheets()
        
        # 尝试生成车辆类型图表
        print("\n🚗 尝试生成车辆类型图表...")
        result = chart_generator.generate_vehicle_type_traffic_charts()
        
        if result:
            print(f"✅ 成功生成: {os.path.basename(result)}")
            return True
        else:
            print("❌ 未能生成车辆类型图表")
            print("💡 可能原因:")
            print("   1. 数据中没有车辆类型相关的列")
            print("   2. 列名格式不符合识别模式")
            print("   3. 数据为空或格式错误")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_vehicle_charts()
    if success:
        print("\n🎉 测试成功！")
    else:
        print("\n⚠️ 测试失败，请检查数据格式")
    
    input("\n按回车键退出...")
