#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试饼图5%阈值处理功能
验证占比小于5%的出入口是否正确合并到"其他出入口"
"""

import os

def test_pie_5percent_threshold():
    """测试饼图5%阈值处理功能"""
    print("🥧 测试饼图5%阈值处理功能")
    print("=" * 50)
    
    # Excel文件路径
    excel_file = r'C:\Users\<USER>\Desktop\停车分析\义乌火车站道闸_私家车_合并_20250617_083509_analysis_20250618_211554.xlsx'
    
    if not os.path.exists(excel_file):
        print(f"❌ Excel文件不存在: {excel_file}")
        return False
    
    try:
        # 导入图表生成器
        from parking_chart_generator import ParkingChartGenerator
        
        # 创建图表生成器
        print("📊 创建图表生成器...")
        chart_generator = ParkingChartGenerator(excel_file, None)
        
        # 检查是否有进出量时间分布(按道闸)工作表
        target_sheet = '进出量时间分布(按道闸)'
        if target_sheet not in chart_generator.excel_data:
            print(f"❌ 未找到'{target_sheet}'工作表")
            available_sheets = list(chart_generator.excel_data.keys())
            print(f"可用工作表: {available_sheets}")
            return False
        
        # 获取数据并分析
        data = chart_generator.excel_data[target_sheet]
        columns = list(data.columns)
        total_cols = len(columns)
        
        print(f"📋 数据结构分析:")
        print(f"   总列数: {total_cols}")
        
        if total_cols > 1:
            gate_cols_count = total_cols - 1
            estimated_gates = gate_cols_count // 3
            print(f"   预估出入口数量: {estimated_gates}")
            
            # 分析几个时间段的数据，预测5%阈值效果
            print(f"\n📊 5%阈值处理预测:")
            for row_idx in range(min(3, len(data))):  # 分析前3个时间段
                time_period = data.iloc[row_idx, 0]
                total_entry = 0
                gate_values = []
                
                for i in range(estimated_gates):
                    entry_col_idx = 1 + i * 3
                    if entry_col_idx < total_cols:
                        value = data.iloc[row_idx, entry_col_idx]
                        if pd.notna(value):
                            gate_values.append(value)
                            total_entry += value
                
                if total_entry > 0:
                    threshold_value = total_entry * 0.05
                    main_gates = sum(1 for v in gate_values if v >= threshold_value)
                    other_gates = len(gate_values) - main_gates
                    
                    print(f"   时间段 {time_period}:")
                    print(f"     总进场量: {total_entry}")
                    print(f"     5%阈值: {threshold_value:.1f}")
                    print(f"     主要出入口: {main_gates}个")
                    print(f"     合并到'其他': {other_gates}个")
        
        # 尝试生成带5%阈值处理的饼图
        print(f"\n🥧 生成带5%阈值处理的进场占比饼图...")
        result = chart_generator.generate_gate_entry_proportion_timeline()
        
        if result:
            print(f"✅ 成功生成: {os.path.basename(result)}")
            
            # 检查文件是否存在
            if os.path.exists(result):
                file_size = os.path.getsize(result) / 1024
                print(f"📄 文件大小: {file_size:.1f}KB")
                
                # 读取HTML内容检查5%阈值处理特征
                with open(result, 'r', encoding='utf-8') as f:
                    html_content = f.read()
                
                # 检查5%阈值处理相关特征
                threshold_checks = {
                    '其他出入口项': '其他出入口' in html_content,
                    '饼图组件': 'Pie(' in html_content,
                    'Timeline组件': 'Timeline' in html_content,
                    '玫瑰图样式': 'rosetype:"radius"' in html_content or 'rosetype":"radius"' in html_content,
                    '专业颜色': '#2E86AB' in html_content and '#A23B72' in html_content,
                    '灰色配置': '#95A5A6' in html_content,  # "其他出入口"的灰色
                }
                
                print(f"\n📊 5%阈值处理验证:")
                all_checks_passed = True
                for check_name, is_passed in threshold_checks.items():
                    status = "✅" if is_passed else "❌"
                    print(f"   {status} {check_name}: {'通过' if is_passed else '未通过'}")
                    if not is_passed:
                        all_checks_passed = False
                
                # 检查"其他出入口"的出现次数
                other_count = html_content.count('其他出入口')
                print(f"\n🔍 阈值处理效果:")
                print(f"   '其他出入口'出现次数: {other_count}")
                if other_count > 0:
                    print(f"   ✅ 成功应用5%阈值处理")
                    print(f"   ✅ 小占比出入口已合并到'其他出入口'")
                    print(f"   ✅ 饼图显示重点更加突出")
                else:
                    print(f"   ℹ️ 可能所有出入口占比都>=5%，无需合并")
                
                if all_checks_passed:
                    print("\n✅ 5%阈值处理饼图生成成功！")
                    print("   - 占比<5%的出入口自动合并")
                    print("   - '其他出入口'使用灰色显示")
                    print("   - 主要出入口更加突出")
                    print("   - 饼图重点更加清晰")
                    return True
                else:
                    print("\n⚠️ 饼图生成但5%阈值处理可能有问题")
                    return False
            else:
                print(f"❌ 文件未生成: {result}")
                return False
        else:
            print("❌ 未能生成饼图")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def demo_5percent_threshold_benefits():
    """演示5%阈值处理的优势"""
    print("\n📊 5%阈值处理优势")
    print("=" * 50)
    
    print("🎯 处理目标:")
    print("   - 突出主要出入口，弱化次要出入口")
    print("   - 简化饼图显示，提升可读性")
    print("   - 避免过多小扇区造成的视觉混乱")
    print("   - 保持数据完整性，不丢失信息")
    
    print("\n🔄 处理逻辑:")
    print("   1. 计算每个出入口的占比")
    print("   2. 识别占比<5%的出入口")
    print("   3. 将小占比出入口合并到'其他出入口'")
    print("   4. 保留占比>=5%的主要出入口")
    
    print("\n📊 视觉效果对比:")
    print("   处理前:")
    print("     ❌ 可能有很多小扇区")
    print("     ❌ 主要出入口不够突出")
    print("     ❌ 图例项目过多，难以阅读")
    print("     ❌ 视觉焦点分散")
    
    print("\n   处理后:")
    print("     ✅ 主要出入口清晰突出")
    print("     ✅ 小占比合并为'其他出入口'")
    print("     ✅ 图例简洁，重点明确")
    print("     ✅ 视觉焦点集中")
    
    print("\n🎨 颜色设计:")
    print("   - 主要出入口: 使用专业演讲风格颜色")
    print("   - 其他出入口: 使用中性灰色 (#95A5A6)")
    print("   - 视觉层次: 主要信息突出，次要信息弱化")
    
    print("\n💡 应用价值:")
    print("   - 管理决策: 快速识别关键出入口")
    print("   - 资源配置: 重点关注主要出入口")
    print("   - 演示效果: 图表更加清晰专业")
    print("   - 数据分析: 突出重点，简化分析")

def main():
    """主函数"""
    # 导入pandas用于数据分析
    try:
        import pandas as pd
        globals()['pd'] = pd
    except ImportError:
        print("❌ 需要安装pandas: pip install pandas")
        return
    
    # 演示5%阈值处理的优势
    demo_5percent_threshold_benefits()
    
    # 测试功能
    success = test_pie_5percent_threshold()
    
    if success:
        print("\n🎉 测试成功！5%阈值处理饼图已生成！")
        print("📁 文件名: 出入口占比_进.html")
        print("💡 现在饼图会自动处理小占比出入口")
        print("🔍 建议在浏览器中验证效果:")
        print("   1. 观察是否有'其他出入口'项")
        print("   2. 检查主要出入口是否更加突出")
        print("   3. 验证图例是否更加简洁")
        print("   4. 对比不同时间段的处理效果")
        
        print("\n📋 处理规则:")
        print("   - 占比>=5%: 单独显示，使用彩色")
        print("   - 占比<5%: 合并到'其他出入口'，使用灰色")
        print("   - 阈值: 5%（可根据需要调整）")
    else:
        print("\n⚠️ 测试失败")
        print("💡 可能的原因:")
        print("   1. Excel文件中没有'进出量时间分布(按道闸)'工作表")
        print("   2. 数据格式不正确")
        print("   3. 代码执行过程中出现错误")
    
    print("\n" + "="*50)
    input("按回车键退出...")

if __name__ == "__main__":
    main()
