#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试完整图表集成
"""

import pandas as pd
import numpy as np
import os
from parking_report_generator import ReportGenerator

def test_full_chart_integration():
    """测试完整图表集成"""
    print("=== 测试完整图表集成 ===\n")
    
    # 创建更丰富的测试数据
    np.random.seed(42)
    
    # 生成多样化的停车时长数据
    durations = []
    vehicle_types = []
    
    # 短时停车
    short_durations = np.random.exponential(0.8, 30)
    short_durations = np.clip(short_durations, 0.1, 2.0)
    durations.extend(short_durations)
    vehicle_types.extend(['小型车'] * len(short_durations))
    
    # 中时停车
    medium_durations = np.random.normal(4.5, 1.2, 20)
    medium_durations = np.clip(medium_durations, 2.0, 8.0)
    durations.extend(medium_durations)
    vehicle_types.extend(['中型车'] * len(medium_durations))
    
    # 长时停车
    long_durations = np.random.lognormal(2.5, 0.8, 15)
    long_durations = np.clip(long_durations, 8.0, 72.0)
    durations.extend(long_durations)
    vehicle_types.extend(['大型车'] * len(long_durations))
    
    # 创建完整数据
    n_records = len(durations)
    base_time = pd.Timestamp('2024-01-01 08:00:00')
    
    entry_times = [base_time + pd.Timedelta(hours=i*0.5) for i in range(n_records)]
    exit_times = [entry_times[i] + pd.Timedelta(hours=durations[i]) for i in range(n_records)]
    
    test_data = pd.DataFrame({
        'entry_time': entry_times,
        'exit_time': exit_times,
        'duration': durations,
        'vtype': vehicle_types,
        'vehicle_id': [f'车{i+1:04d}' for i in range(n_records)],
        'entry_gate': np.random.choice(['A口', 'B口', 'C口'], n_records),
        'exit_gate': np.random.choice(['A口', 'B口', 'C口'], n_records)
    })
    
    print(f"创建测试数据: {len(test_data)} 条记录")
    print(f"停车时长范围: {test_data['duration'].min():.2f} - {test_data['duration'].max():.2f} 小时")
    print(f"车辆类型分布: {test_data['vtype'].value_counts().to_dict()}")
    print()
    
    # 配置参数
    params = {
        'mode': 'mode2',
        '聚焦日期': '2024-01-01',
        '聚焦月份': '2024-01'
    }
    
    # 创建分析结果（模拟）
    analysis_results = {
        'peak_flow': pd.DataFrame({
            '时段': ['08:00-09:00', '09:00-10:00', '10:00-11:00'],
            '进场': [10, 15, 8],
            '出场': [5, 12, 10],
            '总量': [15, 27, 18]
        }),
        'daily_stats': pd.DataFrame({
            '日期': ['2024-01-01'],
            '进场': [50],
            '出场': [48],
            '总量': [98]
        })
    }
    
    # 创建报告生成器
    generator = ReportGenerator(test_data, analysis_results, params)
    generator.processed_data = test_data
    
    # 测试完整报告生成
    print("1. 生成完整报告:")
    try:
        output_path = os.path.join(os.getcwd(), "完整报告_图表集成测试.xlsx")
        
        # 删除已存在的文件
        if os.path.exists(output_path):
            os.remove(output_path)
        
        result_path = generator.export_to_excel(output_path)
        
        if result_path and os.path.exists(result_path):
            print(f"   ✅ 报告生成成功: {result_path}")
            
            # 验证工作表
            with pd.ExcelFile(result_path) as xls:
                sheet_names = xls.sheet_names
                
            print(f"   ✅ 包含 {len(sheet_names)} 个工作表:")
            for i, sheet in enumerate(sheet_names, 1):
                print(f"      {i:2d}. {sheet}")
            
            # 检查关键工作表
            required_sheets = ['延停时长概率密度', '图_分析日']
            missing_sheets = [sheet for sheet in required_sheets if sheet not in sheet_names]
            
            if missing_sheets:
                print(f"   ❌ 缺少工作表: {missing_sheets}")
            else:
                print(f"   ✅ 所有关键工作表都存在")
                
                # 验证延停时长概率密度数据
                density_df = pd.read_excel(result_path, sheet_name='延停时长概率密度')
                print(f"   ✅ 延停时长概率密度数据: {len(density_df)} 行 x {len(density_df.columns)} 列")
                
                if not density_df.empty:
                    total_records = density_df['频数'].sum()
                    print(f"   ✅ 总记录数验证: {total_records} (原始: {len(test_data)})")
                    
                    # 显示前几行数据
                    print(f"   ✅ 前3行数据:")
                    for i in range(min(3, len(density_df))):
                        row = density_df.iloc[i]
                        print(f"      {row['时长区间']:20} | 频数: {row['频数']:3d} | 百分比: {row['百分比(%)']:6.2f}%")
        else:
            print("   ❌ 报告生成失败")
            
    except Exception as e:
        print(f"   ❌ 报告生成异常: {str(e)}")
        import traceback
        traceback.print_exc()
    
    print("\n" + "="*60 + "\n")
    
    # 测试单独的图_分析日工作表创建
    print("2. 测试图_分析日工作表创建:")
    try:
        import xlsxwriter
        
        test_chart_file = "图表工作表测试.xlsx"
        if os.path.exists(test_chart_file):
            os.remove(test_chart_file)
        
        with pd.ExcelWriter(test_chart_file, engine='xlsxwriter') as writer:
            workbook = writer.book
            
            # 先创建必要的数据工作表
            generator._export_duration_probability_density(writer, workbook)
            
            # 创建一些其他必要的工作表（模拟）
            peak_flow_data = analysis_results['peak_flow']
            peak_flow_data.to_excel(writer, sheet_name='进出量时间分布_分析日', index=False)
            
            daily_stats_data = analysis_results['daily_stats']
            daily_stats_data.to_excel(writer, sheet_name='日进出量_分析周期', index=False)
            
            # 创建图_分析日工作表
            generator._create_charts_sheet(writer, workbook)
        
        if os.path.exists(test_chart_file):
            print(f"   ✅ 图表工作表文件生成成功: {test_chart_file}")
            
            # 验证内容
            with pd.ExcelFile(test_chart_file) as xls:
                chart_sheets = xls.sheet_names
                
            print(f"   ✅ 包含工作表: {chart_sheets}")
            
            if '图_分析日' in chart_sheets:
                print(f"   ✅ 图_分析日工作表创建成功")
            else:
                print(f"   ❌ 图_分析日工作表创建失败")
        else:
            print("   ❌ 图表工作表文件生成失败")
            
    except Exception as e:
        print(f"   ❌ 图表工作表测试异常: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_full_chart_integration()
    
    print("="*60)
    print("测试总结:")
    print("1. 完整报告生成测试")
    print("2. 图_分析日工作表单独测试")
    print("如果测试通过，说明延停时长概率密度图表已成功集成到报告系统中！")
