#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修正后的出入口流量占比sheet功能
验证是否正确显示同一时段、车辆类型和道闸的进出统计量
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta

def test_corrected_gate_flow_distribution():
    """测试修正后的出入口流量占比sheet功能"""
    print("=" * 80)
    print("测试修正后的出入口流量占比sheet功能")
    print("验证是否正确显示同一时段、车辆类型和道闸的进出统计量")
    print("=" * 80)
    
    try:
        # 1. 创建测试数据
        print("\n📋 创建测试数据...")
        
        # 模拟数据 - 确保有明确的进出场关系
        test_data = pd.DataFrame({
            'entry_time': [
                datetime(2024, 6, 1, 8, 30),   # 8:30进场
                datetime(2024, 6, 1, 9, 15),   # 9:15进场
                datetime(2024, 6, 1, 8, 45),   # 8:45进场
                datetime(2024, 6, 1, 9, 30),   # 9:30进场
                datetime(2024, 6, 1, 8, 20),   # 8:20进场
            ],
            'exit_time': [
                datetime(2024, 6, 1, 10, 30),  # 10:30出场
                datetime(2024, 6, 1, 11, 15),  # 11:15出场
                datetime(2024, 6, 1, 10, 45),  # 10:45出场
                datetime(2024, 6, 1, 11, 30),  # 11:30出场
                datetime(2024, 6, 1, 10, 20),  # 10:20出场
            ],
            'vtype': ['小型车', '大型车', '小型车', '大型车', '小型车'],
            'entry_gate': ['入口A', '入口B', '入口A', '入口B', '入口A'],
            'exit_gate': ['出口A', '出口B', '出口A', '出口B', '出口A'],
            'duration': [2.0, 2.0, 2.0, 2.0, 2.0]
        })
        
        print(f"   测试数据: {len(test_data)} 条记录")
        print(f"   车辆类型: {test_data['vtype'].unique().tolist()}")
        print(f"   进场道闸: {test_data['entry_gate'].unique().tolist()}")
        print(f"   出场道闸: {test_data['exit_gate'].unique().tolist()}")
        
        # 显示详细数据
        print(f"\n   详细数据:")
        for i, row in test_data.iterrows():
            print(f"     车辆{i+1}: {row['vtype']}, {row['entry_gate']} -> {row['exit_gate']}")
            print(f"       进场: {row['entry_time'].strftime('%H:%M')}, 出场: {row['exit_time'].strftime('%H:%M')}")
        
        # 2. 模拟ReportGenerator的参数
        params = {
            'mode': 'mode2',
            'time_interval': 60,  # 60分钟间隔
            'time_slip': 15,      # 15分钟滑动
            '进场道闸字段': 'entry_gate',
            '出场道闸字段': 'exit_gate'
        }
        
        # 3. 直接测试_calculate_gate_flow_distribution方法
        print(f"\n🔍 测试_calculate_gate_flow_distribution方法...")
        
        # 模拟ReportGenerator类的必要方法
        class MockReportGenerator:
            def __init__(self, data, params):
                self.data = data
                self.params = params
            
            def _log_warning(self, message):
                print(f"   警告: {message}")
            
            def _log_error(self, message):
                print(f"   错误: {message}")
            
            def _generate_time_periods(self, data):
                """生成时间段"""
                # 简化的时间段生成，基于数据的时间范围
                start_hour = 8
                end_hour = 12
                periods = []
                for hour in range(start_hour, end_hour):
                    periods.append(f"{hour:02d}:00-{hour+1:02d}:00")
                return periods
            
            def _preprocess_gate_data(self, data, entry_gate_field, exit_gate_field, vtype_field):
                """预处理道闸数据"""
                data = data.copy()
                data['entry_time'] = pd.to_datetime(data['entry_time'], errors='coerce')
                data['exit_time'] = pd.to_datetime(data['exit_time'], errors='coerce')
                data = data[~data['entry_time'].isna() & ~data['exit_time'].isna()]
                return data
            
            def _get_period_data(self, data, start_dt, end_dt):
                """获取指定时间段的进出场数据"""
                entry_mask = (
                    (data['entry_time'].dt.time >= start_dt) & 
                    (data['entry_time'].dt.time < end_dt)
                )
                exit_mask = (
                    (data['exit_time'].dt.time >= start_dt) & 
                    (data['exit_time'].dt.time < end_dt)
                )
                return data[entry_mask], data[exit_mask]
            
            def _calculate_gate_flow_distribution(self, data=None):
                """计算出入口流量占比统计（修正后的版本）"""
                try:
                    data = data if data is not None else self.data
                    if data.empty:
                        self._log_warning("输入数据为空")
                        return pd.DataFrame(columns=['时间段', '车辆类型', '出入口', '进场数量', '出场数量'])
                    
                    # 使用标准化的字段名
                    entry_gate_field = 'entry_gate'
                    exit_gate_field = 'exit_gate'
                    vtype_field = 'vtype'
                    
                    # 预处理数据
                    data = self._preprocess_gate_data(data, entry_gate_field, exit_gate_field, vtype_field)
                    if data.empty:
                        return pd.DataFrame(columns=['时间段', '车辆类型', '出入口', '进场数量', '出场数量'])
                    
                    # 生成时间段
                    time_periods = self._generate_time_periods(data)
                    
                    # 统计各时间段各道闸的进出场数量
                    results = []
                    for period in time_periods:
                        start_time, end_time = period.split('-')
                        start_dt = datetime.strptime(start_time, '%H:%M').time()
                        end_dt = datetime.strptime(end_time, '%H:%M').time()
                        
                        # 获取当前时间段的进出场数据
                        entry_data, exit_data = self._get_period_data(data, start_dt, end_dt)
                        
                        # 获取进场道闸
                        entry_gates = sorted(data[entry_gate_field].dropna().unique())
                        
                        # 统计进场道闸的进场数量
                        for gate in entry_gates:
                            # 按车辆类型统计
                            if vtype_field in data.columns:
                                vtypes = sorted(data[vtype_field].dropna().unique())
                                for vtype in vtypes:
                                    # 统计进场数量（该道闸在该时间段的进场）
                                    entry_count = len(entry_data[
                                        (entry_data[entry_gate_field] == gate) & 
                                        (entry_data[vtype_field] == vtype)
                                    ])
                                    
                                    # 统计出场数量（该时间段内，从该进场道闸进入后又出场的车辆）
                                    exit_count = len(exit_data[
                                        (exit_data[entry_gate_field] == gate) & 
                                        (exit_data[vtype_field] == vtype)
                                    ])
                                    
                                    results.append({
                                        '时间段': period,
                                        '车辆类型': vtype,
                                        '出入口': gate,
                                        '进场数量': entry_count,
                                        '出场数量': exit_count
                                    })
                            else:
                                # 没有车辆类型字段时只统计总量
                                entry_count = len(entry_data[entry_data[entry_gate_field] == gate])
                                exit_count = len(exit_data[exit_data[entry_gate_field] == gate])
                                
                                results.append({
                                    '时间段': period,
                                    '车辆类型': '所有车辆',
                                    '出入口': gate,
                                    '进场数量': entry_count,
                                    '出场数量': exit_count
                                })
                    
                    if not results:
                        self._log_warning("未生成任何统计结果")
                        return pd.DataFrame(columns=['时间段', '车辆类型', '出入口', '进场数量', '出场数量'])
                        
                    return pd.DataFrame(results)
                    
                except Exception as e:
                    self._log_error(f"计算出入口流量分布失败: {str(e)}")
                    return pd.DataFrame(columns=['时间段', '车辆类型', '出入口', '进场数量', '出场数量'])
        
        # 4. 执行测试
        mock_generator = MockReportGenerator(test_data, params)
        flow_distribution = mock_generator._calculate_gate_flow_distribution()
        
        if not flow_distribution.empty:
            print(f"   ✅ 成功生成出入口流量占比数据")
            print(f"   - 数据行数: {len(flow_distribution)}")
            print(f"   - 数据列数: {len(flow_distribution.columns)}")
            print(f"   - 列名: {flow_distribution.columns.tolist()}")
            
            # 5. 详细检查数据内容
            print(f"\n📊 详细检查数据内容...")
            
            print(f"   完整数据:")
            for i, row in flow_distribution.iterrows():
                print(f"     {row['时间段']} | {row['车辆类型']} | {row['出入口']} | 进场:{row['进场数量']} | 出场:{row['出场数量']}")
            
            # 6. 验证逻辑正确性
            print(f"\n🔍 验证逻辑正确性...")
            
            # 检查8:00-9:00时间段
            period_8_9 = flow_distribution[flow_distribution['时间段'] == '08:00-09:00']
            print(f"\n   8:00-9:00时间段分析:")
            print(f"     原始数据中8:00-9:00进场的车辆:")
            entry_8_9 = test_data[
                (test_data['entry_time'].dt.hour >= 8) & 
                (test_data['entry_time'].dt.hour < 9)
            ]
            for i, row in entry_8_9.iterrows():
                print(f"       {row['vtype']} 从 {row['entry_gate']} 在 {row['entry_time'].strftime('%H:%M')} 进场")
            
            print(f"     统计结果:")
            for i, row in period_8_9.iterrows():
                print(f"       {row['车辆类型']} | {row['出入口']} | 进场:{row['进场数量']} | 出场:{row['出场数量']}")
            
            # 检查10:00-11:00时间段
            period_10_11 = flow_distribution[flow_distribution['时间段'] == '10:00-11:00']
            print(f"\n   10:00-11:00时间段分析:")
            print(f"     原始数据中10:00-11:00出场的车辆:")
            exit_10_11 = test_data[
                (test_data['exit_time'].dt.hour >= 10) & 
                (test_data['exit_time'].dt.hour < 11)
            ]
            for i, row in exit_10_11.iterrows():
                print(f"       {row['vtype']} 从 {row['entry_gate']} 进入，在 {row['exit_time'].strftime('%H:%M')} 出场")
            
            print(f"     统计结果:")
            for i, row in period_10_11.iterrows():
                print(f"       {row['车辆类型']} | {row['出入口']} | 进场:{row['进场数量']} | 出场:{row['出场数量']}")
            
            # 7. 验证修正效果
            print(f"\n{'='*60}")
            print("修正效果验证")
            print('='*60)
            
            # 检查是否正确统计了同一道闸的进出场
            correct_logic = True
            issues = []
            
            # 验证进场统计
            entry_8_9_A_small = len(entry_8_9[
                (entry_8_9['entry_gate'] == '入口A') & 
                (entry_8_9['vtype'] == '小型车')
            ])
            
            stat_8_9_A_small = period_8_9[
                (period_8_9['出入口'] == '入口A') & 
                (period_8_9['车辆类型'] == '小型车')
            ]
            
            if not stat_8_9_A_small.empty:
                stat_entry_count = stat_8_9_A_small.iloc[0]['进场数量']
                if stat_entry_count == entry_8_9_A_small:
                    print(f"✅ 进场统计正确: 入口A小型车 8:00-9:00 进场 {stat_entry_count} 辆")
                else:
                    print(f"❌ 进场统计错误: 期望 {entry_8_9_A_small}，实际 {stat_entry_count}")
                    correct_logic = False
                    issues.append("进场统计不正确")
            
            # 验证出场统计逻辑
            exit_10_11_from_A_small = len(exit_10_11[
                (exit_10_11['entry_gate'] == '入口A') & 
                (exit_10_11['vtype'] == '小型车')
            ])
            
            stat_10_11_A_small = period_10_11[
                (period_10_11['出入口'] == '入口A') & 
                (period_10_11['车辆类型'] == '小型车')
            ]
            
            if not stat_10_11_A_small.empty:
                stat_exit_count = stat_10_11_A_small.iloc[0]['出场数量']
                if stat_exit_count == exit_10_11_from_A_small:
                    print(f"✅ 出场统计正确: 从入口A进入的小型车在 10:00-11:00 出场 {stat_exit_count} 辆")
                else:
                    print(f"❌ 出场统计错误: 期望 {exit_10_11_from_A_small}，实际 {stat_exit_count}")
                    correct_logic = False
                    issues.append("出场统计不正确")
            
            if correct_logic:
                print("🎉 出入口流量占比逻辑修正成功！")
            else:
                print("⚠️  出入口流量占比逻辑仍需进一步修正")
                for issue in issues:
                    print(f"   - {issue}")
            
            # 8. 功能说明
            print(f"\n📝 修正后的功能:")
            print("1. ✅ 进场数量: 统计该时间段内从该道闸进入的车辆")
            print("2. ✅ 出场数量: 统计该时间段内从该道闸进入后又出场的车辆")
            print("3. ✅ 同一行显示: 同一道闸的进出场数据在同一行")
            print("4. ✅ 车辆类型分组: 按车辆类型分别统计")
            print("5. ✅ 时间段分组: 按时间段分别统计")
            
            return correct_logic
        else:
            print(f"   ❌ 出入口流量占比数据为空")
            return False
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_corrected_gate_flow_distribution()
    
    print("\n" + "=" * 80)
    print("修正后的出入口流量占比sheet功能测试完成！")
    print("=" * 80)
