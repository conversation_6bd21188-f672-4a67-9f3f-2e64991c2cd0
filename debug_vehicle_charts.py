#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试车辆类型图表生成问题
"""

import os
import sys
import traceback

def debug_vehicle_charts():
    """调试车辆类型图表生成"""
    print("🔍 调试车辆类型图表生成")
    print("=" * 50)
    
    try:
        # 1. 检查文件是否存在
        excel_file = r'C:\Users\<USER>\Desktop\停车分析\义乌火车站道闸_私家车_合并_20250617_083509_analysis_20250618_211554.xlsx'
        print(f"1. 检查Excel文件: {os.path.exists(excel_file)}")
        
        if not os.path.exists(excel_file):
            print("❌ Excel文件不存在")
            return
        
        # 2. 导入模块
        print("2. 导入parking_chart_generator模块...")
        from parking_chart_generator import ParkingChartGenerator
        print("✅ 模块导入成功")
        
        # 3. 创建图表生成器
        print("3. 创建图表生成器...")
        chart_generator = ParkingChartGenerator(excel_file, None)
        print("✅ 图表生成器创建成功")
        
        # 4. 检查工作表
        print("4. 检查可用工作表...")
        available_sheets = list(chart_generator.excel_data.keys())
        print(f"   可用工作表: {available_sheets}")
        
        if '进出量时间分布' not in available_sheets:
            print("❌ 未找到'进出量时间分布'工作表")
            return
        
        # 5. 分析数据结构
        print("5. 分析数据结构...")
        data = chart_generator.excel_data['进出量时间分布']
        columns = list(data.columns)
        total_cols = len(columns)
        
        print(f"   总列数: {total_cols}")
        print(f"   列名: {columns}")
        
        if total_cols < 7:
            print("❌ 列数不足，无法生成车辆类型图表")
            return
        
        # 6. 检查车辆类型数据列
        vehicle_start_col = 4
        vehicle_end_col = total_cols - 3
        vehicle_cols_count = vehicle_end_col - vehicle_start_col
        
        print(f"   车辆类型数据列范围: 第{vehicle_start_col+1}列 到 第{vehicle_end_col}列")
        print(f"   车辆类型列数: {vehicle_cols_count}")
        
        if vehicle_cols_count < 2:
            print("❌ 车辆类型数据列数不足")
            return
        
        vehicle_cols = columns[vehicle_start_col:vehicle_end_col]
        print(f"   车辆类型列: {vehicle_cols}")
        
        # 7. 尝试调用方法
        print("7. 尝试调用generate_vehicle_type_traffic_charts方法...")
        
        # 检查方法是否存在
        if hasattr(chart_generator, 'generate_vehicle_type_traffic_charts'):
            print("✅ 方法存在")
            
            # 调用方法
            result = chart_generator.generate_vehicle_type_traffic_charts()
            
            if result:
                print(f"✅ 方法调用成功，返回: {result}")
                
                # 检查文件是否真的生成了
                if os.path.exists(result):
                    file_size = os.path.getsize(result) / 1024
                    print(f"✅ 文件已生成: {os.path.basename(result)} ({file_size:.1f}KB)")
                else:
                    print(f"❌ 方法返回了路径但文件不存在: {result}")
            else:
                print("❌ 方法调用失败，返回None")
        else:
            print("❌ 方法不存在")
            
    except Exception as e:
        print(f"❌ 发生异常: {str(e)}")
        print("详细错误信息:")
        traceback.print_exc()

if __name__ == "__main__":
    debug_vehicle_charts()
    input("\n按回车键退出...")
