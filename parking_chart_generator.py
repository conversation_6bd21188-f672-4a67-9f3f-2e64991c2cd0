#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
停车数据图表生成器
专门用于读取Excel中的各个sheet数据并生成交互式HTML图表
"""

import pandas as pd
import os
from datetime import datetime
import pyecharts.options as opts
from pyecharts.charts import Bar, Line, Pie, Scatter, Page, Timeline, Grid
from pyecharts.globals import ThemeType
from pyecharts.commons.utils import JsCode

class ParkingChartGenerator:
    """停车数据图表生成器"""
    
    def __init__(self, excel_file_path, output_dir=None):
        """
        初始化图表生成器
        
        Args:
            excel_file_path: str, Excel文件路径
            output_dir: str, 输出目录，默认为Excel文件所在目录
        """
        self.excel_file_path = excel_file_path
        self.output_dir = output_dir or os.path.dirname(excel_file_path)
        self.excel_data = {}
        
        # 图表样式配置
        self.chart_config = {
            'theme': ThemeType.MACARONS,  # 图表主题
            'bar_colors': ['#1f77b4', '#ff7f0e', '#fac858'],  # 柱状图颜色：蓝色(进场)、橙色(出场)、黄色
            'line_colors': ['#ee6666', '#73c0de', '#3ba272'],  # 线条颜色
            'line_width': 3,  # 线条粗细
            'chart_width': '1200px',  # 图表宽度
            'chart_height': '600px',  # 图表高度
            # 专业演讲风格的进出量图表颜色配置
            'traffic_flow_colors': {
                'entry': '#2E86AB',      # 深蓝色 - 进场（稳重、专业）
                'exit': '#A23B72',       # 深紫红色 - 出场（对比鲜明）
                'total': '#F18F01',      # 橙色 - 总流量线（醒目、温暖）
            }
        }
        
        # 加载Excel数据
        self._load_excel_data()
    
    def _load_excel_data(self):
        """加载Excel文件中的所有sheet数据"""
        try:
            # 读取所有sheet
            all_sheets = pd.read_excel(self.excel_file_path, sheet_name=None)
            
            for sheet_name, sheet_data in all_sheets.items():
                if not sheet_data.empty:
                    self.excel_data[sheet_name] = sheet_data
                    print(f"✅ 加载sheet: {sheet_name} ({len(sheet_data)}行)")
            
            print(f"📊 成功加载 {len(self.excel_data)} 个工作表")
            
        except Exception as e:
            print(f"❌ 加载Excel文件失败: {str(e)}")
            raise
    
    def generate_traffic_flow_chart(self, sheet_name='进出量时间分布'):
        """
        生成进出量时间分布图表（柱状图+趋势线）

        Args:
            sheet_name: str, 工作表名称

        Returns:
            str: 生成的HTML文件路径
        """
        if sheet_name not in self.excel_data:
            print(f"❌ 未找到工作表: {sheet_name}")
            return None

        data = self.excel_data[sheet_name]
        print(f"📈 生成进出量时间分布图表...")

        # 获取数据列
        time_col = data.iloc[:, 0].astype(str).tolist()  # 时间段
        entry_col = data.iloc[:, 1].fillna(0).tolist()   # 进场数量
        exit_col = data.iloc[:, 2].fillna(0).tolist()    # 出场数量
        total_col = data.iloc[:, 3].fillna(0).tolist()   # 总流量

        # 计算统一的Y轴范围（确保bar和line使用相同的纵坐标）
        all_values = entry_col + exit_col + total_col
        max_value = max(all_values) if all_values else 100
        y_axis_max = max_value * 1.2  # 留出20%的空间

        # 创建柱状图
        bar = (
            Bar(init_opts=opts.InitOpts(
                theme=self.chart_config['theme'],
                width=self.chart_config['chart_width'],
                height=self.chart_config['chart_height']
            ))
            .add_xaxis(xaxis_data=time_col)
            .add_yaxis(
                series_name="进场数量",
                y_axis=entry_col,
                label_opts=opts.LabelOpts(is_show=False),
                color=self.chart_config['traffic_flow_colors']['entry'],
                itemstyle_opts=opts.ItemStyleOpts(
                    color=self.chart_config['traffic_flow_colors']['entry'],
                    border_color='#ffffff',
                    border_width=1
                ),
            )
            .add_yaxis(
                series_name="出场数量",
                y_axis=exit_col,
                label_opts=opts.LabelOpts(is_show=False),
                color=self.chart_config['traffic_flow_colors']['exit'],
                itemstyle_opts=opts.ItemStyleOpts(
                    color=self.chart_config['traffic_flow_colors']['exit'],
                    border_color='#ffffff',
                    border_width=1
                ),
            )
            .set_global_opts(
                title_opts=opts.TitleOpts(
                    title="停车场进出量时间分布",
                    subtitle="柱状图显示进出场数量，折线图显示总流量趋势",
                    pos_left="center",
                    title_textstyle_opts=opts.TextStyleOpts(
                        font_size=20,
                        font_weight="bold",
                        color="#2c3e50"
                    ),
                    subtitle_textstyle_opts=opts.TextStyleOpts(
                        font_size=14,
                        color="#7f8c8d"
                    )
                ),
                tooltip_opts=opts.TooltipOpts(
                    is_show=True,
                    trigger="axis",
                    axis_pointer_type="cross",
                    formatter="{b}<br/>{a0}: {c0}<br/>{a1}: {c1}<br/>{a2}: {c2}",
                    background_color="rgba(255, 255, 255, 0.95)",  # 白色半透明背景
                    border_color="#cccccc",  # 浅灰色边框
                    border_width=1,
                    textstyle_opts=opts.TextStyleOpts(
                        color="#333333",  # 深灰色文字，确保在白色背景上清晰可见
                        font_size=12,
                        font_weight="normal"
                    )
                ),
                xaxis_opts=opts.AxisOpts(
                    name="时间段",
                    type_="category",
                    axispointer_opts=opts.AxisPointerOpts(is_show=True, type_="shadow"),
                    axislabel_opts=opts.LabelOpts(rotate=45),  # 标签旋转45度
                ),
                yaxis_opts=opts.AxisOpts(
                    name="车辆数量",
                    type_="value",
                    min_=0,
                    max_=y_axis_max,  # 使用统一的最大值
                    axislabel_opts=opts.LabelOpts(formatter="{value}"),
                    axistick_opts=opts.AxisTickOpts(is_show=True),
                    splitline_opts=opts.SplitLineOpts(is_show=True),
                ),
                legend_opts=opts.LegendOpts(
                    pos_top="8%",
                    textstyle_opts=opts.TextStyleOpts(
                        font_size=12,
                        font_weight="bold"
                    ),
                    item_gap=20
                ),
                datazoom_opts=[
                    opts.DataZoomOpts(type_="slider", range_start=0, range_end=100),
                    opts.DataZoomOpts(type_="inside")
                ],
            )
        )

        # 创建折线图（总流量趋势）- 使用相同的Y轴范围
        line = (
            Line()
            .add_xaxis(xaxis_data=time_col)
            .add_yaxis(
                series_name="总流量",
                y_axis=total_col,
                label_opts=opts.LabelOpts(is_show=False),
                linestyle_opts=opts.LineStyleOpts(
                    width=self.chart_config['line_width'] + 1,  # 稍微加粗线条
                    color=self.chart_config['traffic_flow_colors']['total']
                ),
                itemstyle_opts=opts.ItemStyleOpts(
                    color=self.chart_config['traffic_flow_colors']['total'],
                    border_color=self.chart_config['traffic_flow_colors']['total'],
                    border_width=2
                ),
                markpoint_opts=opts.MarkPointOpts(
                    data=[
                        opts.MarkPointItem(
                            type_="max",
                            name="峰值",
                            itemstyle_opts=opts.ItemStyleOpts(
                                color=self.chart_config['traffic_flow_colors']['total']
                            )
                        ),
                        opts.MarkPointItem(
                            type_="min",
                            name="谷值",
                            itemstyle_opts=opts.ItemStyleOpts(
                                color=self.chart_config['traffic_flow_colors']['total']
                            )
                        ),
                    ]
                ),
                markline_opts=opts.MarkLineOpts(
                    data=[opts.MarkLineItem(
                        type_="average",
                        name="平均值",
                        linestyle_opts=opts.LineStyleOpts(
                            color=self.chart_config['traffic_flow_colors']['total'],
                            type_="dashed",
                            width=2
                        )
                    )]
                ),
            )
            .set_global_opts(
                yaxis_opts=opts.AxisOpts(
                    type_="value",
                    min_=0,
                    max_=y_axis_max,  # 使用与bar相同的最大值
                )
            )
        )

        # 合并图表
        combined_chart = bar.overlap(line)

        # 只生成一个组合图表文件
        output_file = os.path.join(self.output_dir, "进出量时间分布_总量.html")
        combined_chart.render(output_file)

        print(f"✅ 进出量时间分布_总量已生成: {output_file}")
        return output_file

    def generate_vehicle_type_traffic_charts(self, sheet_name='进出量时间分布'):
        """
        生成各类车辆类型的进出量时间分布图表（整合到一个页面）

        Args:
            sheet_name: str, 工作表名称

        Returns:
            str: 生成的HTML文件路径
        """
        if sheet_name not in self.excel_data:
            print(f"❌ 未找到工作表: {sheet_name}")
            return None

        data = self.excel_data[sheet_name]
        print(f"📈 生成各类车辆类型进出量时间分布图表...")

        # 获取时间段列
        time_col = data.iloc[:, 0].astype(str).tolist()

        # 分析数据结构：前4列 + 车辆类型列 + 后3列
        columns = list(data.columns)
        total_cols = len(columns)
        print(f"📋 数据列名: {columns}")
        print(f"📊 总列数: {total_cols}")

        # 检查列数是否足够（至少需要前4列 + 2列车辆数据）
        if total_cols < 6:
            print("❌ 数据列数不足，无法识别车辆类型数据")
            print(f"   当前列数: {total_cols}，最少需要: 6列")
            return None

        # 计算车辆类型数据列的范围：去除前4列和后3列（如果有的话）
        vehicle_start_col = 4  # 从第5列开始（索引4）

        # 智能判断后面的列数：如果总列数大于等于7，去除后3列；否则使用所有剩余列
        if total_cols >= 7:
            vehicle_end_col = total_cols - 3  # 去除后3列
            print("📊 检测到后3列为高峰时段标识，将被排除")
        else:
            vehicle_end_col = total_cols  # 使用所有剩余列
            print("📊 列数较少，使用所有剩余列作为车辆类型数据")

        vehicle_cols_count = vehicle_end_col - vehicle_start_col

        print(f"🚗 车辆类型数据列范围: 第{vehicle_start_col+1}列 到 第{vehicle_end_col}列")
        print(f"📊 车辆类型相关列数: {vehicle_cols_count}")

        if vehicle_cols_count < 1:
            print("❌ 车辆类型数据列数不足")
            return None

        # 检查车辆类型列数
        if vehicle_cols_count == 1:
            print("⚠️ 只有1列车辆类型数据，将作为单一车型的进场数据处理")
        elif vehicle_cols_count % 2 != 0:
            print("⚠️ 车辆类型列数为奇数，最后一列可能不完整")
            print("   将按现有数据处理，但最后一个车型可能只有进场数据")

        # 提取车辆类型数据列
        vehicle_type_columns = []
        vehicle_types = set()

        # 灵活处理车辆类型列
        i = vehicle_start_col
        vehicle_index = 1

        while i < vehicle_end_col:
            entry_col = columns[i]
            exit_col = None
            exit_col_idx = None

            # 检查是否有配对的出场列
            if i + 1 < vehicle_end_col:
                exit_col = columns[i + 1]
                exit_col_idx = i + 1

            # 从列名中提取车辆类型名称
            vehicle_type = self._extract_vehicle_type_from_column_name(entry_col)
            if not vehicle_type:
                vehicle_type = f"车型{vehicle_index}"  # 使用序号作为默认名称

            vehicle_types.add(vehicle_type)

            vehicle_type_columns.append({
                'vehicle_type': vehicle_type,
                'entry_col': i,
                'exit_col': exit_col_idx,
                'entry_name': entry_col,
                'exit_name': exit_col
            })

            print(f"🚗 识别车辆类型: {vehicle_type}")
            print(f"   进场列: {entry_col} (第{i+1}列)")
            if exit_col:
                print(f"   出场列: {exit_col} (第{exit_col_idx+1}列)")
                i += 2  # 跳过两列
            else:
                print(f"   出场列: 无（只有进场数据）")
                i += 1  # 只跳过一列

            vehicle_index += 1

        if not vehicle_type_columns:
            print("❌ 未能识别到有效的车辆类型数据")
            return None

        print(f"✅ 成功识别 {len(vehicle_type_columns)} 种车辆类型")

        # 调用继续方法生成图表
        return self.generate_vehicle_type_traffic_charts_continue(data, time_col, vehicle_type_columns, vehicle_types)

    def _extract_vehicle_type_from_column_name(self, column_name):
        """
        从列名中提取车辆类型名称

        Args:
            column_name: str, 列名

        Returns:
            str: 车辆类型名称，如果无法提取则返回None
        """
        col_str = str(column_name)

        # 常见的车辆类型关键词
        vehicle_types = [
            '私家车', '网约车', '出租车', '货车', '客车', '摩托车', '电动车',
            '小型车', '大型车', '中型车', '轿车', 'SUV', '面包车', '卡车'
        ]

        # 查找车辆类型
        for vehicle_type in vehicle_types:
            if vehicle_type in col_str:
                return vehicle_type

        # 如果没有找到预定义的车辆类型，尝试提取列名中的主要部分
        # 去除常见的方向和数量关键词
        remove_keywords = ['进场', '出场', '进入', '离开', '入场', '出入', '进', '出', '数量', '统计', '计数']

        extracted_name = col_str
        for keyword in remove_keywords:
            extracted_name = extracted_name.replace(keyword, '')

        # 清理空白字符
        extracted_name = extracted_name.strip()

        # 如果提取后的名称不为空且长度合理，返回它
        if extracted_name and len(extracted_name) > 0 and len(extracted_name) <= 10:
            return extracted_name

        return None

    def generate_vehicle_type_traffic_charts_continue(self, data, time_col, vehicle_type_columns, vehicle_types):
        """
        继续生成车辆类型图表的方法（分离出来避免代码结构问题）
        """
        # 按车辆类型分组数据
        vehicle_data = {}
        for col_info in vehicle_type_columns:
            vehicle_type = col_info['vehicle_type']
            entry_col_idx = col_info['entry_col']
            exit_col_idx = col_info['exit_col']

            # 获取进场数据
            entry_data = data.iloc[:, entry_col_idx].fillna(0).tolist()

            # 获取出场数据（如果存在）
            if exit_col_idx is not None:
                exit_data = data.iloc[:, exit_col_idx].fillna(0).tolist()
            else:
                # 如果没有出场列，使用0填充
                exit_data = [0] * len(entry_data)

            vehicle_data[vehicle_type] = {
                '进场': entry_data,
                '出场': exit_data
            }

        # 创建专业演讲风格的颜色配置
        vehicle_colors = {
            '私家车': {'进场': '#2E86AB', '出场': '#A23B72'},
            '网约车': {'进场': '#1B4F72', '出场': '#922B21'},
            '出租车': {'进场': '#148F77', '出场': '#B7950B'},
            '货车': {'进场': '#7D3C98', '出场': '#D35400'},
            '客车': {'进场': '#2874A6', '出场': '#C0392B'},
            '摩托车': {'进场': '#117A65', '出场': '#E67E22'},
            '电动车': {'进场': '#5B2C6F', '出场': '#F39C12'}
        }

        # 创建页面容器，使用紧凑布局
        page = Page(layout=Page.SimplePageLayout)

        # 计算每个子图的高度：总高度600px除以车辆类型数量
        valid_vehicle_types = []
        for vehicle_type in sorted(vehicle_types):
            entry_data = vehicle_data[vehicle_type]['进场']
            exit_data = vehicle_data[vehicle_type]['出场']
            if any(entry_data) or any(exit_data):
                valid_vehicle_types.append(vehicle_type)

        if not valid_vehicle_types:
            print("❌ 没有找到有效的车辆类型数据")
            return None

        # 计算子图高度：总高度600px，减去标题和间距，然后平均分配
        total_height = 600
        title_space = 60  # 为整体标题预留空间
        spacing = 20 * (len(valid_vehicle_types) - 1)  # 子图间距
        available_height = total_height - title_space - spacing
        sub_chart_height = max(150, available_height // len(valid_vehicle_types))  # 最小高度150px

        print(f"📊 子图布局: {len(valid_vehicle_types)}个子图，每个高度{sub_chart_height}px")

        # 为每种车辆类型创建紧凑的子图
        charts = []
        for i, vehicle_type in enumerate(valid_vehicle_types):
            entry_data = vehicle_data[vehicle_type]['进场']
            exit_data = vehicle_data[vehicle_type]['出场']

            # 获取颜色配置
            colors = vehicle_colors.get(vehicle_type, {'进场': '#2E86AB', '出场': '#A23B72'})

            # 创建紧凑的柱状图
            chart = (
                Bar(init_opts=opts.InitOpts(
                    theme=self.chart_config['theme'],
                    width="1200px",
                    height=f"{sub_chart_height}px"
                ))
                .add_xaxis(xaxis_data=time_col)
                .add_yaxis(
                    series_name=f"{vehicle_type}-进场",
                    y_axis=entry_data,
                    label_opts=opts.LabelOpts(is_show=False),
                    color=colors['进场'],
                    itemstyle_opts=opts.ItemStyleOpts(
                        color=colors['进场'],
                        border_color='#ffffff',
                        border_width=1
                    ),
                )
                .add_yaxis(
                    series_name=f"{vehicle_type}-出场",
                    y_axis=exit_data,
                    label_opts=opts.LabelOpts(is_show=False),
                    color=colors['出场'],
                    itemstyle_opts=opts.ItemStyleOpts(
                        color=colors['出场'],
                        border_color='#ffffff',
                        border_width=1
                    ),
                )
                .set_global_opts(
                    title_opts=opts.TitleOpts(
                        title=f"{vehicle_type}进出量分布",
                        pos_left="center",
                        title_textstyle_opts=opts.TextStyleOpts(
                            font_size=14,  # 缩小标题字体
                            font_weight="bold",
                            color="#2c3e50"
                        )
                    ),
                    tooltip_opts=opts.TooltipOpts(
                        is_show=True,
                        trigger="axis",
                        axis_pointer_type="cross",
                        formatter="{b}<br/>{a0}: {c0}<br/>{a1}: {c1}",
                        background_color="rgba(255, 255, 255, 0.95)",
                        border_color="#cccccc",
                        border_width=1,
                        textstyle_opts=opts.TextStyleOpts(
                            color="#333333",
                            font_size=11  # 缩小tooltip字体
                        )
                    ),
                    xaxis_opts=opts.AxisOpts(
                        name="时间段" if i == len(valid_vehicle_types) - 1 else "",  # 只在最后一个图显示X轴名称
                        type_="category",
                        axispointer_opts=opts.AxisPointerOpts(is_show=True, type_="shadow"),
                        axislabel_opts=opts.LabelOpts(
                            rotate=45,
                            font_size=8,  # 进一步缩小横坐标字体
                            is_show=True  # 每个子图都显示X轴标签
                        ),
                    ),
                    yaxis_opts=opts.AxisOpts(
                        name="数量",
                        type_="value",
                        min_=0,
                        axislabel_opts=opts.LabelOpts(formatter="{value}", font_size=10),
                        axistick_opts=opts.AxisTickOpts(is_show=True),
                        splitline_opts=opts.SplitLineOpts(is_show=True),
                        name_textstyle_opts=opts.TextStyleOpts(font_size=10)
                    ),
                    legend_opts=opts.LegendOpts(
                        pos_top="5%",
                        textstyle_opts=opts.TextStyleOpts(
                            font_size=10,  # 缩小图例字体
                            font_weight="bold"
                        ),
                        item_gap=10,
                        item_width=15,  # 缩小图例图标
                        item_height=10
                    ),
                    # 移除缩放控件以节省空间
                )
            )

            charts.append(chart)

        # 将所有子图添加到页面
        for chart in charts:
            page.add(chart)

        # 生成HTML文件
        output_file = os.path.join(self.output_dir, "进出量时间分布_车型.html")
        page.render(output_file)

        print(f"✅ 车辆类型进出量时间分布图表已生成: {output_file}")
        print(f"📊 包含 {len(valid_vehicle_types)} 种车辆类型，{len(charts)} 个紧凑子图")
        print(f"📏 每个子图高度: {sub_chart_height}px，总体高度控制在600px内")

        return output_file

    def generate_gate_traffic_timeline(self, sheet_name='进出量时间分布(按道闸)'):
        """
        生成出入口进出量Timeline动态图表（仅显示总量）

        Args:
            sheet_name: str, 工作表名称

        Returns:
            str: 生成的HTML文件路径
        """
        if sheet_name not in self.excel_data:
            print(f"❌ 未找到工作表: {sheet_name}")
            return None

        data = self.excel_data[sheet_name]
        print(f"📈 生成出入口进出量Timeline动态图表...")

        # 分析数据结构：时间段 + 出入口数据（每个出入口3列：进、出、总量）
        columns = list(data.columns)
        total_cols = len(columns)
        print(f"📋 数据列名: {columns}")
        print(f"📊 总列数: {total_cols}")

        # 检查列数是否足够（至少需要时间段 + 1个出入口的3列数据）
        if total_cols < 4:
            print("❌ 数据列数不足，无法识别出入口数据")
            print(f"   当前列数: {total_cols}，最少需要: 4列")
            return None

        # 计算出入口数据列的范围：去除第1列（时间段）
        gate_start_col = 1  # 从第2列开始（索引1）
        gate_cols_count = total_cols - 1

        # 检查是否为3的倍数（每个出入口3列：进、出、总量）
        if gate_cols_count % 3 != 0:
            print("⚠️ 出入口数据列数不是3的倍数，可能数据结构不完整")
            print(f"   出入口相关列数: {gate_cols_count}，应为3的倍数")
            # 调整为最接近的3的倍数
            gate_cols_count = (gate_cols_count // 3) * 3
            print(f"   调整后使用列数: {gate_cols_count}")

        gate_count = gate_cols_count // 3
        print(f"🚪 识别出入口数量: {gate_count}")

        if gate_count == 0:
            print("❌ 未能识别到有效的出入口数据")
            return None

        # 提取出入口信息和总量数据
        gate_info = []
        gate_total_data = {}

        for i in range(gate_count):
            # 每个出入口的3列：进、出、总量（我们只需要总量列）
            total_col_idx = gate_start_col + i * 3 + 2  # 总量列

            # 从列名中提取出入口名称
            total_col_name = columns[total_col_idx]
            gate_name = self._extract_gate_name_from_column(total_col_name)
            if not gate_name:
                gate_name = f"出入口{i + 1}"

            gate_info.append({
                'name': gate_name,
                'total_col_idx': total_col_idx,
                'total_col_name': total_col_name
            })

            # 获取总量数据
            total_data = data.iloc[:, total_col_idx].fillna(0).tolist()
            gate_total_data[gate_name] = total_data

            print(f"🚪 识别出入口: {gate_name}")
            print(f"   总量列: {total_col_name} (第{total_col_idx+1}列)")

        # 获取时间段数据
        time_periods = data.iloc[:, 0].astype(str).tolist()
        print(f"⏰ 时间段数量: {len(time_periods)}")

        # 计算全局Y轴范围（所有时间段和出入口的最大值）
        all_values = []
        for gate_name in gate_total_data.keys():
            all_values.extend(gate_total_data[gate_name])

        global_max = max(all_values) if all_values else 100
        y_axis_max = global_max * 1.2  # 留出20%的空间
        y_axis_min = 0

        print(f"📊 Y轴范围设置: {y_axis_min} - {y_axis_max:.0f} (固定刻度)")

        # 创建Timeline容器
        timeline = Timeline(init_opts=opts.InitOpts(
            theme=self.chart_config['theme'],
            width="1200px",
            height="600px"
        ))

        # 为每个时间段创建柱状图
        for i, time_period in enumerate(time_periods):
            # 获取该时间段各出入口的总量数据
            period_data = []
            for gate_name in gate_total_data.keys():
                value = gate_total_data[gate_name][i]
                period_data.append(value)

            # 创建该时间段的柱状图
            bar = (
                Bar()
                .add_xaxis([gate['name'] for gate in gate_info])
                .add_yaxis(
                    series_name="总流量",
                    y_axis=period_data,
                    label_opts=opts.LabelOpts(is_show=True, position="top"),
                    color=self.chart_config['traffic_flow_colors']['total'],
                    itemstyle_opts=opts.ItemStyleOpts(
                        color=self.chart_config['traffic_flow_colors']['total'],
                        border_color='#ffffff',
                        border_width=1
                    ),
                )
                .set_global_opts(
                    title_opts=opts.TitleOpts(
                        title=f"出入口流量分布 - {time_period}",
                        subtitle="显示各出入口在该时间段的总流量",
                        pos_left="center",
                        title_textstyle_opts=opts.TextStyleOpts(
                            font_size=18,
                            font_weight="bold",
                            color="#2c3e50"
                        ),
                        subtitle_textstyle_opts=opts.TextStyleOpts(
                            font_size=12,
                            color="#7f8c8d"
                        )
                    ),
                    tooltip_opts=opts.TooltipOpts(
                        is_show=True,
                        trigger="axis",
                        formatter="{b}: {c}辆",
                        background_color="rgba(255, 255, 255, 0.95)",
                        border_color="#cccccc",
                        border_width=1,
                        textstyle_opts=opts.TextStyleOpts(
                            color="#333333",
                            font_size=12
                        )
                    ),
                    xaxis_opts=opts.AxisOpts(
                        name="出入口",
                        type_="category",
                        axislabel_opts=opts.LabelOpts(rotate=45),
                    ),
                    yaxis_opts=opts.AxisOpts(
                        name="车辆数量",
                        type_="value",
                        min_=y_axis_min,
                        max_=y_axis_max,  # 使用固定的最大值
                        axislabel_opts=opts.LabelOpts(formatter="{value}"),
                        axistick_opts=opts.AxisTickOpts(is_show=True),
                        splitline_opts=opts.SplitLineOpts(is_show=True),
                    ),
                    legend_opts=opts.LegendOpts(
                        pos_top="8%",
                        textstyle_opts=opts.TextStyleOpts(
                            font_size=12,
                            font_weight="bold"
                        )
                    ),
                )
            )

            # 添加到Timeline
            timeline.add(bar, time_period)

        # 设置Timeline播放配置
        timeline.add_schema(
            play_interval=2000,  # 播放间隔2秒
            is_auto_play=False,  # 不自动播放
            is_loop_play=True,   # 循环播放
            pos_left="10%",      # 左边距与图表对齐
            pos_right="10%",     # 右边距与图表对齐
            pos_bottom="5%",     # 底部位置
            width="80%"          # 时间轴宽度与X轴对齐
        )

        # 生成HTML文件
        output_file = os.path.join(self.output_dir, "出入口进出量_总量.html")
        timeline.render(output_file)

        print(f"✅ 出入口进出量Timeline图表已生成: {output_file}")
        print(f"📊 包含 {gate_count} 个出入口，{len(time_periods)} 个时间段")
        print(f"🎬 支持Timeline动态播放，可查看各时间段的出入口流量变化")

        return output_file

    def generate_gate_traffic_direction_timeline(self, sheet_name='进出量时间分布(按道闸)'):
        """
        生成出入口进出量Timeline动态图表（显示进场和出场）

        Args:
            sheet_name: str, 工作表名称

        Returns:
            str: 生成的HTML文件路径
        """
        if sheet_name not in self.excel_data:
            print(f"❌ 未找到工作表: {sheet_name}")
            return None

        data = self.excel_data[sheet_name]
        print(f"📈 生成出入口进出量方向Timeline动态图表...")

        # 分析数据结构：时间段 + 出入口数据（每个出入口3列：进、出、总量）
        columns = list(data.columns)
        total_cols = len(columns)
        print(f"📋 数据列名: {columns}")
        print(f"📊 总列数: {total_cols}")

        # 检查列数是否足够
        if total_cols < 4:
            print("❌ 数据列数不足，无法识别出入口数据")
            return None

        # 计算出入口数据列的范围
        gate_start_col = 1
        gate_cols_count = total_cols - 1

        # 检查是否为3的倍数
        if gate_cols_count % 3 != 0:
            print("⚠️ 出入口数据列数不是3的倍数，调整处理")
            gate_cols_count = (gate_cols_count // 3) * 3

        gate_count = gate_cols_count // 3
        print(f"🚪 识别出入口数量: {gate_count}")

        if gate_count == 0:
            print("❌ 未能识别到有效的出入口数据")
            return None

        # 提取出入口信息和进出数据
        gate_info = []
        gate_entry_data = {}
        gate_exit_data = {}

        for i in range(gate_count):
            # 每个出入口的3列：进、出、总量（我们需要进场和出场列）
            entry_col_idx = gate_start_col + i * 3      # 进场列
            exit_col_idx = gate_start_col + i * 3 + 1   # 出场列

            # 从列名中提取出入口名称
            entry_col_name = columns[entry_col_idx]
            gate_name = self._extract_gate_name_from_column(entry_col_name)
            if not gate_name:
                gate_name = f"出入口{i + 1}"

            gate_info.append({
                'name': gate_name,
                'entry_col_idx': entry_col_idx,
                'exit_col_idx': exit_col_idx,
                'entry_col_name': entry_col_name,
                'exit_col_name': columns[exit_col_idx]
            })

            # 获取进场和出场数据
            entry_data = data.iloc[:, entry_col_idx].fillna(0).tolist()
            exit_data = data.iloc[:, exit_col_idx].fillna(0).tolist()

            gate_entry_data[gate_name] = entry_data
            gate_exit_data[gate_name] = exit_data

            print(f"🚪 识别出入口: {gate_name}")
            print(f"   进场列: {entry_col_name} (第{entry_col_idx+1}列)")
            print(f"   出场列: {columns[exit_col_idx]} (第{exit_col_idx+1}列)")

        # 获取时间段数据
        time_periods = data.iloc[:, 0].astype(str).tolist()
        print(f"⏰ 时间段数量: {len(time_periods)}")

        # 计算全局Y轴范围（所有时间段和出入口的进出数据最大值）
        all_values = []
        for gate_name in gate_entry_data.keys():
            all_values.extend(gate_entry_data[gate_name])
            all_values.extend(gate_exit_data[gate_name])

        global_max = max(all_values) if all_values else 100
        y_axis_max = global_max * 1.2
        y_axis_min = 0

        print(f"📊 Y轴范围设置: {y_axis_min} - {y_axis_max:.0f} (固定刻度)")

        # 创建Timeline容器
        timeline = Timeline(init_opts=opts.InitOpts(
            theme=self.chart_config['theme'],
            width="1200px",
            height="600px"
        ))

        # 为每个时间段创建柱状图
        for i, time_period in enumerate(time_periods):
            # 准备该时间段的数据
            gate_names = [gate['name'] for gate in gate_info]
            entry_values = [gate_entry_data[gate_name][i] for gate_name in gate_names]
            exit_values = [gate_exit_data[gate_name][i] for gate_name in gate_names]

            # 创建该时间段的柱状图
            bar = (
                Bar()
                .add_xaxis(gate_names)
                .add_yaxis(
                    series_name="进场",
                    y_axis=entry_values,
                    label_opts=opts.LabelOpts(is_show=True, position="top"),
                    color=self.chart_config['traffic_flow_colors']['entry'],
                    itemstyle_opts=opts.ItemStyleOpts(
                        color=self.chart_config['traffic_flow_colors']['entry'],
                        border_color='#ffffff',
                        border_width=1
                    ),
                )
                .add_yaxis(
                    series_name="出场",
                    y_axis=exit_values,
                    label_opts=opts.LabelOpts(is_show=True, position="top"),
                    color=self.chart_config['traffic_flow_colors']['exit'],
                    itemstyle_opts=opts.ItemStyleOpts(
                        color=self.chart_config['traffic_flow_colors']['exit'],
                        border_color='#ffffff',
                        border_width=1
                    ),
                )
                .set_global_opts(
                    title_opts=opts.TitleOpts(
                        title=f"出入口进出量分布 - {time_period}",
                        subtitle="显示各出入口在该时间段的进场和出场流量",
                        pos_left="center",
                        title_textstyle_opts=opts.TextStyleOpts(
                            font_size=18,
                            font_weight="bold",
                            color="#2c3e50"
                        ),
                        subtitle_textstyle_opts=opts.TextStyleOpts(
                            font_size=12,
                            color="#7f8c8d"
                        )
                    ),
                    tooltip_opts=opts.TooltipOpts(
                        is_show=True,
                        trigger="axis",
                        formatter="{b}<br/>{a0}: {c0}辆<br/>{a1}: {c1}辆",
                        background_color="rgba(255, 255, 255, 0.95)",
                        border_color="#cccccc",
                        border_width=1,
                        textstyle_opts=opts.TextStyleOpts(
                            color="#333333",
                            font_size=12
                        )
                    ),
                    xaxis_opts=opts.AxisOpts(
                        name="出入口",
                        type_="category",
                        axislabel_opts=opts.LabelOpts(rotate=45),
                    ),
                    yaxis_opts=opts.AxisOpts(
                        name="车辆数量",
                        type_="value",
                        min_=y_axis_min,
                        max_=y_axis_max,
                        axislabel_opts=opts.LabelOpts(formatter="{value}"),
                        axistick_opts=opts.AxisTickOpts(is_show=True),
                        splitline_opts=opts.SplitLineOpts(is_show=True),
                    ),
                    legend_opts=opts.LegendOpts(
                        pos_top="8%",
                        textstyle_opts=opts.TextStyleOpts(
                            font_size=12,
                            font_weight="bold"
                        )
                    ),
                )
            )

            # 添加到Timeline
            timeline.add(bar, time_period)

        # 设置Timeline播放配置
        timeline.add_schema(
            play_interval=2000,
            is_auto_play=False,
            is_loop_play=True,
            pos_left="10%",
            pos_right="10%",
            pos_bottom="5%",
            width="80%"
        )

        # 生成HTML文件
        output_file = os.path.join(self.output_dir, "出入口进出量_方向.html")
        timeline.render(output_file)

        print(f"✅ 出入口进出量方向Timeline图表已生成: {output_file}")
        print(f"📊 包含 {gate_count} 个出入口，{len(time_periods)} 个时间段")
        print(f"🎬 显示每个出入口的进场和出场数据，支持Timeline动态播放")

        return output_file

    def generate_gate_entry_proportion_timeline(self, sheet_name='进出量时间分布(按道闸)'):
        """
        生成出入口进场占比Timeline饼图

        Args:
            sheet_name: str, 工作表名称

        Returns:
            str: 生成的HTML文件路径
        """
        if sheet_name not in self.excel_data:
            print(f"❌ 未找到工作表: {sheet_name}")
            return None

        data = self.excel_data[sheet_name]
        print(f"🥧 生成出入口进场占比Timeline饼图...")

        # 分析数据结构：时间段 + 出入口数据（每个出入口3列：进、出、总量）
        columns = list(data.columns)
        total_cols = len(columns)
        print(f"📋 数据列名: {columns}")
        print(f"📊 总列数: {total_cols}")

        # 检查列数是否足够
        if total_cols < 4:
            print("❌ 数据列数不足，无法识别出入口数据")
            return None

        # 计算出入口数据列的范围
        gate_start_col = 1
        gate_cols_count = total_cols - 1

        # 检查是否为3的倍数
        if gate_cols_count % 3 != 0:
            print("⚠️ 出入口数据列数不是3的倍数，调整处理")
            gate_cols_count = (gate_cols_count // 3) * 3

        gate_count = gate_cols_count // 3
        print(f"🚪 识别出入口数量: {gate_count}")

        if gate_count == 0:
            print("❌ 未能识别到有效的出入口数据")
            return None

        # 提取出入口信息和进场数据
        gate_info = []
        gate_entry_data = {}

        for i in range(gate_count):
            # 每个出入口的3列：进、出、总量（我们只需要进场列）
            entry_col_idx = gate_start_col + i * 3  # 进场列

            # 从列名中提取出入口名称
            entry_col_name = columns[entry_col_idx]
            gate_name = self._extract_gate_name_from_column(entry_col_name)
            if not gate_name:
                gate_name = f"出入口{i + 1}"

            gate_info.append({
                'name': gate_name,
                'entry_col_idx': entry_col_idx,
                'entry_col_name': entry_col_name
            })

            # 获取进场数据
            entry_data = data.iloc[:, entry_col_idx].fillna(0).tolist()
            gate_entry_data[gate_name] = entry_data

            print(f"🚪 识别出入口: {gate_name}")
            print(f"   进场列: {entry_col_name} (第{entry_col_idx+1}列)")

        # 获取时间段数据
        time_periods = data.iloc[:, 0].astype(str).tolist()
        print(f"⏰ 时间段数量: {len(time_periods)}")

        # 创建专业演讲风格的饼图颜色配置
        pie_colors = [
            "#2E86AB", "#A23B72", "#F18F01", "#148F77", "#7D3C98",
            "#2874A6", "#117A65", "#5B2C6F", "#B7950B", "#D35400",
            "#C0392B", "#E67E22", "#922B21", "#1B4F72"
        ]

        # "其他出入口"使用特殊的灰色
        other_gate_color = "#95A5A6"  # 中性灰色，表示合并项

        # 创建Timeline容器
        timeline = Timeline(init_opts=opts.InitOpts(
            theme=self.chart_config['theme'],
            width="1200px",
            height="600px"
        ))

        # 为每个时间段创建饼图
        for i, time_period in enumerate(time_periods):
            # 获取该时间段各出入口的进场数据
            raw_entry_data = []
            total_entry = 0

            for gate_name in gate_entry_data.keys():
                raw_value = gate_entry_data[gate_name][i]
                # 处理NaN值和非数值类型
                try:
                    value = float(raw_value) if raw_value is not None and str(raw_value).strip() != '' else 0.0
                    if value != value:  # 检查NaN
                        value = 0.0
                except (ValueError, TypeError):
                    value = 0.0

                raw_entry_data.append([gate_name, value])
                total_entry += value

            # 如果该时间段总进场量为0，跳过或使用默认值
            if total_entry == 0:
                print(f"⚠️ 时间段 {time_period} 的总进场量为0，使用默认占比")
                # 为每个出入口分配相等的默认值
                period_entry_data = [[gate_name, 1] for gate_name in gate_entry_data.keys()]
            else:
                # 数据预处理：将占比小于5%的出入口合并到"其他出入口"
                threshold_percentage = 5.0  # 5%阈值
                threshold_value = total_entry * (threshold_percentage / 100.0)

                # 智能阈值调整：如果出入口数量较少，降低阈值确保有合并项
                gate_count_current = len(raw_entry_data)
                if gate_count_current <= 4:
                    # 出入口数量<=4时，使用3%阈值
                    threshold_percentage = 3.0
                    threshold_value = total_entry * (threshold_percentage / 100.0)
                elif gate_count_current <= 6:
                    # 出入口数量<=6时，使用4%阈值
                    threshold_percentage = 4.0
                    threshold_value = total_entry * (threshold_percentage / 100.0)

                print(f"🔍 时间段 {time_period} 详细分析:")
                print(f"   总进场量: {total_entry}")
                print(f"   出入口数量: {gate_count_current}")
                print(f"   使用阈值: {threshold_percentage}%")
                print(f"   阈值数值: {threshold_value:.2f}")

                main_gates = []  # 主要出入口（占比>=5%）
                other_gates_total = 0  # 其他出入口的总量
                other_gates_names = []  # 其他出入口的名称列表

                for gate_name, value in raw_entry_data:
                    percentage = (value / total_entry * 100) if total_entry > 0 else 0
                    print(f"   {gate_name}: {value} ({percentage:.2f}%)")

                    if value >= threshold_value:
                        # 占比>=阈值的出入口，保留
                        main_gates.append([gate_name, value])
                        print(f"     → 保留 (>={threshold_percentage}%)")
                    else:
                        # 占比<阈值的出入口，合并到"其他"
                        other_gates_total += value
                        other_gates_names.append(gate_name)
                        print(f"     → 合并到其他 (<{threshold_percentage}%)")

                # 构建最终的饼图数据
                period_entry_data = main_gates.copy()

                # 如果有需要合并的出入口，添加"其他出入口"项
                if other_gates_total > 0:
                    period_entry_data.append(["其他出入口", other_gates_total])
                    print(f"📊 时间段 {time_period}: 将 {len(other_gates_names)} 个出入口合并到'其他出入口'")
                    print(f"   合并的出入口: {', '.join(other_gates_names)}")
                    print(f"   主要出入口数量: {len(main_gates)}, 其他出入口总量: {other_gates_total}")
                else:
                    print(f"📊 时间段 {time_period}: 所有出入口占比均>={threshold_percentage}%，无需合并")

            # 创建该时间段的饼图
            pie = (
                Pie(init_opts=opts.InitOpts(
                    theme=self.chart_config['theme'],
                    width="1200px",
                    height="600px"
                ))
                .add(
                    series_name="进场占比",
                    data_pair=period_entry_data,
                    rosetype="radius",  # 玫瑰图样式
                    radius=["30%", "55%"],  # 内外半径
                    label_opts=opts.LabelOpts(
                        is_show=True,
                        position="outside",
                        formatter="{b}: {c}辆\n({d}%)",
                        font_size=11
                    ),
                    itemstyle_opts=opts.ItemStyleOpts(
                        border_color='#ffffff',
                        border_width=2
                    )
                )
                .set_colors(self._get_pie_colors_for_period(period_entry_data, pie_colors, other_gate_color))  # 动态分配颜色
                .set_global_opts(
                    title_opts=opts.TitleOpts(
                        title=f"出入口进场占比 - {time_period}",
                        subtitle=f"显示各出入口在该时间段的进场量占比",
                        pos_left="center",
                        title_textstyle_opts=opts.TextStyleOpts(
                            font_size=18,
                            font_weight="bold",
                            color="#2c3e50"
                        ),
                        subtitle_textstyle_opts=opts.TextStyleOpts(
                            font_size=12,
                            color="#7f8c8d"
                        )
                    ),
                    tooltip_opts=opts.TooltipOpts(
                        is_show=True,
                        trigger="item",
                        formatter="{b}<br/>进场量: {c}辆<br/>占比: {d}%",
                        background_color="rgba(255, 255, 255, 0.95)",
                        border_color="#cccccc",
                        border_width=1,
                        textstyle_opts=opts.TextStyleOpts(
                            color="#333333",
                            font_size=12
                        )
                    ),
                    legend_opts=opts.LegendOpts(
                        pos_top="8%",
                        pos_left="left",
                        orient="vertical",
                        textstyle_opts=opts.TextStyleOpts(
                            font_size=11,
                            font_weight="bold"
                        )
                    ),
                )
            )

            # 添加到Timeline
            timeline.add(pie, time_period)

        # 设置Timeline播放配置
        timeline.add_schema(
            play_interval=3000,  # 饼图播放间隔稍长，3秒
            is_auto_play=False,
            is_loop_play=True,
            pos_left="10%",
            pos_right="10%",
            pos_bottom="5%",
            width="80%"
        )

        # 生成HTML文件
        output_file = os.path.join(self.output_dir, "出入口占比_进.html")
        timeline.render(output_file)

        print(f"✅ 出入口进场占比Timeline饼图已生成: {output_file}")
        print(f"🥧 包含 {gate_count} 个出入口，{len(time_periods)} 个时间段")
        print(f"📊 显示各出入口进场量占比，支持Timeline动态播放")

        return output_file

    def generate_gate_exit_proportion_timeline(self, sheet_name='进出量时间分布(按道闸)'):
        """
        生成出入口出场占比Timeline饼图

        Args:
            sheet_name: str, 工作表名称

        Returns:
            str: 生成的HTML文件路径
        """
        if sheet_name not in self.excel_data:
            print(f"❌ 未找到工作表: {sheet_name}")
            return None

        data = self.excel_data[sheet_name]
        print(f"🥧 生成出入口出场占比Timeline饼图...")

        # 分析数据结构：时间段 + 出入口数据（每个出入口3列：进、出、总量）
        columns = list(data.columns)
        total_cols = len(columns)
        print(f"📋 数据列名: {columns}")
        print(f"📊 总列数: {total_cols}")

        # 检查列数是否足够
        if total_cols < 4:
            print("❌ 数据列数不足，无法识别出入口数据")
            return None

        # 计算出入口数据列的范围
        gate_start_col = 1
        gate_cols_count = total_cols - 1

        # 检查是否为3的倍数
        if gate_cols_count % 3 != 0:
            print("⚠️ 出入口数据列数不是3的倍数，调整处理")
            gate_cols_count = (gate_cols_count // 3) * 3

        gate_count = gate_cols_count // 3
        print(f"🚪 识别出入口数量: {gate_count}")

        if gate_count == 0:
            print("❌ 未能识别到有效的出入口数据")
            return None

        # 提取出入口信息和出场数据
        gate_info = []
        gate_exit_data = {}

        for i in range(gate_count):
            # 每个出入口的3列：进、出、总量（我们只需要出场列）
            exit_col_idx = gate_start_col + i * 3 + 1  # 出场列

            # 从列名中提取出入口名称
            exit_col_name = columns[exit_col_idx]
            gate_name = self._extract_gate_name_from_column(exit_col_name)
            if not gate_name:
                gate_name = f"出入口{i + 1}"

            gate_info.append({
                'name': gate_name,
                'exit_col_idx': exit_col_idx,
                'exit_col_name': exit_col_name
            })

            # 获取出场数据
            exit_data = data.iloc[:, exit_col_idx].fillna(0).tolist()
            gate_exit_data[gate_name] = exit_data

            print(f"🚪 识别出入口: {gate_name}")
            print(f"   出场列: {exit_col_name} (第{exit_col_idx+1}列)")

        # 获取时间段数据
        time_periods = data.iloc[:, 0].astype(str).tolist()
        print(f"⏰ 时间段数量: {len(time_periods)}")

        # 创建专业演讲风格的饼图颜色配置
        pie_colors = [
            "#2E86AB", "#A23B72", "#F18F01", "#148F77", "#7D3C98",
            "#2874A6", "#117A65", "#5B2C6F", "#B7950B", "#D35400",
            "#C0392B", "#E67E22", "#922B21", "#1B4F72"
        ]

        # "其他出入口"使用特殊的灰色
        other_gate_color = "#95A5A6"  # 中性灰色，表示合并项

        # 创建Timeline容器
        timeline = Timeline(init_opts=opts.InitOpts(
            theme=self.chart_config['theme'],
            width="1200px",
            height="600px"
        ))

        # 为每个时间段创建饼图
        for i, time_period in enumerate(time_periods):
            # 获取该时间段各出入口的出场数据
            raw_exit_data = []
            total_exit = 0

            for gate_name in gate_exit_data.keys():
                raw_value = gate_exit_data[gate_name][i]
                # 处理NaN值和非数值类型
                try:
                    value = float(raw_value) if raw_value is not None and str(raw_value).strip() != '' else 0.0
                    if value != value:  # 检查NaN
                        value = 0.0
                except (ValueError, TypeError):
                    value = 0.0

                raw_exit_data.append([gate_name, value])
                total_exit += value

            # 如果该时间段总出场量为0，跳过或使用默认值
            if total_exit == 0:
                print(f"⚠️ 时间段 {time_period} 的总出场量为0，使用默认占比")
                # 为每个出入口分配相等的默认值
                period_exit_data = [[gate_name, 1] for gate_name in gate_exit_data.keys()]
            else:
                # 数据预处理：将占比小于阈值的出入口合并到"其他出入口"
                threshold_percentage = 5.0  # 5%阈值
                threshold_value = total_exit * (threshold_percentage / 100.0)

                # 智能阈值调整：如果出入口数量较少，降低阈值确保有合并项
                gate_count_current = len(raw_exit_data)
                if gate_count_current <= 4:
                    # 出入口数量<=4时，使用3%阈值
                    threshold_percentage = 3.0
                    threshold_value = total_exit * (threshold_percentage / 100.0)
                elif gate_count_current <= 6:
                    # 出入口数量<=6时，使用4%阈值
                    threshold_percentage = 4.0
                    threshold_value = total_exit * (threshold_percentage / 100.0)

                print(f"🔍 时间段 {time_period} 详细分析:")
                print(f"   总出场量: {total_exit}")
                print(f"   出入口数量: {gate_count_current}")
                print(f"   使用阈值: {threshold_percentage}%")
                print(f"   阈值数值: {threshold_value:.2f}")

                main_gates = []  # 主要出入口（占比>=阈值）
                other_gates_total = 0  # 其他出入口的总量
                other_gates_names = []  # 其他出入口的名称列表

                for gate_name, value in raw_exit_data:
                    percentage = (value / total_exit * 100) if total_exit > 0 else 0
                    print(f"   {gate_name}: {value} ({percentage:.2f}%)")

                    if value >= threshold_value:
                        # 占比>=阈值的出入口，保留
                        main_gates.append([gate_name, value])
                        print(f"     → 保留 (>={threshold_percentage}%)")
                    else:
                        # 占比<阈值的出入口，合并到"其他"
                        other_gates_total += value
                        other_gates_names.append(gate_name)
                        print(f"     → 合并到其他 (<{threshold_percentage}%)")

                # 构建最终的饼图数据
                period_exit_data = main_gates.copy()

                # 如果有需要合并的出入口，添加"其他出入口"项
                if other_gates_total > 0:
                    period_exit_data.append(["其他出入口", other_gates_total])
                    print(f"📊 时间段 {time_period}: 将 {len(other_gates_names)} 个出入口合并到'其他出入口'")
                    print(f"   合并的出入口: {', '.join(other_gates_names)}")
                    print(f"   主要出入口数量: {len(main_gates)}, 其他出入口总量: {other_gates_total}")
                else:
                    print(f"📊 时间段 {time_period}: 所有出入口占比均>={threshold_percentage}%，无需合并")

            # 创建该时间段的饼图
            pie = (
                Pie(init_opts=opts.InitOpts(
                    theme=self.chart_config['theme'],
                    width="1200px",
                    height="600px"
                ))
                .add(
                    series_name="出场占比",
                    data_pair=period_exit_data,
                    rosetype="radius",  # 玫瑰图样式
                    radius=["30%", "55%"],  # 内外半径
                    label_opts=opts.LabelOpts(
                        is_show=True,
                        position="outside",
                        formatter="{b}: {c}辆\n({d}%)",
                        font_size=11
                    ),
                    itemstyle_opts=opts.ItemStyleOpts(
                        border_color='#ffffff',
                        border_width=2
                    )
                )
                .set_colors(self._get_pie_colors_for_period(period_exit_data, pie_colors, other_gate_color))  # 动态分配颜色
                .set_global_opts(
                    title_opts=opts.TitleOpts(
                        title=f"出入口出场占比 - {time_period}",
                        subtitle=f"显示各出入口在该时间段的出场量占比",
                        pos_left="center",
                        title_textstyle_opts=opts.TextStyleOpts(
                            font_size=18,
                            font_weight="bold",
                            color="#2c3e50"
                        ),
                        subtitle_textstyle_opts=opts.TextStyleOpts(
                            font_size=12,
                            color="#7f8c8d"
                        )
                    ),
                    tooltip_opts=opts.TooltipOpts(
                        is_show=True,
                        trigger="item",
                        formatter="{b}<br/>出场量: {c}辆<br/>占比: {d}%",
                        background_color="rgba(255, 255, 255, 0.95)",
                        border_color="#cccccc",
                        border_width=1,
                        textstyle_opts=opts.TextStyleOpts(
                            color="#333333",
                            font_size=12
                        )
                    ),
                    legend_opts=opts.LegendOpts(
                        pos_top="8%",
                        pos_left="left",
                        orient="vertical",
                        textstyle_opts=opts.TextStyleOpts(
                            font_size=11,
                            font_weight="bold"
                        )
                    ),
                )
            )

            # 添加到Timeline
            timeline.add(pie, time_period)

        # 设置Timeline播放配置
        timeline.add_schema(
            play_interval=3000,  # 饼图播放间隔稍长，3秒
            is_auto_play=False,
            is_loop_play=True,
            pos_left="10%",
            pos_right="10%",
            pos_bottom="5%",
            width="80%"
        )

        # 生成HTML文件
        output_file = os.path.join(self.output_dir, "出入口占比_出.html")
        timeline.render(output_file)

        print(f"✅ 出入口出场占比Timeline饼图已生成: {output_file}")
        print(f"🥧 包含 {gate_count} 个出入口，{len(time_periods)} 个时间段")
        print(f"📊 显示各出入口出场量占比，支持Timeline动态播放")

        return output_file

    def generate_combined_gate_analysis(self, sheet_name='进出量时间分布(按道闸)'):
        """
        生成出入口进出量综合分析页面
        使用正确的Grid布局实现2x2布局：上方两个饼图，下方一个柱状图

        Args:
            sheet_name: str, 工作表名称

        Returns:
            str: 生成的HTML文件路径
        """
        if sheet_name not in self.excel_data:
            print(f"❌ 未找到工作表: {sheet_name}")
            return None

        print(f"📊 生成出入口进出量综合分析页面...")
        print(f"📐 采用正确的Grid 2x2布局：上方两个饼图，下方一个柱状图")

        data = self.excel_data[sheet_name]

        # 分析数据结构
        columns = list(data.columns)
        total_cols = len(columns)

        if total_cols < 4:
            print("❌ 数据列数不足，无法识别出入口数据")
            return None

        # 计算出入口数据列的范围
        gate_start_col = 1
        gate_cols_count = total_cols - 1

        if gate_cols_count % 3 != 0:
            print("⚠️ 出入口数据列数不是3的倍数，调整处理")
            gate_cols_count = (gate_cols_count // 3) * 3

        gate_count = gate_cols_count // 3
        print(f"🚪 识别出入口数量: {gate_count}")

        if gate_count == 0:
            print("❌ 未能识别到有效的出入口数据")
            return None

        # 获取时间段数据
        time_periods = data.iloc[:, 0].astype(str).tolist()
        print(f"⏰ 时间段数量: {len(time_periods)}")

        # 创建Timeline容器
        timeline = Timeline(init_opts=opts.InitOpts(
            theme=self.chart_config['theme'],
            width="1400px",
            height="800px"
        ))

        # 为每个时间段创建组合图表
        for i, time_period in enumerate(time_periods):
            try:
                # 创建组合图表
                combined_chart = self._create_combined_chart_for_timeline(data, gate_count, gate_start_col, i, time_period)

                if combined_chart:
                    timeline.add(combined_chart, time_period)
                else:
                    print(f"⚠️ 时间段 {time_period} 的图表创建失败，跳过")

            except Exception as e:
                print(f"⚠️ 时间段 {time_period} 处理失败: {str(e)}")
                continue

        # 设置Timeline播放配置
        timeline.add_schema(
            play_interval=4000,
            is_auto_play=False,
            is_loop_play=True,
            pos_left="5%",
            pos_right="5%",
            pos_bottom="5%",
            width="90%"
        )

        # 生成HTML文件
        output_file = os.path.join(self.output_dir, "出入口进出量_进出方向.html")
        timeline.render(output_file)

        print(f"✅ 出入口进出量综合分析页面已生成: {output_file}")
        print(f"📊 采用正确的Grid 2x2布局包含三个分析图表")
        print(f"🎬 Timeline包含 {len(time_periods)} 个时间段")

        return output_file

    def _create_combined_chart_for_timeline(self, data, gate_count, gate_start_col, time_index, time_period):
        """为Timeline创建组合图表，使用Page布局避免Grid问题"""
        try:
            # 提取进场和出场数据
            gate_names = []
            entry_values = []
            exit_values = []
            columns = list(data.columns)

            for i in range(gate_count):
                entry_col_idx = gate_start_col + i * 3      # 进场列
                exit_col_idx = gate_start_col + i * 3 + 1   # 出场列

                entry_col_name = columns[entry_col_idx]
                gate_name = self._extract_gate_name_from_column(entry_col_name)
                if not gate_name:
                    gate_name = f"出入口{i + 1}"

                # 获取该时间段的进出数据
                entry_value = data.iloc[time_index, entry_col_idx]
                exit_value = data.iloc[time_index, exit_col_idx]

                gate_names.append(gate_name)
                entry_values.append(float(entry_value) if pd.notna(entry_value) else 0.0)
                exit_values.append(float(exit_value) if pd.notna(exit_value) else 0.0)

            # 计算总量和统计信息
            total_entry = sum(entry_values)
            total_exit = sum(exit_values)
            net_flow = total_entry - total_exit

            # 计算全天最大值，用于固定Y轴范围
            max_entry_all_time = 0
            max_exit_all_time = 0

            for time_idx in range(len(data)):
                for i in range(gate_count):
                    entry_col_idx = gate_start_col + i * 3
                    exit_col_idx = gate_start_col + i * 3 + 1

                    entry_val = data.iloc[time_idx, entry_col_idx]
                    exit_val = data.iloc[time_idx, exit_col_idx]

                    if pd.notna(entry_val):
                        max_entry_all_time = max(max_entry_all_time, float(entry_val))
                    if pd.notna(exit_val):
                        max_exit_all_time = max(max_exit_all_time, float(exit_val))

            # 设置固定的Y轴最大值（取两者最大值并增加10%的余量）
            fixed_y_max = max(max_entry_all_time, max_exit_all_time) * 1.1

            # 创建进场和出场饼图数据
            entry_pie_data = self._create_pie_data_with_threshold(
                dict(zip(gate_names, entry_values)), "进场"
            )
            exit_pie_data = self._create_pie_data_with_threshold(
                dict(zip(gate_names, exit_values)), "出场"
            )

            # 创建组合饼图（包含进场和出场）- 参考示例中的pie_rosetype()
            pie = (
                Pie(init_opts=opts.InitOpts(
                    theme=self.chart_config['theme'],
                    width="1400px",
                    height="400px"
                ))
                .add(
                    "进场占比",
                    entry_pie_data,
                    radius=["15%", "35%"],
                    center=["25%", "50%"],  # 左侧饼图
                    rosetype="radius",
                    label_opts=opts.LabelOpts(
                        is_show=True,
                        position="outside",
                        formatter="{b}: {d}%",
                        font_size=9
                    )
                )
                .add(
                    "出场占比",
                    exit_pie_data,
                    radius=["15%", "35%"],
                    center=["75%", "50%"],  # 右侧饼图
                    rosetype="radius",
                    label_opts=opts.LabelOpts(
                        is_show=True,
                        position="outside",
                        formatter="{b}: {d}%",
                        font_size=9
                    )
                )
                .set_global_opts(
                    title_opts=opts.TitleOpts(
                        title=f"出入口占比分析 - {time_period}",
                        subtitle=f"左：进场占比({total_entry:.0f}辆)    右：出场占比({total_exit:.0f}辆)    净流量：{net_flow:+.0f}辆",
                        pos_left="center",
                        title_textstyle_opts=opts.TextStyleOpts(
                            font_size=16,
                            font_weight="bold"
                        ),
                        subtitle_textstyle_opts=opts.TextStyleOpts(
                            font_size=12,
                            color="#7f8c8d"
                        )
                    ),
                    legend_opts=opts.LegendOpts(
                        pos_top="85%",
                        pos_left="center",
                        orient="horizontal",
                        textstyle_opts=opts.TextStyleOpts(font_size=8)
                    )
                )
            )

            # 创建柱状图（进出量对比）
            bar = (
                Bar(init_opts=opts.InitOpts(
                    theme=self.chart_config['theme'],
                    width="1400px",
                    height="400px"
                ))
                .add_xaxis(gate_names)
                .add_yaxis(
                    "进场",
                    entry_values,
                    color=self.chart_config['bar_colors'][0],
                    label_opts=opts.LabelOpts(is_show=True, position="top")
                )
                .add_yaxis(
                    "出场",
                    exit_values,
                    color=self.chart_config['bar_colors'][1],
                    label_opts=opts.LabelOpts(is_show=True, position="top")
                )
                .set_global_opts(
                    title_opts=opts.TitleOpts(
                        title="进出量对比",
                        pos_left="center",
                        title_textstyle_opts=opts.TextStyleOpts(
                            font_size=16,
                            font_weight="bold"
                        )
                    ),
                    tooltip_opts=opts.TooltipOpts(
                        trigger="axis",
                        background_color="rgba(255, 255, 255, 0.95)",
                        border_color="#cccccc",
                        border_width=1,
                        textstyle_opts=opts.TextStyleOpts(
                            color="#333333",
                            font_size=12
                        )
                    ),
                    xaxis_opts=opts.AxisOpts(
                        axislabel_opts=opts.LabelOpts(rotate=45, font_size=9)
                    ),
                    legend_opts=opts.LegendOpts(
                        pos_top="10%",
                        pos_left="center"
                    )
                )
            )

            # 调整饼图高度，为上下布局做准备
            pie = (
                Pie(init_opts=opts.InitOpts(
                    theme=self.chart_config['theme'],
                    width="1400px",
                    height="350px"  # 减小高度
                ))
                .add(
                    "进场占比",
                    entry_pie_data,
                    radius=["15%", "30%"],  # 减小半径
                    center=["25%", "50%"],  # 左侧饼图
                    rosetype="radius",
                    label_opts=opts.LabelOpts(
                        is_show=True,
                        position="outside",
                        formatter="{b}: {d}%",
                        font_size=8  # 减小字体
                    )
                )
                .add(
                    "出场占比",
                    exit_pie_data,
                    radius=["15%", "30%"],  # 减小半径
                    center=["75%", "50%"],  # 右侧饼图
                    rosetype="radius",
                    label_opts=opts.LabelOpts(
                        is_show=True,
                        position="outside",
                        formatter="{b}: {d}%",
                        font_size=8  # 减小字体
                    )
                )
                .set_global_opts(
                    title_opts=opts.TitleOpts(
                        title=f"出入口占比分析 - {time_period}",
                        subtitle=f"左：进场占比({total_entry:.0f}辆)    右：出场占比({total_exit:.0f}辆)    净流量：{net_flow:+.0f}辆",
                        pos_left="center",
                        title_textstyle_opts=opts.TextStyleOpts(
                            font_size=14,  # 减小字体
                            font_weight="bold"
                        ),
                        subtitle_textstyle_opts=opts.TextStyleOpts(
                            font_size=10,  # 减小字体
                            color="#7f8c8d"
                        )
                    ),
                    legend_opts=opts.LegendOpts(
                        pos_top="85%",
                        pos_left="center",
                        orient="horizontal",
                        textstyle_opts=opts.TextStyleOpts(font_size=7)  # 减小字体
                    )
                )
            )

            # 调整柱状图高度
            bar = (
                Bar(init_opts=opts.InitOpts(
                    theme=self.chart_config['theme'],
                    width="1400px",
                    height="350px"  # 减小高度
                ))
                .add_xaxis(gate_names)
                .add_yaxis(
                    "进场",
                    entry_values,
                    color=self.chart_config['bar_colors'][0],
                    label_opts=opts.LabelOpts(is_show=True, position="top", font_size=8)
                )
                .add_yaxis(
                    "出场",
                    exit_values,
                    color=self.chart_config['bar_colors'][1],
                    label_opts=opts.LabelOpts(is_show=True, position="top", font_size=8)
                )
                .set_global_opts(
                    title_opts=opts.TitleOpts(
                        title="进出量对比",
                        pos_left="center",
                        title_textstyle_opts=opts.TextStyleOpts(
                            font_size=14,  # 减小字体
                            font_weight="bold"
                        )
                    ),
                    tooltip_opts=opts.TooltipOpts(
                        trigger="axis",
                        background_color="rgba(255, 255, 255, 0.95)",
                        border_color="#cccccc",
                        border_width=1,
                        textstyle_opts=opts.TextStyleOpts(
                            color="#333333",
                            font_size=12
                        )
                    ),
                    xaxis_opts=opts.AxisOpts(
                        axislabel_opts=opts.LabelOpts(rotate=45, font_size=8)  # 减小字体
                    ),
                    legend_opts=opts.LegendOpts(
                        pos_top="10%",
                        pos_left="center"
                    )
                )
            )

            # 参考grid_mutil_yaxis示例，创建真正的Grid布局
            # 首先创建饼图（上方区域）
            pie_chart = (
                Pie(init_opts=opts.InitOpts(
                    theme=self.chart_config['theme'],
                    width="1400px",
                    height="400px"
                ))
                .add(
                    "进场占比",
                    entry_pie_data,
                    radius=["15%", "30%"],
                    center=["25%", "50%"],  # 左侧饼图
                    rosetype="radius",
                    label_opts=opts.LabelOpts(
                        is_show=True,
                        position="outside",
                        formatter="{b}: {d}%",
                        font_size=9
                    )
                )
                .add(
                    "出场占比",
                    exit_pie_data,
                    radius=["15%", "30%"],
                    center=["75%", "50%"],  # 右侧饼图
                    rosetype="radius",
                    label_opts=opts.LabelOpts(
                        is_show=True,
                        position="outside",
                        formatter="{b}: {d}%",
                        font_size=9
                    )
                )
                .set_global_opts(
                    title_opts=opts.TitleOpts(
                        title=f"出入口占比分析 - {time_period}",
                        subtitle=f"左：进场占比({total_entry:.0f}辆)    右：出场占比({total_exit:.0f}辆)    净流量：{net_flow:+.0f}辆",
                        pos_left="center",
                        title_textstyle_opts=opts.TextStyleOpts(
                            font_size=14,
                            font_weight="bold"
                        ),
                        subtitle_textstyle_opts=opts.TextStyleOpts(
                            font_size=10,
                            color="#7f8c8d"
                        )
                    ),
                    legend_opts=opts.LegendOpts(
                        pos_top="85%",
                        pos_left="center",
                        orient="horizontal",
                        textstyle_opts=opts.TextStyleOpts(font_size=8)
                    )
                )
            )

            # 创建柱状图（下方区域）
            bar_chart = (
                Bar(init_opts=opts.InitOpts(
                    theme=self.chart_config['theme'],
                    width="1400px",
                    height="350px"
                ))
                .add_xaxis(gate_names)
                .add_yaxis(
                    "进场",
                    entry_values,
                    color=self.chart_config['bar_colors'][0],
                    label_opts=opts.LabelOpts(is_show=True, position="top", font_size=8)
                )
                .add_yaxis(
                    "出场",
                    exit_values,
                    color=self.chart_config['bar_colors'][1],
                    label_opts=opts.LabelOpts(is_show=True, position="top", font_size=8)
                )
                .set_global_opts(
                    title_opts=opts.TitleOpts(
                        title="进出量对比",
                        pos_left="center",
                        title_textstyle_opts=opts.TextStyleOpts(
                            font_size=14,
                            font_weight="bold"
                        )
                    ),
                    tooltip_opts=opts.TooltipOpts(
                        trigger="axis",
                        background_color="rgba(255, 255, 255, 0.95)",
                        border_color="#cccccc",
                        border_width=1,
                        textstyle_opts=opts.TextStyleOpts(
                            color="#333333",
                            font_size=12
                        )
                    ),
                    xaxis_opts=opts.AxisOpts(
                        axislabel_opts=opts.LabelOpts(rotate=45, font_size=8)
                    ),
                    legend_opts=opts.LegendOpts(
                        pos_top="10%",
                        pos_left="center"
                    )
                )
            )

            # 创建一个包含饼图和柱状图的混合图表
            # 首先创建一个大的画布，上方放饼图，下方放柱状图

            # 计算最大和最小出入口的进出量
            max_entry_gate = max(zip(gate_names, entry_values), key=lambda x: x[1])
            max_exit_gate = max(zip(gate_names, exit_values), key=lambda x: x[1])

            # 创建一个混合图表，使用Bar作为基础，然后添加饼图元素
            # 参考grid_mutil_yaxis示例的思路
            mixed_chart = (
                Bar(init_opts=opts.InitOpts(
                    theme=self.chart_config['theme'],
                    width="1400px",
                    height="800px"
                ))
                .add_xaxis(gate_names)
                .add_yaxis(
                    "进场",
                    entry_values,
                    color=self.chart_config['bar_colors'][0],
                    label_opts=opts.LabelOpts(is_show=True, position="top", font_size=8)
                )
                .add_yaxis(
                    "出场",
                    exit_values,
                    color=self.chart_config['bar_colors'][1],
                    label_opts=opts.LabelOpts(is_show=True, position="top", font_size=8)
                )
                .set_global_opts(
                    title_opts=opts.TitleOpts(
                        title=f"出入口综合分析 - {time_period}",
                        subtitle=f"上方：进出场占比分析  下方：进出量对比  净流量：{net_flow:+.0f}辆\n" +
                                f"流量对比 - 最大进场：{max_entry_gate[0]}({max_entry_gate[1]:.0f}辆)  最大出场：{max_exit_gate[0]}({max_exit_gate[1]:.0f}辆)",
                        pos_left="center",
                        title_textstyle_opts=opts.TextStyleOpts(
                            font_size=16,
                            font_weight="bold"
                        ),
                        subtitle_textstyle_opts=opts.TextStyleOpts(
                            font_size=11,
                            color="#7f8c8d"
                        )
                    ),
                    tooltip_opts=opts.TooltipOpts(
                        trigger="axis",
                        background_color="rgba(255, 255, 255, 0.95)",
                        border_color="#cccccc",
                        border_width=1,
                        textstyle_opts=opts.TextStyleOpts(
                            color="#333333",
                            font_size=12
                        )
                    ),
                    xaxis_opts=opts.AxisOpts(
                        axislabel_opts=opts.LabelOpts(rotate=45, font_size=8)
                    ),
                    yaxis_opts=opts.AxisOpts(
                        type_="value",
                        min_=0,
                        max_=fixed_y_max,  # 使用固定的Y轴最大值
                        axislabel_opts=opts.LabelOpts(formatter="{value}")
                    ),
                    legend_opts=opts.LegendOpts(
                        pos_top="85%",
                        pos_left="center",
                        orient="horizontal"
                    )
                )
            )

            # 创建饼图，但将其作为独立的图表元素添加
            pie_chart = (
                Pie(init_opts=opts.InitOpts(
                    theme=self.chart_config['theme'],
                    width="1400px",
                    height="800px"
                ))
                .add(
                    "进场占比",
                    entry_pie_data,
                    radius=["10%", "22%"],  # 增大半径：从8%-18%调整为10%-22%
                    center=["35%", "35%"],  # 左侧饼图，进一步靠近中心并向下移动
                    rosetype="radius",
                    label_opts=opts.LabelOpts(
                        is_show=True,
                        position="outside",
                        formatter="{b}: {d}%",
                        font_size=9  # 稍微增大字体
                    )
                )
                .add(
                    "出场占比",
                    exit_pie_data,
                    radius=["10%", "22%"],  # 增大半径：从8%-18%调整为10%-22%
                    center=["65%", "35%"],  # 右侧饼图，进一步靠近中心并向下移动
                    rosetype="radius",
                    label_opts=opts.LabelOpts(
                        is_show=True,
                        position="outside",
                        formatter="{b}: {d}%",
                        font_size=9  # 稍微增大字体
                    )
                )
                .set_global_opts(
                    title_opts=opts.TitleOpts(title=""),  # 不显示标题
                    legend_opts=opts.LegendOpts(
                        pos_top="45%",
                        pos_left="center",
                        orient="horizontal",
                        textstyle_opts=opts.TextStyleOpts(font_size=8)
                    )
                )
            )

            # 参考grid_mutil_yaxis示例，使用overlap方法组合
            combined_chart = mixed_chart.overlap(pie_chart)

            return combined_chart

        except Exception as e:
            print(f"❌ 创建时间段 {time_period} 的组合图表失败: {str(e)}")
            return None

    def _create_combined_pie_chart(self, data, gate_count, gate_start_col, time_index, time_period):
        """创建组合饼图，包含进场和出场占比"""
        try:
            # 提取进场和出场数据
            gate_entry_data = {}
            gate_exit_data = {}
            columns = list(data.columns)

            for i in range(gate_count):
                entry_col_idx = gate_start_col + i * 3      # 进场列
                exit_col_idx = gate_start_col + i * 3 + 1   # 出场列

                entry_col_name = columns[entry_col_idx]
                gate_name = self._extract_gate_name_from_column(entry_col_name)
                if not gate_name:
                    gate_name = f"出入口{i + 1}"

                # 获取该时间段的进出数据
                entry_value = data.iloc[time_index, entry_col_idx]
                exit_value = data.iloc[time_index, exit_col_idx]

                gate_entry_data[gate_name] = float(entry_value) if pd.notna(entry_value) else 0.0
                gate_exit_data[gate_name] = float(exit_value) if pd.notna(exit_value) else 0.0

            # 创建进场和出场饼图数据
            entry_pie_data = self._create_pie_data_with_threshold(gate_entry_data, "进场")
            exit_pie_data = self._create_pie_data_with_threshold(gate_exit_data, "出场")

            # 创建组合饼图 - 参考示例中的pie_rosetype()
            pie = (
                Pie(init_opts=opts.InitOpts(
                    theme=self.chart_config['theme'],
                    width="1400px",
                    height="360px"
                ))
                .add(
                    "进场占比",
                    entry_pie_data,
                    radius=["15%", "35%"],
                    center=["25%", "50%"],
                    rosetype="radius",
                    label_opts=opts.LabelOpts(
                        is_show=True,
                        position="outside",
                        formatter="{b}: {d}%",
                        font_size=10
                    ),
                    itemstyle_opts=opts.ItemStyleOpts(
                        border_color='#ffffff',
                        border_width=1
                    )
                )
                .add(
                    "出场占比",
                    exit_pie_data,
                    radius=["15%", "35%"],
                    center=["75%", "50%"],
                    rosetype="radius",
                    label_opts=opts.LabelOpts(
                        is_show=True,
                        position="outside",
                        formatter="{b}: {d}%",
                        font_size=10
                    ),
                    itemstyle_opts=opts.ItemStyleOpts(
                        border_color='#ffffff',
                        border_width=1
                    )
                )
                .set_global_opts(
                    title_opts=opts.TitleOpts(
                        title=f"出入口进出占比分析 - {time_period}",
                        subtitle="左：进场占比    右：出场占比",
                        pos_left="center",
                        title_textstyle_opts=opts.TextStyleOpts(
                            font_size=16,
                            font_weight="bold",
                            color="#2c3e50"
                        )
                    ),
                    tooltip_opts=opts.TooltipOpts(
                        is_show=True,
                        trigger="item",
                        formatter="{a}<br/>{b}: {c}辆<br/>占比: {d}%"
                    ),
                    legend_opts=opts.LegendOpts(
                        pos_top="85%",
                        pos_left="center",
                        orient="horizontal",
                        textstyle_opts=opts.TextStyleOpts(font_size=9)
                    ),
                )
            )

            return pie

        except Exception as e:
            print(f"❌ 创建组合饼图失败: {str(e)}")
            return None

    def _create_summary_chart(self, title, chart_type):
        """创建简化的汇总图表用于Page展示"""
        try:
            # 创建一个简单的柱状图作为汇总展示
            bar = (
                Bar(init_opts=opts.InitOpts(
                    theme=self.chart_config['theme'],
                    width="800px",
                    height="400px"
                ))
                .add_xaxis(["汇总数据"])
                .add_yaxis(title, [100])
                .set_global_opts(
                    title_opts=opts.TitleOpts(
                        title=title,
                        subtitle=f"详细的{chart_type}分析请查看独立文件",
                        pos_left="center"
                    ),
                    tooltip_opts=opts.TooltipOpts(
                        trigger="axis",
                        background_color="rgba(255, 255, 255, 0.95)",
                        border_color="#cccccc",
                        border_width=1,
                        textstyle_opts=opts.TextStyleOpts(
                            color="#333333",
                            font_size=12
                        )
                    ),
                    xaxis_opts=opts.AxisOpts(
                        name="数据类型",
                        type_="category"
                    ),
                    yaxis_opts=opts.AxisOpts(
                        name="数量",
                        type_="value"
                    ),
                )
            )
            return bar
        except Exception as e:
            print(f"❌ 创建{title}汇总图表失败: {str(e)}")
            return None

    def _create_simple_combined_chart(self, time_period, time_index):
        """创建简单的替代图表，当Grid失败时使用"""
        try:
            # 创建一个简单的柱状图显示时间段信息
            bar = (
                Bar(init_opts=opts.InitOpts(
                    theme=self.chart_config['theme'],
                    width="1400px",
                    height="800px"
                ))
                .add_xaxis([time_period])
                .add_yaxis("数据", [100])
                .set_global_opts(
                    title_opts=opts.TitleOpts(
                        title=f"时间段: {time_period}",
                        subtitle="Grid布局失败，显示简化图表",
                        pos_left="center"
                    ),
                    tooltip_opts=opts.TooltipOpts(trigger="axis"),
                    xaxis_opts=opts.AxisOpts(type_="category"),
                    yaxis_opts=opts.AxisOpts(type_="value")
                )
            )
            return bar
        except Exception as e:
            print(f"❌ 创建简单图表失败: {str(e)}")
            return None

    def _create_entry_pie_for_combined(self, data, gate_count, gate_start_col, time_index, time_period):
        """为综合页面创建进场占比饼图"""
        try:
            # 提取出入口信息和进场数据
            gate_entry_data = {}
            columns = list(data.columns)

            for i in range(gate_count):
                entry_col_idx = gate_start_col + i * 3  # 进场列
                entry_col_name = columns[entry_col_idx]
                gate_name = self._extract_gate_name_from_column(entry_col_name)
                if not gate_name:
                    gate_name = f"出入口{i + 1}"

                # 获取该时间段的进场数据
                entry_value = data.iloc[time_index, entry_col_idx]
                gate_entry_data[gate_name] = float(entry_value) if pd.notna(entry_value) else 0.0

            # 创建饼图数据（应用智能阈值）
            pie_data = self._create_pie_data_with_threshold(gate_entry_data, "进场")

            # 创建饼图
            pie = (
                Pie(init_opts=opts.InitOpts(
                    theme=self.chart_config['theme'],
                    width="650px",
                    height="350px"
                ))
                .add(
                    series_name="进场占比",
                    data_pair=pie_data,
                    rosetype="radius",
                    radius=["25%", "50%"],
                    label_opts=opts.LabelOpts(
                        is_show=True,
                        position="outside",
                        formatter="{b}: {d}%",
                        font_size=10
                    ),
                    itemstyle_opts=opts.ItemStyleOpts(
                        border_color='#ffffff',
                        border_width=1
                    )
                )
                .set_global_opts(
                    title_opts=opts.TitleOpts(
                        title="进场占比",
                        pos_left="center",
                        title_textstyle_opts=opts.TextStyleOpts(
                            font_size=14,
                            font_weight="bold",
                            color="#2c3e50"
                        )
                    ),
                    tooltip_opts=opts.TooltipOpts(
                        is_show=True,
                        trigger="item",
                        formatter="{b}<br/>{a}: {c}辆<br/>占比: {d}%"
                    ),
                    legend_opts=opts.LegendOpts(
                        pos_top="75%",
                        pos_left="center",
                        orient="horizontal",
                        textstyle_opts=opts.TextStyleOpts(font_size=9)
                    ),
                )
            )

            return pie

        except Exception as e:
            print(f"❌ 创建进场饼图失败: {str(e)}")
            return None

    def _create_exit_pie_for_combined(self, data, gate_count, gate_start_col, time_index, time_period):
        """为综合页面创建出场占比饼图"""
        try:
            # 提取出入口信息和出场数据
            gate_exit_data = {}
            columns = list(data.columns)

            for i in range(gate_count):
                exit_col_idx = gate_start_col + i * 3 + 1  # 出场列
                exit_col_name = columns[exit_col_idx]
                gate_name = self._extract_gate_name_from_column(exit_col_name)
                if not gate_name:
                    gate_name = f"出入口{i + 1}"

                # 获取该时间段的出场数据
                exit_value = data.iloc[time_index, exit_col_idx]
                gate_exit_data[gate_name] = float(exit_value) if pd.notna(exit_value) else 0.0

            # 创建饼图数据（应用智能阈值）
            pie_data = self._create_pie_data_with_threshold(gate_exit_data, "出场")

            # 创建饼图
            pie = (
                Pie(init_opts=opts.InitOpts(
                    theme=self.chart_config['theme'],
                    width="650px",
                    height="350px"
                ))
                .add(
                    series_name="出场占比",
                    data_pair=pie_data,
                    rosetype="radius",
                    radius=["25%", "50%"],
                    label_opts=opts.LabelOpts(
                        is_show=True,
                        position="outside",
                        formatter="{b}: {d}%",
                        font_size=10
                    ),
                    itemstyle_opts=opts.ItemStyleOpts(
                        border_color='#ffffff',
                        border_width=1
                    )
                )
                .set_global_opts(
                    title_opts=opts.TitleOpts(
                        title="出场占比",
                        pos_left="center",
                        title_textstyle_opts=opts.TextStyleOpts(
                            font_size=14,
                            font_weight="bold",
                            color="#2c3e50"
                        )
                    ),
                    tooltip_opts=opts.TooltipOpts(
                        is_show=True,
                        trigger="item",
                        formatter="{b}<br/>{a}: {c}辆<br/>占比: {d}%"
                    ),
                    legend_opts=opts.LegendOpts(
                        pos_top="75%",
                        pos_left="center",
                        orient="horizontal",
                        textstyle_opts=opts.TextStyleOpts(font_size=9)
                    ),
                )
            )

            return pie

        except Exception as e:
            print(f"❌ 创建出场饼图失败: {str(e)}")
            return None

    def _create_direction_bar_for_combined(self, data, gate_count, gate_start_col, time_index, time_period):
        """为综合页面创建进出量方向对比柱状图"""
        try:
            # 提取出入口信息和进出数据
            gate_names = []
            entry_values = []
            exit_values = []
            columns = list(data.columns)

            for i in range(gate_count):
                entry_col_idx = gate_start_col + i * 3      # 进场列
                exit_col_idx = gate_start_col + i * 3 + 1   # 出场列

                entry_col_name = columns[entry_col_idx]
                gate_name = self._extract_gate_name_from_column(entry_col_name)
                if not gate_name:
                    gate_name = f"出入口{i + 1}"

                # 获取该时间段的进出数据
                entry_value = data.iloc[time_index, entry_col_idx]
                exit_value = data.iloc[time_index, exit_col_idx]

                gate_names.append(gate_name)
                entry_values.append(float(entry_value) if pd.notna(entry_value) else 0.0)
                exit_values.append(float(exit_value) if pd.notna(exit_value) else 0.0)

            # 创建柱状图 - 确保轴配置正确
            bar = Bar(init_opts=opts.InitOpts(
                theme=self.chart_config['theme'],
                width="1300px",
                height="350px"
            ))

            # 添加X轴数据
            bar.add_xaxis(gate_names)

            # 添加Y轴数据系列
            bar.add_yaxis(
                "进场",
                entry_values,
                color=self.chart_config['bar_colors'][0],
                label_opts=opts.LabelOpts(is_show=False)
            )
            bar.add_yaxis(
                "出场",
                exit_values,
                color=self.chart_config['bar_colors'][1],
                label_opts=opts.LabelOpts(is_show=False)
            )

            # 设置全局选项
            bar.set_global_opts(
                title_opts=opts.TitleOpts(
                    title="进出量对比",
                    pos_left="center",
                    title_textstyle_opts=opts.TextStyleOpts(
                        font_size=16,
                        font_weight="bold",
                        color="#2c3e50"
                    )
                ),
                tooltip_opts=opts.TooltipOpts(
                    trigger="axis",
                    background_color="rgba(255, 255, 255, 0.95)",
                    border_color="#cccccc",
                    border_width=1,
                    textstyle_opts=opts.TextStyleOpts(
                        color="#333333",
                        font_size=12
                    )
                ),
                xaxis_opts=opts.AxisOpts(
                    name="出入口",
                    axislabel_opts=opts.LabelOpts(rotate=45, font_size=10),
                    type_="category"
                ),
                yaxis_opts=opts.AxisOpts(
                    name="车辆数量",
                    axislabel_opts=opts.LabelOpts(formatter="{value}"),
                    type_="value"
                ),
                legend_opts=opts.LegendOpts(
                    pos_top="10%",
                    pos_left="center",
                    orient="horizontal"
                ),
            )

            return bar

        except Exception as e:
            print(f"❌ 创建方向柱状图失败: {str(e)}")
            return None

    def _create_pie_data_with_threshold(self, gate_data, data_type):
        """创建饼图数据，应用智能阈值处理"""
        try:
            # 计算总量
            total_value = sum(gate_data.values())
            if total_value == 0:
                return [["无数据", 1]]

            # 智能阈值：5%
            threshold_percentage = 5.0
            threshold_value = total_value * threshold_percentage / 100

            # 分类数据
            main_gates = []
            other_total = 0

            for gate_name, value in gate_data.items():
                if value >= threshold_value:
                    main_gates.append([gate_name, value])
                else:
                    other_total += value

            # 如果有需要合并的小占比出入口，添加"其他出入口"
            if other_total > 0:
                main_gates.append(["其他出入口", other_total])

            return main_gates if main_gates else [["无数据", 1]]

        except Exception as e:
            print(f"❌ 创建{data_type}饼图数据失败: {str(e)}")
            return [["错误", 1]]

    def _create_pie_data(self, gate_data, time_index, time_period, data_type):
        """
        创建饼图数据，包含智能阈值处理

        Args:
            gate_data: dict, 出入口数据
            time_index: int, 时间段索引
            time_period: str, 时间段名称
            data_type: str, 数据类型（"进场"或"出场"）

        Returns:
            list: 饼图数据 [["名称", 值], ...]
        """
        # 获取该时间段的原始数据
        raw_data = []
        total_value = 0

        for gate_name in gate_data.keys():
            raw_value = gate_data[gate_name][time_index]
            # 处理NaN值和非数值类型
            try:
                value = float(raw_value) if raw_value is not None and str(raw_value).strip() != '' else 0.0
                if value != value:  # 检查NaN
                    value = 0.0
            except (ValueError, TypeError):
                value = 0.0

            raw_data.append([gate_name, value])
            total_value += value

        # 如果总值为0，使用默认值
        if total_value == 0:
            return [[gate_name, 1] for gate_name, _ in raw_data]

        # 智能阈值处理
        threshold_percentage = 5.0
        gate_count_current = len(raw_data)

        if gate_count_current <= 4:
            threshold_percentage = 3.0
        elif gate_count_current <= 6:
            threshold_percentage = 4.0

        threshold_value = total_value * (threshold_percentage / 100.0)

        # 分类数据
        main_gates = []
        other_gates_total = 0

        for gate_name, value in raw_data:
            if value >= threshold_value:
                main_gates.append([gate_name, value])
            else:
                other_gates_total += value

        # 构建最终数据
        final_data = main_gates.copy()
        if other_gates_total > 0:
            final_data.append(["其他出入口", other_gates_total])

        return final_data

    def _create_pie_chart(self, pie_data, title, series_name, pie_colors, other_gate_color, width="600px", height="400px"):
        """
        创建饼图

        Args:
            pie_data: list, 饼图数据
            title: str, 图表标题
            series_name: str, 数据系列名称
            pie_colors: list, 颜色列表
            other_gate_color: str, "其他出入口"颜色
            width: str, 图表宽度
            height: str, 图表高度

        Returns:
            Pie: 饼图对象
        """
        pie = (
            Pie(init_opts=opts.InitOpts(
                theme=self.chart_config['theme'],
                width=width,
                height=height
            ))
            .add(
                series_name=series_name,
                data_pair=pie_data,
                rosetype="radius",
                radius=["25%", "50%"],  # 调整半径以适应小尺寸
                label_opts=opts.LabelOpts(
                    is_show=True,
                    position="outside",
                    formatter="{b}: {d}%",  # 简化标签格式
                    font_size=10
                ),
                itemstyle_opts=opts.ItemStyleOpts(
                    border_color='#ffffff',
                    border_width=1
                )
            )
            .set_colors(self._get_pie_colors_for_period(pie_data, pie_colors, other_gate_color))
            .set_global_opts(
                title_opts=opts.TitleOpts(
                    title=title,
                    pos_left="center",
                    title_textstyle_opts=opts.TextStyleOpts(
                        font_size=14,
                        font_weight="bold",
                        color="#2c3e50"
                    )
                ),
                tooltip_opts=opts.TooltipOpts(
                    is_show=True,
                    trigger="item",
                    formatter="{b}<br/>{a}: {c}辆<br/>占比: {d}%"
                ),
                legend_opts=opts.LegendOpts(
                    pos_top="75%",  # 图例放在底部
                    pos_left="center",
                    orient="horizontal",
                    textstyle_opts=opts.TextStyleOpts(font_size=9)
                ),
            )
        )

        return pie

    def _get_pie_colors_for_period(self, period_entry_data, pie_colors, other_gate_color):
        """
        为饼图数据分配颜色，"其他出入口"使用特殊颜色

        Args:
            period_entry_data: list, 饼图数据 [["名称", 值], ...]
            pie_colors: list, 主要颜色列表
            other_gate_color: str, "其他出入口"的颜色

        Returns:
            list: 颜色列表
        """
        colors = []
        color_index = 0

        for gate_name, _ in period_entry_data:
            if gate_name == "其他出入口":
                colors.append(other_gate_color)
            else:
                colors.append(pie_colors[color_index % len(pie_colors)])
                color_index += 1

        return colors

    def _extract_gate_name_from_column(self, column_name):
        """
        从列名中提取出入口名称

        Args:
            column_name: str, 列名

        Returns:
            str: 出入口名称，如果无法提取则返回None
        """
        col_str = str(column_name)

        # 去除常见的后缀词
        remove_keywords = ['总量', '总计', '合计', '流量', '数量', '统计', '计数', '-进', '-出', '-总']

        extracted_name = col_str
        for keyword in remove_keywords:
            extracted_name = extracted_name.replace(keyword, '')

        # 清理空白字符
        extracted_name = extracted_name.strip()

        # 如果提取后的名称不为空且长度合理，返回它
        if extracted_name and len(extracted_name) > 0 and len(extracted_name) <= 15:
            return extracted_name

        # 如果无法提取有意义的名称，返回None
        return None

    def generate_parking_duration_chart(self, sheet_name='停车时长分布'):
        """
        生成停车时长分布图表（包含总量和各车辆类型的柱状图）

        Args:
            sheet_name: str, 工作表名称

        Returns:
            str: 生成的HTML文件路径
        """
        if sheet_name not in self.excel_data:
            print(f"❌ 未找到工作表: {sheet_name}")
            return None

        data = self.excel_data[sheet_name]
        print(f"📈 生成停车时长分布图表...")

        # 分析数据结构
        columns = list(data.columns)
        total_cols = len(columns)
        print(f"📋 数据列名: {columns}")
        print(f"📊 总列数: {total_cols}")

        # 获取基础数据
        duration_col = data.iloc[:, 0].astype(str).tolist()  # 时长
        total_col = data.iloc[:, 1].fillna(0).tolist()       # 总量

        # 创建柱状图
        bar = (
            Bar(init_opts=opts.InitOpts(
                theme=self.chart_config['theme'],
                width=self.chart_config['chart_width'],
                height=self.chart_config['chart_height']
            ))
            .add_xaxis(xaxis_data=duration_col)
            .add_yaxis(
                series_name="总量",
                y_axis=total_col,
                label_opts=opts.LabelOpts(is_show=False),
                color=self.chart_config['traffic_flow_colors']['total'],
                itemstyle_opts=opts.ItemStyleOpts(
                    color=self.chart_config['traffic_flow_colors']['total'],
                    border_color='#ffffff',
                    border_width=1
                ),
            )
        )

        # 识别和添加车辆类型数据
        vehicle_type_count = 0
        color_index = 0

        # 从第4列开始查找车辆类型数据（跳过时长、总量、总量占比）
        for col_idx in range(3, total_cols, 2):  # 每2列为一组（数量+占比）
            if col_idx < total_cols:
                vehicle_col_name = columns[col_idx]
                vehicle_data = data.iloc[:, col_idx].fillna(0).tolist()

                # 提取车辆类型名称（去除占比列）
                if col_idx + 1 < total_cols and '占比' in columns[col_idx + 1]:
                    # 这是一个车辆类型的数量列
                    vehicle_type_name = vehicle_col_name

                    # 使用专业演讲风格的颜色
                    vehicle_colors = [
                        '#2E86AB',  # 深蓝色
                        '#A23B72',  # 深紫红色
                        '#148F77',  # 深绿色
                        '#7D3C98',  # 深紫色
                        '#2874A6',  # 蓝色
                        '#117A65',  # 绿色
                        '#5B2C6F'   # 紫色
                    ]

                    color = vehicle_colors[color_index % len(vehicle_colors)]

                    bar.add_yaxis(
                        series_name=vehicle_type_name,
                        y_axis=vehicle_data,
                        label_opts=opts.LabelOpts(is_show=False),
                        color=color,
                        itemstyle_opts=opts.ItemStyleOpts(
                            color=color,
                            border_color='#ffffff',
                            border_width=1
                        ),
                    )

                    vehicle_type_count += 1
                    color_index += 1
                    print(f"🚗 添加车辆类型: {vehicle_type_name}")

        # 设置全局选项
        bar.set_global_opts(
            title_opts=opts.TitleOpts(
                title="停车时长分布",
                subtitle=f"显示不同停车时长的车辆数量分布（总量 + {vehicle_type_count}种车辆类型）",
                pos_left="center",
                title_textstyle_opts=opts.TextStyleOpts(
                    font_size=20,
                    font_weight="bold",
                    color="#2c3e50"
                ),
                subtitle_textstyle_opts=opts.TextStyleOpts(
                    font_size=14,
                    color="#7f8c8d"
                )
            ),
            tooltip_opts=opts.TooltipOpts(
                is_show=True,
                trigger="axis",
                axis_pointer_type="cross",
                background_color="rgba(255, 255, 255, 0.95)",
                border_color="#cccccc",
                border_width=1,
                textstyle_opts=opts.TextStyleOpts(
                    color="#333333",
                    font_size=12,
                    font_weight="normal"
                )
            ),
            xaxis_opts=opts.AxisOpts(
                name="停车时长",
                type_="category",
                axispointer_opts=opts.AxisPointerOpts(is_show=True, type_="shadow"),
                axislabel_opts=opts.LabelOpts(rotate=45),
            ),
            yaxis_opts=opts.AxisOpts(
                name="车辆数量",
                type_="value",
                axislabel_opts=opts.LabelOpts(formatter="{value}"),
                axistick_opts=opts.AxisTickOpts(is_show=True),
                splitline_opts=opts.SplitLineOpts(is_show=True),
            ),
            legend_opts=opts.LegendOpts(
                pos_top="8%",
                textstyle_opts=opts.TextStyleOpts(
                    font_size=12,
                    font_weight="bold"
                ),
                item_gap=20
            ),
            datazoom_opts=[
                opts.DataZoomOpts(type_="slider", range_start=0, range_end=100),
                opts.DataZoomOpts(type_="inside")
            ],
        )

        # 生成HTML文件
        output_file = os.path.join(self.output_dir, "停车时长分布图表.html")
        bar.render(output_file)

        print(f"✅ 停车时长分布图表已生成: {output_file}")
        print(f"📊 包含总量 + {vehicle_type_count}种车辆类型的柱状图")
        return output_file
    
    def update_chart_config(self, **kwargs):
        """
        更新图表配置
        
        Args:
            **kwargs: 配置参数
                - bar_colors: list, 柱状图颜色列表
                - line_colors: list, 线条颜色列表  
                - line_width: int, 线条粗细
                - theme: ThemeType, 图表主题
                - chart_width: str, 图表宽度
                - chart_height: str, 图表高度
        """
        for key, value in kwargs.items():
            if key in self.chart_config:
                self.chart_config[key] = value
                print(f"✅ 更新配置 {key}: {value}")
            else:
                print(f"⚠️ 未知配置项: {key}")
    
    def generate_vehicle_type_chart(self, sheet_name='车辆类型停车时长'):
        """
        生成车辆类型停车时长图表

        Args:
            sheet_name: str, 工作表名称

        Returns:
            str: 生成的HTML文件路径
        """
        if sheet_name not in self.excel_data:
            print(f"❌ 未找到工作表: {sheet_name}")
            return None

        data = self.excel_data[sheet_name]
        print(f"📈 生成车辆类型停车时长图表...")

        # 获取数据
        vehicle_types = data.iloc[:, 0].astype(str).tolist()  # 车辆类型
        avg_duration = data.iloc[:, 1].fillna(0).tolist()     # 平均停车时长

        # 创建柱状图
        bar = (
            Bar(init_opts=opts.InitOpts(
                theme=self.chart_config['theme'],
                width=self.chart_config['chart_width'],
                height=self.chart_config['chart_height']
            ))
            .add_xaxis(xaxis_data=vehicle_types)
            .add_yaxis(
                series_name="平均停车时长(小时)",
                y_axis=avg_duration,
                label_opts=opts.LabelOpts(is_show=True, position="top"),
                color=self.chart_config['bar_colors'][1],
            )
            .set_global_opts(
                title_opts=opts.TitleOpts(
                    title="不同车辆类型平均停车时长",
                    subtitle="显示各类车辆的平均停车时长对比",
                    pos_left="center"
                ),
                tooltip_opts=opts.TooltipOpts(
                    is_show=True,
                    trigger="axis",
                    formatter="{b}: {c}小时",
                    background_color="rgba(255, 255, 255, 0.95)",
                    border_color="#cccccc",
                    border_width=1,
                    textstyle_opts=opts.TextStyleOpts(
                        color="#333333",
                        font_size=12
                    )
                ),
                xaxis_opts=opts.AxisOpts(name="车辆类型"),
                yaxis_opts=opts.AxisOpts(
                    name="平均停车时长(小时)",
                    axislabel_opts=opts.LabelOpts(formatter="{value}h"),
                ),
            )
        )

        # 生成HTML文件
        output_file = os.path.join(self.output_dir, "车辆类型停车时长图表.html")
        bar.render(output_file)

        print(f"✅ 车辆类型停车时长图表已生成: {output_file}")
        return output_file



    def generate_occupancy_chart(self, sheet_name='在场车辆分布'):
        """
        生成在场车辆分布图表

        Args:
            sheet_name: str, 工作表名称

        Returns:
            str: 生成的HTML文件路径
        """
        if sheet_name not in self.excel_data:
            print(f"❌ 未找到工作表: {sheet_name}")
            return None

        data = self.excel_data[sheet_name]
        print(f"📈 生成在场车辆分布图表...")

        # 获取数据
        time_periods = data.iloc[:, 0].astype(str).tolist()  # 时间段
        occupancy_data = data.iloc[:, 1].fillna(0).tolist()  # 在场车辆数

        # 创建面积图
        line = (
            Line(init_opts=opts.InitOpts(
                theme=self.chart_config['theme'],
                width=self.chart_config['chart_width'],
                height=self.chart_config['chart_height']
            ))
            .add_xaxis(xaxis_data=time_periods)
            .add_yaxis(
                series_name="在场车辆数",
                y_axis=occupancy_data,
                is_smooth=True,
                areastyle_opts=opts.AreaStyleOpts(opacity=0.5),
                linestyle_opts=opts.LineStyleOpts(
                    width=self.chart_config['line_width'],
                    color=self.chart_config['line_colors'][1]
                ),
                label_opts=opts.LabelOpts(is_show=False),
                markpoint_opts=opts.MarkPointOpts(
                    data=[
                        opts.MarkPointItem(type_="max", name="峰值"),
                        opts.MarkPointItem(type_="min", name="谷值"),
                    ]
                ),
            )
            .set_global_opts(
                title_opts=opts.TitleOpts(
                    title="停车场在场车辆分布",
                    subtitle="显示不同时间段的在场车辆数量变化",
                    pos_left="center"
                ),
                tooltip_opts=opts.TooltipOpts(
                    is_show=True,
                    trigger="axis",
                    formatter="{b}: {c}辆",
                    background_color="rgba(255, 255, 255, 0.95)",
                    border_color="#cccccc",
                    border_width=1,
                    textstyle_opts=opts.TextStyleOpts(
                        color="#333333",
                        font_size=12
                    )
                ),
                xaxis_opts=opts.AxisOpts(
                    name="时间段",
                    axislabel_opts=opts.LabelOpts(rotate=45),
                ),
                yaxis_opts=opts.AxisOpts(
                    name="在场车辆数",
                    axislabel_opts=opts.LabelOpts(formatter="{value}"),
                ),
                datazoom_opts=[opts.DataZoomOpts(type_="slider")],
            )
        )

        # 生成HTML文件
        output_file = os.path.join(self.output_dir, "在场车辆分布图表.html")
        line.render(output_file)

        print(f"✅ 在场车辆分布图表已生成: {output_file}")
        return output_file

    def generate_all_charts(self):
        """生成所有可用的图表"""
        generated_files = []

        # 进出量时间分布图表 - 支持多种工作表名称
        traffic_flow_sheets = ['进出量时间分布', '进出量时间分布_分析日']
        for sheet_name in traffic_flow_sheets:
            if sheet_name in self.excel_data:
                print(f"📈 找到进出量时间分布数据: {sheet_name}")
                file_path = self.generate_traffic_flow_chart(sheet_name)
                if file_path:
                    generated_files.append(file_path)

                # 生成各类车辆类型的进出量时间分布图表
                vehicle_type_file = self.generate_vehicle_type_traffic_charts(sheet_name)
                if vehicle_type_file:
                    generated_files.append(vehicle_type_file)
                break  # 找到一个就停止

        # 进出量时间分布(按道闸)Timeline图表 - 支持多种工作表名称
        gate_flow_sheets = ['进出量时间分布(按道闸)', '进出量-道闸时间分布_分析日']
        for sheet_name in gate_flow_sheets:
            if sheet_name in self.excel_data:
                print(f"📈 找到道闸进出量时间分布数据: {sheet_name}")
                # 生成总量Timeline图表
                gate_timeline_file = self.generate_gate_traffic_timeline(sheet_name)
                if gate_timeline_file:
                    generated_files.append(gate_timeline_file)

                # 生成方向Timeline图表（进场和出场分别显示）
                gate_direction_file = self.generate_gate_traffic_direction_timeline(sheet_name)
                if gate_direction_file:
                    generated_files.append(gate_direction_file)

                # 生成进场占比Timeline饼图 - 已注释，因为综合分析页面已包含此功能
                # gate_entry_pie_file = self.generate_gate_entry_proportion_timeline(sheet_name)
                # if gate_entry_pie_file:
                #     generated_files.append(gate_entry_pie_file)

                # 生成出场占比Timeline饼图 - 已注释，因为综合分析页面已包含此功能
                # gate_exit_pie_file = self.generate_gate_exit_proportion_timeline(sheet_name)
                # if gate_exit_pie_file:
                #     generated_files.append(gate_exit_pie_file)

                # 生成综合分析页面（2x2布局）
                combined_analysis_file = self.generate_combined_gate_analysis(sheet_name)
                if combined_analysis_file:
                    generated_files.append(combined_analysis_file)
                break  # 找到一个就停止

        # 停车时长分布图表 - 支持多种工作表名称
        duration_sheets = ['停车时长分布', '停车时长_分析日']
        for sheet_name in duration_sheets:
            if sheet_name in self.excel_data:
                print(f"📈 找到停车时长分布数据: {sheet_name}")
                file_path = self.generate_parking_duration_chart(sheet_name)
                if file_path:
                    generated_files.append(file_path)
                break  # 找到一个就停止

        # 车辆类型停车时长图表 - 支持多种工作表名称
        vehicle_type_sheets = ['车辆类型停车时长', '车辆类型_分析日']
        for sheet_name in vehicle_type_sheets:
            if sheet_name in self.excel_data:
                print(f"📈 找到车辆类型停车时长数据: {sheet_name}")
                file_path = self.generate_vehicle_type_chart(sheet_name)
                if file_path:
                    generated_files.append(file_path)
                break  # 找到一个就停止

        # 在场车辆分布图表 - 已删除
        # if '在场车辆分布' in self.excel_data:
        #     print(f"📈 找到在场车辆分布数据: 在场车辆分布")
        #     file_path = self.generate_occupancy_chart()
        #     if file_path:
        #         generated_files.append(file_path)

        # 延停时长概率密度散点图
        if '延停时长概率密度' in self.excel_data:
            print(f"📈 找到延停时长概率密度数据: 延停时长概率密度")
            file_path = self.generate_duration_probability_density_scatter()
            if file_path:
                generated_files.append(file_path)

        print(f"🎉 共生成 {len(generated_files)} 个图表文件")
        return generated_files
    
    def generate_custom_chart(self, sheet_name, chart_type='bar', x_col=0, y_cols=[1],
                             title=None, subtitle=None, colors=None):
        """
        生成自定义图表

        Args:
            sheet_name: str, 工作表名称
            chart_type: str, 图表类型 ('bar', 'line', 'pie')
            x_col: int, X轴数据列索引
            y_cols: list, Y轴数据列索引列表
            title: str, 图表标题
            subtitle: str, 图表副标题
            colors: list, 自定义颜色列表

        Returns:
            str: 生成的HTML文件路径
        """
        if sheet_name not in self.excel_data:
            print(f"❌ 未找到工作表: {sheet_name}")
            return None

        data = self.excel_data[sheet_name]
        print(f"📈 生成自定义图表: {sheet_name} ({chart_type})")

        # 获取数据
        x_data = data.iloc[:, x_col].astype(str).tolist()

        # 默认标题
        if not title:
            title = f"{sheet_name}数据图表"
        if not subtitle:
            subtitle = f"基于{sheet_name}工作表数据生成"

        # 默认颜色
        if not colors:
            colors = self.chart_config['bar_colors'] if chart_type == 'bar' else self.chart_config['line_colors']

        if chart_type == 'bar':
            # 柱状图
            chart = (
                Bar(init_opts=opts.InitOpts(
                    theme=self.chart_config['theme'],
                    width=self.chart_config['chart_width'],
                    height=self.chart_config['chart_height']
                ))
                .add_xaxis(xaxis_data=x_data)
            )

            for i, y_col in enumerate(y_cols):
                y_data = data.iloc[:, y_col].fillna(0).tolist()
                series_name = data.columns[y_col] if y_col < len(data.columns) else f"数据{i+1}"
                color = colors[i % len(colors)]

                chart.add_yaxis(
                    series_name=series_name,
                    y_axis=y_data,
                    label_opts=opts.LabelOpts(is_show=False),
                    color=color,
                )

            chart.set_global_opts(
                title_opts=opts.TitleOpts(title=title, subtitle=subtitle, pos_left="center"),
                tooltip_opts=opts.TooltipOpts(
                    is_show=True,
                    trigger="axis",
                    background_color="rgba(255, 255, 255, 0.95)",
                    border_color="#cccccc",
                    border_width=1,
                    textstyle_opts=opts.TextStyleOpts(
                        color="#333333",
                        font_size=12
                    )
                ),
                xaxis_opts=opts.AxisOpts(axislabel_opts=opts.LabelOpts(rotate=45)),
                yaxis_opts=opts.AxisOpts(axislabel_opts=opts.LabelOpts(formatter="{value}")),
                datazoom_opts=[opts.DataZoomOpts(type_="slider")],
            )

        elif chart_type == 'line':
            # 折线图
            chart = (
                Line(init_opts=opts.InitOpts(
                    theme=self.chart_config['theme'],
                    width=self.chart_config['chart_width'],
                    height=self.chart_config['chart_height']
                ))
                .add_xaxis(xaxis_data=x_data)
            )

            for i, y_col in enumerate(y_cols):
                y_data = data.iloc[:, y_col].fillna(0).tolist()
                series_name = data.columns[y_col] if y_col < len(data.columns) else f"数据{i+1}"
                color = colors[i % len(colors)]

                chart.add_yaxis(
                    series_name=series_name,
                    y_axis=y_data,
                    is_smooth=True,
                    linestyle_opts=opts.LineStyleOpts(
                        width=self.chart_config['line_width'],
                        color=color
                    ),
                    label_opts=opts.LabelOpts(is_show=False),
                )

            chart.set_global_opts(
                title_opts=opts.TitleOpts(title=title, subtitle=subtitle, pos_left="center"),
                tooltip_opts=opts.TooltipOpts(
                    is_show=True,
                    trigger="axis",
                    background_color="rgba(255, 255, 255, 0.95)",
                    border_color="#cccccc",
                    border_width=1,
                    textstyle_opts=opts.TextStyleOpts(
                        color="#333333",
                        font_size=12
                    )
                ),
                xaxis_opts=opts.AxisOpts(axislabel_opts=opts.LabelOpts(rotate=45)),
                yaxis_opts=opts.AxisOpts(axislabel_opts=opts.LabelOpts(formatter="{value}")),
                datazoom_opts=[opts.DataZoomOpts(type_="slider")],
            )

        elif chart_type == 'pie':
            # 饼图（只使用第一个y列）
            y_col = y_cols[0]
            y_data = data.iloc[:, y_col].fillna(0).tolist()
            pie_data = [(x_data[i], y_data[i]) for i in range(len(x_data))]

            chart = (
                Pie(init_opts=opts.InitOpts(
                    theme=self.chart_config['theme'],
                    width=self.chart_config['chart_width'],
                    height=self.chart_config['chart_height']
                ))
                .add(
                    series_name="数据分布",
                    data_pair=pie_data,
                    radius=["40%", "75%"],
                    label_opts=opts.LabelOpts(position="outside"),
                )
                .set_global_opts(
                    title_opts=opts.TitleOpts(title=title, subtitle=subtitle, pos_left="center"),
                    legend_opts=opts.LegendOpts(pos_left="left", orient="vertical"),
                )
            )

        else:
            print(f"❌ 不支持的图表类型: {chart_type}")
            return None

        # 生成HTML文件
        safe_sheet_name = sheet_name.replace('/', '_').replace('\\', '_')
        output_file = os.path.join(self.output_dir, f"{safe_sheet_name}_{chart_type}图表.html")
        chart.render(output_file)

        print(f"✅ 自定义图表已生成: {output_file}")
        return output_file

    def generate_duration_probability_density_scatter(self, sheet_name='延停时长概率密度'):
        """
        生成延停时长概率密度散点图

        Args:
            sheet_name: str, 工作表名称，默认为'延停时长概率密度'

        Returns:
            str: 生成的HTML文件路径
        """
        try:
            print(f"📊 开始生成延停时长概率密度散点图...")

            # 检查工作表是否存在
            if sheet_name not in self.excel_data:
                print(f"❌ 工作表 '{sheet_name}' 不存在")
                return None

            data = self.excel_data[sheet_name]
            if data.empty:
                print(f"❌ 工作表 '{sheet_name}' 数据为空")
                return None

            print(f"📋 数据概览: {len(data)} 行 x {len(data.columns)} 列")
            print(f"📋 列名: {list(data.columns)}")

            # 检查必要的列
            required_cols = ['时长区间', '频数']
            missing_cols = [col for col in required_cols if col not in data.columns]
            if missing_cols:
                print(f"❌ 缺少必要列: {missing_cols}")
                return None

            # 获取车辆类型列（以_频数结尾的列）
            vtype_freq_cols = [col for col in data.columns if col.endswith('_频数') and col != '频数']
            vehicle_types = [col.replace('_频数', '') for col in vtype_freq_cols]

            if not vehicle_types:
                print(f"❌ 未找到车辆类型数据列")
                return None

            print(f"🚗 发现车辆类型: {vehicle_types}")

            # 准备散点图数据
            scatter_data = []
            time_periods = data['时长区间'].tolist()

            # 为每个车辆类型创建散点数据
            for i, vtype in enumerate(vehicle_types):
                freq_col = f'{vtype}_频数'
                if freq_col in data.columns:
                    for j, (period, freq) in enumerate(zip(time_periods, data[freq_col])):
                        if pd.notna(freq) and freq > 0:  # 只显示有数据的点
                            # 散点数据格式: [x轴索引, y轴频数, 车辆类型索引, 时长区间, 车辆类型]
                            scatter_data.append([j, freq, i, period, vtype])

            if not scatter_data:
                print(f"❌ 没有有效的散点数据")
                return None

            print(f"📊 准备散点数据: {len(scatter_data)} 个数据点")

            # 创建散点图
            scatter = (
                Scatter(init_opts=opts.InitOpts(
                    theme=self.chart_config['theme'],
                    width=self.chart_config['chart_width'],
                    height=self.chart_config['chart_height']
                ))
                .add_xaxis(time_periods)
                .add_yaxis(
                    "停车时长频度分布",
                    scatter_data,
                    symbol_size=20,
                    label_opts=opts.LabelOpts(
                        is_show=False,  # 不显示标签，避免图表过于拥挤
                    ),
                )
                .set_global_opts(
                    title_opts=opts.TitleOpts(
                        title="延停时长概率密度散点图",
                        subtitle="按车辆类型分类的停车时长频度分布",
                        pos_left="center"
                    ),
                    tooltip_opts=opts.TooltipOpts(
                        formatter=JsCode(
                            """
                            function (params) {
                                var data = params.value;
                                return '时长区间: ' + data[3] + '<br/>' +
                                       '车辆类型: ' + data[4] + '<br/>' +
                                       '频数: ' + data[1];
                            }
                            """
                        )
                    ),
                    xaxis_opts=opts.AxisOpts(
                        name="停车时长区间",
                        name_location="middle",
                        name_gap=30,
                        axislabel_opts=opts.LabelOpts(
                            rotate=45,  # 旋转标签避免重叠
                            font_size=10
                        )
                    ),
                    yaxis_opts=opts.AxisOpts(
                        name="频数",
                        name_location="middle",
                        name_gap=50
                    ),
                    visualmap_opts=opts.VisualMapOpts(
                        type_="color",
                        max_=len(vehicle_types) - 1,
                        min_=0,
                        dimension=2,  # 使用第3个维度（车辆类型索引）进行颜色映射
                        range_color=['#313695', '#4575b4', '#74add1', '#abd9e9', '#e0f3f8',
                                   '#ffffcc', '#fee090', '#fdae61', '#f46d43', '#d73027', '#a50026'],
                        pos_left="left",
                        pos_top="middle",
                        orient="vertical",
                        pieces=[
                            {"min": i, "max": i, "label": vtype, "color": self._get_vehicle_color(i)}
                            for i, vtype in enumerate(vehicle_types)
                        ]
                    ),
                    legend_opts=opts.LegendOpts(
                        is_show=False  # 使用visualmap代替图例
                    ),
                    datazoom_opts=[
                        opts.DataZoomOpts(
                            type_="slider",
                            range_start=0,
                            range_end=100,
                            orient="horizontal"
                        ),
                        opts.DataZoomOpts(
                            type_="slider",
                            range_start=0,
                            range_end=100,
                            orient="vertical"
                        )
                    ]
                )
            )

            # 生成输出文件名
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_file = os.path.join(self.output_dir, f"延停时长概率密度散点图_{timestamp}.html")

            # 渲染图表
            scatter.render(output_file)

            print(f"✅ 延停时长概率密度散点图已生成: {output_file}")
            return output_file

        except Exception as e:
            print(f"❌ 生成延停时长概率密度散点图失败: {str(e)}")
            import traceback
            traceback.print_exc()
            return None

    def _get_vehicle_color(self, index):
        """获取车辆类型对应的颜色"""
        colors = [
            '#1f77b4',  # 蓝色
            '#ff7f0e',  # 橙色
            '#2ca02c',  # 绿色
            '#d62728',  # 红色
            '#9467bd',  # 紫色
            '#8c564b',  # 棕色
            '#e377c2',  # 粉色
            '#7f7f7f',  # 灰色
            '#bcbd22',  # 橄榄色
            '#17becf'   # 青色
        ]
        return colors[index % len(colors)]

    def generate_dashboard(self):
        """
        生成综合仪表板（包含多个图表的HTML页面）

        Returns:
            str: 生成的HTML文件路径
        """
        print(f"📊 生成综合仪表板...")

        # 生成所有图表
        chart_files = self.generate_all_charts()

        if not chart_files:
            print("❌ 没有可用的图表数据")
            return None

        # 创建仪表板HTML
        dashboard_html = f"""
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>停车数据分析仪表板</title>
    <style>
        body {{
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }}
        .header {{
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }}
        .chart-container {{
            margin-bottom: 30px;
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            overflow: hidden;
        }}
        .chart-title {{
            padding: 15px 20px;
            background-color: #4CAF50;
            color: white;
            margin: 0;
            font-size: 18px;
        }}
        iframe {{
            width: 100%;
            height: 650px;
            border: none;
        }}
        .footer {{
            text-align: center;
            margin-top: 30px;
            padding: 20px;
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            color: #666;
        }}
    </style>
</head>
<body>
    <div class="header">
        <h1>🚗 停车数据分析仪表板</h1>
        <p>生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
        <p>数据来源: {os.path.basename(self.excel_file_path)}</p>
    </div>
"""

        # 添加每个图表
        chart_titles = {
            "进出量时间分布_总量.html": "📈 进出量时间分布",
            "停车时长分布图表.html": "⏱️ 停车时长分布",
            "车辆类型停车时长图表.html": "🚙 车辆类型停车时长"
        }

        for chart_file in chart_files:
            chart_filename = os.path.basename(chart_file)
            chart_title = chart_titles.get(chart_filename, chart_filename.replace('.html', ''))

            dashboard_html += f"""
    <div class="chart-container">
        <h2 class="chart-title">{chart_title}</h2>
        <iframe src="{chart_filename}"></iframe>
    </div>
"""

        dashboard_html += f"""
    <div class="footer">
        <p>📊 共包含 {len(chart_files)} 个图表 | 🔧 由停车数据图表生成器创建</p>
    </div>
</body>
</html>
"""

        # 保存仪表板文件
        dashboard_file = os.path.join(self.output_dir, "停车数据分析仪表板.html")
        with open(dashboard_file, 'w', encoding='utf-8') as f:
            f.write(dashboard_html)

        print(f"✅ 综合仪表板已生成: {dashboard_file}")
        return dashboard_file

    def list_available_sheets(self):
        """列出可用的工作表"""
        print("📋 可用的工作表:")
        for i, sheet_name in enumerate(self.excel_data.keys(), 1):
            row_count = len(self.excel_data[sheet_name])
            col_count = len(self.excel_data[sheet_name].columns)
            print(f"  {i}. {sheet_name} ({row_count}行 x {col_count}列)")

            # 显示前几列的列名
            if col_count > 0:
                columns = list(self.excel_data[sheet_name].columns)[:5]  # 显示前5列
                columns_str = ', '.join([str(col) for col in columns])
                if col_count > 5:
                    columns_str += f", ... (共{col_count}列)"
                print(f"     列名: {columns_str}")





def demo_custom_usage():
    """演示自定义用法"""
    print("🎯 自定义用法示例:")
    print("""
# 1. 基本用法
chart_generator = ParkingChartGenerator('your_excel_file.xlsx')
chart_generator.generate_all_charts()

# 2. 自定义样式
chart_generator.update_chart_config(
    bar_colors=['#FF6B6B', '#4ECDC4', '#45B7D1'],
    line_colors=['#96CEB4', '#FFEAA7', '#DDA0DD'],
    line_width=5,
    chart_width='1600px',
    chart_height='800px'
)

# 3. 生成特定图表
chart_generator.generate_traffic_flow_chart()
chart_generator.generate_parking_duration_chart()

# 4. 生成自定义图表
chart_generator.generate_custom_chart(
    sheet_name='进出量时间分布',
    chart_type='bar',
    x_col=0,
    y_cols=[1, 2, 3],
    title='自定义标题',
    colors=['#FF6B6B', '#4ECDC4', '#45B7D1']
)

# 5. 生成综合仪表板
chart_generator.generate_dashboard()
""")


if __name__ == "__main__":
    # ==================== 配置参数区域 ====================
    # 📁 文件路径配置
    EXCEL_FILE_PATH = r"C:\Users\<USER>\Desktop\停车分析\义乌正泰_北门_合并_20250623_230242_analysis_20250626_003636.xlsx"  # Excel输入文件路径
    OUTPUT_DIR = None  # 输出目录，None表示使用Excel文件所在目录，也可指定如: r"C:\output"

    # 🎨 图表样式配置
    CHART_THEME = 'MACARONS'  # 图表主题: LIGHT, DARK, CHALK, MACARONS, PURPLE_PASSION, ROMA, SHINE, VINTAGE等
    BAR_COLORS = ['#1f77b4', '#ff7f0e', '#2ca02c', '#d62728', '#9467bd']  # 柱状图颜色列表
    LINE_COLORS = ['#d62728', '#9467bd', '#8c564b', '#e377c2', '#7f7f7f']  # 线条颜色列表
    LINE_WIDTH = 4  # 线条粗细 (1-10)
    CHART_WIDTH = '1400px'  # 图表宽度
    CHART_HEIGHT = '700px'  # 图表高度

    # 📊 图表生成配置
    GENERATE_ALL_CHARTS = True  # 是否生成所有预定义图表
    GENERATE_DASHBOARD = True  # 是否生成综合仪表板
    GENERATE_CUSTOM_CHARTS = True  # 是否生成自定义示例图表
    AUTO_OPEN_DASHBOARD = False  # 是否自动打开仪表板 (True/False)

    # 🔧 自定义图表配置
    CUSTOM_CHARTS_CONFIG = [
        # 注释掉单独的bar和line图表配置，只保留组合图表
        # {
        #     'sheet_name': '进出量时间分布',
        #     'chart_type': 'bar',
        #     'x_col': 0,
        #     'y_cols': [1, 2],
        #     'title': '进出量对比柱状图',
        #     'subtitle': '显示进场和出场数量对比',
        #     'colors': ['#FF6B6B', '#4ECDC4']
        # },
        # {
        #     'sheet_name': '进出量时间分布',
        #     'chart_type': 'line',
        #     'x_col': 0,
        #     'y_cols': [3],
        #     'title': '总流量趋势折线图',
        #     'subtitle': '显示总流量变化趋势',
        #     'colors': ['#FF9F43']
        # }
    ]

    # ==================== 执行区域 ====================
    print("🚗 停车数据图表生成器")
    print("=" * 60)
    print(f"📁 输入文件: {EXCEL_FILE_PATH}")
    print(f"📁 输出目录: {OUTPUT_DIR or '与Excel文件同目录'}")
    print(f"🎨 图表主题: {CHART_THEME}")
    print(f"📏 图表尺寸: {CHART_WIDTH} x {CHART_HEIGHT}")
    print(f"🖌️ 线条粗细: {LINE_WIDTH}px")
    print("=" * 60)

    # 检查文件是否存在
    import os
    if not os.path.exists(EXCEL_FILE_PATH):
        print(f"❌ Excel文件不存在: {EXCEL_FILE_PATH}")
        print("请修改 EXCEL_FILE_PATH 变量为正确的文件路径")
        input("按回车键退出...")
        exit(1)

    try:
        # 创建图表生成器
        from pyecharts.globals import ThemeType

        # 主题映射
        theme_map = {
            'LIGHT': ThemeType.LIGHT,
            'DARK': ThemeType.DARK,
            'CHALK': ThemeType.CHALK,
            'ESSOS': ThemeType.ESSOS,
            'INFOGRAPHIC': ThemeType.INFOGRAPHIC,
            'MACARONS': ThemeType.MACARONS,
            'PURPLE_PASSION': ThemeType.PURPLE_PASSION,
            'ROMA': ThemeType.ROMA,
            'ROMANTIC': ThemeType.ROMANTIC,
            'SHINE': ThemeType.SHINE,
            'VINTAGE': ThemeType.VINTAGE,
            'WALDEN': ThemeType.WALDEN,
            'WESTEROS': ThemeType.WESTEROS,
            'WONDERLAND': ThemeType.WONDERLAND
        }

        selected_theme = theme_map.get(CHART_THEME, ThemeType.MACARONS)

        chart_generator = ParkingChartGenerator(EXCEL_FILE_PATH, OUTPUT_DIR)

        # 列出可用的工作表
        print("\n📋 Excel文件中的工作表:")
        chart_generator.list_available_sheets()

        print(f"\n🎨 应用自定义样式配置...")
        # 应用自定义样式
        chart_generator.update_chart_config(
            theme=selected_theme,
            bar_colors=BAR_COLORS,
            line_colors=LINE_COLORS,
            line_width=LINE_WIDTH,
            chart_width=CHART_WIDTH,
            chart_height=CHART_HEIGHT
        )

        generated_files = []

        # 生成预定义图表
        if GENERATE_ALL_CHARTS:
            print(f"\n📊 生成预定义图表...")
            files = chart_generator.generate_all_charts()
            generated_files.extend(files)

        # 生成自定义图表
        if GENERATE_CUSTOM_CHARTS and CUSTOM_CHARTS_CONFIG:
            print(f"\n🔧 生成自定义图表...")
            for config in CUSTOM_CHARTS_CONFIG:
                try:
                    file_path = chart_generator.generate_custom_chart(**config)
                    if file_path:
                        generated_files.append(file_path)
                        print(f"  ✅ {config['title']}")
                except Exception as e:
                    print(f"  ❌ {config['title']}: {str(e)}")

        # 生成综合仪表板
        if GENERATE_DASHBOARD:
            print(f"\n🏠 生成综合仪表板...")
            dashboard_file = chart_generator.generate_dashboard()
            if dashboard_file:
                generated_files.append(dashboard_file)

        # 显示结果
        print(f"\n🎉 图表生成完成!")
        print("=" * 60)
        if generated_files:
            print(f"📊 共生成 {len(generated_files)} 个文件:")
            for i, file_path in enumerate(generated_files, 1):
                file_name = os.path.basename(file_path)
                file_size = os.path.getsize(file_path) / 1024  # KB
                print(f"  {i:2d}. {file_name} ({file_size:.1f}KB)")

            print(f"\n📁 输出目录: {chart_generator.output_dir}")

            # 自动打开仪表板
            if AUTO_OPEN_DASHBOARD:
                dashboard_path = os.path.join(chart_generator.output_dir, "停车数据分析仪表板.html")
                if os.path.exists(dashboard_path):
                    import webbrowser
                    webbrowser.open(f'file://{os.path.abspath(dashboard_path)}')
                    print("🌐 仪表板已自动在浏览器中打开")
            else:
                print("\n💡 提示: 双击 '停车数据分析仪表板.html' 查看所有图表")

    except Exception as e:
        print(f"❌ 生成图表时发生错误: {str(e)}")
        import traceback
        traceback.print_exc()
        input("按回车键退出...")

    print("\n👋 程序执行完成")
