<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Awesome-pyecharts</title>
                <script type="text/javascript" src="https://assets.pyecharts.org/assets/v5/echarts.min.js"></script>
            <script type="text/javascript" src="https://assets.pyecharts.org/assets/v5/themes/macarons.js"></script>

    
</head>
<body >
    <div id="211992591914494d94b34623429345f9" class="chart-container" style="width:1200px; height:600px; "></div>
    <script>
        var chart_211992591914494d94b34623429345f9 = echarts.init(
            document.getElementById('211992591914494d94b34623429345f9'), 'macarons', {renderer: 'canvas'});
        var option_211992591914494d94b34623429345f9 = {
    "animation": true,
    "animationThreshold": 2000,
    "animationDuration": 1000,
    "animationEasing": "cubicOut",
    "animationDelay": 0,
    "animationDurationUpdate": 300,
    "animationEasingUpdate": "cubicOut",
    "animationDelayUpdate": 0,
    "aria": {
        "enabled": false
    },
    "series": [
        {
            "type": "bar",
            "name": "\u8fdb\u573a\u6570\u91cf",
            "legendHoverLink": true,
            "data": [
                3,
                3,
                4,
                3,
                3,
                4,
                3,
                3,
                8,
                6,
                6,
                8,
                6,
                6,
                8,
                6,
                6,
                4,
                3,
                3,
                4,
                3,
                3,
                4
            ],
            "realtimeSort": false,
            "showBackground": false,
            "stackStrategy": "samesign",
            "cursor": "pointer",
            "barMinHeight": 0,
            "barCategoryGap": "20%",
            "barGap": "30%",
            "large": false,
            "largeThreshold": 400,
            "seriesLayoutBy": "column",
            "datasetIndex": 0,
            "clip": true,
            "zlevel": 0,
            "z": 2,
            "label": {
                "show": false,
                "margin": 8,
                "valueAnimation": false
            },
            "itemStyle": {
                "color": "#2E86AB",
                "borderColor": "#ffffff",
                "borderWidth": 1
            }
        },
        {
            "type": "bar",
            "name": "\u51fa\u573a\u6570\u91cf",
            "legendHoverLink": true,
            "data": [
                1,
                1,
                2,
                6,
                8,
                0,
                5,
                1,
                8,
                10,
                2,
                7,
                6,
                7,
                4,
                3,
                3,
                5,
                2,
                6,
                10,
                4,
                7,
                2
            ],
            "realtimeSort": false,
            "showBackground": false,
            "stackStrategy": "samesign",
            "cursor": "pointer",
            "barMinHeight": 0,
            "barCategoryGap": "20%",
            "barGap": "30%",
            "large": false,
            "largeThreshold": 400,
            "seriesLayoutBy": "column",
            "datasetIndex": 0,
            "clip": true,
            "zlevel": 0,
            "z": 2,
            "label": {
                "show": false,
                "margin": 8,
                "valueAnimation": false
            },
            "itemStyle": {
                "color": "#A23B72",
                "borderColor": "#ffffff",
                "borderWidth": 1
            }
        },
        {
            "type": "line",
            "name": "\u603b\u6d41\u91cf",
            "connectNulls": false,
            "xAxisIndex": 0,
            "symbolSize": 4,
            "showSymbol": true,
            "smooth": false,
            "clip": true,
            "step": false,
            "stackStrategy": "samesign",
            "data": [
                [
                    "00:00-01:00",
                    4
                ],
                [
                    "01:00-02:00",
                    4
                ],
                [
                    "02:00-03:00",
                    6
                ],
                [
                    "03:00-04:00",
                    9
                ],
                [
                    "04:00-05:00",
                    11
                ],
                [
                    "05:00-06:00",
                    4
                ],
                [
                    "06:00-07:00",
                    8
                ],
                [
                    "07:00-08:00",
                    4
                ],
                [
                    "08:00-09:00",
                    16
                ],
                [
                    "09:00-10:00",
                    16
                ],
                [
                    "10:00-11:00",
                    8
                ],
                [
                    "11:00-12:00",
                    15
                ],
                [
                    "12:00-13:00",
                    12
                ],
                [
                    "13:00-14:00",
                    13
                ],
                [
                    "14:00-15:00",
                    12
                ],
                [
                    "15:00-16:00",
                    9
                ],
                [
                    "16:00-17:00",
                    9
                ],
                [
                    "17:00-18:00",
                    9
                ],
                [
                    "18:00-19:00",
                    5
                ],
                [
                    "19:00-20:00",
                    9
                ],
                [
                    "20:00-21:00",
                    14
                ],
                [
                    "21:00-22:00",
                    7
                ],
                [
                    "22:00-23:00",
                    10
                ],
                [
                    "23:00-23:59",
                    6
                ]
            ],
            "hoverAnimation": true,
            "label": {
                "show": false,
                "margin": 8,
                "valueAnimation": false
            },
            "logBase": 10,
            "seriesLayoutBy": "column",
            "lineStyle": {
                "show": true,
                "width": 4,
                "opacity": 1,
                "curveness": 0,
                "type": "solid",
                "color": "#F18F01"
            },
            "areaStyle": {
                "opacity": 0
            },
            "markPoint": {
                "label": {
                    "show": true,
                    "position": "inside",
                    "color": "#fff",
                    "margin": 8,
                    "valueAnimation": false
                },
                "data": [
                    {
                        "name": "\u5cf0\u503c",
                        "type": "max",
                        "itemStyle": {
                            "color": "#F18F01"
                        }
                    },
                    {
                        "name": "\u8c37\u503c",
                        "type": "min",
                        "itemStyle": {
                            "color": "#F18F01"
                        }
                    }
                ]
            },
            "markLine": {
                "silent": false,
                "precision": 2,
                "label": {
                    "show": true,
                    "margin": 8,
                    "valueAnimation": false
                },
                "data": [
                    {
                        "name": "\u5e73\u5747\u503c",
                        "type": "average",
                        "lineStyle": {
                            "show": true,
                            "width": 2,
                            "opacity": 1,
                            "curveness": 0,
                            "type": "dashed",
                            "color": "#F18F01"
                        }
                    }
                ]
            },
            "itemStyle": {
                "color": "#F18F01",
                "borderColor": "#F18F01",
                "borderWidth": 2
            },
            "zlevel": 0,
            "z": 0
        }
    ],
    "legend": [
        {
            "data": [
                "\u8fdb\u573a\u6570\u91cf",
                "\u51fa\u573a\u6570\u91cf",
                "\u603b\u6d41\u91cf"
            ],
            "selected": {},
            "show": true,
            "top": "8%",
            "padding": 5,
            "itemGap": 20,
            "itemWidth": 25,
            "itemHeight": 14,
            "textStyle": {
                "fontWeight": "bold",
                "fontSize": 12
            },
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderRadius": 0,
            "pageButtonItemGap": 5,
            "pageButtonPosition": "end",
            "pageFormatter": "{current}/{total}",
            "pageIconColor": "#2f4554",
            "pageIconInactiveColor": "#aaa",
            "pageIconSize": 15,
            "animationDurationUpdate": 800,
            "selector": false,
            "selectorPosition": "auto",
            "selectorItemGap": 7,
            "selectorButtonGap": 10
        }
    ],
    "tooltip": {
        "show": true,
        "trigger": "axis",
        "triggerOn": "mousemove|click",
        "axisPointer": {
            "type": "cross"
        },
        "showContent": true,
        "alwaysShowContent": false,
        "showDelay": 0,
        "hideDelay": 100,
        "enterable": false,
        "confine": false,
        "appendToBody": false,
        "transitionDuration": 0.4,
        "formatter": "{b}<br/>{a0}: {c0}<br/>{a1}: {c1}<br/>{a2}: {c2}",
        "textStyle": {
            "color": "#333333",
            "fontWeight": "normal",
            "fontSize": 12
        },
        "backgroundColor": "rgba(255, 255, 255, 0.95)",
        "borderColor": "#cccccc",
        "borderWidth": 1,
        "padding": 5,
        "order": "seriesAsc"
    },
    "xAxis": [
        {
            "type": "category",
            "name": "\u65f6\u95f4\u6bb5",
            "show": true,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "axisLabel": {
                "show": true,
                "rotate": 45,
                "margin": 8,
                "valueAnimation": false
            },
            "axisPointer": {
                "show": true,
                "type": "shadow",
                "triggerTooltip": true,
                "triggerOn": "mousemove|click"
            },
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0,
            "data": [
                "00:00-01:00",
                "01:00-02:00",
                "02:00-03:00",
                "03:00-04:00",
                "04:00-05:00",
                "05:00-06:00",
                "06:00-07:00",
                "07:00-08:00",
                "08:00-09:00",
                "09:00-10:00",
                "10:00-11:00",
                "11:00-12:00",
                "12:00-13:00",
                "13:00-14:00",
                "14:00-15:00",
                "15:00-16:00",
                "16:00-17:00",
                "17:00-18:00",
                "18:00-19:00",
                "19:00-20:00",
                "20:00-21:00",
                "21:00-22:00",
                "22:00-23:00",
                "23:00-23:59"
            ]
        }
    ],
    "yAxis": [
        {
            "type": "value",
            "name": "\u8f66\u8f86\u6570\u91cf",
            "show": true,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "axisTick": {
                "show": true,
                "alignWithLabel": false,
                "inside": false
            },
            "axisLabel": {
                "show": true,
                "margin": 8,
                "formatter": "{value}",
                "valueAnimation": false
            },
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "min": 0,
            "max": 19.2,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0
        }
    ],
    "title": [
        {
            "show": true,
            "text": "\u505c\u8f66\u573a\u8fdb\u51fa\u91cf\u65f6\u95f4\u5206\u5e03",
            "target": "blank",
            "subtext": "\u67f1\u72b6\u56fe\u663e\u793a\u8fdb\u51fa\u573a\u6570\u91cf\uff0c\u6298\u7ebf\u56fe\u663e\u793a\u603b\u6d41\u91cf\u8d8b\u52bf",
            "subtarget": "blank",
            "left": "center",
            "padding": 5,
            "itemGap": 10,
            "textAlign": "auto",
            "textVerticalAlign": "auto",
            "triggerEvent": false,
            "textStyle": {
                "color": "#2c3e50",
                "fontWeight": "bold",
                "fontSize": 20
            },
            "subtextStyle": {
                "color": "#7f8c8d",
                "fontSize": 14
            }
        }
    ],
    "dataZoom": [
        {
            "show": true,
            "type": "slider",
            "showDetail": true,
            "showDataShadow": true,
            "realtime": true,
            "start": 0,
            "end": 100,
            "orient": "horizontal",
            "zoomLock": false,
            "filterMode": "filter"
        },
        {
            "show": true,
            "type": "inside",
            "showDetail": true,
            "showDataShadow": true,
            "realtime": true,
            "start": 20,
            "end": 80,
            "orient": "horizontal",
            "zoomLock": false,
            "filterMode": "filter",
            "disabled": false,
            "zoomOnMouseWheel": true,
            "moveOnMouseMove": true,
            "moveOnMouseWheel": true,
            "preventDefaultMouseMove": true
        }
    ]
};
        chart_211992591914494d94b34623429345f9.setOption(option_211992591914494d94b34623429345f9);
    </script>
</body>
</html>
