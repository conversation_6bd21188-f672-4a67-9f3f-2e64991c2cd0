import pandas as pd
from parking_data_base import ParkingDataBase
import logging

# 设置日志
logging.basicConfig(level=logging.DEBUG, format='%(levelname)s:%(name)s:%(message)s')
logger = logging.getLogger(__name__)

# 读取实际数据
csv_file = r'C:\Users\<USER>\Desktop\停车分析\数据\正泰\义乌正泰_北门_合并_20250623_230242.csv'

try:
    data = pd.read_csv(csv_file, encoding='utf-8')
except:
    try:
        data = pd.read_csv(csv_file, encoding='gbk')
    except:
        data = pd.read_csv(csv_file, encoding='gb2312')

print(f"原始数据: {len(data)} 条记录")
print(f"列名: {list(data.columns)}")
print("前5行数据:")
print(data.head())

# 设置参数
params = {
    'mode': 'mode1',
    'input_file': csv_file,
    'output': r'C:\Users\<USER>\Desktop\停车分析',
    'date': '2024-12-25',
    'time_interval': 60,
    'time_slip': 30,
}

MODE_CONFIG = {
    'mode1': {
        '车辆唯一标识字段': '车牌',
        '车辆类型字段': '车辆类型',
        '时间记录字段': '时间',
        '进出类型字段': '方向',
        '进出标识值': ('进', '出'),
        '道闸编号字段': '出入口'
    }
}

# 合并配置
params.update(MODE_CONFIG)

# 创建处理器
print(f"参数: {params}")
processor = ParkingDataBase(data, params, logger)

# 处理数据
try:
    print(f"处理器参数: {processor.params}")
    processor.process()
    print(f"\n处理后数据: {len(processor.processed_data)} 条记录")
    
    if len(processor.processed_data) > 0:
        print("处理后的前5行:")
        print(processor.processed_data.head())
        
        # 统计车辆数量
        unique_vehicles = processor.processed_data['vid'].nunique()
        print(f"\n唯一车辆数: {unique_vehicles}")
        
        # 统计停车时长分布
        duration_stats = processor.processed_data['duration'].describe()
        print(f"\n停车时长统计:")
        print(duration_stats)
    else:
        print("❌ 处理后数据为空")
        
        # 分析原因
        print("\n分析原因:")
        print("检查原始数据中的方向字段值:")
        if '方向' in data.columns:
            direction_counts = data['方向'].value_counts()
            print(direction_counts)
        
        print("\n检查车牌字段:")
        if '车牌' in data.columns:
            vehicle_counts = data['车牌'].value_counts().head(10)
            print(f"前10个车牌的记录数:")
            print(vehicle_counts)
            
            # 选择一个车牌进行详细分析
            sample_vehicle = vehicle_counts.index[0]
            sample_data = data[data['车牌'] == sample_vehicle].sort_values('时间')
            print(f"\n车牌 {sample_vehicle} 的记录:")
            print(sample_data[['时间', '方向', '出入口']].head(10))
        
except Exception as e:
    print(f"处理失败: {e}")
    import traceback
    traceback.print_exc()
