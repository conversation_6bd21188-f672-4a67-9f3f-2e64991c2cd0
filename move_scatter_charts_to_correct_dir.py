#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
将散点图移动到正确的目录并重新生成
"""

import os
import shutil
import pandas as pd
from parking_chart_generator import ParkingChartGenerator

def check_current_html_files():
    """检查当前HTML文件的分布"""
    print("🔍 检查HTML文件分布\n")
    
    # 检查开发目录
    dev_dir = "."
    dev_html_files = [f for f in os.listdir(dev_dir) if f.endswith('.html')]
    
    # 检查停车分析根目录
    root_dir = r"C:\Users\<USER>\Desktop\停车分析"
    root_html_files = []
    if os.path.exists(root_dir):
        root_html_files = [f for f in os.listdir(root_dir) if f.endswith('.html')]
    
    print(f"📁 开发目录 ({dev_dir}):")
    if dev_html_files:
        for i, file in enumerate(dev_html_files, 1):
            file_size = os.path.getsize(file)
            print(f"   {i}. {file} ({file_size:,} 字节)")
    else:
        print(f"   (无HTML文件)")
    
    print(f"\n📁 停车分析根目录 ({root_dir}):")
    if root_html_files:
        for i, file in enumerate(root_html_files, 1):
            file_path = os.path.join(root_dir, file)
            file_size = os.path.getsize(file_path)
            print(f"   {i}. {file} ({file_size:,} 字节)")
    else:
        print(f"   (无HTML文件)")
    
    return dev_html_files, root_html_files, root_dir

def move_existing_scatter_charts(dev_html_files, root_dir):
    """移动现有的散点图文件"""
    print(f"\n📦 移动现有散点图文件\n")
    
    scatter_files = [f for f in dev_html_files if '散点图' in f]
    
    if not scatter_files:
        print(f"   ⏭️  没有找到需要移动的散点图文件")
        return []
    
    moved_files = []
    for file in scatter_files:
        src_path = file
        dst_path = os.path.join(root_dir, file)
        
        try:
            # 如果目标文件已存在，先删除
            if os.path.exists(dst_path):
                os.remove(dst_path)
                print(f"   🗑️  删除已存在的文件: {file}")
            
            # 移动文件
            shutil.move(src_path, dst_path)
            print(f"   ✅ 移动成功: {file}")
            moved_files.append(file)
            
        except Exception as e:
            print(f"   ❌ 移动失败 {file}: {str(e)}")
    
    return moved_files

def regenerate_scatter_charts_in_correct_dir():
    """在正确的目录重新生成散点图"""
    print(f"\n🔄 在正确目录重新生成散点图\n")
    
    # 查找Excel文件
    excel_files = [f for f in os.listdir('.') if f.endswith('.xlsx') and not f.startswith('~')]
    
    if not excel_files:
        print(f"❌ 未找到Excel文件")
        return []
    
    # 选择最佳Excel文件
    best_file = None
    best_score = 0
    
    for excel_file in excel_files:
        try:
            with pd.ExcelFile(excel_file) as xls:
                sheet_names = xls.sheet_names
                score = 0
                
                if '延停时长概率密度' in sheet_names:
                    score += 10
                if '停车时长频率分布' in sheet_names:
                    score += 10
                
                if score > best_score:
                    best_score = score
                    best_file = excel_file
        except:
            continue
    
    if not best_file:
        print(f"❌ 未找到合适的Excel文件")
        return []
    
    print(f"📊 使用Excel文件: {best_file}")
    
    try:
        # 创建图表生成器（现在会自动使用正确的输出目录）
        chart_generator = ParkingChartGenerator(best_file)
        
        print(f"📁 输出目录: {chart_generator.output_dir}")
        
        generated_files = []
        
        # 生成延停时长概率密度散点图
        if '延停时长概率密度' in chart_generator.excel_data:
            print(f"📈 生成延停时长概率密度散点图...")
            file1 = chart_generator.generate_duration_probability_density_scatter()
            if file1:
                generated_files.append(file1)
                print(f"   ✅ 生成成功: {os.path.basename(file1)}")
        
        # 生成停车时长频率分布散点图
        if '停车时长频率分布' in chart_generator.excel_data:
            print(f"📈 生成停车时长频率分布散点图...")
            file2 = chart_generator.generate_duration_frequency_scatter()
            if file2:
                generated_files.append(file2)
                print(f"   ✅ 生成成功: {os.path.basename(file2)}")
        
        return generated_files
        
    except Exception as e:
        print(f"❌ 重新生成散点图时出错: {str(e)}")
        import traceback
        traceback.print_exc()
        return []

def verify_final_result(root_dir):
    """验证最终结果"""
    print(f"\n✅ 验证最终结果\n")
    
    if not os.path.exists(root_dir):
        print(f"❌ 目标目录不存在: {root_dir}")
        return
    
    html_files = [f for f in os.listdir(root_dir) if f.endswith('.html')]
    scatter_files = [f for f in html_files if '散点图' in f]
    
    print(f"📁 停车分析根目录 ({root_dir}):")
    print(f"   总HTML文件: {len(html_files)} 个")
    print(f"   散点图文件: {len(scatter_files)} 个")
    
    if scatter_files:
        print(f"\n📊 散点图文件详情:")
        for i, file in enumerate(scatter_files, 1):
            file_path = os.path.join(root_dir, file)
            file_size = os.path.getsize(file_path)
            print(f"   {i}. {file} ({file_size:,} 字节)")
    
    # 检查开发目录是否还有散点图文件
    dev_scatter_files = [f for f in os.listdir('.') if f.endswith('.html') and '散点图' in f]
    if dev_scatter_files:
        print(f"\n⚠️  开发目录仍有散点图文件:")
        for file in dev_scatter_files:
            print(f"   - {file}")
    else:
        print(f"\n✅ 开发目录已清理完毕")

def main():
    """主函数"""
    print("🎯 将散点图移动到正确目录\n")
    
    # 1. 检查当前HTML文件分布
    dev_html_files, root_html_files, root_dir = check_current_html_files()
    
    # 2. 移动现有的散点图文件
    moved_files = move_existing_scatter_charts(dev_html_files, root_dir)
    
    # 3. 在正确目录重新生成散点图
    generated_files = regenerate_scatter_charts_in_correct_dir()
    
    # 4. 验证最终结果
    verify_final_result(root_dir)
    
    # 5. 总结
    print(f"\n{'='*60}")
    print(f"🎉 操作完成!")
    
    if moved_files:
        print(f"📦 移动文件: {len(moved_files)} 个")
        for file in moved_files:
            print(f"   - {file}")
    
    if generated_files:
        print(f"🔄 重新生成: {len(generated_files)} 个")
        for file in generated_files:
            print(f"   - {os.path.basename(file)}")
    
    print(f"\n📁 所有HTML文件现在都保存在: {root_dir}")
    print(f"💡 可以在浏览器中打开查看效果")
    print(f"{'='*60}")

if __name__ == "__main__":
    main()
