# 🥧 饼图5%阈值处理功能说明

## 🎯 功能概览

在出入口进场占比Timeline饼图的基础上，新增5%阈值处理功能，将占比小于5%的出入口自动合并到"其他出入口"中，突出显示主要出入口，提升图表的可读性和重点突出效果。

## 🔄 处理对比

### 处理前（原始数据）
- **显示方式**：所有出入口都单独显示
- **视觉效果**：可能有很多小扇区
- **问题**：主要出入口不够突出，图例项目过多
- **适用场景**：详细数据分析

### 处理后（5%阈值处理）
- **显示方式**：占比>=5%单独显示，占比<5%合并显示
- **视觉效果**：主要出入口清晰突出
- **优势**：重点明确，图例简洁
- **适用场景**：管理汇报、重点分析

## 🔧 技术实现

### 核心处理逻辑
```python
# 数据预处理：将占比小于5%的出入口合并到"其他出入口"
threshold_percentage = 5.0  # 5%阈值
threshold_value = total_entry * (threshold_percentage / 100.0)

main_gates = []  # 主要出入口（占比>=5%）
other_gates_total = 0  # 其他出入口的总量

for gate_name, value in raw_entry_data:
    if value >= threshold_value:
        main_gates.append([gate_name, value])  # 保留
    else:
        other_gates_total += value  # 合并

# 添加"其他出入口"项
if other_gates_total > 0:
    period_entry_data.append(["其他出入口", other_gates_total])
```

### 颜色分配策略
```python
def _get_pie_colors_for_period(self, period_entry_data, pie_colors, other_gate_color):
    """动态分配颜色，"其他出入口"使用特殊颜色"""
    colors = []
    for gate_name, _ in period_entry_data:
        if gate_name == "其他出入口":
            colors.append("#95A5A6")  # 中性灰色
        else:
            colors.append(pie_colors[color_index])  # 专业彩色
    return colors
```

## 📊 处理效果

### 1. 数据分类
- **主要出入口**：占比>=5%，单独显示
- **其他出入口**：占比<5%，合并显示
- **阈值设定**：5%（可根据需要调整）

### 2. 视觉层次
- **主要信息**：使用专业演讲风格彩色
- **次要信息**：使用中性灰色 (#95A5A6)
- **视觉焦点**：自动聚焦到主要出入口

### 3. 信息保持
- **数据完整性**：所有数据都被保留
- **占比准确性**：合并后的占比计算准确
- **可追溯性**：控制台输出合并详情

## 🎨 视觉设计

### 1. 颜色配置
```python
# 主要出入口颜色（专业演讲风格）
pie_colors = [
    "#2E86AB", "#A23B72", "#F18F01", "#148F77", "#7D3C98", 
    "#2874A6", "#117A65", "#5B2C6F", "#B7950B", "#D35400"
]

# "其他出入口"颜色（中性灰色）
other_gate_color = "#95A5A6"
```

### 2. 标签显示
- **主要出入口**：`{出入口名}: {数量}辆\n({占比}%)`
- **其他出入口**：`其他出入口: {合并总量}辆\n({合并占比}%)`
- **位置**：外部显示，清晰可读

### 3. 图例优化
- **主要项目**：使用彩色图标
- **其他项目**：使用灰色图标
- **排列方式**：左侧垂直排列

## 💡 应用价值

### 1. 管理决策
- **重点识别**：快速识别关键出入口
- **资源配置**：重点关注主要出入口
- **效率提升**：避免被次要信息干扰

### 2. 演示效果
- **视觉清晰**：图表更加清晰专业
- **重点突出**：主要信息一目了然
- **专业外观**：符合商务演示标准

### 3. 数据分析
- **简化分析**：减少分析复杂度
- **突出重点**：聚焦主要数据
- **提升效率**：快速获得关键信息

## 🔍 处理示例

### 示例数据（某时间段）
```
原始数据:
- 出入口A: 150辆 (37.5%)
- 出入口B: 120辆 (30.0%)
- 出入口C: 80辆 (20.0%)
- 出入口D: 30辆 (7.5%)
- 出入口E: 15辆 (3.75%) ← 小于5%
- 出入口F: 5辆 (1.25%)  ← 小于5%
总计: 400辆

处理后:
- 出入口A: 150辆 (37.5%)
- 出入口B: 120辆 (30.0%)
- 出入口C: 80辆 (20.0%)
- 出入口D: 30辆 (7.5%)
- 其他出入口: 20辆 (5.0%) ← 合并E+F
```

### 处理效果
- **主要出入口**：4个（A、B、C、D）
- **合并项目**：2个（E、F合并为"其他出入口"）
- **图例简化**：从6项减少到5项
- **重点突出**：前4个主要出入口更加明显

## 📈 控制台输出

### 处理信息
```
📊 时间段 08:00-09:00: 将 2 个出入口合并到'其他出入口'
   合并的出入口: 出入口E, 出入口F
   主要出入口数量: 4, 其他出入口总量: 20
```

### 无需合并情况
```
📊 时间段 10:00-11:00: 所有出入口占比均>=5%，无需合并
```

## 🎯 使用建议

### 1. 阈值设置
- **5%阈值**：适合大多数情况
- **可调整性**：可根据实际需要调整阈值
- **平衡考虑**：在简化和完整性之间找平衡

### 2. 分析策略
- **重点分析**：关注主要出入口的变化
- **趋势观察**：观察"其他出入口"占比的变化
- **详细分析**：必要时查看原始数据

### 3. 演示技巧
- **重点说明**：重点介绍主要出入口
- **简要提及**：简要说明"其他出入口"的含义
- **数据支撑**：用具体数值支撑分析结论

## 🔄 版本更新

### v3.1 新功能
- ✅ 5%阈值自动处理
- ✅ "其他出入口"智能合并
- ✅ 专业颜色动态分配
- ✅ 处理过程详细日志
- ✅ 视觉重点突出优化

### 兼容性
- 完全兼容现有的饼图功能
- 保持数据完整性和准确性
- 不影响其他图表的生成

---

*功能优化完成时间: 2025-06-20*  
*适用版本: parking_chart_generator.py v3.1+*
