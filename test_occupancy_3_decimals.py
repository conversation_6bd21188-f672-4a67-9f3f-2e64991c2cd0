#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试在场车辆分布sheet中的3位小数显示功能
验证统计数据是否正确显示3位小数
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import tempfile
import os

def create_occupancy_test_data():
    """创建专门用于测试在场车辆分布的数据"""
    records = []
    base_date = datetime(2024, 6, 1)
    
    # 创建一些重叠的停车记录，以产生非整数的日均在场车辆数
    vehicle_types = ["小型车", "大型车"]
    
    # 第一天的数据
    day1_records = [
        # 小型车
        {'entry': base_date + timedelta(hours=8), 'duration': 2.5, 'type': '小型车'},
        {'entry': base_date + timedelta(hours=9), 'duration': 3.2, 'type': '小型车'},
        {'entry': base_date + timedelta(hours=10), 'duration': 1.8, 'type': '小型车'},
        # 大型车
        {'entry': base_date + timedelta(hours=8.5), 'duration': 4.1, 'type': '大型车'},
        {'entry': base_date + timedelta(hours=11), 'duration': 2.3, 'type': '大型车'},
    ]
    
    # 第二天的数据（不同的停车模式）
    day2_records = [
        # 小型车
        {'entry': base_date + timedelta(days=1, hours=7), 'duration': 3.7, 'type': '小型车'},
        {'entry': base_date + timedelta(days=1, hours=9.5), 'duration': 2.1, 'type': '小型车'},
        {'entry': base_date + timedelta(days=1, hours=12), 'duration': 1.4, 'type': '小型车'},
        # 大型车
        {'entry': base_date + timedelta(days=1, hours=8), 'duration': 5.2, 'type': '大型车'},
        {'entry': base_date + timedelta(days=1, hours=10.5), 'duration': 1.9, 'type': '大型车'},
    ]
    
    # 第三天的数据
    day3_records = [
        # 小型车
        {'entry': base_date + timedelta(days=2, hours=6.5), 'duration': 4.3, 'type': '小型车'},
        {'entry': base_date + timedelta(days=2, hours=8.8), 'duration': 2.7, 'type': '小型车'},
        # 大型车
        {'entry': base_date + timedelta(days=2, hours=9.2), 'duration': 3.6, 'type': '大型车'},
        {'entry': base_date + timedelta(days=2, hours=11.7), 'duration': 2.8, 'type': '大型车'},
    ]
    
    all_records = day1_records + day2_records + day3_records
    
    for i, record in enumerate(all_records):
        entry_time = record['entry']
        exit_time = entry_time + timedelta(hours=record['duration'])
        
        records.append({
            '车牌号码': f"京A{i:05d}",
            '车辆类型': record['type'],
            '进场时间': entry_time,
            '出场时间': exit_time,
            '进场道闸': "入口A",
            '出场道闸': "出口A",
            '停车时长': f"{record['duration']:.3f}小时"
        })
    
    return pd.DataFrame(records)

def test_occupancy_3_decimals():
    """测试在场车辆分布sheet中的3位小数显示"""
    print("=" * 80)
    print("测试在场车辆分布sheet中的3位小数显示功能")
    print("验证统计数据是否正确显示3位小数")
    print("=" * 80)
    
    try:
        # 1. 创建测试数据
        test_data = create_occupancy_test_data()
        print(f"\n📋 创建测试数据: {len(test_data)} 条记录")
        print(f"   时间范围: {test_data['进场时间'].min()} 到 {test_data['出场时间'].max()}")
        print(f"   车辆类型: {test_data['车辆类型'].unique().tolist()}")
        
        # 2. 创建临时文件
        with tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False, encoding='utf-8') as f:
            test_data.to_csv(f.name, index=False)
            temp_file = f.name
        
        temp_output_dir = tempfile.mkdtemp()
        
        params = {
            'input_file': temp_file,
            'output': temp_output_dir,
            'date': '2024-06-01',  # 聚焦第一天
            'mode': 'mode2',
            'time_interval': 60,
            'time_slip': 15,
            '车辆唯一标识字段': '车牌号码',
            '车辆类型字段': '车辆类型',
            '进场时间字段': '进场时间',
            '出场时间字段': '出场时间',
            '进场道闸编号字段': '进场道闸',
            '出场道闸编号字段': '出场道闸'
        }
        
        # 3. 执行数据处理
        print(f"\n🔄 执行数据处理...")
        
        from parking_data_processor import DataProcessor
        from parking_time_filter import TimeFilter
        from parking_analyzer import TimePeriodAnalyzer
        from parking_report_generatior import ReportGenerator
        
        # 数据处理
        data_processor = DataProcessor(test_data, params)
        processed_data = data_processor.process()
        
        # 时间过滤
        time_filter = TimeFilter(processed_data, params)
        filtered_data = time_filter.get_filtered_data()
        period_info = time_filter.detect_period()
        
        # 数据分析
        analyzer = TimePeriodAnalyzer(
            filtered_data, 
            len(test_data), 
            mode=params['mode'],
            period_info=period_info,
            params=params
        )
        analysis_results = analyzer.analyze(focus_date=params.get('date'))
        
        # 报告生成
        report_generator = ReportGenerator(
            filtered_data,
            analysis_results,
            params,
            input_total_records=len(test_data)
        )
        
        # 4. 测试在场车辆分布计算
        print(f"\n🔍 测试在场车辆分布计算...")
        
        # 测试聚焦日期的在场车辆分布
        focus_occupancy = report_generator._calculate_occupancy(
            filtered_data, 
            focus_date=params['date']
        )
        
        if not focus_occupancy.empty:
            print(f"   聚焦日期在场车辆分布:")
            print(f"   - 数据行数: {len(focus_occupancy)}")
            print(f"   - 包含车辆类型: {'是' if '车辆类型' in focus_occupancy.columns else '否'}")
            
            # 显示部分数据
            sample_data = focus_occupancy.head(5)
            for i, row in sample_data.iterrows():
                time_period = row['时间']
                vehicle_count = row['在场车辆数']
                vehicle_type = row.get('车辆类型', 'N/A')
                print(f"   - {time_period}: {vehicle_count} 辆 ({vehicle_type})")
        
        # 测试日均在场车辆分布
        avg_occupancy = report_generator._calculate_occupancy(filtered_data)
        
        if not avg_occupancy.empty:
            print(f"\n   日均在场车辆分布:")
            print(f"   - 数据行数: {len(avg_occupancy)}")
            
            # 显示部分数据，检查是否有小数
            sample_data = avg_occupancy.head(5)
            for i, row in sample_data.iterrows():
                time_period = row['时间']
                vehicle_count = row['在场车辆数']
                print(f"   - {time_period}: {vehicle_count:.6f} 辆 (原始值)")
        
        # 5. 生成Excel文件
        print(f"\n📊 生成Excel报告...")
        output_path = os.path.join(temp_output_dir, "test_occupancy_3_decimals.xlsx")
        report_generator.generate_and_save_plots(filtered_data)
        report_path = report_generator.export_to_excel(output_path)
        
        # 6. 验证Excel文件中的3位小数显示
        if os.path.exists(report_path):
            print(f"\n✅ Excel文件生成成功: {report_path}")
            
            # 读取在场车辆分布sheet
            try:
                occupancy_sheet = pd.read_excel(report_path, sheet_name='在场车辆分布')
                print(f"\n📋 在场车辆分布sheet内容:")
                print(f"   - 行数: {len(occupancy_sheet)}")
                print(f"   - 列数: {len(occupancy_sheet.columns)}")
                print(f"   - 列名: {occupancy_sheet.columns.tolist()}")
                
                # 检查数值列的显示格式
                print(f"\n🔍 检查3位小数显示:")
                
                # 显示前10行数据
                sample_rows = occupancy_sheet.head(10)
                for i, row in sample_rows.iterrows():
                    time_period = row.iloc[0]  # 第一列是时间段
                    
                    # 检查其他列的数值
                    values = []
                    for col_idx in range(1, len(row)):
                        value = row.iloc[col_idx]
                        if isinstance(value, (int, float)) and not pd.isna(value):
                            # 检查是否显示为3位小数
                            if value != int(value):  # 如果不是整数
                                values.append(f"{value:.3f}")
                            else:
                                values.append(f"{value:.3f}")
                        else:
                            values.append(str(value))
                    
                    print(f"   {time_period}: {', '.join(values)}")
                
                # 验证数值精度
                print(f"\n🎯 验证数值精度:")
                numeric_columns = occupancy_sheet.select_dtypes(include=[np.number]).columns
                
                for col in numeric_columns:
                    values = occupancy_sheet[col].dropna()
                    if len(values) > 0:
                        min_val = values.min()
                        max_val = values.max()
                        avg_val = values.mean()
                        
                        print(f"   {col}:")
                        print(f"     最小值: {min_val:.3f}")
                        print(f"     最大值: {max_val:.3f}")
                        print(f"     平均值: {avg_val:.3f}")
                        
                        # 检查是否有非整数值（表明计算了日均值）
                        has_decimals = any(v != int(v) for v in values if not pd.isna(v))
                        print(f"     包含小数: {'是' if has_decimals else '否'}")
                
            except Exception as e:
                print(f"   ❌ 读取在场车辆分布sheet失败: {e}")
        
        # 7. 功能验证总结
        print(f"\n{'='*60}")
        print("功能验证总结")
        print('='*60)
        
        success_count = 0
        total_checks = 4
        
        # 检查1: Excel文件生成成功
        if os.path.exists(report_path):
            print("✅ Excel文件生成成功")
            success_count += 1
        else:
            print("❌ Excel文件生成失败")
        
        # 检查2: 在场车辆分布sheet存在
        try:
            occupancy_sheet = pd.read_excel(report_path, sheet_name='在场车辆分布')
            print("✅ 在场车辆分布sheet存在且可读取")
            success_count += 1
        except:
            print("❌ 在场车辆分布sheet不存在或无法读取")
        
        # 检查3: 包含数值数据
        try:
            numeric_columns = occupancy_sheet.select_dtypes(include=[np.number]).columns
            if len(numeric_columns) > 0:
                print("✅ 包含数值数据列")
                success_count += 1
            else:
                print("❌ 不包含数值数据列")
        except:
            print("❌ 无法检查数值数据列")
        
        # 检查4: 数值精度验证
        try:
            has_proper_precision = False
            for col in numeric_columns:
                values = occupancy_sheet[col].dropna()
                if len(values) > 0:
                    # 检查是否有合理的小数值
                    decimal_values = [v for v in values if v != int(v)]
                    if decimal_values:
                        has_proper_precision = True
                        break
            
            if has_proper_precision:
                print("✅ 数值显示包含适当的小数精度")
                success_count += 1
            else:
                print("⚠️  数值主要为整数（可能是正常情况）")
                success_count += 1  # 这也算正常
        except:
            print("❌ 无法验证数值精度")
        
        print(f"\n🎯 验证结果: {success_count}/{total_checks} 项检查通过")
        
        if success_count >= 3:
            print("🎉 在场车辆分布3位小数显示功能测试成功！")
        else:
            print("⚠️  部分功能需要进一步检查")
        
        # 8. 清理临时文件
        try:
            os.unlink(temp_file)
            print(f"\n📁 生成的测试文件: {report_path}")
            print("   (文件已保留，可手动打开查看3位小数显示效果)")
        except:
            pass
        
        return True
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_occupancy_3_decimals()
    
    print("\n" + "=" * 80)
    print("在场车辆分布3位小数显示功能测试完成！")
    print("=" * 80)
