2025-06-07 19:51:07,089 - main - INFO - 开始数据分析任务
2025-06-07 19:51:07,089 - main - INFO - 使用处理模式: mode_p2
2025-06-07 19:51:07,089 - main - INFO - 读取数据文件: C:\Users\<USER>\Desktop\停车分析\数据\火车站\5.31-6.2私家车.csv
2025-06-07 19:51:26,133 - DataReader - INFO - 检测到文件编码: GB2312
2025-06-07 19:51:26,374 - DataReader - INFO - 成功读取数据文件，共 15479 行
2025-06-07 19:51:26,402 - DataReader - INFO - 数据结构验证通过
2025-06-07 19:51:26,402 - main - INFO - 开始数据处理
2025-06-07 19:51:26,407 - DataProcessor - INFO - 开始数据处理
2025-06-07 19:51:26,551 - DataProcessor - INFO - 数据处理完成，统计信息: {'total_records': 15479, 'vehicle_type_counts': {'临时用户A': 15168, '免费用户A': 311}, 'entry_gate_counts': {'2号进口1': 10783, '地下3号入口2': 3161, '地下3号入口1': 1291, '地面入口': 139, '地面入口2': 37, '地下室2号出口-2': 31, '停车场1号西出口2': 25, '停车场1号西出口': 9, '地面出口': 3}, 'exit_gate_counts': {'地下室2号出口-2': 8948, '停车场1号西出口2': 3960, '停车场1号西出口': 2392, '地面出口': 142, '地面出口2': 37}, 'avg_duration': 1.955036931757004, 'max_duration': 256.15777777777777}
2025-06-07 19:51:26,558 - DataProcessor - WARNING - 过滤掉 3200 条异常停车时长记录
2025-06-07 19:51:26,581 - main - INFO - 开始数据分析
2025-06-07 19:51:26,632 - main - INFO - 生成分析报告
2025-06-07 19:51:26,633 - DataReader - INFO - 输出文件路径: reports\5.31-6.2私家车_分析结果_20250607_195126.xlsx
2025-06-07 19:51:26,749 - ReportGenerator - ERROR - 生成Excel报告失败: 'gate_counts'
Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\停车分析\pythoncode\1.开发中\p_parking_report_generator.py", line 204, in generate_excel_report
    '使用次数': self.results['gate_usage']['gate_counts'],
KeyError: 'gate_counts'
2025-06-07 19:51:26,751 - main - ERROR - 处理过程中出现错误: 'gate_counts'
Traceback (most recent call last):
  File "c:/Users/<USER>/Desktop/停车分析/pythoncode/1.开发中/p_parking_main.py", line 108, in main
    report_generator.generate_report(output_path, report_format)
  File "c:\Users\<USER>\Desktop\停车分析\pythoncode\1.开发中\p_parking_report_generator.py", line 326, in generate_report
    self.generate_excel_report(output_path)
  File "c:\Users\<USER>\Desktop\停车分析\pythoncode\1.开发中\p_parking_report_generator.py", line 204, in generate_excel_report
    '使用次数': self.results['gate_usage']['gate_counts'],
KeyError: 'gate_counts'
