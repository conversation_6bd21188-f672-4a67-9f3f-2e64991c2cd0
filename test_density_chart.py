#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试延停时长概率密度图表生成功能
"""

import pandas as pd
import numpy as np
import os
from parking_report_generator import ReportGenerator

def create_test_data():
    """创建测试数据"""
    np.random.seed(42)
    
    # 生成多样化的停车时长数据
    durations = []
    vehicle_types = []
    
    # 短时停车 (0-2小时) - 购物、办事
    short_durations = np.random.exponential(0.8, 60)  # 平均48分钟
    short_durations = np.clip(short_durations, 0.1, 2.0)
    durations.extend(short_durations)
    vehicle_types.extend(['小型车'] * len(short_durations))
    
    # 中时停车 (2-8小时) - 工作、会议
    medium_durations = np.random.normal(4.5, 1.2, 40)  # 平均4.5小时
    medium_durations = np.clip(medium_durations, 2.0, 8.0)
    durations.extend(medium_durations)
    vehicle_types.extend(['中型车'] * len(medium_durations))
    
    # 长时停车 (8小时-3天) - 过夜、住宿
    long_durations = np.random.lognormal(2.5, 0.8, 30)  # 对数正态分布
    long_durations = np.clip(long_durations, 8.0, 72.0)
    durations.extend(long_durations)
    vehicle_types.extend(['大型车'] * len(long_durations))
    
    # 超长时停车 (>3天) - 长期停放
    very_long_durations = np.random.uniform(72, 168, 10)  # 3-7天
    durations.extend(very_long_durations)
    vehicle_types.extend(['特种车'] * len(very_long_durations))
    
    # 创建完整数据
    n_records = len(durations)
    base_time = pd.Timestamp('2024-01-01 08:00:00')
    
    # 生成入场时间（随机分布在一天内）
    entry_times = []
    for i in range(n_records):
        hour_offset = np.random.uniform(0, 24)
        entry_times.append(base_time + pd.Timedelta(hours=hour_offset))
    
    # 计算出场时间
    exit_times = [entry_times[i] + pd.Timedelta(hours=durations[i]) for i in range(n_records)]
    
    test_data = pd.DataFrame({
        'entry_time': entry_times,
        'exit_time': exit_times,
        'duration': durations,  # 停车时长（小时）
        'vtype': vehicle_types,
        'vehicle_id': [f'车{i+1:04d}' for i in range(n_records)],
        'entry_gate': np.random.choice(['A口', 'B口', 'C口'], n_records),
        'exit_gate': np.random.choice(['A口', 'B口', 'C口'], n_records),
        'license_plate': [f'京A{i+1:05d}' for i in range(n_records)]
    })
    
    return test_data

def test_density_chart_generation():
    """测试延停时长概率密度图表生成"""
    print("=== 测试延停时长概率密度图表生成 ===\n")
    
    # 创建测试数据
    test_data = create_test_data()
    print(f"创建测试数据: {len(test_data)} 条记录")
    print(f"停车时长范围: {test_data['duration'].min():.2f} - {test_data['duration'].max():.2f} 小时")
    print(f"车辆类型分布: {test_data['vtype'].value_counts().to_dict()}")
    print()
    
    # 配置参数
    params = {
        'mode': 'mode2',
        '聚焦日期': '2024-01-01',
        '聚焦月份': '2024-01',
        # 自定义时长分段配置
        'duration_control_points': [2, 8, 24, 72],  # 2小时, 8小时, 1天, 3天
        'duration_period_lengths': [0.5, 1, 16, 48]  # 30分钟, 1小时, 16小时, 2天
    }
    
    # 创建报告生成器
    generator = ReportGenerator(test_data, params)
    generator.processed_data = test_data
    
    # 测试1：单独测试延停时长概率密度计算
    print("1. 测试延停时长概率密度数据计算:")
    try:
        density_stats = generator._calculate_duration_probability_density()
        if not density_stats.empty:
            print(f"   ✅ 数据计算成功，共 {len(density_stats)} 个时段")
            print(f"   ✅ 总记录数: {density_stats['频数'].sum()}")
            print(f"   ✅ 数据列: {list(density_stats.columns)}")
            
            # 显示前几个时段的数据
            print("\n   前5个时段数据:")
            for i in range(min(5, len(density_stats))):
                row = density_stats.iloc[i]
                print(f"     {row['时长区间']:25} | 频数: {row['频数']:3d} | 百分比: {row['百分比(%)']:6.2f}% | 累积: {row['累积百分比(%)']:6.2f}%")
        else:
            print("   ❌ 数据计算失败")
            return
    except Exception as e:
        print(f"   ❌ 数据计算异常: {str(e)}")
        return
    
    print("\n" + "="*70 + "\n")
    
    # 测试2：测试图表生成
    print("2. 测试延停时长概率密度图表生成:")
    try:
        # 创建临时Excel文件来测试图表生成
        import xlsxwriter
        test_output_path = os.path.join(os.getcwd(), "测试_延停时长概率密度图表.xlsx")
        
        # 删除已存在的文件
        if os.path.exists(test_output_path):
            os.remove(test_output_path)
        
        with pd.ExcelWriter(test_output_path, engine='xlsxwriter') as writer:
            workbook = writer.book
            
            # 先导出延停时长概率密度数据
            generator._export_duration_probability_density(writer, workbook)
            
            # 测试图表创建
            chart_config = {
                'title': '延停时长概率密度分布',
                'type': 'duration_probability_density',
                'position': (2, 0),
                'size': {'width': 600, 'height': 400}
            }
            
            chart = generator._create_chart_for_sheet(workbook, chart_config)
            
            if chart:
                print("   ✅ 图表对象创建成功")
                
                # 创建一个测试工作表来放置图表
                test_worksheet = workbook.add_worksheet('图表测试')
                
                # 插入图表
                test_worksheet.insert_chart(2, 0, chart, {
                    'x_scale': chart_config['size']['width'] / 480,
                    'y_scale': chart_config['size']['height'] / 288
                })
                
                # 添加标题
                title_format = workbook.add_format({
                    'bold': True,
                    'bg_color': '#D9E1F2',
                    'border': 1,
                    'align': 'center'
                })
                test_worksheet.write(1, 0, chart_config['title'], title_format)
                
                print("   ✅ 图表插入工作表成功")
            else:
                print("   ❌ 图表对象创建失败")
        
        if os.path.exists(test_output_path):
            print(f"   ✅ 测试文件生成成功: {test_output_path}")
            
            # 验证文件内容
            with pd.ExcelFile(test_output_path) as xls:
                sheet_names = xls.sheet_names
                print(f"   ✅ 包含工作表: {sheet_names}")
                
                if '延停时长概率密度' in sheet_names:
                    df = pd.read_excel(test_output_path, sheet_name='延停时长概率密度')
                    print(f"   ✅ 数据工作表: {len(df)} 行 x {len(df.columns)} 列")
                
                if '图表测试' in sheet_names:
                    print(f"   ✅ 图表工作表创建成功")
        else:
            print("   ❌ 测试文件生成失败")
            
    except Exception as e:
        print(f"   ❌ 图表生成异常: {str(e)}")
        import traceback
        traceback.print_exc()
    
    print("\n" + "="*70 + "\n")
    
    # 测试3：测试完整报告中的图表集成
    print("3. 测试完整报告中的图表集成:")
    try:
        full_report_path = os.path.join(os.getcwd(), "完整报告_含延停时长概率密度图.xlsx")
        
        # 删除已存在的文件
        if os.path.exists(full_report_path):
            os.remove(full_report_path)
        
        # 生成完整报告
        result_path = generator.export_to_excel(full_report_path)
        
        if result_path and os.path.exists(result_path):
            print(f"   ✅ 完整报告生成成功: {result_path}")
            
            # 验证图_分析日工作表
            with pd.ExcelFile(result_path) as xls:
                sheet_names = xls.sheet_names
                
                if '图_分析日' in sheet_names:
                    print(f"   ✅ '图_分析日'工作表存在")
                    
                    # 检查是否包含延停时长概率密度相关内容
                    # 注意：由于图表是嵌入的，我们无法直接读取图表内容
                    # 但可以检查数据源工作表是否存在
                    if '延停时长概率密度' in sheet_names:
                        print(f"   ✅ 延停时长概率密度数据源存在，图表应该已生成")
                    else:
                        print(f"   ⚠️  延停时长概率密度数据源不存在")
                else:
                    print(f"   ❌ '图_分析日'工作表不存在")
                
                print(f"   ✅ 报告包含 {len(sheet_names)} 个工作表")
        else:
            print("   ❌ 完整报告生成失败")
            
    except Exception as e:
        print(f"   ❌ 完整报告生成异常: {str(e)}")

if __name__ == "__main__":
    test_density_chart_generation()
    
    print("="*70)
    print("测试总结:")
    print("1. 延停时长概率密度数据计算")
    print("2. 延停时长概率密度图表生成")
    print("3. 图表在完整报告中的集成")
    print("如果所有测试通过，说明延停时长概率密度图表功能已成功实现！")
